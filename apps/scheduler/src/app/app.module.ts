import { InternalServerErrorException, Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { TasksService } from '../services/tasks/tasks.service';
import {
  FollowUpService,
  LibDataAccessModule,
  LibGlobalModule,
  LibProfileService,
} from '@core/libs';
import { PrismaClientModule } from '@core/prisma-client';
import { LibFcmService, LibNotificationService } from '@core_be/notifications';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { LibQueueService } from '../services/queue/queue.service';
import {
  AIMessagingService,
  LibAimlModule,
  LibOpenaiService,
} from '@core_be/aiml';
import hyperid from 'hyperid';
import { Options } from 'pino-http';
import { DestinationStream } from 'pino';
import { LoggerModule } from 'nestjs-pino';
import {
  LibSessionService,
  SystemConfigService,
  WS_LISTEN,
} from '@core_be/global';
import { ConsumerService } from 'libs/global/src/lib/queue/consumer.service';
import { PublisherService } from 'libs/global/src/lib/queue/publisher.service';
import { LibPlaceboService } from 'libs/global/src/lib/services/placebo.service';
import { HealerStatusRepository } from 'libs/data-access/src/lib/healer_status/healer_status.repository';

const instance = hyperid();
const pinoConfig: Options | DestinationStream | [Options, DestinationStream] = {
  autoLogging: false,
  level: 'info',
  timestamp: true,
  genReqId: () => instance.uuid, // BUG: currently UUID is not being set in the request id
  transport: {
    target: 'pino-pretty',
    options: {
      json: true,
      colorize: false,
      singleLine: true,
      translateTime: 'SYS:standard',
      ignore: 'pid,hostname',
    },
  },
  serializers: {
    req: (req) => ({
      id: req.id,
      method: req.method,
      url: req.url,
      remoteAddress: req.remoteAddress,
      remotePort: req.remotePort,
    }),
    res: (res) => ({
      statusCode: res.statusCode,
    }),
    err: (err) => ({
      type: err.constructor.name,
      message: err.message,
      stack: err.stack,
      ...(err.code && { code: err.code }),
      ...(err.statusCode && { statusCode: err.statusCode }),
    }),
    // Custom serializer for any object - ensures JSON objects are properly logged
    data: (data) => {
      if (typeof data === 'object' && data !== null) {
        try {
          return JSON.parse(JSON.stringify(data)); // Ensures proper JSON serialization
        } catch {
          return data;
        }
      }
      return data;
    },
  },
  formatters: {
    level: (label: string) => {
      return { level: label };
    },
    log: (object: any) => {
      // Ensure JSON objects are properly formatted
      if (typeof object === 'object' && object !== null) {
        return object;
      }
      return { message: object };
    },
  },
};

if (!process.env.NODE_ENV) {
  throw new InternalServerErrorException(
    'NODE_ENV is not set. Expected values are "local" or "development" or "production"'
  );
}

if (
  ['development'].includes(process.env.NODE_ENV.trim().toLowerCase())
) {
  pinoConfig.level = 'debug';
} else if (
  ['local'].includes(process.env.NODE_ENV.trim().toLowerCase())
) {
  pinoConfig.level = 'debug';
  pinoConfig.transport.options.colorize = true;
}

@Module({
  imports: [
    ScheduleModule.forRoot(),
    LibGlobalModule,
    LibDataAccessModule,
    PrismaClientModule,
    LibAimlModule,
    LoggerModule.forRoot({
      pinoHttp: pinoConfig,
    }),
    ClientsModule.registerAsync([
      {
        imports: [ConfigModule],
        name: WS_LISTEN.DELIMITER,
        useFactory: async (configService: ConfigService) => ({
          transport: Transport.REDIS,
          options: {
            host: configService.get('REDIS_HOST'),
            port: parseInt(configService.get('REDIS_PORT')),
            wildcards: true,
          },
        }),
        inject: [ConfigService],
      },
    ]),
  ],
  providers: [
    ConsumerService,
    TasksService,
    LibQueueService,
    LibFcmService,
    LibNotificationService,
    ConfigService,
    LibOpenaiService,
    AIMessagingService,
    FollowUpService,
    LibProfileService,
    LibSessionService,
    PublisherService,
    LibPlaceboService,
    HealerStatusRepository,
    SystemConfigService,
  ],
})
export class AppModule {}
