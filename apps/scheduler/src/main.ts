/**
 * This is not a production server yet!
 * This is only a minimal backend to get started.
 */

import { Logger } from 'nestjs-pino';
import { NestFactory } from '@nestjs/core';
import { LogLevel } from '@nestjs/common';

import { AppModule } from './app/app.module';
import { DatadogTracer } from '@core/libs';

const tracer = new DatadogTracer({
  service: 'scheduler',
  env: process.env.NODE_ENV || 'unknown',
  logInjection: true,
});

// Get log levels from environment
const getLogLevels = (): LogLevel[] => {
  const logLevel = process.env.LOG_LEVEL || 'error,warn,log';
  return logLevel.split(',').map((level) => level.trim()) as LogLevel[];
};

// This is a workaround for BigInt serialization issues in JSON
(BigInt.prototype as any).toJSON = function () {
  return this.toString();
};

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const globalPrefix = 'api';
  const logger = app.get<Logger>(Logger);
  app.setGlobalPrefix(globalPrefix);
  const port = process.env.SCHEDULER_PORT || 4000;

  app.useLogger(getLogLevels());
  await app.listen(port);

  tracer.use('pino', true);

  logger.log(
    `🚀 Scheduler is running on: http://localhost:${port}/${globalPrefix}`
  );
}

bootstrap();
