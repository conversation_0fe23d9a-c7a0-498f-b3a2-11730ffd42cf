import { FollowUpService } from '@core/libs';
import { LibQueueService } from '../queue/queue.service';
import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { LibNotificationService } from '@core_be/notifications';
import { Logger } from 'nestjs-pino';

@Injectable()
export class TasksService {
  constructor(
    private readonly queueService: LibQueueService,
    private readonly followUpService: FollowUpService,
    private readonly notificationService: LibNotificationService,
    private readonly logger: Logger
  ) {}

  // Cron job that runs every 5 Seconds
  // @Cron(CronExpression.EVERY_5_SECONDS)
  // async scheduleQueueSubStatus() {
  //   this.logger.debug(
  //     'Cron job executed every 5 sec at ' + new Date().toISOString()
  //   );

  //   await this.queueService.assignQueueNumbers(5, 1);
  // }

  // <PERSON>ron job that runs every 5 Seconds
  @Cron(CronExpression.EVERY_5_SECONDS)
  async scheduleTimeoutSessionOffers() {
    // --- mark session offers as NOT_AVAILABLE every 5 seconds
    this.logger.debug(
      'Healing session offer timeouts starts ' + new Date().toISOString()
    );
    await this.queueService.sessionOfferTimeouts();
  }

  // Cron job that runs every 5 Seconds
  @Cron(CronExpression.EVERY_5_SECONDS)
  async scheduleTimeoutStatus() {
    // --- update healing session status every 10 seconds
    this.logger.debug(
      'Healing session timeouts starts' +
        new Date().toISOString() +
        'new update for session'
    );
    await this.queueService.sessionUpdateAndTimeouts();
  }

  // Cron job that runs every 5 Seconds
  @Cron(CronExpression.EVERY_5_SECONDS)
  async scheduleMarkInactiveSessions() {
    // --- update healing session status every 10 seconds
    this.logger.debug(
      'Marking inactive healing sessions starts ' + new Date().toISOString()
    );
    await this.queueService.markInactiveSessions();
  }

  // Cron job that runs every 5 Seconds
  @Cron(CronExpression.EVERY_5_SECONDS)
  async scheduleHealingRoundStatus() {
    // --- update healing round status every 10 seconds now Time
    this.logger.debug(
      'Healing round timeouts starts ' +
        new Date().toISOString() +
        ' new update for rounds'
    );
    await this.queueService.healingRoundUpdateAndTimeouts();
  }

  @Cron(CronExpression.EVERY_5_MINUTES)
  async handleFollowUpNotification24Hours() {
    this.logger.debug(
      'Follow up services Cron job executed EVERY_5_MINUTES - 24hrs ' +
        new Date().toISOString()
    );

    await this.followUpService.sendFollowUpMessageEveryDay();
  }

  @Cron(CronExpression.EVERY_HOUR)
  async handleFollowUpNotification() {
    this.logger.debug(
      'Follow up services Cron job executed EVERY_HOUR - 7days ' +
        new Date().toISOString()
    );

    await this.followUpService.sendFollowUpMessageEveryWeek();
  }

  @Cron(CronExpression.EVERY_HOUR)
  async handleScheduledNotifications() {
    this.logger.debug(
      'Notification service Cron job executed EVERY_HOUR ' +
        new Date().toISOString()
    );

    await this.notificationService.sendPushNotifications();
  }
}
