/* eslint-disable @typescript-eslint/no-inferrable-types */
import {
  healing_rounds,
  healing_sessions,
  Prisma,
  PrismaService,
} from '@core/prisma-client';
import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import {
  BaseMessageType,
  HealingRoundSubStatus,
  NotificationStatus,
  SessionSubStatusType,
  SessionStatus,
  AbandonmentSubStatus,
  AiHealingRoundCheckInEvent,
  SenderType,
} from '@core/libs';
import { LibNotificationService } from '@core_be/notifications';
import { ConfigService } from '@nestjs/config';
import { AIHealingRoundService } from '@core_be/aiml';
import {
  LibSessionService,
  PushNotificationBodyType,
  PushNotificationTitleType,
  SessionType,
  WS_LISTEN,
} from '@core_be/global';
import { ClientProxy } from '@nestjs/microservices';
import { LibPlaceboService } from 'libs/global/src/lib/services/placebo.service';
import { Logger } from 'nestjs-pino';

@Injectable()
export class LibQueueService {
  private readonly SESSION_INTAKE_TIMEOUT: number;
  private readonly SESSION_IN_QUEUE_TIMEOUT: number;
  private readonly SESSION_IN_QUEUE_AVAILABLE_TIMEOUT: number;
  private readonly SESSION_AVAILABILITY_TIMEOUT: number;

  private readonly HEALING_ROUND_HEALER_ASSIGNED_TIMEOUT: number;
  private readonly HEALING_ROUND_FEEDBACK_REQUIRED_TIMEOUT: number;
  private readonly SESSION_OFFER_TIMEOUT: number;
  private currentTime = new Date();

  private HEALING_ROUND_UPDATE_DEBOUNCE: boolean;
  private HEALING_ROUND_LAST_RUN = new Date();
  // remove openai logic from here - move it to a microservice
  private readonly healingRoundAssistantID?: string;
  private readonly notificationMessage: {
    [key in SessionSubStatusType]: string;
  };
  constructor(
    private readonly notificationServices: LibNotificationService,
    private readonly configService: ConfigService,
    private readonly prisma: PrismaService,
    private readonly aiHealingService: AIHealingRoundService,
    private readonly aiHealingRoundService: AIHealingRoundService,
    private readonly libSessionService: LibSessionService,
    private readonly libPlaceboService: LibPlaceboService,
    private readonly notificationService: LibNotificationService,
    private readonly logger: Logger,
    @Inject(WS_LISTEN.DELIMITER) private wsClient: ClientProxy
  ) {
    // Timeout configurations
    this.SESSION_INTAKE_TIMEOUT =
      parseInt('' + this.configService.get('SESSION_INTAKE_TIMEOUT')) ||
      43200000;

    this.SESSION_IN_QUEUE_TIMEOUT =
      parseInt('' + this.configService.get('SESSION_IN_QUEUE_TIMEOUT')) ||
      2592000000;
    this.SESSION_AVAILABILITY_TIMEOUT =
      parseInt('' + this.configService.get('SESSION_AVAILABILITY_TIMEOUT')) ||
      1800000;

    this.SESSION_IN_QUEUE_AVAILABLE_TIMEOUT =
      parseInt(
        '' + this.configService.get('SESSION_IN_QUEUE_AVAILABLE_TIMEOUT')
      ) || 3600000;

    this.HEALING_ROUND_HEALER_ASSIGNED_TIMEOUT =
      parseInt(
        '' + this.configService.get('HEALING_ROUND_HEALER_ASSIGNED_TIMEOUT')
      ) || 20000;

    this.HEALING_ROUND_FEEDBACK_REQUIRED_TIMEOUT =
      parseInt(
        '' + this.configService.get('HEALING_ROUND_FEEDBACK_REQUIRED_TIMEOUT')
      ) || 60000;

    this.SESSION_OFFER_TIMEOUT =
      parseInt('' + this.configService.get('SESSION_OFFER_TIMEOUT')) || 120000;

    // Open AI Configuration
    this.healingRoundAssistantID = this.configService.getOrThrow<string>(
      'HEALING_ROUND_ASSISTANT_ID'
    );

    // Notification messages
    this.notificationMessage = {
      [SessionSubStatusType.AVAILABLE]: this.configService.getOrThrow<string>(
        `${SessionSubStatusType.AVAILABLE}_NOTIFICATION_BODY`
      ),
      // [SessionSubStatusType.IN_FRONT_OF_QUEUE]: this.configService.getOrThrow<string>(
      //   `${SessionSubStatusType.IN_FRONT_OF_QUEUE}_NOTIFICATION_BODY`
      // ),
      [SessionSubStatusType.AI_CONFIRMATION_REQUIRED]:
        this.configService.getOrThrow<string>(
          `${SessionSubStatusType.AI_CONFIRMATION_REQUIRED}_NOTIFICATION_BODY`
        ),
      // [SessionSubStatusType.ENTER_IN_QUEUE]: this.configService.getOrThrow<string>(
      //   `${SessionSubStatusType.ENTER_IN_QUEUE}_NOTIFICATION_BODY`
      // ),
    } as any;
  }

  private hasTimeoutPassed(
    startTime: Date,
    timeout: number,
    endTime = new Date()
  ) {
    return endTime.getTime() - startTime.getTime() > timeout;
  }

  async updateHealingSessionStatus(
    session_id: number,
    data: Partial<healing_sessions>
  ) {
    return this.prisma.healing_sessions.update({
      where: { session_id: session_id },
      data,
    });
  }
  async updateHealingRoundStatus(
    round_id: number,
    data: Partial<healing_rounds>
  ) {
    return this.prisma.healing_rounds.update({
      where: { round_id },
      data,
    });
  }

  async getOldestAvailablePatient() {
    return await this.prisma.healing_sessions.findMany({
      where: {
        status: SessionStatus.IN_QUEUE,
        sub_status: SessionSubStatusType.AVAILABLE,
        is_deleted: false,
      },
    });
  }

  formatNotification<T = Record<string, unknown>>(
    token: string,
    notificationStatus: NotificationStatus,
    sent_at: Date,
    notification: {
      title: string;
      body: string;
    },
    payload: T
  ): BaseMessageType | false {
    if (token) {
      return {
        token,
        notificationStatus,
        sent_at,
        payload: {
          notification,
          data: {
            status: NotificationStatus.SENT,
            data: JSON.stringify(payload),
          },
        },
      };
    } else return false;
  }

  private getMessageSessionDataForIndex(
    session: healing_sessions,
    index: number,
    newAvailableSlots: number,
    frontOfQueueNumber: number,
    fcm_token: string
  ): [Prisma.healing_sessionsUpdateInput, false | BaseMessageType] {
    let messagePayload: BaseMessageType | false = false;
    const sessionData: Prisma.healing_sessionsUpdateInput = {
      queue_number: index + 1,
      updated_at: new Date(),
      sub_status_updated_at: new Date(),
      sub_status: SessionSubStatusType.AVAILABLE,
    };
    if (
      index < newAvailableSlots &&
      session.sub_status !== SessionSubStatusType.AVAILABLE
    ) {
      sessionData.sub_status = SessionSubStatusType.AVAILABLE;
    }
    if (session.queue_number === null) {
      sessionData.queue_start_time = new Date();
    }
    if (
      sessionData.sub_status &&
      session.sub_status !== sessionData.sub_status &&
      this.notificationMessage[sessionData.sub_status + ''].trim().length > 0
    ) {
      const notificationTitleBody = this.notificationMessage[
        sessionData.sub_status + ''
      ]
        .trim()
        .split(' | ');
      messagePayload = this.formatNotification(
        fcm_token,
        NotificationStatus.DRAFT,
        new Date(),
        {
          title: notificationTitleBody[0],
          body: notificationTitleBody[1],
        },
        {
          profileId: session.profile_id,
          sessionId: session.session_id,
          status: SessionStatus.IN_QUEUE,
          sub_status: sessionData.sub_status,
          session_queue_info: {
            queue_number: session.queue_number,
            queue_status: sessionData.sub_status,
          },
        }
      );
    }
    return [sessionData, messagePayload];
  }

  async assignQueueNumbers(
    frontOfQueueNumber: number = 3,
    maxAvailableNumber: number = 1
  ) {
    const messagePayloads: BaseMessageType[] = [];
    const healingSessionsInAvailableCount =
      await this.prisma.healing_sessions.count({
        where: {
          status: SessionStatus.IN_QUEUE,
          sub_status: SessionSubStatusType.AVAILABLE,
        },
      });
    const newAvailableSlots =
      maxAvailableNumber - healingSessionsInAvailableCount || 0;
    const healingSessions = await this.prisma.healing_sessions.findMany({
      where: {
        status: SessionStatus.IN_QUEUE,
        sub_status: {
          in: [SessionSubStatusType.AVAILABLE],
        },
        is_deleted: false,
      },
      take: 500,
      orderBy: {
        session_id: 'asc',
      },
      include: {
        profiles: {
          select: {
            users: {
              select: {
                user_devices: {
                  where: {
                    is_deleted: false,
                  },
                },
              },
            },
          },
        },
      },
    });
    const updatedSessionsData: {
      [key: number]: Prisma.healing_sessionsUncheckedUpdateInput;
    } = {};
    healingSessions.forEach((session, index) => {
      const [sessionData, messagePayload] = this.getMessageSessionDataForIndex(
        session,
        index,
        newAvailableSlots,
        frontOfQueueNumber,
        session.profiles.users.user_devices[0].fcm_token
      );

      if (messagePayload) {
        messagePayloads.push(messagePayload);
      }
      updatedSessionsData['' + session.session_id] = sessionData;
    });
    if (messagePayloads.length > 0) {
      await this.notificationServices.generateAndDispatchNotification(
        messagePayloads
      );
    }

    if (Object.entries(updatedSessionsData)?.length === 0) {
      return {
        maxQueueNumber: healingSessions.length,
        sessions: [],
      };
    }

    const sessionsToUpdate = Object.entries(updatedSessionsData);
    for (let i = 0, iL = sessionsToUpdate.length; i < iL; i++) {
      const [key, value] = sessionsToUpdate[i];
      const profile_id = healingSessions[i].profile_id;
      await this.libSessionService.update(
        +key,
        value,
        profile_id,
        this.wsClient
      );
    }

    return {
      maxQueueNumber: healingSessions.length,
      sessions: sessionsToUpdate,
    };
  }

  async setSessionStatus(
    sessionId: number,
    newStatus: SessionStatus,
    newSubStatus?: string
  ) {
    const session = await this.prisma.healing_sessions.findFirst({
      where: { session_id: sessionId },
    });

    if (!session) {
      throw new BadRequestException('Session Not Found!');
    }

    session.status = newStatus;
    return this.prisma.healing_sessions.update({
      where: { session_id: sessionId },
      data: {
        status: newStatus,
        sub_status: newSubStatus || null,
        sub_status_updated_at: new Date(),
      },
    });
  }

  async sessionOfferTimeouts() {
    const sessionOffers = await this.prisma.healing_sessions.findMany({
      where: {
        status: SessionStatus.IN_QUEUE,
        sub_status: SessionSubStatusType.CONFIRMATION_REQUIRED,
        is_deleted: false,
      },
      include: {
        profiles: {
          select: {
            users: {
              select: {
                user_id: true,
              },
            },
          },
        },
      },
      take: 10000,
    });

    sessionOffers.forEach(async (session) => {
      this.currentTime = new Date();
      const sessionOfferTime = session.last_session_offer_at;
      if (
        this.currentTime.getTime() - sessionOfferTime.getTime() >
        this.SESSION_OFFER_TIMEOUT
      ) {
        const updatePayload = {
          healer_id: null,
          sub_status: SessionSubStatusType.SESSION_MISSED,
          sub_status_updated_at: new Date(),
          updated_at: new Date(),
        };
        const profile_id = session.profile_id;
        await this.libSessionService.update(
          session.session_id,
          updatePayload,
          profile_id,
          this.wsClient
        );
        await this.notificationService.sendPushNotification(
          session?.profiles?.users?.user_id,
          {
            title: PushNotificationTitleType.SESSION_MISSED,
            body: PushNotificationBodyType.SESSION_MISSED,
          }
        );
        if (session.session_type === SessionType.HEALER) {
          try {
            // Assign next available patient to healer
            await this.libSessionService.processHealerRequest({
              profile_id: session.healer_id,
            } as { profile_id: number });
          } catch (error) {
            console.error(
              `Error assigning next available patient for the healer ${session.healer_id}:`,
              error
            );
          }
        }
      }
    });

    // if (Object.entries(updatedSessionsData)?.length !== 0) {
    //   for (
    //     let i = 0,
    //       objectEntries = Object.entries(updatedSessionsData),
    //       iL = objectEntries.length;
    //     i < iL;
    //     i++
    //   ) {
    //     const [key, value] = objectEntries[i];
    //     const profile_id = sessionOffers[i].profile_id;
    //     await this.libSessionService.update(
    //       +key,
    //       value,
    //       profile_id,
    //       this.wsClient
    //     );
    //   }
    // }
  }

  async markInactiveSessions() {
    // Collecting users with available sessions that have not been seen for 30 minutes
    const inactiveUserSessions = await this.prisma.healing_sessions.findMany({
      where: {
        profiles: {
          users: {
            user_devices: {
              every: {
                last_seen_at: {
                  lte: new Date(
                    new Date().getTime() - this.SESSION_AVAILABILITY_TIMEOUT // minus 30 mins
                  ),
                },
                is_deleted: false,
              },
            },
          },
        },
        status: SessionStatus.IN_QUEUE,
        sub_status: SessionSubStatusType.AVAILABLE,
      },
      include: {
        profiles: {
          select: {
            users: true,
          },
        },
      },
      take: 100000,
    });

    inactiveUserSessions.forEach(async (session) => {
      await this.prisma.healing_sessions.update({
        where: { session_id: session.session_id },
        data: {
          sub_status: SessionSubStatusType.NOT_AVAILABLE,
          sub_status_updated_at: new Date(),
          updated_at: new Date(),
        },
      });
      await this.notificationServices.sendPushNotification(
        session?.profiles?.users?.user_id,
        {
          title: PushNotificationTitleType.MARK_UNAVAILABLE,
          body: PushNotificationBodyType.MARK_UNAVAILABLE,
        }
      );
    });
  }

  async sessionUpdateAndTimeouts() {
    const openSessions = await this.prisma.healing_sessions.findMany({
      where: {
        status: {
          notIn: [
            SessionStatus.ABANDONED,
            SessionStatus.COMPLETED,
            SessionStatus.CANCELLED,
          ],
        },
        is_deleted: false,
      },
      take: 100,
    });

    const updatedSessionsData: {
      [key: number]: Prisma.healing_sessionsUpdateInput;
    } = {};

    openSessions.forEach((session) => {
      this.currentTime = new Date();
      const sessionStartTime = session.updated_at;

      if (
        session.status === SessionStatus.INTAKE &&
        this.currentTime.getTime() - sessionStartTime.getTime() >
          this.SESSION_INTAKE_TIMEOUT
      ) {
        updatedSessionsData[session.session_id] = {
          status: SessionStatus.ABANDONED,
          sub_status: AbandonmentSubStatus.TIMEOUT,
          sub_status_updated_at: new Date(),
          updated_at: new Date(),
        };
      }

      if (
        session.status === SessionStatus.IN_QUEUE &&
        this.currentTime.getTime() - sessionStartTime.getTime() >
          this.SESSION_IN_QUEUE_TIMEOUT
      ) {
        updatedSessionsData[session.session_id] = {
          status: SessionStatus.ABANDONED,
          sub_status: AbandonmentSubStatus.TIMEOUT,
          sub_status_updated_at: new Date(),
          updated_at: new Date(),
        };
      }

      // if (
      //   session.status === SessionStatus.IN_QUEUE &&
      //   session.sub_status === SessionSubStatusType.AVAILABLE &&
      //   this.currentTime.getTime() - session.updated_at.getTime() >
      //     this.SESSION_IN_QUEUE_AVAILABLE_TIMEOUT
      // ) {
      //   updatedSessionsData[session.session_id] = {
      //     status: SessionStatus.IN_QUEUE,
      //     sub_status: SessionSubStatusType.AI_CONFIRMATION_REQUIRED,
      //     sub_status_updated_at: new Date(),
      //     updated_at: new Date(),
      //   };
      // }
    });

    if (Object.entries(updatedSessionsData)?.length !== 0) {
      for (
        let i = 0,
          objectEntries = Object.entries(updatedSessionsData),
          iL = objectEntries.length;
        i < iL;
        i++
      ) {
        const [key, value] = objectEntries[i];
        const profile_id = openSessions[i].profile_id;
        await this.libSessionService.update(
          +key,
          value,
          profile_id,
          this.wsClient
        );
      }
    }
  }

  async healingRoundUpdateAndTimeouts() {
    this.currentTime = new Date();

    // debounce logic - to be removed or reduced after offload to microservice
    if (
      this.HEALING_ROUND_UPDATE_DEBOUNCE &&
      this.currentTime.getTime() - this.HEALING_ROUND_LAST_RUN.getTime() > 30000
    ) {
      this.logger.log('skipping execution, as last execution in progress');
      return;
    }
    this.HEALING_ROUND_LAST_RUN = new Date();
    this.HEALING_ROUND_UPDATE_DEBOUNCE = true;
    this.logger.debug(
      'HEALING ROUND TIMEOUTS - LAST RUN - ',
      this.HEALING_ROUND_LAST_RUN
    );
    // debounce logic ends here

    const sessions = await this.prisma.healing_sessions.findMany({
      where: {
        status: { in: [`${SessionStatus.HEALING_ROUND}`] },
        is_deleted: false,
        healer_id: { not: null },
      },
      include: {
        healing_rounds: {
          orderBy: { round_id: 'desc' },
          take: 1,
        },
        conversations: {
          where: {
            is_deleted: false,
            round_id: { not: null },
          },
          orderBy: { conversation_id: 'desc' },
          take: 1,
        },
      },
    });

    for (const session of sessions) {
      const subStatusUpdatedAt = session.sub_status_updated_at;

      if (session.healing_rounds && session.healing_rounds[0]) {
        const lastHealingRound = session.healing_rounds[0];
        const healingRoundStart =
          lastHealingRound.round_start_at || lastHealingRound.created_at;
        const healingRoundUpdatedAt = lastHealingRound.updated_at;

        // 2. IN_PROGRESS Timeout (5 minutes)
        if (
          session.status === SessionStatus.HEALING_ROUND &&
          session.sub_status === HealingRoundSubStatus.IN_PROGRESS &&
          this.hasTimeoutPassed(healingRoundStart, lastHealingRound.max_time)
        ) {
          if (session.session_type === SessionType.PLACEBO) {
            this.libPlaceboService.processPlaceboHealingRoundCheckIn({
              session_id: session.session_id,
              round_id: lastHealingRound.round_id,
            });
          } else {
            this.aiHealingRoundService.processHealingRoundCheckIn(
              new AiHealingRoundCheckInEvent({
                profiles: {
                  connect: {
                    profile_id: session.healer_id,
                  },
                },
                healing_sessions: {
                  connect: {
                    session_id: session.session_id,
                  },
                },
              })
            );
          }
          continue;
        }

        // 3. FEEDBACK_REQUIRED Timeout (60 seconds)
        if (
          session.status === SessionStatus.HEALING_ROUND &&
          session.sub_status === HealingRoundSubStatus.FEEDBACK_REQUIRED &&
          this.hasTimeoutPassed(
            healingRoundUpdatedAt,
            this.HEALING_ROUND_FEEDBACK_REQUIRED_TIMEOUT *
              (session.conversations[0] &&
              session.conversations[0].sender_type == SenderType.AI
                ? 1
                : 3)
          )
        ) {
          this.logger.log(
            'Round set ABANDONED after 5 mins from FEEDBACK_REQUIRED'
          );
          this.setHealingRoundStatus(
            session.session_id,
            lastHealingRound.round_id,
            SessionStatus.ABANDONED
          );
          continue;
        }

        // 4. Proceed confirmation Timeout (60 seconds)
        if (
          session.status === SessionStatus.HEALING_ROUND &&
          session.sub_status === HealingRoundSubStatus.COMPLETED &&
          this.hasTimeoutPassed(
            healingRoundUpdatedAt,
            this.HEALING_ROUND_HEALER_ASSIGNED_TIMEOUT
          )
        ) {
          this.logger.log(
            'Round set ABANDONED after 60 secs Proceed confirmation'
          );
          if (session.session_type === SessionType.PLACEBO) {
            this.libPlaceboService.processPlaceboHealingRoundStart(
              session.session_id
            );
          } else {
            this.aiHealingService.processHealingRoundStart(
              {
                session_id: session.session_id,
                profile_id: session.healer_id,
              },
              null,
              true
            );
          }

          continue;
        }
      }

      if (
        session.status === SessionStatus.HEALING_ROUND &&
        session.sub_status === HealingRoundSubStatus.HEALER_ASSIGNED &&
        session.healer_id &&
        this.hasTimeoutPassed(
          subStatusUpdatedAt,
          this.HEALING_ROUND_HEALER_ASSIGNED_TIMEOUT
        )
      ) {
        this.aiHealingService.processHealingRoundStart(
          {
            session_id: session.session_id,
            profile_id: session.healer_id,
          },
          null,
          true
        );
        continue;
      }
    }

    this.HEALING_ROUND_UPDATE_DEBOUNCE = false;
  }

  async setHealingRoundStatus(
    sessionId: number,
    roundId: number,
    newSessionStatus?: string,
    newRoundStatus?: string
  ): Promise<{ session: healing_sessions; round: healing_rounds }> {
    const healingRoundData: Prisma.healing_roundsUpdateInput = {
      status:
        newSessionStatus == SessionStatus.HEALING_ROUND
          ? newRoundStatus
          : HealingRoundSubStatus.COMPLETED,
      updated_at: new Date(),
      round_end_at: new Date(),
    };
    const isFeedbackRequired =
      newRoundStatus == HealingRoundSubStatus.FEEDBACK_REQUIRED;
    if (isFeedbackRequired) {
      healingRoundData.feedback_start_at = new Date();
      healingRoundData.remaining_time = 0;
    }

    const txResult = await this.prisma.$transaction(async (tx) => {
      const healingRoundUpdate = await tx.healing_rounds.update({
        where: {
          round_id: roundId,
        },
        data: healingRoundData,
      });
      const sessionUpdatePayload: Prisma.healing_sessionsUpdateInput = {
        status: newSessionStatus,
        sub_status:
          newSessionStatus === SessionStatus.ABANDONED
            ? AbandonmentSubStatus.TIMEOUT
            : newRoundStatus,
        sub_status_updated_at: new Date(),
        updated_at: new Date(),
      };
      if (newSessionStatus === SessionStatus.ABANDONED) {
        sessionUpdatePayload.healer_id = null;
      }
      const sessionUpdate = await tx.healing_sessions.update({
        where: {
          session_id: sessionId,
        },
        data: sessionUpdatePayload,
      });

      return {
        healingRoundUpdate,
        sessionUpdate,
      };
    });
    return {
      session: txResult.sessionUpdate,
      round: txResult.healingRoundUpdate,
    };
  }
}
