/* eslint-disable @typescript-eslint/no-inferrable-types */
import {
  healing_rounds,
  healing_sessions,
  Prisma,
  PrismaService,
} from '@core/prisma-client';
import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import {
  BaseMessageType,
  HealingRoundSubStatus,
  NotificationStatus,
  SessionSubStatusType,
  SessionStatus,
  AbandonmentSubStatus,
  AiHealingRoundCheckInEvent,
  SenderType,
} from '@core/libs';
import { LibNotificationService } from '@core_be/notifications';
import { ConfigService } from '@nestjs/config';
import { AIHealingRoundService } from '@core_be/aiml';
import {
  LibSessionService,
  PushNotificationBodyType,
  PushNotificationTitleType,
  SessionType,
  WS_LISTEN,
} from '@core_be/global';
import { ClientProxy } from '@nestjs/microservices';
import { LibPlaceboService } from 'libs/global/src/lib/services/placebo.service';
import { Logger } from 'nestjs-pino';

@Injectable()
export class LibQueueService {
  private readonly SESSION_INTAKE_TIMEOUT: number;
  private readonly SESSION_IN_QUEUE_TIMEOUT: number;
  private readonly SESSION_IN_QUEUE_AVAILABLE_TIMEOUT: number;
  private readonly SESSION_AVAILABILITY_TIMEOUT: number;

  private readonly HEALING_ROUND_HEALER_ASSIGNED_TIMEOUT: number;
  private readonly HEALING_ROUND_FEEDBACK_REQUIRED_TIMEOUT: number;
  private readonly SESSION_OFFER_TIMEOUT: number;
  private currentTime = new Date();

  private HEALING_ROUND_UPDATE_DEBOUNCE: boolean;
  private HEALING_ROUND_LAST_RUN = new Date();
  private readonly notificationMessage: {
    [key in SessionSubStatusType]: string;
  };
  constructor(
    private readonly notificationServices: LibNotificationService,
    private readonly configService: ConfigService,
    private readonly prisma: PrismaService,
    private readonly aiHealingService: AIHealingRoundService,
    private readonly aiHealingRoundService: AIHealingRoundService,
    private readonly libSessionService: LibSessionService,
    private readonly libPlaceboService: LibPlaceboService,
    private readonly notificationService: LibNotificationService,
    private readonly logger: Logger,
    @Inject(WS_LISTEN.DELIMITER) private wsClient: ClientProxy
  ) {
    // Timeout configurations
    this.SESSION_INTAKE_TIMEOUT =
      parseInt('' + this.configService.get('SESSION_INTAKE_TIMEOUT')) ||
      43200000;

    this.SESSION_IN_QUEUE_TIMEOUT =
      parseInt('' + this.configService.get('SESSION_IN_QUEUE_TIMEOUT')) ||
      2592000000;
    this.SESSION_AVAILABILITY_TIMEOUT =
      parseInt('' + this.configService.get('SESSION_AVAILABILITY_TIMEOUT')) ||
      1800000;

    this.SESSION_IN_QUEUE_AVAILABLE_TIMEOUT =
      parseInt(
        '' + this.configService.get('SESSION_IN_QUEUE_AVAILABLE_TIMEOUT')
      ) || 3600000;

    this.HEALING_ROUND_HEALER_ASSIGNED_TIMEOUT =
      parseInt(
        '' + this.configService.get('HEALING_ROUND_HEALER_ASSIGNED_TIMEOUT')
      ) || 20000;

    this.HEALING_ROUND_FEEDBACK_REQUIRED_TIMEOUT =
      parseInt(
        '' + this.configService.get('HEALING_ROUND_FEEDBACK_REQUIRED_TIMEOUT')
      ) || 60000;

    this.SESSION_OFFER_TIMEOUT =
      parseInt('' + this.configService.get('SESSION_OFFER_TIMEOUT')) || 120000;

    // Notification messages
    this.notificationMessage = {
      [SessionSubStatusType.AVAILABLE]: this.configService.getOrThrow<string>(
        `${SessionSubStatusType.AVAILABLE}_NOTIFICATION_BODY`
      ),
      // [SessionSubStatusType.IN_FRONT_OF_QUEUE]: this.configService.getOrThrow<string>(
      //   `${SessionSubStatusType.IN_FRONT_OF_QUEUE}_NOTIFICATION_BODY`
      // ),
      [SessionSubStatusType.AI_CONFIRMATION_REQUIRED]:
        this.configService.getOrThrow<string>(
          `${SessionSubStatusType.AI_CONFIRMATION_REQUIRED}_NOTIFICATION_BODY`
        ),
      // [SessionSubStatusType.ENTER_IN_QUEUE]: this.configService.getOrThrow<string>(
      //   `${SessionSubStatusType.ENTER_IN_QUEUE}_NOTIFICATION_BODY`
      // ),
    } as any;
  }

  private hasTimeoutPassed(
    startTime: Date,
    timeout: number,
    endTime = new Date()
  ) {
    return endTime.getTime() - startTime.getTime() > timeout;
  }

  async updateHealingSessionStatus(
    session_id: number,
    data: Partial<healing_sessions>
  ) {
    return this.prisma.healing_sessions.update({
      where: { session_id: session_id },
      data,
    });
  }
  async updateHealingRoundStatus(
    round_id: number,
    data: Partial<healing_rounds>
  ) {
    return this.prisma.healing_rounds.update({
      where: { round_id },
      data,
    });
  }

  async getOldestAvailablePatient() {
    return await this.prisma.healing_sessions.findMany({
      where: {
        status: SessionStatus.IN_QUEUE,
        sub_status: SessionSubStatusType.AVAILABLE,
        is_deleted: false,
      },
    });
  }

  formatNotification<T = Record<string, unknown>>(
    token: string,
    notificationStatus: NotificationStatus,
    sent_at: Date,
    notification: {
      title: string;
      body: string;
    },
    payload: T
  ): BaseMessageType | false {
    if (token) {
      return {
        token,
        notificationStatus,
        sent_at,
        payload: {
          notification,
          data: {
            status: NotificationStatus.SENT,
            data: JSON.stringify(payload),
          },
        },
      };
    } else return false;
  }

  private getMessageSessionDataForIndex(
    session: healing_sessions,
    index: number,
    newAvailableSlots: number,
    frontOfQueueNumber: number,
    fcm_token: string
  ): [Prisma.healing_sessionsUpdateInput, false | BaseMessageType] {
    let messagePayload: BaseMessageType | false = false;
    const sessionData: Prisma.healing_sessionsUpdateInput = {
      queue_number: index + 1,
      updated_at: new Date(),
      sub_status_updated_at: new Date(),
      sub_status: SessionSubStatusType.AVAILABLE,
    };
    if (
      index < newAvailableSlots &&
      session.sub_status !== SessionSubStatusType.AVAILABLE
    ) {
      sessionData.sub_status = SessionSubStatusType.AVAILABLE;
    }
    if (session.queue_number === null) {
      sessionData.queue_start_time = new Date();
    }
    if (
      sessionData.sub_status &&
      session.sub_status !== sessionData.sub_status &&
      this.notificationMessage[sessionData.sub_status + ''].trim().length > 0
    ) {
      const notificationTitleBody = this.notificationMessage[
        sessionData.sub_status + ''
      ]
        .trim()
        .split(' | ');
      messagePayload = this.formatNotification(
        fcm_token,
        NotificationStatus.DRAFT,
        new Date(),
        {
          title: notificationTitleBody[0],
          body: notificationTitleBody[1],
        },
        {
          profileId: session.profile_id,
          sessionId: session.session_id,
          status: SessionStatus.IN_QUEUE,
          sub_status: sessionData.sub_status,
          session_queue_info: {
            queue_number: session.queue_number,
            queue_status: sessionData.sub_status,
          },
        }
      );
    }
    return [sessionData, messagePayload];
  }

  async assignQueueNumbers(
    frontOfQueueNumber: number = 3,
    maxAvailableNumber: number = 1
  ) {
    const messagePayloads: BaseMessageType[] = [];
    const healingSessionsInAvailableCount =
      await this.prisma.healing_sessions.count({
        where: {
          status: SessionStatus.IN_QUEUE,
          sub_status: SessionSubStatusType.AVAILABLE,
        },
      });
    const newAvailableSlots =
      maxAvailableNumber - healingSessionsInAvailableCount || 0;
    const healingSessions = await this.prisma.healing_sessions.findMany({
      where: {
        status: SessionStatus.IN_QUEUE,
        sub_status: {
          in: [SessionSubStatusType.AVAILABLE],
        },
        is_deleted: false,
      },
      take: 500,
      orderBy: {
        session_id: 'asc',
      },
      include: {
        profiles: {
          select: {
            users: {
              select: {
                user_devices: {
                  where: {
                    is_deleted: false,
                  },
                },
              },
            },
          },
        },
      },
    });
    const updatedSessionsData: {
      [key: number]: Prisma.healing_sessionsUncheckedUpdateInput;
    } = {};
    healingSessions.forEach((session, index) => {
      const [sessionData, messagePayload] = this.getMessageSessionDataForIndex(
        session,
        index,
        newAvailableSlots,
        frontOfQueueNumber,
        session.profiles.users.user_devices[0].fcm_token
      );

      if (messagePayload) {
        messagePayloads.push(messagePayload);
      }
      updatedSessionsData['' + session.session_id] = sessionData;
    });
    if (messagePayloads.length > 0) {
      await this.notificationServices.generateAndDispatchNotification(
        messagePayloads
      );
    }

    if (Object.entries(updatedSessionsData)?.length === 0) {
      return {
        maxQueueNumber: healingSessions.length,
        sessions: [],
      };
    }

    const sessionsToUpdate = Object.entries(updatedSessionsData);
    for (let i = 0, iL = sessionsToUpdate.length; i < iL; i++) {
      const [key, value] = sessionsToUpdate[i];
      const profile_id = healingSessions[i].profile_id;
      await this.libSessionService.update(
        +key,
        value,
        profile_id,
        this.wsClient
      );
    }

    return {
      maxQueueNumber: healingSessions.length,
      sessions: sessionsToUpdate,
    };
  }

  async setSessionStatus(
    sessionId: number,
    newStatus: SessionStatus,
    newSubStatus?: string
  ) {
    const session = await this.prisma.healing_sessions.findFirst({
      where: { session_id: sessionId },
    });

    if (!session) {
      throw new BadRequestException('Session Not Found!');
    }

    session.status = newStatus;
    return this.prisma.healing_sessions.update({
      where: { session_id: sessionId },
      data: {
        status: newStatus,
        sub_status: newSubStatus || null,
        sub_status_updated_at: new Date(),
      },
    });
  }

  async sessionOfferTimeouts() {
    this.logger.log(
      {
        event: 'SESSION_OFFER_TIMEOUTS_START',
        timeout_duration_ms: this.SESSION_OFFER_TIMEOUT,
        message: 'Starting session offer timeout processing',
      },
      'Session Offer Timeouts'
    );

    const sessionOffers = await this.prisma.healing_sessions.findMany({
      where: {
        status: SessionStatus.IN_QUEUE,
        sub_status: SessionSubStatusType.CONFIRMATION_REQUIRED,
        is_deleted: false,
      },
      include: {
        profiles: {
          select: {
            users: {
              select: {
                user_id: true,
              },
            },
          },
        },
      },
      take: 10000,
    });

    this.logger.debug('session offers context', {
      session_offers_count: sessionOffers.length,
      timeout_duration_ms: this.SESSION_OFFER_TIMEOUT,
    });

    let timedOutSessions = 0;
    sessionOffers.forEach(async (session) => {
      this.currentTime = new Date();
      const sessionOfferTime = session.last_session_offer_at;

      if (
        this.currentTime.getTime() - sessionOfferTime.getTime() >
        this.SESSION_OFFER_TIMEOUT
      ) {
        timedOutSessions++;

        this.logger.debug('session offer timeout detected', {
          session_id: session.session_id,
          profile_id: session.profile_id,
          healer_id: session.healer_id,
          session_type: session.session_type,
          offer_time: sessionOfferTime,
          timeout_duration_ms: this.SESSION_OFFER_TIMEOUT,
        });

        const updatePayload = {
          healer_id: null,
          sub_status: SessionSubStatusType.SESSION_MISSED,
          sub_status_updated_at: new Date(),
          updated_at: new Date(),
        };
        const profile_id = session.profile_id;

        await this.libSessionService.update(
          session.session_id,
          updatePayload,
          profile_id,
          this.wsClient
        );

        await this.notificationService.sendPushNotification(
          session?.profiles?.users?.user_id,
          {
            title: PushNotificationTitleType.SESSION_MISSED,
            body: PushNotificationBodyType.SESSION_MISSED,
          }
        );

        if (session.session_type === SessionType.HEALER) {
          try {
            // Assign next available patient to healer
            await this.libSessionService.processHealerRequest({
              profile_id: session.healer_id,
            } as { profile_id: number });
          } catch (error) {
            this.logger.warn('failed to process healer request', {
              session_id: session.session_id,
              healer_id: session.healer_id,
              error: error instanceof Error ? error.message : 'unknown error',
            });
          }
        }
      }
    });

    this.logger.log(
      {
        event: 'SESSION_OFFER_TIMEOUTS_COMPLETE',
        total_sessions_checked: sessionOffers.length,
        timed_out_sessions: timedOutSessions,
        message: 'Session offer timeout processing completed',
      },
      'Session Offer Timeouts'
    );

    // if (Object.entries(updatedSessionsData)?.length !== 0) {
    //   for (
    //     let i = 0,
    //       objectEntries = Object.entries(updatedSessionsData),
    //       iL = objectEntries.length;
    //     i < iL;
    //     i++
    //   ) {
    //     const [key, value] = objectEntries[i];
    //     const profile_id = sessionOffers[i].profile_id;
    //     await this.libSessionService.update(
    //       +key,
    //       value,
    //       profile_id,
    //       this.wsClient
    //     );
    //   }
    // }
  }

  async markInactiveSessions() {
    this.logger.log(
      {
        event: 'MARK_INACTIVE_SESSIONS_START',
        availability_timeout_ms: this.SESSION_AVAILABILITY_TIMEOUT,
        message: 'Starting to mark inactive sessions',
      },
      'Mark Inactive Sessions'
    );

    try {
      // Collecting users with available sessions that have not been seen for 30 minutes
      const inactiveUserSessions = await this.prisma.healing_sessions.findMany({
        where: {
          profiles: {
            users: {
              user_devices: {
                every: {
                  last_seen_at: {
                    lte: new Date(
                      new Date().getTime() - this.SESSION_AVAILABILITY_TIMEOUT // minus 30 mins
                    ),
                  },
                  is_deleted: false,
                },
              },
            },
          },
          status: SessionStatus.IN_QUEUE,
          sub_status: SessionSubStatusType.AVAILABLE,
        },
        include: {
          profiles: {
            select: {
              users: true,
            },
          },
        },
        take: 100000,
      });

      this.logger.debug('inactive sessions context', {
        inactive_sessions_count: inactiveUserSessions.length,
        availability_timeout_ms: this.SESSION_AVAILABILITY_TIMEOUT,
      });

      if (inactiveUserSessions.length === 0) {
        this.logger.debug('no inactive sessions found to process');
        return;
      }

      let processedCount = 0;
      let errorCount = 0;

      // Process sessions in batches for better performance
      const batchSize = 100;
      for (let i = 0; i < inactiveUserSessions.length; i += batchSize) {
        const batch = inactiveUserSessions.slice(i, i + batchSize);

        // Process batch concurrently
        const batchPromises = batch.map(async (session) => {
          try {
            await this.prisma.healing_sessions.update({
              where: { session_id: session.session_id },
              data: {
                sub_status: SessionSubStatusType.NOT_AVAILABLE,
                sub_status_updated_at: new Date(),
                updated_at: new Date(),
              },
            });

            // Send push notification
            if (session?.profiles?.users?.user_id) {
              await this.notificationServices.sendPushNotification(
                session.profiles.users.user_id,
                {
                  title: PushNotificationTitleType.MARK_UNAVAILABLE,
                  body: PushNotificationBodyType.MARK_UNAVAILABLE,
                }
              );
            }

            processedCount++;
            return { success: true, session_id: session.session_id };
          } catch (error) {
            errorCount++;
            this.logger.error('failed to process inactive session', {
              session_id: session.session_id,
              error: error.message,
              stack: error.stack,
            });
            return {
              success: false,
              session_id: session.session_id,
              error: error.message,
            };
          }
        });

        // Wait for batch to complete
        await Promise.all(batchPromises);
      }

      this.logger.log(
        {
          event: 'MARK_INACTIVE_SESSIONS_COMPLETE',
          total_sessions: inactiveUserSessions.length,
          processed_count: processedCount,
          error_count: errorCount,
          message: 'Completed marking inactive sessions',
        },
        'Mark Inactive Sessions'
      );
    } catch (error) {
      this.logger.error('failed to mark inactive sessions', {
        error: error.message,
        stack: error.stack,
      });
      throw error;
    }
  }

  async sessionUpdateAndTimeouts() {
    this.logger.log(
      {
        event: 'SESSION_UPDATE_AND_TIMEOUTS_START',
        intake_timeout_ms: this.SESSION_INTAKE_TIMEOUT,
        queue_timeout_ms: this.SESSION_IN_QUEUE_TIMEOUT,
        message: 'Starting session update and timeout processing',
      },
      'Session Update And Timeouts'
    );

    const openSessions = await this.prisma.healing_sessions.findMany({
      where: {
        status: {
          notIn: [
            SessionStatus.ABANDONED,
            SessionStatus.COMPLETED,
            SessionStatus.CANCELLED,
          ],
        },
        is_deleted: false,
      },
      take: 100,
    });

    this.logger.debug('open sessions context', {
      open_sessions_count: openSessions.length,
      intake_timeout_ms: this.SESSION_INTAKE_TIMEOUT,
      queue_timeout_ms: this.SESSION_IN_QUEUE_TIMEOUT,
    });

    const updatedSessionsData: {
      [key: number]: Prisma.healing_sessionsUpdateInput;
    } = {};

    let intakeTimeouts = 0;
    let queueTimeouts = 0;

    for (const session of openSessions) {
      this.currentTime = new Date();
      const sessionStartTime = session.updated_at;

      if (
        session.status === SessionStatus.INTAKE &&
        this.currentTime.getTime() - sessionStartTime.getTime() >
          this.SESSION_INTAKE_TIMEOUT
      ) {
        intakeTimeouts++;
        this.logger.debug('intake session timeout detected', {
          session_id: session.session_id,
          profile_id: session.profile_id,
          session_status: session.status,
          start_time: sessionStartTime,
          timeout_duration_ms: this.SESSION_INTAKE_TIMEOUT,
        });

        updatedSessionsData[session.session_id] = {
          status: SessionStatus.ABANDONED,
          sub_status: AbandonmentSubStatus.TIMEOUT,
          sub_status_updated_at: new Date(),
          updated_at: new Date(),
        };
      }

      if (
        session.status === SessionStatus.IN_QUEUE &&
        this.currentTime.getTime() - sessionStartTime.getTime() >
          this.SESSION_IN_QUEUE_TIMEOUT
      ) {
        queueTimeouts++;
        this.logger.debug('queue session timeout detected', {
          session_id: session.session_id,
          profile_id: session.profile_id,
          session_status: session.status,
          session_sub_status: session.sub_status,
          start_time: sessionStartTime,
          timeout_duration_ms: this.SESSION_IN_QUEUE_TIMEOUT,
        });

        updatedSessionsData[session.session_id] = {
          status: SessionStatus.ABANDONED,
          sub_status: AbandonmentSubStatus.TIMEOUT,
          sub_status_updated_at: new Date(),
          updated_at: new Date(),
        };
      }

      // if (
      //   session.status === SessionStatus.IN_QUEUE &&
      //   session.sub_status === SessionSubStatusType.AVAILABLE &&
      //   this.currentTime.getTime() - session.updated_at.getTime() >
      //     this.SESSION_IN_QUEUE_AVAILABLE_TIMEOUT
      // ) {
      //   updatedSessionsData[session.session_id] = {
      //     status: SessionStatus.IN_QUEUE,
      //     sub_status: SessionSubStatusType.AI_CONFIRMATION_REQUIRED,
      //     sub_status_updated_at: new Date(),
      //     updated_at: new Date(),
      //   };
      // }
    }

    const sessionsToUpdate = Object.entries(updatedSessionsData);
    this.logger.debug('sessions to update', {
      total_sessions_to_update: sessionsToUpdate.length,
      intake_timeouts: intakeTimeouts,
      queue_timeouts: queueTimeouts,
    });

    if (sessionsToUpdate.length > 0) {
      for (let i = 0, iL = sessionsToUpdate.length; i < iL; i++) {
        const [key, value] = sessionsToUpdate[i];
        const profile_id = openSessions[i].profile_id;
        await this.libSessionService.update(
          +key,
          value,
          profile_id,
          this.wsClient
        );
      }
    }

    this.logger.log(
      {
        event: 'SESSION_UPDATE_AND_TIMEOUTS_COMPLETE',
        total_sessions_checked: openSessions.length,
        sessions_updated: sessionsToUpdate.length,
        intake_timeouts: intakeTimeouts,
        queue_timeouts: queueTimeouts,
        message: 'Session update and timeout processing completed',
      },
      'Session Update And Timeouts'
    );
  }

  async healingRoundUpdateAndTimeouts() {
    this.logger.log(
      {
        event: 'HEALING_ROUND_UPDATE_AND_TIMEOUTS_START',
        healer_assigned_timeout_ms: this.HEALING_ROUND_HEALER_ASSIGNED_TIMEOUT,
        feedback_required_timeout_ms:
          this.HEALING_ROUND_FEEDBACK_REQUIRED_TIMEOUT,
        message: 'Starting healing round update and timeout processing',
      },
      'Healing Round Update And Timeouts'
    );

    this.currentTime = new Date();

    // debounce logic - to be removed or reduced after offload to microservice
    if (
      this.HEALING_ROUND_UPDATE_DEBOUNCE &&
      this.currentTime.getTime() - this.HEALING_ROUND_LAST_RUN.getTime() > 30000
    ) {
      this.logger.log('skipping execution, as last execution in progress');
      return;
    }
    this.HEALING_ROUND_LAST_RUN = new Date();
    this.HEALING_ROUND_UPDATE_DEBOUNCE = true;

    this.logger.debug('healing round timeouts context', {
      last_run: this.HEALING_ROUND_LAST_RUN,
      healer_assigned_timeout_ms: this.HEALING_ROUND_HEALER_ASSIGNED_TIMEOUT,
      feedback_required_timeout_ms:
        this.HEALING_ROUND_FEEDBACK_REQUIRED_TIMEOUT,
    });

    try {
      const sessions = await this.prisma.healing_sessions.findMany({
        where: {
          status: { in: [`${SessionStatus.HEALING_ROUND}`] },
          is_deleted: false,
          healer_id: { not: null },
        },
        include: {
          healing_rounds: {
            orderBy: { round_id: 'desc' },
            take: 1,
          },
          conversations: {
            where: {
              is_deleted: false,
              round_id: { not: null },
            },
            orderBy: { conversation_id: 'desc' },
            take: 1,
          },
        },
      });

      this.logger.debug('healing round sessions context', {
        healing_round_sessions_count: sessions.length,
      });

      if (sessions.length === 0) {
        this.logger.debug('no healing round sessions found to process');
        return;
      }

      for (const session of sessions) {
        try {
          const subStatusUpdatedAt = session.sub_status_updated_at;

          if (session.healing_rounds && session.healing_rounds[0]) {
            const lastHealingRound = session.healing_rounds[0];
            const healingRoundStart =
              lastHealingRound.round_start_at || lastHealingRound.created_at;
            const healingRoundUpdatedAt = lastHealingRound.updated_at;

            // 2. IN_PROGRESS Timeout (5 minutes)
            if (
              session.status === SessionStatus.HEALING_ROUND &&
              session.sub_status === HealingRoundSubStatus.IN_PROGRESS &&
              this.hasTimeoutPassed(
                healingRoundStart,
                lastHealingRound.max_time
              )
            ) {
              this.logger.debug('IN_PROGRESS timeout detected', {
                session_id: session.session_id,
                round_id: lastHealingRound.round_id,
                session_type: session.session_type,
              });

              if (session.session_type === SessionType.PLACEBO) {
                await this.libPlaceboService.processPlaceboHealingRoundCheckIn({
                  session_id: session.session_id,
                  round_id: lastHealingRound.round_id,
                  region: lastHealingRound.cloud_region,
                });
              } else {
                await this.aiHealingRoundService.processHealingRoundCheckIn(
                  new AiHealingRoundCheckInEvent({
                    profiles: {
                      connect: {
                        profile_id: session.healer_id,
                      },
                    },
                    healing_sessions: {
                      connect: {
                        session_id: session.session_id,
                      },
                    },
                  }),
                  lastHealingRound.cloud_region
                );
              }
              continue;
            }

            // 3. FEEDBACK_REQUIRED Timeout (60 seconds)
            if (
              session.status === SessionStatus.HEALING_ROUND &&
              session.sub_status === HealingRoundSubStatus.FEEDBACK_REQUIRED &&
              this.hasTimeoutPassed(
                healingRoundUpdatedAt,
                this.HEALING_ROUND_FEEDBACK_REQUIRED_TIMEOUT *
                  (session.conversations[0] &&
                  session.conversations[0].sender_type == SenderType.AI
                    ? 1
                    : 3)
              )
            ) {
              this.logger.debug('FEEDBACK_REQUIRED timeout detected', {
                session_id: session.session_id,
                round_id: lastHealingRound.round_id,
                conversation_sender_type: session.conversations[0]?.sender_type,
              });

              this.logger.log(
                'Round set ABANDONED after 5 mins from FEEDBACK_REQUIRED'
              );

              await this.setHealingRoundStatus(
                session.session_id,
                lastHealingRound.round_id,
                SessionStatus.ABANDONED
              );
              continue;
            }

            // 4. Proceed confirmation Timeout (60 seconds)
            if (
              session.status === SessionStatus.HEALING_ROUND &&
              session.sub_status === HealingRoundSubStatus.COMPLETED &&
              this.hasTimeoutPassed(
                healingRoundUpdatedAt,
                this.HEALING_ROUND_HEALER_ASSIGNED_TIMEOUT
              )
            ) {
              this.logger.debug('Proceed confirmation timeout detected', {
                session_id: session.session_id,
                round_id: lastHealingRound.round_id,
                session_type: session.session_type,
              });

              this.logger.log(
                `Triggering healing round start after ${this.HEALING_ROUND_HEALER_ASSIGNED_TIMEOUT}`
              );

              if (session.session_type === SessionType.PLACEBO) {
                this.libPlaceboService.processPlaceboHealingRoundStart(
                  session.session_id
                );
              } else {
                this.aiHealingService.processHealingRoundStart(
                  {
                    session_id: session.session_id,
                    profile_id: session.healer_id,
                  },
                  null,
                  true
                );
              }
              continue;
            }
          }

          if (
            session.status === SessionStatus.HEALING_ROUND &&
            session.sub_status === HealingRoundSubStatus.HEALER_ASSIGNED &&
            session.healer_id &&
            this.hasTimeoutPassed(
              subStatusUpdatedAt,
              this.HEALING_ROUND_HEALER_ASSIGNED_TIMEOUT
            )
          ) {
            this.logger.debug('HEALER_ASSIGNED timeout detected', {
              session_id: session.session_id,
              healer_id: session.healer_id,
            });

            await this.aiHealingService.processHealingRoundStart(
              {
                session_id: session.session_id,
                profile_id: session.healer_id,
              },
              null,
              true
            );
            continue;
          }
        } catch (error) {
          this.logger.error('failed to process healing round session', {
            session_id: session.session_id,
            error: error.message,
            stack: error.stack,
          });
        }
      }

      this.logger.log(
        {
          event: 'HEALING_ROUND_UPDATE_AND_TIMEOUTS_COMPLETE',
          total_sessions: sessions.length,
          message: 'Completed healing round update and timeout processing',
        },
        'Healing Round Update And Timeouts'
      );
    } catch (error) {
      this.logger.error(
        'failed to process healing round updates and timeouts',
        {
          error: error.message,
          stack: error.stack,
        }
      );
      throw error;
    } finally {
      this.HEALING_ROUND_UPDATE_DEBOUNCE = false;
    }
  }

  async setHealingRoundStatus(
    sessionId: number,
    roundId: number,
    newSessionStatus?: SessionStatus,
    newRoundStatus?: HealingRoundSubStatus
  ): Promise<{ session: healing_sessions; round: healing_rounds }> {
    let roundStatus = HealingRoundSubStatus.COMPLETED;
    if (newSessionStatus === SessionStatus.ABANDONED) {
      roundStatus = HealingRoundSubStatus.ABANDONED;
    } else if (newSessionStatus === SessionStatus.HEALING_ROUND) {
      roundStatus = newRoundStatus;
    }
    const healingRoundData: Prisma.healing_roundsUpdateInput = {
      status: roundStatus,
      updated_at: new Date(),
      round_end_at: new Date(),
    };
    const isFeedbackRequired =
      newRoundStatus == HealingRoundSubStatus.FEEDBACK_REQUIRED;
    if (isFeedbackRequired) {
      healingRoundData.feedback_start_at = new Date();
      healingRoundData.remaining_time = 0;
    }

    const txResult = await this.prisma.$transaction(async (tx) => {
      const healingRoundUpdate = await tx.healing_rounds.update({
        where: {
          round_id: roundId,
        },
        data: healingRoundData,
      });
      const sessionUpdatePayload: Prisma.healing_sessionsUpdateInput = {
        status: newSessionStatus,
        sub_status:
          newSessionStatus === SessionStatus.ABANDONED
            ? AbandonmentSubStatus.TIMEOUT
            : newRoundStatus,
        sub_status_updated_at: new Date(),
        updated_at: new Date(),
      };
      const sessionUpdate = await tx.healing_sessions.update({
        where: {
          session_id: sessionId,
        },
        data: sessionUpdatePayload,
      });

      return {
        healingRoundUpdate,
        sessionUpdate,
      };
    });
    return {
      session: txResult.sessionUpdate,
      round: txResult.healingRoundUpdate,
    };
  }
}
