{"name": "scheduler", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/scheduler/src", "projectType": "application", "tags": [], "targets": {"serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "options": {"buildTarget": "scheduler:build", "port": 4000}, "configurations": {"development": {"buildTarget": "scheduler:build:development"}, "production": {"buildTarget": "scheduler:build:production"}}}}}