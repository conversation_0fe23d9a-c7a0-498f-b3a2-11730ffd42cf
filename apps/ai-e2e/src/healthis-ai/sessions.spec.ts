import axios from 'axios';
import {healThisToken} from '@core_be/global';
import { SessionSubStatusType, SessionStatus } from '@core/libs';

const sessionUrl = 'http://127.0.0.1:3001/api/sessions';
const healThisBearerToken = healThisToken();
const mockRequest = {
  raw: {
    user: {
      user_id: 1,
    },
  },
} as any;

describe('SessionController (e2e)', () => {
  const sessionIdsToCleanup: any[] = [];

  afterEach(async () => {
    while (sessionIdsToCleanup.length > 0) {
      const id = sessionIdsToCleanup.pop();
      await axios
        .delete(`${sessionUrl}/${id}`, {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
            req: mockRequest,
          },
        })
        .catch((e) => e);
    }
  });

  describe('Session Validation Errors', () => {
    it('should return validation errors for invalid session input', async () => {
      const data = {
        profile_id: '',
      }

      const response = await axios.post(`${sessionUrl}`, data, {headers: 
        {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      }).catch((e) => e.response);
      expect(response.status).toBe(400);
      expect(response.data.message).toContain('profile_id must be a number conforming to the specified constraints');
    });

    it('should return 404 for a non-existent session record', async () => {
      const response = await axios.get(`${sessionUrl}/99999/9999`, {headers: 
        {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      }).catch((e) => e.response);
      
      expect(response.status).toBe(404);
      expect(response.data.message).toContain('Session not found');
    });

    it('should return validation errors for invalid take session input', async () => {
      const data = {
        profile_id: '',
      }

      const response = await axios.post(`${sessionUrl}/take-session`, data, {headers: 
        {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      }).catch((e) => e.response);
      expect(response.status).toBe(400);
      expect(response.data.message).toContain('profile_id must be a number conforming to the specified constraints');
    });

    it('should return validation errors for invalid healing round start input', async () => {
      const data = {
        profile_id: '',
        session_id: '',
      }

      const response = await axios.post(`${sessionUrl}/healing-round/start`, data, {headers: 
        {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      }).catch((e) => e.response);
      expect(response.status).toBe(400);
      expect(response.data.message).toContain('profile_id must be a number conforming to the specified constraints');
      expect(response.data.message).toContain('session_id must be a number conforming to the specified constraints');
    });

    it('should return validation errors for invalid healing round check-in input', async () => {
      const data = {
        profile_id: '',
        session_id: '',
        round_id: '',
      }

      const response = await axios.post(`${sessionUrl}/healing-round/check-in`, data, {headers: 
        {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      }).catch((e) => e.response);
      expect(response.status).toBe(400);
      expect(response.data.message).toContain('profile_id must be a number conforming to the specified constraints');
      expect(response.data.message).toContain('session_id must be a number conforming to the specified constraints');
      expect(response.data.message).toContain('round_id must be a number conforming to the specified constraints');
    });

    it('should return validation errors for invalid queue confirmation input', async () => {
      const data = {
        session_id: '',
        is_confirmed: '',
      }

      const response = await axios.post(`${sessionUrl}/queue-confirmation`, data, {headers: 
        {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      }).catch((e) => e.response);
      expect(response.status).toBe(400);
      expect(response.data.message).toContain('session_id must be a number conforming to the specified constraints');
      expect(response.data.message).toContain('is_confirmed must be a boolean value');
    });

    it('should return validation errors for invalid update status input', async () => {
      const data = {
        status:'',
        sub_status:'',
        session_id: '',
        profile_id: '',
      }

      const response = await axios.patch(`${sessionUrl}/update-status`, data, {headers: 
        {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      }).catch((e) => e.response);
      expect(response.status).toBe(400);
      expect(response.data.message).toContain('status must be a valid enum value');
      expect(response.data.message).toContain('Invalid sub_status for the given status');
      expect(response.data.message).toContain('session_id must be a number conforming to the specified constraints');
      expect(response.data.message).toContain('profile_id must be a number conforming to the specified constraints');
    });

    it('should return validation errors for invalid update status without status,session_id and profile_id', async () => {
      const data = {
        sub_status:SessionStatus.IN_QUEUE,
      }

      const response = await axios.patch(`${sessionUrl}/update-status`, data, {headers: 
        {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      }).catch((e) => e.response);
      expect(response.status).toBe(400);
      expect(response.data.message).toContain('status should not be empty');
      expect(response.data.message).toContain('profile_id should not be empty');
      expect(response.data.message).toContain('session_id should not be empty');
    });

    it('should return validation errors for invalid session end input', async () => {
      const data = {
        profile_id: '',
        session_id: '',
      }

      const response = await axios.post(`${sessionUrl}/end`, data, {headers: 
        {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      }).catch((e) => e.response);
      expect(response.status).toBe(400);
      expect(response.data.message).toContain('profile_id must be a number conforming to the specified constraints');
      expect(response.data.message).toContain('session_id must be a number conforming to the specified constraints');
    });
  });

  describe('Session CRUD operations', () => {
    it('should create a new session', async () => {
      const createSessionDto = {
        profile_id: 1,
      };

      const res = await axios.post(sessionUrl, createSessionDto, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
          req: mockRequest,
        },
      });

      sessionIdsToCleanup.push(res.data.data.session_id);

      expect(res.status).toBe(201);
      expect(res.data.data.session_id).toBeDefined();
    });

    it('should retrieve all sessions', async () => {
      const res = await axios.get(sessionUrl, {
        headers: {
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      });

      expect(res.status).toBe(200);
      expect(Array.isArray(res.data.data)).toBe(true);
    });

    it('should retrieve a Profile and  session by ID', async () => {
      const createSessionDto = {
        profile_id: 1,
      };

      const createdSession = await axios.post(sessionUrl, createSessionDto, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
          req: mockRequest,
        },
      });

      sessionIdsToCleanup.push(createdSession.data.session_id);

      const res = await axios.get(
        `${sessionUrl}/${createdSession.data.session_id}/${createSessionDto.profile_id}`,
        {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
          },
        }
      );

      expect(res.status).toBe(200);
      expect(res.data.data.session_id).toBe(createdSession.data.session_id);
    });

    it('should update a session', async () => {
      const createSessionDto = {
        profile_id: 1,
      };

      const createdSession = await axios.post(sessionUrl, createSessionDto, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
          req: mockRequest,
        },
      });

      sessionIdsToCleanup.push(createdSession.data.data.session_id);

      const updateSessionDto = {
        profile_id: 1,
        is_follow_up: true,
      };

      const res = await axios.patch(
        `${sessionUrl}/${createdSession.data.data.session_id}`,
        updateSessionDto,
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${healThisBearerToken}`,
            req: mockRequest,
          },
        }
      );

      expect(res.status).toBe(200);
    });

    it('should soft delete a session', async () => {
      const createSessionDto = {
        profile_id: 1,
      };

      const createdSession = await axios.post(sessionUrl, createSessionDto, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
          req: mockRequest,
        },
      });

      const res = await axios.delete(
        `${sessionUrl}/${createdSession.data.data.session_id}`,
        {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
            req: mockRequest,
          },
        }
      );

      expect(res.status).toBe(200);
    });
  });

  it('should take a session', async () => {
    const updateSessionDto = {
      profile_id: 1,
    };

    const res = await axios.post(
      `${sessionUrl}/take-session`,
      updateSessionDto,
      {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
          req: mockRequest,
        },
      }
    );

    expect(res.status).toBe(200);
  });

  it('should session start healing round', async () => {
    const updateSessionDto = {
      profile_id: 1,
      session_id: 1,
    };

    const res = await axios.post(
      `${sessionUrl}/healing-round/start`,
      updateSessionDto,
      {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
          req: mockRequest,
        },
      }
    );

    expect(res.status).toBe(200);
  });

  it('should session  healing round check in ', async () => {
    const updateSessionDto = {
      profile_id: 1,
      session_id: 1,
      round_id: 1,
    };

    const res = await axios.post(
      `${sessionUrl}/healing-round/check-in`,
      updateSessionDto,
      {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
          req: mockRequest,
        },
      }
    );

    expect(res.status).toBe(200);
  });

  it('should session queue confirmation', async () => {
    const updateSessionDto = {
      session_id: 1,
      is_confirmed: true,
    };

    const res = await axios.post(
      `${sessionUrl}/queue-confirmation`,
      updateSessionDto,
      {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
          req: mockRequest,
        },
      }
    );

    expect(res.status).toBe(200);
  });

  it('should session update status', async () => {
    const updateSessionDto = {
      session_id: 1,
      profile_id: 1,
      status: SessionStatus.CANCELLED,
      sub_status: SessionSubStatusType.AVAILABLE,
    };

    const res = await axios.post(
      `${sessionUrl}/update-status`,
      updateSessionDto,
      {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
          req: mockRequest,
        },
      }
    );

    expect(res.status).toBe(200);
  });

  it('should session end', async () => {
    const updateSessionDto = {
      session_id: 1,
    };

    const res = await axios.post(
      `${sessionUrl}/${updateSessionDto.session_id}/end`,
      {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
          req: mockRequest,
        },
      }
    );

    expect(res.status).toBe(200);
  });
});
