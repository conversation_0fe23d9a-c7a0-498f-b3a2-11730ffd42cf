import axios from 'axios';

jest.mock('axios');

const axiosRequestMock = axios.request as jest.Mock;
const baseUrl = 'http://localhost:3000/api/healer/';

beforeEach(() => {
  axiosRequestMock.mockClear();
});

describe('Healer (e2e) with Axios', () => {
  it('should throw BadRequestException if profile_id is invalid', async () => {
    const invalidProfileId = 'abc';

    axiosRequestMock.mockRejectedValue({
      response: {
        status: 400,
        data: { message: 'Invalid profile_id' },
      },
    });

    try {
      await axios.request({
        method: 'GET',
        url: `${baseUrl}${invalidProfileId}`,
        headers: {
          Authorization: 'Bearer mockAuthToken',
        },
      });
    } catch (error) {
      expect(error.response.status).toBe(400);
      expect(error.response.data.message).toBe('Invalid profile_id');
    }
  });

  it('should return dashboard data for a valid profile_id', async () => {
    const validProfileId = 1;
    const mockDashboardData = { profile_id: 123, data: 'Dashboard data' };

    axiosRequestMock.mockResolvedValue({
      status: 200,
      data: mockDashboardData,
    });

    const response = await axios.request({
      method: 'GET',
      url: `${baseUrl}/${validProfileId}`,
      headers: {
        Authorization: 'Bearer mockAuthToken',
      },
    });

    expect(response.status).toBe(200);
    expect(response.data).toEqual(mockDashboardData);
  });
});
