import axios from 'axios';
import {healThisToken} from '@core_be/global';
import { faker } from '@faker-js/faker';
import { SenderType } from '@core/libs';

const conversationUrl = 'http://127.0.0.1:3001/api/conversations';
const user_id = 1;
const healThisBearerToken = healThisToken();
const mockRequest: Request = {
  raw: {
    user: {
      user_id,
    },
  },
} as any;

describe('Conversation', () => { 

  describe('Conversation Validation Errors', () => {
    it('should return validation errors for invalid conversation input', async () => {
      const data = {
        profile_id: '',
        sender_type: 1123,
        session_id: '',
      }

      const response = await axios.post(`${conversationUrl}`, data, {headers: 
        {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      }).catch((e) => e.response);
      expect(response.status).toBe(400);
      expect(response.data.message).toContain('profile_id must be a number conforming to the specified constraints');
      expect(response.data.message).toContain('sender_type must be a string');
      expect(response.data.message).toContain('session_id must be a number conforming to the specified constraints');
    });

    it('should return an error when creating conversation without a profile_id and session_id', async () => {
      const data = {
        sender_type: SenderType.Patient,
      }

      const response = await axios.post(`${conversationUrl}`, data, {headers: 
        {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      }).catch((e) => e.response);
      expect(response.status).toBe(400);
      expect(response.data.message).toContain('profile_id should not be empty');
      expect(response.data.message).toContain('session_id should not be empty');
    });
  });

  describe('Conversation (e2e) with Axios', () => {
    it('should create a conversation', async () => {
      const conversationData = {
        session_id: 1,
        profile_id: 1,
        content: faker.lorem.sentence(),
        sender_type: SenderType.Patient,
        round_id: 1,
      };

      const response = await axios
        .post(conversationUrl, conversationData, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${healThisBearerToken}`,
            req: mockRequest as any,
          },
        })
        .catch((e) => e);

      expect(response.status).toBe(201);
      expect(response.data.data).toHaveProperty('session_id');
    });

    it('should return a list of conversations', async () => {
      const queryParams = {
        session_id: 1,
        profile_id: 1,
        limit: 5,
        skip: 0,
        offset: 0,
        order: 'desc',
        sortBy: 'created_at',
      };

      const response = await axios.get(conversationUrl, {
        params: queryParams,
        headers: {
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      });

      expect(response.status).toBe(200);
      expect(response.data.data.conversations).toBeInstanceOf(Array);
      expect(response.data.data.conversations.length).toBeGreaterThan(0);
    });

    it('should fail if session does not exist when creating a conversation', async () => {
      const conversationData = {
        session_id: 999,
        profile_id: 1,
        content: faker.lorem.sentence(),
        sender_type: SenderType.Patient,
        round_id: 1,
      };

      try {
        await axios.post(conversationUrl, conversationData, {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
          },
        });
      } catch (error) {
        expect(error.response.status).toBe(400);
        expect(error.response.data.message).toBe('Session not found.');
      }
    });

    it('should fail if profile validation fails when creating a conversation', async () => {
      const conversationData = {
        session_id: 1,
        profile_id: 999,
        content: faker.lorem.sentence(),
        sender_type: SenderType.Patient,
        round_id: 1,
      };

      try {
        await axios.post(conversationUrl, conversationData, {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
          },
        });
      } catch (error) {
        expect(error.response.status).toBe(404);
        expect(error.response.data.message).toBe('User profile not found!');
      }
    });
  });
});
