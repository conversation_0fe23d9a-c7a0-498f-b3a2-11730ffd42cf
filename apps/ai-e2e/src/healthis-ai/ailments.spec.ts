import axios from 'axios';
import { faker } from '@faker-js/faker';
import {healThisToken} from '@core_be/global';

const ailmentsUrl = 'http://127.0.0.1:3001/api/ailments';
const user_id = 1;
const healThisBearerToken = healThisToken();
const mockRequest: Request = {
  raw: {
    user: {
      user_id,
    },
  },
} as any;

describe('Ailments (E2E) with Axios', () => {
  const ailmentIdsToCleanup: any[] = [];

  afterEach(async () => {
    while (ailmentIdsToCleanup.length > 0) {
      const id = ailmentIdsToCleanup.pop();
      await axios
        .delete(`${ailmentsUrl}/${id}`, {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
            req: mockRequest as any,
          },
        })
        .catch((e) => e);
    }
  });

  it('should create a new ailment', async () => {
    const createAilmentDto = {
      name: faker.lorem.word(),
      description: faker.word.noun(),
    };

    const res = await axios
      .post(ailmentsUrl, createAilmentDto, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
          req: mockRequest as any,
        },
      })
      .catch((e) => e);

    ailmentIdsToCleanup.push(res.data.data.ailment_id);
    expect(res.status).toBe(201);
    expect(res.data.message).toBe('Success');
    expect(res.data.data.name).toBe(createAilmentDto.name);
  });

  it('should retrieve all ailments', async () => {
    const createAilmentDto = {
      name: faker.lorem.word(),
      description: faker.word.noun(),
    };

    const createdAilment = await axios
      .post(ailmentsUrl, createAilmentDto, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
          req: mockRequest as any,
        },
      })
      .catch((e) => e);

    const res = await axios.get(ailmentsUrl, {
      headers: {
        Authorization: `Bearer ${healThisBearerToken}`,
      },
    });

    ailmentIdsToCleanup.push(createdAilment.data.data.ailment_id);

    expect(res.status).toBe(200);
    expect(res.data.data.length).toBeGreaterThan(0);
    expect(res.data.data).toContainEqual(
      expect.objectContaining({
        ailment_id: createdAilment.data.data.ailment_id,
        name: createAilmentDto.name,
      })
    );
  });

  it('should retrieve an ailment by ID', async () => {
    const createAilmentDto = {
      name: faker.lorem.word(),
      description: faker.word.noun(),
    };

    const createdAilment = await axios
      .post(ailmentsUrl, createAilmentDto, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
          req: mockRequest as any,
        },
      })
      .catch((e) => e);

    const res = await axios.get(
      `${ailmentsUrl}/${createdAilment.data.data.ailment_id}`,
      {
        headers: {
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      }
    );

    ailmentIdsToCleanup.push(createdAilment.data.data.ailment_id);

    expect(res.status).toBe(200);
    expect(res.data.data.name).toBe(createAilmentDto.name);
  });

  it('should update an ailment', async () => {
    const createAilmentDto = {
      name: faker.lorem.word(),
      description: faker.word.noun(),
    };

    const createdAilment = await axios
      .post(ailmentsUrl, createAilmentDto, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
          req: mockRequest as any,
        },
      })
      .catch((e) => e);

    const updateAilmentDto = {
      name: faker.lorem.word(),
      description: faker.word.noun(),
    };

    const res = await axios
      .patch(
        `${ailmentsUrl}/${createdAilment.data.data.ailment_id}`,
        updateAilmentDto,
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${healThisBearerToken}`,
            req: mockRequest as any,
          },
        }
      )
      .catch((e) => e);

    ailmentIdsToCleanup.push(createdAilment.data.data.ailment_id);

    expect(res.status).toBe(200);
    expect(res.data.data.name).toBe(updateAilmentDto.name);
  });

  it('should delete an ailment', async () => {
    const createAilmentDto = {
      name: faker.lorem.word(),
      description: faker.word.noun(),
    };

    const createdAilment = await axios
      .post(ailmentsUrl, createAilmentDto, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
          req: mockRequest as any,
        },
      })
      .catch((e) => e);

    const res = await axios.delete(
      `${ailmentsUrl}/${createdAilment.data.data.ailment_id}`,
      {
        headers: {
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      }
    );

    expect(res.status).toBe(200);
    expect(res.data.data.is_deleted).toBe(true);
    expect(res.data.data.deleted_by).toBeDefined();
    expect(res.data.data.deleted_at).toBeDefined();
  });
});
