import axios from 'axios';
import {healThisToken, ProfileType, RelationToUser} from '@core_be/global';
import { faker } from '@faker-js/faker';

const profileUrl = 'http://127.0.0.1:3000/api/profile';
const user_id = 1;
const healThisBearerToken = healThisToken();
const mockRequest: Request = {
  raw: {
    user: {
      user_id,
    },
  },
} as any;

describe('Profile', () => {
  const profileIdsToCleanup: any[] = [];

  afterEach(async () => {
    while (profileIdsToCleanup.length > 0) {
      const id = profileIdsToCleanup.pop();
      await axios
        .delete(`${profileUrl}/${id}`, {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
            req: mockRequest as any,
          },
        })
        .catch((e) => e);
    }
  });

  describe('Profile Validation Errors', () => {

    it('should return validation errors for invalid profile input', async () => {
      const data = {
        first_name: '',
        last_name: 123, 
        profile_type: 'INVALID_TYPE',
        date_of_birth: 'not-a-date', 
        gender: '',
        relation_to_user:'',
        country:'',
      }

      const response = await axios.post(`${profileUrl}`, data, {headers: 
        {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      }).catch((e) => e.response);

      expect(response.status).toBe(400);
      expect(response.data.message).toContain('first_name should not be empty');
      expect(response.data.message).toContain('last_name must be a string');
      expect(response.data.message).toContain('profile_type must be a valid value');
      expect(response.data.message).toContain('relation_to_user should not be empty');
      expect(response.data.message).toContain('gender should not be empty');
      expect(response.data.message).toContain('country should not be empty');
    });

    it('should return 404 when updating a non-existent profile', async () => {
      const response = await axios.get(`${profileUrl}/9990`, {headers: 
        {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      }).catch((e) => e.response);

      expect(response.status).toBe(404);
      expect(response.data.message).toContain('Profile not found.');
    });

    it('should return 502 when profile type Healer can be created with relation type Self', async () => {
      const data = {
        first_name: faker.person.firstName(),
        last_name: faker.person.lastName(),
        profile_type: ProfileType.HEALER,
        gender: faker.person.gender(),
        date_of_birth: faker.date.past(),
        country: faker.location.country(),
        relation_to_user:RelationToUser.Friend
      }

      const response = await axios.post(`${profileUrl}`, data, {headers: 
        {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`, 
        },
      }).catch((e) => e.response);
      
      expect(response.status).toBe(502);
      expect(response.data.message).toContain(`A profile type ${ProfileType.HEALER} can be created with relation type ${RelationToUser.Self}.`);
    });

    it('should return 409 when user already has a relation as a Healer', async () => {
      const data = {
        first_name: faker.person.firstName(),
        last_name: faker.person.lastName(),
        profile_type: ProfileType.HEALER,
        gender: faker.person.gender(),
        date_of_birth: faker.date.past(),
        country: faker.location.country(),
        relation_to_user:RelationToUser.Self
      }
      const response = await axios.post(`${profileUrl}`, data, {headers: 
        {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`, 
        },
      }).catch((e) => e.response);
      
      expect(response.status).toBe(409);
      expect(response.data.message).toContain(`You already have a Self relation as a Healer.`);
    });
  });

  describe('Profile (e2e) with Axios', () => {

    it('should create a profile successfully', async () => {
      const data = {
        first_name: faker.person.firstName(),
        last_name: faker.person.lastName(),
        profile_type: ProfileType.PATIENT,
        gender: faker.person.gender(),
        date_of_birth: faker.date.past(),
        country: faker.location.country(),
        relation_to_user:RelationToUser.Friend,
        user_id: user_id,
      }

      const response = await axios.post(`${profileUrl}`, data, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      }).catch((e) => e.response);

      expect(response.status).toBe(201);
      expect(response.data.message).toBe('Success');
      expect(response.data.data.first_name).toBeDefined();
      expect(response.data.data.profile_type).toBeDefined();
    });

    it('should update a profile', async () => {
      const data = {
        first_name: faker.person.firstName(),
        last_name: faker.person.lastName(),
        profile_type: ProfileType.PATIENT,
        gender: faker.person.gender(),
        date_of_birth: faker.date.past(),
        country: faker.location.country(),
        relation_to_user:RelationToUser.Friend,
        user_id: user_id,
      }

      const createdProfile = await axios.post(profileUrl, data, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
          req: mockRequest as any,
        },
      })
      .catch((e) => e);

      const updateData = {
        first_name: faker.person.firstName(),
        country: faker.location.country(),
      }

      const response = await axios.patch(
        `${profileUrl}/${createdProfile.data.data.profile_id}`,updateData,
        {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
          },
        }
      );

      profileIdsToCleanup.push(response.data.data.profile_id);
      expect(response.status).toBe(200);
      expect(response.data.data.first_name).toBe(updateData.first_name);
      expect(response.data.data.country).toBe(updateData.country);
    });

    it('should find all profile', async () => {
      const response = await axios.get(profileUrl, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      });

      expect(response.status).toBe(200);
      expect(response.data.data.length).toBeGreaterThan(0);
    });

    it('should find a profile by ID', async () => {
      const data = {
        first_name: faker.person.firstName(),
        last_name: faker.person.lastName(),
        profile_type: ProfileType.PATIENT,
        gender: faker.person.gender(),
        date_of_birth: faker.date.past(),
        country: faker.location.country(),
        relation_to_user:RelationToUser.Friend,
        user_id: user_id,
      }

      const createdProfile = await axios.post(profileUrl, data, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
          req: mockRequest as any,
        },
      })
      .catch((e) => e);

      const response = await axios.get(
        `${profileUrl}/${createdProfile.data.data.profile_id}`,
        {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
          },
        }
      );

      profileIdsToCleanup.push(response.data.data.profile_id);
      expect(response.status).toBe(200);
      expect(response.data.data.profile_type).toBe(data.profile_type);
      expect(response.data.data.first_name).toBe(data.first_name);
      expect(response.data.data.relation_to_user).toBe(data.relation_to_user);
    });

    it('should delete a profile', async () => {
      const data = {
        first_name: faker.person.firstName(),
        last_name: faker.person.lastName(),
        profile_type: ProfileType.PATIENT,
        gender: faker.person.gender(),
        date_of_birth: faker.date.past(),
        country: faker.location.country(),
        relation_to_user:RelationToUser.Friend,
        user_id: user_id,
      }

      const createdProfile = await axios.post(profileUrl, data, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
          req: mockRequest as any,
        },
      })
      .catch((e) => e);

      const response = await axios.delete(
        `${profileUrl}/${createdProfile.data.data.profile_id}`,
        {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
          },
        }
      );

      expect(response.status).toBe(200);
      expect(response.data.data.is_deleted).toBe(true);
      expect(response.data.data.deleted_by).toBeDefined();
      expect(response.data.data.deleted_at).toBeDefined();
    });
  });
});
