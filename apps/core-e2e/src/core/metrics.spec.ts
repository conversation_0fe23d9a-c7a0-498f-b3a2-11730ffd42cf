import axios from 'axios';
import {healThisToken} from '@core_be/global';
import { faker } from '@faker-js/faker';

const metricsUrl = 'http://127.0.0.1:3000/api/metrics';
const user_id = 1;
const healThisBearerToken = healThisToken();
const mockRequest: Request = {
  raw: {
    user: {
      user_id,
    },
  },
} as any;

describe('Metrics', () => {
  describe('Metrics Validation Errors', () => {
    it('hould return 404 for non-existent metric', async () => {
      const response = await axios.get(`${metricsUrl}/99999`, {headers: 
        {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      }).catch((e) => e.response);
      
      expect(response.status).toBe(404);
      expect(response.data.message).toContain('Metric not found.');
    });
  });

  describe('Metrics (e2e) with Axios', () => {

    it('should create metrics successfully', async () => {
      const createData = {
        data: [
          {
            app_state: 'Active',
            app_version: '1.0.0',
            click_item: 'navigation',
            destination_page_name: 'Dashboard',
            device_country: 'USA',
            device_id: '12345',
            device_name: 'Model X',
            device_os: 'iOS',
            latitude: 37.7749,
            longitude: -122.4194,
            event: 'page_view',
            event_details_schema_version: '1.0.0',
            session_info: JSON.stringify({ is_emergency: 'false', symptoms: 'false' }),
            event_id: 'EVT123',
            notification_body: 'Notification text',
            notification_title: 'Alert',
            notification_topic: 'General',
            platform: 'Web',
            profile_id: 1,
            profile_type: 'User',
            session_duration: 3000,
            session_end: '2025-01-01T12:00:00Z',
            session_start: '2025-01-01T11:30:00Z',
            source_page_name: 'Homepage',
            user_id: 1,
            version: '1.0.0',
            widgets: 'Sidebar',
            age: '25',
            gender: 'Male',
            no_of_profile: '2',
            subscription_plan: 'Premium',
          },
        ],
      };

      const response = await axios.post(metricsUrl, createData, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
          req: mockRequest as any,
        },
      }).catch((e) => e);

      expect(response.status).toBe(201);
      expect(response.data.message).toBe('Sent to queue');
    });

    it('should fetch all metrics', async () => {
      const response = await axios.get(metricsUrl, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      });

      expect(response.status).toBe(200);
      expect(response.data.data.length).toBeGreaterThan(0);
    });

    it('should return a specific metric by ID', async () => {
      const response = await axios.get(
        `${metricsUrl}/1`,
        {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
          },
        }
      );

      expect(response.status).toBe(200);
      expect(response.data.data).toHaveProperty('event_id', response.data.data.event_id);
    });
  });
});
