import axios from 'axios';
import { faker } from '@faker-js/faker';
import {healThisToken} from '@core_be/global';
const termsUrl = 'http://127.0.0.1:3000/api/terms';
const healThisBearerToken = healThisToken();
const user_id = '4';

const mockRequest: Request = {
  raw: {
    user: {
      user_id,
    },
  },
} as any;

describe('Terms', () => {
  const termsIdsToCleanup: any[] = [];

  afterEach(async () => {
    while (termsIdsToCleanup.length > 0) {
      const id = termsIdsToCleanup.pop();
      await axios
        .delete(`${termsUrl}/${id}`, {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
          },
        })
        .catch((e) => e);
    }
  });

  describe('Terms Validation Errors', () => {
    it('should return validation errors for invalid terms input', async () => {
      const data = {
        title: 123,
        content: '',
      }

      const response = await axios.post(`${termsUrl}`, data, {headers: 
        {
          'Content-Type': 'application/json',
            Authorization: `Bearer ${healThisBearerToken}`,
        },
      }).catch((e) => e.response);
      expect(response.status).toBe(400);
      expect(response.data.message).toContain('title must be a string');
      expect(response.data.message).toContain('content should not be empty');
    });

    it('should return an error when creating terms without a title', async () => {
      const data = {
        content: 'Some content',
      }

      const response = await axios.post(`${termsUrl}`, data, {headers: 
        {
          'Content-Type': 'application/json',
            Authorization: `Bearer ${healThisBearerToken}`,
        },
      }).catch((e) => e.response);
      expect(response.status).toBe(400);
      expect(response.data.message).toContain('title should not be empty');
    });

    it('should return 404 for a non-existent terms record', async () => {
      const response = await axios.get(`${termsUrl}/99999`, {headers: 
        {
          'Content-Type': 'application/json',
            Authorization: `Bearer ${healThisBearerToken}`,
        },
      }).catch((e) => e.response);
      
      expect(response.status).toBe(404);
      expect(response.data.message).toContain('Terms not found.');
    });
  });

  describe('Create User Terms', () => {
    it('should should save user terms ', async () => {
      const terms = {
        title: faker.string.alpha(8),
        content: faker.string.alpha(8),
      };
      const termsRecord = await axios
        .post(`${termsUrl}`, terms, {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
            req: mockRequest as any,
          },
        })
        .catch((e) => e);

      termsIdsToCleanup.push(termsRecord.data.data.terms_id);

      expect(termsRecord.status).toBe(201);
      expect(termsRecord.data.data.title).toBeDefined();
      expect(termsRecord.data.data.content).toBeDefined();
      expect(termsRecord.data.data.is_active).toBeDefined();
    });
  });

  describe('Find All', () => {
    it('should find all terms', async () => {
      const terms = {
        title: faker.string.alpha(8),
        content: faker.string.alpha(8),
      };
      await axios
        .post(`${termsUrl}`, terms, {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
            req: mockRequest as any,
          },
        })
        .catch((e) => e);

      await axios
        .post(`${termsUrl}`, terms, {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
            req: mockRequest as any,
          },
        })
        .catch((e) => e);

      const allTerms = await axios
        .get(`${termsUrl}`, {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
          },
        })
        .catch((e) => e);

      expect(allTerms.data.status_code).toBe(200);
      expect(allTerms.data.success).toBe(true);
      expect(allTerms.data.data.length).toBeGreaterThan(1);
    });
  });

  describe('Find', () => {
    it('should find term by term id', async () => {
      const terms = {
        title: faker.string.alpha(8),
        content: faker.string.alpha(8),
      };
      const termsRecord = await axios
        .post(`${termsUrl}`, terms, {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
            req: mockRequest as any,
          },
        })
        .catch((e) => e);

      await axios
        .get(`${termsUrl}/${termsRecord.data.data.terms_id}`, {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
          },
        })
        .catch((e) => e);

      termsIdsToCleanup.push(termsRecord.data.data.terms_id);

      expect(termsRecord.data.status_code).toBe(201);
      expect(termsRecord.data.success).toBe(true);
      expect(termsRecord.data.data.title).toBeDefined();
      expect(termsRecord.data.data.content).toBeDefined();
      expect(termsRecord.data.data.is_active).toBeDefined();
    });
  });

  describe('Update', () => {
    it('should update term by terms id', async () => {
      const terms = {
        title: faker.string.alpha(8),
        content: faker.string.alpha(8),
      };
      const termsRecord = await axios
        .post(`${termsUrl}`, terms, {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
            req: mockRequest as any,
          },
        })
        .catch((e) => e);

      const updateReq = {
        title: faker.string.alpha(8),
        content: faker.string.alpha(8),
      };

      const response = await axios
        .patch(`${termsUrl}/${termsRecord.data.data.terms_id}`, updateReq, {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
          },
        })
        .catch((e) => e);

      termsIdsToCleanup.push(termsRecord.data.data.terms_id);

      expect(response.data.status_code).toBe(200);
      expect(response.data.success).toBe(true);
      expect(response.data.data.title).toBe(updateReq.title);
      expect(response.data.data.content).toBe(updateReq.content);
    });
  });

  describe('Delete', () => {
    it('should delete term by terms id', async () => {
      const terms = {
        title: faker.string.alpha(8),
        content: faker.string.alpha(8),
      };
      const termsRecord = await axios
        .post(`${termsUrl}`, terms, {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
            req: mockRequest as any,
          },
        })
        .catch((e) => e);

      const response = await axios
        .delete(`${termsUrl}/${termsRecord.data.data.terms_id}`, {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
          },
        })
        .catch((e) => e);

      expect(response.data.status_code).toBe(200);
      expect(response.data.success).toBe(true);
      expect(response.data.data.is_deleted).toBe(true);
      expect(response.data.data.deleted_by).toBeDefined();
      expect(response.data.data.deleted_at).toBeDefined();
    });
  });
});
