import axios from 'axios';
import { healThisToken } from '@core_be/global';
import { faker } from '@faker-js/faker';

const attributeListUrl = 'http://127.0.0.1:3000/api/attributelist';
const user_id = 1;
const healThisBearerToken = healThisToken();
const mockRequest: Request = {
  raw: {
    user: {
      user_id,
    },
  },
} as any;

describe('AttributeList', () => {
  const attributeIdsToCleanup: any[] = [];

  afterEach(async () => {
    while (attributeIdsToCleanup.length > 0) {
      const id = attributeIdsToCleanup.pop();
      await axios
        .delete(`${attributeListUrl}/${id}`, {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
            req: mockRequest as any,
          },
        })
        .catch((e) => e);
    }
  });

  describe('AttributeList Validation Errors', () => {
    it('should return validation errors for invalid attributeList input', async () => {
      const data = {
        name: 123,
        description: '',
      }
      const response = await axios.post(`${attributeListUrl}`, data, {headers: 
        {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      }).catch((e) => e.response);
      expect(response.status).toBe(400);
      expect(response.data.message).toContain('name must be a string');
      expect(response.data.message).toContain('description should not be empty');
    });

    it('should return an error when creating attributeList without a name', async () => {
      const data = {
        description: faker.lorem.sentence(),
      }

      const response = await axios.post(`${attributeListUrl}`, data, {headers: 
        {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      }).catch((e) => e.response);
      expect(response.status).toBe(400);
      expect(response.data.message).toContain('name should not be empty');
    });

    it('should return 404 for a non-existent attributeList record', async () => {
      const response = await axios.get(`${attributeListUrl}/99999`, {headers: 
        {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      }).catch((e) => e.response);
      
      expect(response.status).toBe(404);
      expect(response.data.message).toContain('AttributeList not found.');
    });
  });

  describe('AttributeList (e2e) with Axios', () => {
    it('should create a new attribute', async () => {
      const createAttributeDto = {
        name: faker.lorem.word(),
        description: faker.lorem.sentence(),
      };

      const res = await axios
        .post(attributeListUrl, createAttributeDto, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${healThisBearerToken}`,
            req: mockRequest as any,
          },
        })
        .catch((e) => e);

      attributeIdsToCleanup.push(res.data.data.attribute_id);
      expect(res.status).toBe(201);
      expect(res.data.message).toBe('Success');
      expect(res.data.data.name).toBe(createAttributeDto.name);
      expect(res.data.data.description).toBe(createAttributeDto.description);
    });

    it('should retrieve all attributes', async () => {
      const createAttributeDto = {
        name: faker.lorem.word(),
        description: faker.lorem.sentence(),
      };

      const createdAttribute = await axios
        .post(attributeListUrl, createAttributeDto, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${healThisBearerToken}`,
            req: mockRequest as any,
          },
        })
        .catch((e) => e);

      const res = await axios.get(attributeListUrl, {
        headers: {
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      });

      attributeIdsToCleanup.push(createdAttribute.data.data.attribute_id);

      expect(res.status).toBe(200);
      expect(res.data.data.length).toBeGreaterThan(0);
      expect(res.data.data).toContainEqual(
        expect.objectContaining({
          attribute_id: createdAttribute.data.data.attribute_id,
          name: createAttributeDto.name,
        })
      );
    });

    it('should retrieve an attribute by ID', async () => {
      const createAttributeDto = {
        name: faker.lorem.word(),
        description: faker.lorem.sentence(),
      };

      const createdAttribute = await axios
        .post(attributeListUrl, createAttributeDto, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${healThisBearerToken}`,
            req: mockRequest as any,
          },
        })
        .catch((e) => e);

      const res = await axios.get(
        `${attributeListUrl}/${createdAttribute.data.data.attribute_id}`,
        {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
          },
        }
      );

      attributeIdsToCleanup.push(createdAttribute.data.data.attribute_id);

      expect(res.status).toBe(200);
      expect(res.data.data.name).toBe(createAttributeDto.name);
      expect(res.data.data.description).toBe(createAttributeDto.description);
    });

    it('should update an attribute', async () => {
      const createAttributeDto = {
        name: faker.lorem.word(),
        description: faker.lorem.sentence(),
      };

      const createdAttribute = await axios
        .post(attributeListUrl, createAttributeDto, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${healThisBearerToken}`,
            req: mockRequest as any,
          },
        })
        .catch((e) => e);

      const updateAttributeDto = {
        name: faker.lorem.word(),
        description: faker.lorem.sentence(),
      };

      const res = await axios
        .patch(
          `${attributeListUrl}/${createdAttribute.data.data.attribute_id}`,
          updateAttributeDto,
          {
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${healThisBearerToken}`,
              req: mockRequest as any,
            },
          }
        )
        .catch((e) => e);

      attributeIdsToCleanup.push(createdAttribute.data.data.attribute_id);

      expect(res.status).toBe(200);
      expect(res.data.data.name).toBe(updateAttributeDto.name);
      expect(res.data.data.description).toBe(updateAttributeDto.description);
    });

    it('should delete an attribute', async () => {
      const createAttributeDto = {
        name: faker.lorem.word(),
        description: faker.lorem.sentence(),
      };

      const createdAttribute = await axios
        .post(attributeListUrl, createAttributeDto, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${healThisBearerToken}`,
            req: mockRequest as any,
          },
        })
        .catch((e) => e);

      const res = await axios.delete(
        `${attributeListUrl}/${createdAttribute.data.data.attribute_id}`,
        {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
          },
        }
      );

      expect(res.status).toBe(200);
      expect(res.data.data.is_deleted).toBe(true);
      expect(res.data.data.deleted_by).toBeDefined();
      expect(res.data.data.deleted_at).toBeDefined();
    });
  });
});
