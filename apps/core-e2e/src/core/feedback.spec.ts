import axios from 'axios';
import {healThisToken} from '@core_be/global';
import { faker } from '@faker-js/faker';

const feedbackUrl = 'http://127.0.0.1:3000/api/feedback';
const user_id = 1;
const patient_id = 1;
const healer_id = 2;
const healThisBearerToken = healThisToken();
const mockRequest: Request = {
  raw: {
    user: {
      user_id,
    },
  },
} as any;

describe('Feedback', () => {
  const feedbackIdsToCleanup: any[] = [];

  afterEach(async () => {
    while (feedbackIdsToCleanup.length > 0) {
      const id = feedbackIdsToCleanup.pop();
      await axios
        .delete(`${feedbackUrl}/${id}`, {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
            req: mockRequest as any,
          },
        })
        .catch((e) => e);
    }
  });

  describe('Feedback Validation Errors', () => {
    it('should return validation errors for invalid feedback input', async () => {
      const data = {}
      const response = await axios.post(`${feedbackUrl}`, data, {headers: 
        {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      }).catch((e) => e.response);
      expect(response.status).toBe(400);
      expect(response.data.message).toContain('comment should not be empty');
      expect(response.data.message).toContain('patient_id must be an integer number');
      expect(response.data.message).toContain('healer_id must be an integer number');
    });

    it('should return 404 if patient profile is not found', async () => {
      const data = {
        comment: faker.lorem.sentence(),
        patient_id: 9999, 
        healer_id: 2,
      }
      const response = await axios.post(`${feedbackUrl}`, data, {headers: 
        {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      }).catch((e) => e.response);
      expect(response.status).toBe(404);
      expect(response.data.message).toContain('Patient profile not found!');
    });

    it('should return 404 if healer profile is not found', async () => {
      const data = {
        comment: 'Great experience',
        patient_id: 2, 
        healer_id: 9999,
      }
      const response = await axios.post(`${feedbackUrl}`, data, {headers: 
        {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      }).catch((e) => e.response);
      expect(response.status).toBe(404);
      expect(response.data.message).toContain('Healer profile not found!');
    });
  });

  describe('Feedback API (E2E) with Axios', () => {
    it('should create a new feedback', async () => {
      const createFeedbackDto = {
        comment: faker.lorem.sentence(),
        patient_id,
        healer_id,
      };

      const res = await axios
        .post(feedbackUrl, createFeedbackDto, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${healThisBearerToken}`,
            req: mockRequest as any,
          },
        })
        .catch((e) => e);
      feedbackIdsToCleanup.push(res.data.data.id);
      expect(res.status).toBe(201);
      expect(res.data.message).toBe('Success');
      expect(res.data.data.comment).toBe(createFeedbackDto.comment);
      expect(res.data.data.patient_id).toBe(createFeedbackDto.patient_id);
      expect(res.data.data.healer_id).toBe(createFeedbackDto.healer_id);
    });
  });
});
