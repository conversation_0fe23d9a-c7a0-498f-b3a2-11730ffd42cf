import axios from 'axios';
import {healThisToken} from '@core_be/global';
import { faker } from '@faker-js/faker';
import path from 'path';
import fs from 'fs';
import FormData from 'form-data';

const whitelistUrl = 'http://127.0.0.1:3000/api/whitelist';
const healThisBearerToken = healThisToken();

const headers = {
  'Content-Type': 'application/json',
  Authorization: `Bearer ${healThisBearerToken}`,
};

describe('Whitelist', () => {
  describe('WhitelistController (E2E) with Axios', () => {
    it('should fail when emails array is empty', async () => {
      const requestBody = { emails: [] };
      const res = await axios.post(`${whitelistUrl}`, requestBody, { headers }).catch((e) => e.response);
      expect(res.status).toBe(400);
      expect(res.data.message).toContain('emails should not be empty');
    });

    it('should successfully add valid emails', async () => {
      const requestBody = { emails: [faker.internet.email(), faker.internet.email()] };
      const res = await axios.post(`${whitelistUrl}`, requestBody, { headers });
      expect(res.status).toBe(201);
      expect(res.data.data.whitelistedEmails.length).toBeGreaterThan(0);
    });

    it('should detect duplicate emails', async () => {
      const requestBody = { emails: ['<EMAIL>'] };
      await axios.post(`${whitelistUrl}`, requestBody, { headers }); 
      const res = await axios.post(`${whitelistUrl}`, requestBody, { headers }).catch((e) => e.response); 
      expect(res.status).toBe(201);
      expect(res.data.data.duplicateEmails).toContain('<EMAIL>');
    });

    it('should fail when uploading an invalid file format', async () => {
      const filePath = path.join(__dirname, 'test.txt');
      fs.writeFileSync(filePath, 'invalid content');
      const formData = new FormData();
      formData.append('file', fs.createReadStream(filePath));
      
      const res = await axios.post(`${whitelistUrl}/bulk-upload`, formData, {
        headers: {
          ...formData.getHeaders(),
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      }).catch((e) => e.response);
      expect(res.status).toBe(400);
      expect(res.data.message).toBe('Invalid File. Please upload a CSV file.');
      fs.unlinkSync(filePath);
    });

    it('should successfully upload a valid CSV file', async () => {
      const filePath = path.join(__dirname, 'test.csv');
      fs.writeFileSync(filePath, `email\n${faker.internet.email()}\n${faker.internet.email()}`);
      const formData = new FormData();
      formData.append('file', fs.createReadStream(filePath));
      
      const res = await axios.post(`${whitelistUrl}/bulk-upload`, formData, {
        headers: {
          ...formData.getHeaders(),
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      }).catch((e) => e.response);
      
      expect(res.status).toBe(201);
      expect(res.data.data.whitelistedEmails.length).toBeGreaterThan(0);
      fs.unlinkSync(filePath);
    });
  });
});