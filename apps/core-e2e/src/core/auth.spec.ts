import axios from 'axios';
import {healThisToken} from '@core_be/global';
import { faker } from '@faker-js/faker';
import { ProfileType } from '@core/libs';

const authUrl = 'http://127.0.0.1:3000/api/auth';
const healThisBearerToken = healThisToken();
const deviceTypes = ['iOS', 'Android', 'Web', 'Desktop']; 

describe('Auth API (E2E) with Axios', () => {
  let registeredUser = null;

  describe('Auth Validation Errors', () => {
    it('should return validation errors for invalid register input', async () => {
      const data = {
        profile_type: 123,
        email: '',
        username: '',
        password: '',
        device_id: '',
        device_type: '',
        fcm_token: '',
      }
      const response = await axios.post(`${authUrl}/register`, data, {
        headers: {
          'Content-Type': 'application/json',
        },
      }).catch((e) => e.response);

      expect(response.status).toBe(400);
      expect(response.data.message).toContain('profile_type must be one of the following values: Healer, Patient');
      expect(response.data.message).toContain('email must be an email');
      expect(response.data.message).toContain('username should not be empty');
      expect(response.data.message).toContain('password must be longer than or equal to 8 characters');
      expect(response.data.message).toContain('device_id should not be empty');
      expect(response.data.message).toContain('device_type should not be empty');
      expect(response.data.message).toContain('fcm_token should not be empty');
    });

    it('should return an error when register without a password', async () => {
      const data = {
        profile_type: ProfileType.PATIENT,
        email: faker.internet.email(),
        username: faker.internet.username(),
        device_id: faker.string.alphanumeric(16),
        device_type: faker.helpers.arrayElement(deviceTypes),
        fcm_token: faker.string.alphanumeric(32),
      }
      const response = await axios.post(`${authUrl}/register`, data, {headers: 
        {
          'Content-Type': 'application/json',
        },
      }).catch((e) => e.response);
      expect(response.status).toBe(400);
      expect(response.data.message).toContain('password should not be empty');
    });

    it('should return validation errors for invalid forget password input', async () => {
      const data = {
        email: '',
      }
      const response = await axios.post(`${authUrl}/forgot-password`, data, {
        headers: {
          'Content-Type': 'application/json',
        },
      }).catch((e) => e.response);

      expect(response.status).toBe(400);
      expect(response.data.message).toContain('email must be an email');
    });

    it('should return 400 forget password if user not found ', async () => {
      const data = {
        email: faker.internet.email(),
      }
      const response = await axios.post(`${authUrl}/forgot-password`, data, {
        headers: {
          'Content-Type': 'application/json',
        },
      }).catch((e) => e.response);

      expect(response.status).toBe(400);
      expect(response.data.message).toContain('User not found');
    });

    it('should return validation errors for invalid reset password input', async () => {
      const data = {
        password: '',
        token:1111
      }
      const response = await axios
      .post(`${authUrl}/reset-password`, data, {
        headers: {
          'Content-Type': 'application/json',
        },
      })
      .catch((e) => e.response);

      expect(response.status).toBe(400);
      expect(response.data.message).toContain('password must be longer than or equal to 8 characters');
      expect(response.data.message).toContain('token must be a string');
    });

    it('should return an error when reset password without a password', async () => {
      const data = {
        token:faker.string.alphanumeric(32)
      }
      const response = await axios.post(`${authUrl}/reset-password`, data, {headers: 
        {
          'Content-Type': 'application/json',
        },
      }).catch((e) => e.response);
      expect(response.status).toBe(400);
      expect(response.data.message).toContain('password must be longer than or equal to 8 characters');
    });

    it('should return 400 reset password if invalid token', async () => {
      const data = {
        password: faker.string.alphanumeric({ length:12, exclude: [' ', '-', '_'] }),
        token: faker.string.alphanumeric(32),
      }
      const response = await axios.post(`${authUrl}/reset-password`, data, {headers: 
        {
          'Content-Type': 'application/json',
        },
      }).catch((e) => e.response);

      expect(response.status).toBe(400);
      expect(response.data.message).toContain('Invalid forgot password token');
    });

    it('should return validation errors for invalid refresh token input', async () => {
      const data = {
        token:1111
      }
      const response = await axios
      .post(`${authUrl}/refresh-token`, data, {
        headers: {
          'Content-Type': 'application/json',
        },
      })
      .catch((e) => e.response);

      expect(response.status).toBe(400);
      expect(response.data.message).toContain('token must be a string');
    });
  });

  describe('Register', () => {
    it('should register a new user successfully', async () => {
      const registerDto = {
        username: faker.person.firstName('female'),
        email: '<EMAIL>',
        password: 'Password@123',
        profile_type: ProfileType.PATIENT,
        gender: faker.person.gender(),
        date_of_birth: '2000-11-01',
        device_id: faker.string.uuid(),
        device_type: 'mobile',
        fcm_token: faker.string.uuid(),
      };

      const res = await axios
        .post(`${authUrl}/register`, registerDto, {
          headers: {
            'Content-Type': 'application/json',
          },
        })
        .catch((e) => e.response);

      expect(res.status).toBe(201);
      expect(res.data.data.user_id).toBeDefined();
      registeredUser = res.data.data;
    });

    it('should fail to register an existing user', async () => {
      const registerDto = {
        username: registeredUser.email,
        email: registeredUser.email,
        password: 'Password@123',
        profile_type: ProfileType.PATIENT,
        device_id: faker.string.uuid(),
        device_type: 'mobile',
        fcm_token: faker.string.uuid(),
      };

      const res = await axios
        .post(`${authUrl}/register`, registerDto, {
          headers: {
            'Content-Type': 'application/json',
          },
        })
        .catch((e) => e.response);

      expect(res.status).toBe(409);
      expect(res.data.success).toBe(false);
    });
  });

  describe('Login', () => {
    it('should log in the registered user successfully', async () => {
      const loginDto = {
        username: registeredUser.email,
        password: 'Password@123',
      };

      const res = await axios
        .post(`${authUrl}/login`, loginDto, {
          headers: {
            'Content-Type': 'application/json',
          },
        })
        .catch((e) => e.response);

      expect(res.status).toBe(200);
      expect(res.data.data.access_token).toBeDefined();
    });

    it('should fail to log in with incorrect credentials', async () => {
      const loginDto = {
        username: registeredUser.email,
        password: 'WrongPassword',
      };

      const res = await axios
        .post(`${authUrl}/login`, loginDto, {
          headers: {
            'Content-Type': 'application/json',
          },
        })
        .catch((e) => e.response);

      expect(res.status).toBe(400);
      expect(res.data.success).toBe(false);
    });
  });

  describe('Forgot Password', () => {
    it('should send a forgot password email', async () => {
      const forgotPasswordDto = {
        email: registeredUser.email,
      };

      const res = await axios
        .post(`${authUrl}/forgot-password`, forgotPasswordDto, {
          headers: {
            'Content-Type': 'application/json',
          },
        })
        .catch((e) => e.response);

      expect(res.status).toBe(201);
      expect(res.data.success).toBe(true);
    });

    it('should fail to send forgot password email for non-existent user', async () => {
      const forgotPasswordDto = {
        email: faker.internet.email(),
      };

      const res = await axios
        .post(`${authUrl}/forgot-password`, forgotPasswordDto, {
          headers: {
            'Content-Type': 'application/json',
          },
        })
        .catch((e) => e.response);

      expect(res.status).toBe(400);
      expect(res.data.success).toBe(false);
    });
  });

  describe('Refresh Token', () => {
    it('should refresh the access token', async () => {
      const refreshTokenDto = {
        token: registeredUser.access_token,
      };

      const res = await axios
        .post(`${authUrl}/refresh-token`, refreshTokenDto, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${healThisBearerToken}`,
          },
        })
        .catch((e) => e.response);

      expect(res.status).toBe(201);
      expect(res.data).toBeDefined();
    });
  });

  describe('Logout', () => {
    it('should log out the user', async () => {
      const res = await axios
        .post(
          `${authUrl}/logout`,
          {},
          {
            headers: {
              Authorization: `Bearer ${healThisBearerToken}`,
            },
          }
        )
        .catch((e) => e.response);

      expect(res.status).toBe(201);
      expect(res.data.success).toBe(true);
    });
  });
});
