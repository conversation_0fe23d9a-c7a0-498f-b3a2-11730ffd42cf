import axios from 'axios';
import {healThisToken} from '@core_be/global';
import { faker } from '@faker-js/faker';

const privacyPolicyUrl = 'http://127.0.0.1:3000/api/privacy-policy';
const user_id = 1;
const healThisBearerToken = healThisToken();
const mockRequest: Request = {
  raw: {
    user: {
      user_id,
    },
  },
} as any;

describe('PrivacyPolicy', () => {
  const policyIdsToCleanup: any[] = [];

  afterEach(async () => {
    while (policyIdsToCleanup.length > 0) {
      const id = policyIdsToCleanup.pop();
      await axios
        .delete(`${privacyPolicyUrl}/${id}`, {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
            req: mockRequest as any,
          },
        })
        .catch((e) => e);
    }
  });

  describe('PrivacyPolicy Validation Errors', () => {
    it('should return validation errors for invalid privacyPolicy input', async () => {
      const data = {
        title: 123, 
        content: '',
      }

      const response = await axios.post(`${privacyPolicyUrl}`, data, {headers: 
        {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      }).catch((e) => e.response);
      expect(response.status).toBe(400);
      expect(response.data.message).toContain('title must be a string');
      expect(response.data.message).toContain('content should not be empty');
    });

    it('should return an error when creating privacyPolicy without a title', async () => {
      const data = {
        content: 'Privacy policy content...'
      }

      const response = await axios.post(`${privacyPolicyUrl}`, data, {headers: 
        {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      }).catch((e) => e.response);
      expect(response.status).toBe(400);
      expect(response.data.message).toContain('title should not be empty');
    });

    it('should return 404 for a non-existent privacyPolicy record', async () => {
      const response = await axios.get(`${privacyPolicyUrl}/99999`, {headers: 
        {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      }).catch((e) => e.response);
      
      expect(response.status).toBe(404);
      expect(response.data.message).toContain('Privacy policy not found.');
    });
  });

  describe('PrivacyPolicy (e2e) with Axios', () => {
    it('should create a new privacy policy', async () => {
      const createPolicyDto = {
        title: faker.lorem.sentence(),
        content: faker.lorem.paragraph(),
        language: 'en',
      };

      const res = await axios
        .post(privacyPolicyUrl, createPolicyDto, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${healThisBearerToken}`,
            req: mockRequest as any,
          },
        })
        .catch((e) => e);

      policyIdsToCleanup.push(res.data.data.privacy_policy_id);
      expect(res.status).toBe(201);
      expect(res.data.message).toBe('Success');
      expect(res.data.data.title).toBeDefined();
      expect(res.data.data.content).toBeDefined();
    });

    it('should find all privacy policies', async () => {
      const createPolicyDto = {
        title: faker.lorem.sentence(),
        content: faker.lorem.paragraph(),
        language: 'en',
        is_active: true,
        effective_date: new Date(),
      };

      const createdPolicy = await axios
        .post(privacyPolicyUrl, createPolicyDto, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${healThisBearerToken}`,
            req: mockRequest as any,
          },
        })
        .catch((e) => e);

      const res = await axios.get(privacyPolicyUrl, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      });

      policyIdsToCleanup.push(createdPolicy.data.data.privacy_policy_id);

      expect(res.status).toBe(200);
      expect(res.data.data.length).toBeGreaterThan(0);
      expect(res.data.data).toContainEqual(
        expect.objectContaining({
          privacy_policy_id: createdPolicy.data.data.privacy_policy_id,
          title: createPolicyDto.title,
        })
      );
    });

    it('should find a privacy policy by ID', async () => {
      const createPolicyDto = {
        title: faker.lorem.sentence(),
        content: faker.lorem.paragraph(),
        language: 'en',
        is_active: true,
        effective_date: new Date(),
      };

      const createdPolicy = await axios
        .post(privacyPolicyUrl, createPolicyDto, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${healThisBearerToken}`,
            req: mockRequest as any,
          },
        })
        .catch((e) => e);

      const res = await axios.get(
        `${privacyPolicyUrl}/${createdPolicy.data.data.privacy_policy_id}`,
        {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
          },
        }
      );

      policyIdsToCleanup.push(createdPolicy.data.data.privacy_policy_id);

      expect(res.status).toBe(200);
      expect(res.data.data.title).toBe(createPolicyDto.title);
      expect(res.data.data.content).toBe(createPolicyDto.content);
      expect(res.data.data.language).toBe('en');
    });

    it('should delete a privacy policy', async () => {
      const createPolicyDto = {
        title: faker.lorem.sentence(),
        content: faker.lorem.paragraph(),
        language: 'en',
        is_active: true,
        effective_date: new Date(),
      };

      const createdPolicy = await axios
        .post(privacyPolicyUrl, createPolicyDto, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${healThisBearerToken}`,
            req: mockRequest as any,
          },
        })
        .catch((e) => e);

      const res = await axios.delete(
        `${privacyPolicyUrl}/${createdPolicy.data.data.privacy_policy_id}`,
        {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
          },
        }
      );

      expect(res.status).toBe(200);
      expect(res.data.data.is_deleted).toBe(true);
      expect(res.data.data.deleted_by).toBeDefined();
      expect(res.data.data.deleted_at).toBeDefined();
    });
  });
});
