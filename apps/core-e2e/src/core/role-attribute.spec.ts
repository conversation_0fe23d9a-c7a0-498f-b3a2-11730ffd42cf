import axios from 'axios';
import { faker } from '@faker-js/faker';
import {healThisToken} from '@core_be/global';
const roleAttributeUrl = 'http://127.0.0.1:3000/api/roleattribute';
const attributeUrl = 'http://127.0.0.1:3000/api/attributelist';
const roleUrl = 'http://127.0.0.1:3000/api/role';
const healThisBearerToken = healThisToken();
const user_id = '4';

const mockRequest: Request = {
  raw: {
    user: {
      user_id,
    },
  },
} as any;

const attribute = async () => {
  const payload = {
    name: faker.string.alpha(10),
    description: faker.string.alpha(20),
  };
  const response = await axios.post(attributeUrl, payload, {
    headers: {
      Authorization: `Bearer ${healThisBearerToken}`,
      req: mockRequest as any,
    },
  });
  return await response.data.data.attribute_id;
};

const role = async () => {
  const payload = {
    name: faker.string.alpha(10),
    description: faker.string.alpha(20),
  };
  const response = await axios.post(roleUrl, payload, {
    headers: {
      Authorization: `Bearer ${healThisBearerToken}`,
      req: mockRequest as any,
    },
  });
  return await response.data.data.role_id;
};

describe('Role Attribute', () => {
  const roleAttributeIdsToCleanup: any[] = [];
  const roleIdsToCleanup: any[] = [];
  const attributeIdsToCleanup: any[] = [];

  afterEach(async () => {
    while (roleAttributeIdsToCleanup.length > 0) {
      const id = roleAttributeIdsToCleanup.pop();
      await axios
        .delete(`${roleAttributeUrl}/${id}`, {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
          },
        })
        .catch((e) => e);
    }

    while (roleIdsToCleanup.length > 0) {
      const id = roleIdsToCleanup.pop();
      await axios
        .delete(`${roleUrl}/${id}`, {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
          },
        })
        .catch((e) => e);
    }

    while (attributeIdsToCleanup.length > 0) {
      const id = attributeIdsToCleanup.pop();
      await axios
        .delete(`${attributeUrl}/${id}`, {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
          },
        })
        .catch((e) => e);
    }
  });

  describe('RoleAttribute Validation Errors', () => {
    it('should return validation errors for invalid roleAttribute input', async () => {
      const data = {
        role_id: '1233',
        attribute_id: '1232',
      }

      const response = await axios.post(`${roleAttributeUrl}`, data, {headers: 
        {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      }).catch((e) => e.response);
      expect(response.status).toBe(400);
      expect(response.data.message).toContain('role_id must be an integer number');
      expect(response.data.message).toContain('attribute_id must be an integer number');
    });

    it('should return an error when creating roleAttribute without a role_id', async () => {
      const data = {
        attribute_id: 12,
      }

      const response = await axios.post(`${roleAttributeUrl}`, data, {headers: 
        {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      }).catch((e) => e.response);
      expect(response.status).toBe(400);
      expect(response.data.message).toContain('role_id should not be empty');
    });

    it('should return 404 for a non-existent roleAttribute record', async () => {
      const response = await axios.get(`${roleAttributeUrl}/99999`, {headers: 
        {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      }).catch((e) => e.response);
      
      expect(response.status).toBe(404);
      expect(response.data.message).toContain('Role attribute not found.');
    });
  });

  describe('Create', () => {
    it('should create role attribute ', async () => {
      const attributeId = await attribute();
      const roleId = await role();
      const roleAttribute = {
        role_id: roleId,
        attribute_id: attributeId,
      };
      const response = await axios
        .post(`${roleAttributeUrl}`, roleAttribute, {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
          },
        })
        .catch((e) => e);

      roleAttributeIdsToCleanup.push(response.data.data.role_attribute_id);
      attributeIdsToCleanup.push(attributeId);
      roleIdsToCleanup.push(roleId);

      expect(response.status).toBe(201);
      expect(response.data.data.role_attribute_id).toBeDefined();
      expect(response.data.data.role_id).toBeDefined();
      expect(response.data.data.attribute_id).toBeDefined();
    });
  });

  describe('Find All', () => {
    it('should find all role attributes', async () => {
      const attributeId = await attribute();
      const roleId = await role();
      const roleAttribute = {
        role_id: roleId,
        attribute_id: attributeId,
      };
      await axios
        .post(`${roleAttributeUrl}`, roleAttribute, {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
          },
        })
        .catch((e) => e);

      const attribute_id = await attribute();
      const role_id = await role();
      const updateReq = {
        role_id,
        attribute_id,
      };

      await axios
        .post(`${roleAttributeUrl}`, updateReq, {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
          },
        })
        .catch((e) => e);

      const response = await axios
        .get(`${roleAttributeUrl}`, {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
          },
        })
        .catch((e) => e);

      roleAttributeIdsToCleanup.push(response.data.data.role_attribute_id);
      attributeIdsToCleanup.push(attributeId);
      roleIdsToCleanup.push(roleId);
      attributeIdsToCleanup.push(attribute_id);
      roleIdsToCleanup.push(role_id);

      expect(response.data.status_code).toBe(200);
      expect(response.data.success).toBe(true);
      expect(response.data.data.length).toBeGreaterThan(1);
    });
  });

  describe('Find', () => {
    it('should find role attribute by role attribute id', async () => {
      const attributeId = await attribute();
      const roleId = await role();
      const roleAttribute = {
        role_id: roleId,
        attribute_id: attributeId,
      };
      const response = await axios
        .post(`${roleAttributeUrl}`, roleAttribute, {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
          },
        })
        .catch((e) => e);

      const res = await axios
        .get(`${roleAttributeUrl}/${response.data.data.role_attribute_id}`, {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
          },
        })
        .catch((e) => e);

      roleAttributeIdsToCleanup.push(response.data.data.role_attribute_id);
      attributeIdsToCleanup.push(attributeId);
      roleIdsToCleanup.push(roleId);

      expect(res.data.status_code).toBe(200);
      expect(res.data.success).toBe(true);
      expect(res.data.data.role_attribute_id).toBeDefined();
      expect(res.data.data.role_id).toBeDefined();
      expect(res.data.data.attribute_id).toBeDefined();
    });
  });

  describe('Update', () => {
    it('should update role attribute by role attribute id', async () => {
      const attributeId = await attribute();
      const roleId = await role();
      const roleAttribute = {
        role_id: roleId,
        attribute_id: attributeId,
      };
      const res = await axios
        .post(`${roleAttributeUrl}`, roleAttribute, {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
          },
        })
        .catch((e) => e);

      const attribute_id = await attribute();
      const role_id = await role();
      const updateReq = {
        role_id,
        attribute_id,
      };

      const response = await axios
        .patch(
          `${roleAttributeUrl}/${res.data.data.role_attribute_id}`,
          updateReq,
          {
            headers: {
              Authorization: `Bearer ${healThisBearerToken}`,
            },
          }
        )
        .catch((e) => e);

      roleAttributeIdsToCleanup.push(res.data.data.role_attribute_id);
      attributeIdsToCleanup.push(attributeId);
      roleIdsToCleanup.push(roleId);
      attributeIdsToCleanup.push(attribute_id);
      roleIdsToCleanup.push(role_id);

      expect(response.data.status_code).toBe(200);
      expect(response.data.success).toBe(true);
      expect(response.data.data.role_id).toBe(updateReq.role_id);
      expect(response.data.data.attribute_id).toBe(updateReq.attribute_id);
    });
  });

  describe('Delete', () => {
    it('should delete role attribute by role attribute id', async () => {
      const attributeId = await attribute();
      const roleId = await role();
      const roleAttribute = {
        role_id: roleId,
        attribute_id: attributeId,
      };
      const res = await axios
        .post(`${roleAttributeUrl}`, roleAttribute, {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
          },
        })
        .catch((e) => e);

      const response = await axios
        .delete(`${roleAttributeUrl}/${res.data.data.role_attribute_id}`, {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
          },
        })
        .catch((e) => e);

      expect(response.data.status_code).toBe(200);
      expect(response.data.success).toBe(true);
    });
  });
});
