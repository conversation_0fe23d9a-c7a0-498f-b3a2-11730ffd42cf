import axios from 'axios';
import {healThisToken} from '@core_be/global';
import { faker } from '@faker-js/faker';

const onboardingUrl = 'http://127.0.0.1:3000/api/user-onboarding';
const healThisBearerToken = healThisToken();
const mockRequest: Request = {
  raw: {
    user: {
      user_id: 1,
    },
  },
} as any;

describe('User Onboarding Validation Errors', () => {
  it('should return validation errors for invalid userOnboarding input', async () => {
    const data = {
      user_id: 'invalid',  
      device_id: '',       
      device_type: 123,   
    }

    const response = await axios.post(`${onboardingUrl}`, data, {headers: 
      {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${healThisBearerToken}`,
      },
    }).catch((e) => e.response);
    expect(response.status).toBe(400);
    expect(response.data.message).toContain('user_id must be an integer number');
    expect(response.data.message).toContain('device_id should not be empty');
    expect(response.data.message).toContain('device_type must be a string');
  });

  it('should return an error when creating userOnboarding without a title', async () => {
    const data = {
      device_id: 'f6cd7e22955d4d9c',
      device_type: 'Android',
    }

    const response = await axios.post(`${onboardingUrl}`, data, {headers: 
      {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${healThisBearerToken}`,
      },
    }).catch((e) => e.response);
    expect(response.status).toBe(400);
    expect(response.data.message).toContain('user_id should not be empty');
  });

  it('should return 404 for a non-existent user_id', async () => {
    const data = {
      user_id: 99999,
      device_id: 'f6cd7e22955d4d9c',
      device_type: 'Android',
    }

    const response = await axios.post(`${onboardingUrl}`, data, {headers: 
      {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${healThisBearerToken}`,
      },
    }).catch((e) => e.response);
    
    expect(response.status).toBe(404);
    expect(response.data.message).toContain('User not found.');
  });
});

describe('User Onboarding (e2e)', () => {
  const onboardingIdsToCleanup: any[] = [];

  afterEach(async () => {
    while (onboardingIdsToCleanup.length > 0) {
      const id = onboardingIdsToCleanup.pop();
      await axios
        .delete(`${onboardingUrl}/delete/${id}`, {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
          },
        })
        .catch((e) => e);
    }
  });

  describe('Onboarding CRUD Operations', () => {
    it('should create a new onboarding record', async () => {
      const createOnboardingDto = {
        user_id: 1,
        device_id: faker.string.alpha(8),
        device_type: faker.helpers.arrayElement(['mobile', 'web', 'tablet']),
      };

      const res = await axios
        .post(onboardingUrl, createOnboardingDto, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${healThisBearerToken}`,
            req: mockRequest as any,
          },
        })
        .catch((e) => e);

      onboardingIdsToCleanup.push(res.data.data.status_id);

      expect(res.status).toBe(201);
      expect(res.data.message).toBe('Success');
      expect(res.data.data.user_id).toBe(createOnboardingDto.user_id);
      expect(res.data.data.device_id).toBe(createOnboardingDto.device_id);
      expect(res.data.data.device_type).toBe(createOnboardingDto.device_type);
    });

    it('should update an onboarding record', async () => {
      const createOnboardingDto = {
        user_id: 1,
        device_id: faker.string.alpha(8),
        device_type: faker.helpers.arrayElement(['mobile', 'web', 'tablet']),
      };

      const createResponse = await axios
        .post(onboardingUrl, createOnboardingDto, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${healThisBearerToken}`,
            req: mockRequest as any,
          },
        })
        .catch((e) => e);

      const createdId = createResponse.data.data.status_id;
      expect(createdId).toBeDefined();

      onboardingIdsToCleanup.push(createdId);

      const updateOnboardingDto = {
        user_id: createOnboardingDto.user_id,
        device_id: createOnboardingDto.device_id,
        device_type: 'tablet',
        updated_by: 1,
        updated_at: new Date().toISOString(),
      };

      const updateResponse = await axios
        .patch(onboardingUrl, updateOnboardingDto, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${healThisBearerToken}`,
            req: mockRequest as any,
          },
        })
        .catch((e) => e);

      expect(updateResponse.status).toBe(200);
      expect(updateResponse.data.message).toBeDefined();
    });
  });
});
