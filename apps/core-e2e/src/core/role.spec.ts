import axios from 'axios';
import {healThisToken} from '@core_be/global';
import { faker } from '@faker-js/faker';

const roleUrl = 'http://127.0.0.1:3000/api/role';
const healThisBearerToken = healThisToken();
const user_id = 3;
const mockRequest = {
  raw: {
    user: {
      user_id,
    },
  },
};

const newRole = async () => {
  return {
    name: faker.string.alpha(8),
    description: faker.string.alpha(8),
  };
};
describe('Role (e2e) Tests', () => {
  const roleIdsToCleanup: number[] = [];

  afterEach(async () => {
    while (roleIdsToCleanup.length > 0) {
      const roleId = roleIdsToCleanup.pop();
      if (roleId) {
        try {
          await axios.delete(`${roleUrl}/delete/${roleId}`, {
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${healThisBearerToken}`,
              req: mockRequest as any,
            },
          });
        } catch (err) {
          console.error(`Failed to clean up role with ID: ${roleId}`, err);
        }
      }
    }
  });

  describe('Role Validation Errors', () => {
    it('should return validation errors for invalid role input', async () => {
      const data = {
        name: 123,
        description: '',
      }

      const response = await axios.post(`${roleUrl}`, data, {headers: 
        {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      }).catch((e) => e.response);
      expect(response.status).toBe(400);
      expect(response.data.message).toContain('name must be a string');
      expect(response.data.message).toContain('description should not be empty');
    });

    it('should return an error when creating role without a name', async () => {
      const data = {
        description: 'Some content',
      }

      const response = await axios.post(`${roleUrl}`, data, {headers: 
        {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      }).catch((e) => e.response);
      expect(response.status).toBe(400);
      expect(response.data.message).toContain('name should not be empty');
    });

    it('should return 404 for a non-existent role record', async () => {
      const response = await axios.get(`${roleUrl}/99999`, {headers: 
        {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      }).catch((e) => e.response);
      
      expect(response.status).toBe(404);
      expect(response.data.message).toContain('Role not found.');
    });
  });

  it('should create a new role', async () => {
    const createRoleDto = await newRole();

    const res = await axios.post(roleUrl, createRoleDto, {
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${healThisBearerToken}`,
      },
    });

    roleIdsToCleanup.push(res.data.data.role_id);
    expect(res.status).toBe(201);
    expect(res.data.message).toBe('Success');
    expect(res.data.data.name).toBeDefined();
    expect(res.data.data.description).toBeDefined();
  });

  it('should find all roles', async () => {
    const createRoleDto = {
      name: faker.string.alpha(8),
      description: faker.string.alpha(8),
    };

    const res = await axios.post(roleUrl, createRoleDto, {
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${healThisBearerToken}`,
      },
    });

    roleIdsToCleanup.push(res.data.data.role_id);
    expect(res.status).toBe(201);
    expect(res.data).toBeDefined();
    expect(res.data.data.name).toBe(createRoleDto.name);
  });

  it('should find a role by ID', async () => {
    const createRoleDto = {
      name: faker.string.alpha(8),
      description: faker.string.alpha(8),
    };

    const createResult = await axios.post(roleUrl, createRoleDto, {
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${healThisBearerToken}`,
        req: mockRequest as any,
      },
    });

    const roleId = createResult.data.data.role_id;
    roleIdsToCleanup.push(roleId);

    const res = await axios.get(`${roleUrl}/${roleId}`, {
      headers: {
        Authorization: `Bearer ${healThisBearerToken}`,
      },
    });

    roleIdsToCleanup.push(res.data.data.role_id);
    expect(res.status).toBe(200);
    expect(res.data.data.name).toBe(createRoleDto.name);
    expect(res.data.data.description).toBe(createRoleDto.description);
  });

  it('should update a role', async () => {
    const createRoleDto = {
      name: faker.string.alpha(8),
      description: faker.string.alpha(8),
    };

    const res = await axios.post(roleUrl, createRoleDto, {
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${healThisBearerToken}`,
        req: mockRequest as any,
      },
    });

    const roleId = res.data.data.role_id;
    roleIdsToCleanup.push(roleId);

    const updateRoleDto = {
      name: faker.string.alpha(10),
      description: faker.string.alpha(10),
    };

    const updateRes = await axios.patch(`${roleUrl}/${roleId}`, updateRoleDto, {
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${healThisBearerToken}`,
        req: mockRequest as any,
      },
    });
    roleIdsToCleanup.push(res.data.data.role_id);
    expect(updateRes.status).toBe(200);
    expect(updateRes.data.data.name).toBe(updateRoleDto.name);
    expect(updateRes.data.data.description).toBe(updateRoleDto.description);
  });

  it('should delete a role', async () => {
    const createRoleDto = {
      name: faker.string.alpha(8),
      description: faker.string.alpha(8),
    };

    const res = await axios.post(roleUrl, createRoleDto, {
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${healThisBearerToken}`,
        req: mockRequest as any,
      },
    });

    const roleId = res.data.data.role_id;

    const deleteRes = await axios.delete(`${roleUrl}/${roleId}`, {
      headers: {
        Authorization: `Bearer ${healThisBearerToken}`,
      },
    });

    roleIdsToCleanup.push(res.data.data.role_id);
    expect(deleteRes.status).toBe(200);
    expect(deleteRes.data.data.is_deleted).toBe(true);
  });
});
