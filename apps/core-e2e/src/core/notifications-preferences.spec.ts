import axios from 'axios';
import { healThisToken } from '@core_be/global';

const notificationsUrl = 'http://127.0.0.1:3000/api/notifications';
const userId = 3;
const healThisBearerToken = healThisToken();

describe('Notifications Preferences (E2E)', () => {
  it('should retrieve user notification preferences', async () => {
    const res = await axios
      .get(`${notificationsUrl}/${userId}/preferences`, {
        headers: {
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      })
      .catch((e) => e.response);


    expect(res.status).toBe(200);
    expect(res.data.data).toBeInstanceOf(Array);
    expect(res.data.data.length).toBeGreaterThan(0);
    expect(res.data.data[0]).toHaveProperty('topic_id');
    expect(res.data.data[0]).toHaveProperty('name');
    expect(res.data.data[0]).toHaveProperty('is_subscribed');
  });

  it('should update user notification preferences successfully', async () => {
    const requestBody = {
      preferences: [
        {
          topic_id: 3,
          is_subscribed: false,
        },
        {
          topic_id: 4,
          is_subscribed: true,
        },
      ],
    };

    const res = await axios
      .post(`${notificationsUrl}/${userId}/preferences`, requestBody, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      })
      .catch((e) => e.response);

    expect(res.status).toBe(201);
    expect(res.data.data).toBeInstanceOf(Array);
    expect(res.data.data[0]).toHaveProperty('topic_id');
    expect(res.data.data[0]).toHaveProperty('name');
    expect(res.data.data[0].is_subscribed).toBe(false);
  });

  it('should return 409 error if user does not exist', async () => {
    const invalidUserId = 99999;

    const res = await axios
      .get(`${notificationsUrl}/${invalidUserId}/preferences`, {
        headers: {
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      })
      .catch((e) => e.response);


    expect(res.status).toBe(409);
    expect(res.data.message).toBe('User not found');
  });

  it('should return validation error for missing preferences array', async () => {
    const requestBody = {}; 

    const res = await axios
      .post(`${notificationsUrl}/${userId}/preferences`, requestBody, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      })
      .catch((e) => e.response);

    expect(res.status).toBe(400);
    expect(res.data.message).toContain('preferences should not be empty');
  });
});