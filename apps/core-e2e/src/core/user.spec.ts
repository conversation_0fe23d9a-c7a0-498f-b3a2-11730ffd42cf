import axios from 'axios';
import { faker } from '@faker-js/faker';
import FormData from 'form-data';
import {healThisToken} from '@core_be/global';

const user_id = '4';
const userUrl = 'http://127.0.0.1:3000/api/users';
const termsUrl = 'http://127.0.0.1:3000/api/terms';
const whiteListUrl = 'http://127.0.0.1:3000/api/whitelist/bulk-upload';
const healThisBearerToken = healThisToken();
const whitelistEmail = async (email: string) => {
  try {
    const csvContent = `email\n${email}`;
    const csvFile = Buffer.from(csvContent, 'utf-8');
    const formData = new FormData();
    formData.append('file', csvFile, { filename: 'emails.csv' });
    const response = await axios.post(whiteListUrl, formData, {
      headers: {
        ...formData.getHeaders(),
        Authorization: `Bearer ${healThisBearerToken}`,
        req: mockRequest as any,
      },
    });
    return await response.data;
  } catch (error: any) {
    throw new Error(`Failed to upload CSV file: ${error.message}`);
  }
};
const mockRequest: Request = {
  raw: {
    user: {
      user_id,
    },
  },
} as any;
describe('Users', () => {
  const userIdsToCleanup: any[] = [];

  afterEach(async () => {
    while (userIdsToCleanup.length > 0) {
      const id = userIdsToCleanup.pop();
      await axios
        .delete(`${userUrl}/${id}`, {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
          },
        })
        .catch((e) => e);
    }
  });

  describe('Create', () => {
    it('should create a user, profile and user roles by user_id ', async () => {
      const email = faker.internet.email().toLowerCase();
      await whitelistEmail(email);
      const data = {
        profile_type: faker.string.alpha(8),
        email,
        password: faker.string.alpha(8),
        first_name: faker.string.alpha(8),
        last_name: faker.string.alpha(8),
        gender: faker.string.alpha(8),
        relation_to_user: faker.string.alpha(8),
        date_of_birth: new Date('1990-05-15'),
        profile_picture_file_name: faker.string.alpha(8),
        phone_number: faker.string.alpha(8),
        country: faker.string.alpha(8),
        state: faker.string.alpha(8),
        city: faker.string.alpha(8),
        address: faker.string.alpha(8),
        zip_code: faker.string.alpha(8),
        last_login_date: new Date('2024-11-22T13:31:45.837Z'),
        password_reset_token: faker.string.alpha(8),
        password_reset_token_expiry: new Date('2024-11-25T13:31:45.837Z'),
      };
      const res = await axios
        .post(userUrl, data, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${healThisBearerToken}`,
            req: mockRequest as any,
          },
        })
        .catch((e) => e);
      userIdsToCleanup.push(res.data.data.user_id);

      expect(res.status).toBe(201);
      expect(res.data.data.email).toBe(email);
      expect(res.data.data.username).toBeDefined();
      expect(res.data.data.last_login_date).toBeDefined();
      expect(res.data.data.password_reset_token).toBeDefined();
      expect(res.data.data.password_reset_token_expiry).toBeDefined();
      expect(res.data.data.created_at).toBeDefined();
      expect(res.data.data.created_by).toBeDefined();
      expect(res.data.data.is_deleted).toBeDefined();
      expect(res.data.data.deleted_by).toBeDefined();
      expect(res.data.data.deleted_at).toBeDefined();
      expect(res.data.data.updated_by).toBeDefined();
      expect(res.data.data.updated_at).toBeDefined();
    });
  });

  describe('FindAll', () => {
    it('should find all users', async () => {
      const email = faker.internet.email().toLowerCase();
      await whitelistEmail(email);
      const data = JSON.stringify({
        profile_type: faker.string.alpha(8),
        email,
        password: faker.string.alpha(8),
        first_name: faker.string.alpha(8),
        last_name: faker.string.alpha(8),
        gender: faker.string.alpha(8),
        relation_to_user: faker.string.alpha(8),
        date_of_birth: new Date('1990-05-15'),
        profile_picture_file_name: faker.string.alpha(8),
        phone_number: faker.string.alpha(8),
        country: faker.string.alpha(8),
        state: faker.string.alpha(8),
        city: faker.string.alpha(8),
        address: faker.string.alpha(8),
        zip_code: faker.string.alpha(8),
        last_login_date: new Date('2024-11-22T13:31:45.837Z'),
        password_reset_token: faker.string.alpha(8),
        password_reset_token_expiry: new Date('2024-11-25T13:31:45.837Z'),
      });

      const postUser = await axios
        .post(userUrl, data, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${healThisBearerToken}`,
            req: mockRequest as any,
          },
        })
        .catch((e) => e);

      const res = await axios.get(
        `${userUrl}?orderBy=user_id&limit=10&order=asc`,
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${healThisBearerToken}`,
          },
        }
      );

      userIdsToCleanup.push(postUser.data.data.user_id);

      expect(res.status).toBe(200);
      expect(res.data.data.users.length).toBeGreaterThan(0);
    });
  });

  describe('Find', () => {
    it('should find a user by id', async () => {
      const email = faker.internet.email().toLowerCase();
      await whitelistEmail(email);
      const data = {
        profile_type: faker.string.alpha(8),
        email,
        password: faker.string.alpha(8),
        first_name: faker.string.alpha(8),
        last_name: faker.string.alpha(8),
        gender: faker.string.alpha(8),
        relation_to_user: faker.string.alpha(8),
        date_of_birth: new Date('1990-05-15'),
        profile_picture_file_name: faker.string.alpha(8),
        phone_number: faker.string.alpha(8),
        country: faker.string.alpha(8),
        state: faker.string.alpha(8),
        city: faker.string.alpha(8),
        address: faker.string.alpha(8),
        zip_code: faker.string.alpha(8),
        last_login_date: new Date('2024-11-22T13:31:45.837Z'),
        password_reset_token: faker.string.alpha(8),
        password_reset_token_expiry: new Date('2024-11-25T13:31:45.837Z'),
      };
      const res = await axios
        .post(userUrl, data, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${healThisBearerToken}`,
            req: mockRequest as any,
          },
        })
        .catch((e) => e);

      const response = await axios.get(`${userUrl}/${res.data.data.user_id}`, {
        headers: {
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      });

      userIdsToCleanup.push(res.data.data.user_id);

      expect(response.status).toBe(200);
      expect(response.data.data.email).toBeDefined();
      expect(response.data.data.username).toBeDefined();
      expect(response.data.data.last_login_date).toBeDefined();
      expect(response.data.data.password_reset_token).toBeDefined();
      expect(response.data.data.password_reset_token_expiry).toBeDefined();
      expect(response.data.data.created_at).toBeDefined();
      expect(response.data.data.created_by).toBeDefined();
      expect(response.data.data.is_deleted).toBeDefined();
      expect(response.data.data.deleted_by).toBeDefined();
      expect(response.data.data.deleted_at).toBeDefined();
      expect(response.data.data.updated_by).toBeDefined();
      expect(response.data.data.updated_at).toBeDefined();
    });
  });

  describe('Delete', () => {
    it('should delete a user', async () => {
      const email = faker.internet.email().toLowerCase();
      await whitelistEmail(email);
      const data = {
        profile_type: faker.string.alpha(8),
        email,
        password: faker.string.alpha(8),
        first_name: faker.string.alpha(8),
        last_name: faker.string.alpha(8),
        gender: faker.string.alpha(8),
        relation_to_user: faker.string.alpha(8),
        date_of_birth: new Date('1990-05-15'),
        profile_picture_file_name: faker.string.alpha(8),
        phone_number: faker.string.alpha(8),
        country: faker.string.alpha(8),
        state: faker.string.alpha(8),
        city: faker.string.alpha(8),
        address: faker.string.alpha(8),
        zip_code: faker.string.alpha(8),
        last_login_date: new Date('2024-11-22T13:31:45.837Z'),
        password_reset_token: faker.string.alpha(8),
        password_reset_token_expiry: new Date('2024-11-25T13:31:45.837Z'),
      };
      const res = await axios
        .post(userUrl, data, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${healThisBearerToken}`,
            req: mockRequest as any,
          },
        })
        .catch((e) => e);

      const response = await axios.delete(
        `${userUrl}/${res.data.data.user_id}`,
        {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
          },
        }
      );
      expect(response.status).toBe(200);
      expect(response.data.data.is_deleted).toBe(true);
      expect(response.data.data.deleted_by).toBeDefined();
      expect(response.data.data.deleted_at).toBeDefined();
    });
  });

  describe('Update Password', () => {
    it('should update password', async () => {
      const password = faker.string.alpha(10);
      const data = {
        password,
        confirmpassword: password,
      };
      const res = await axios
        .post(`${userUrl}/updatepassword`, data, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${healThisBearerToken}`,
            req: mockRequest as any,
          },
        })
        .catch((e) => e);
      expect(res.data.data.password_hash).toBeDefined();
    });
  });

  describe('Assign RoleID to UserID ', () => {
    it('should assign role_id to user_id', async () => {
      const email = faker.internet.email().toLowerCase();
      await whitelistEmail(email);
      const data = {
        profile_type: faker.string.alpha(8),
        email,
        password: faker.string.alpha(8),
        first_name: faker.string.alpha(8),
        last_name: faker.string.alpha(8),
        gender: faker.string.alpha(8),
        relation_to_user: faker.string.alpha(8),
        date_of_birth: new Date('1990-05-15'),
        profile_picture_file_name: faker.string.alpha(8),
        phone_number: faker.string.alpha(8),
        country: faker.string.alpha(8),
        state: faker.string.alpha(8),
        city: faker.string.alpha(8),
        address: faker.string.alpha(8),
        zip_code: faker.string.alpha(8),
        last_login_date: new Date('2024-11-22T13:31:45.837Z'),
        password_reset_token: faker.string.alpha(8),
        password_reset_token_expiry: new Date('2024-11-25T13:31:45.837Z'),
      };
      const res = await axios
        .post(userUrl, data, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${healThisBearerToken}`,
            req: mockRequest as any,
          },
        })
        .catch((e) => e);

      const addRoleID = {
        user_id: res.data.data.user_id,
        role_id: faker.number.int(3),
      };

      const updateResponse = await axios
        .post(`${userUrl}/user-role`, addRoleID, {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
          },
        })
        .catch((e) => e);
      expect(updateResponse.data.status_code).toBe(201);
      expect(updateResponse.data.success).toBe(true);
      expect(updateResponse.data.message).toBe('Success');
    });
  });

  describe('FindAll', () => {
    it('should find all user roles', async () => {
      const email = faker.internet.email().toLowerCase();
      await whitelistEmail(email);
      const data = {
        profile_type: faker.string.alpha(8),
        email,
        password: faker.string.alpha(8),
        first_name: faker.string.alpha(8),
        last_name: faker.string.alpha(8),
        gender: faker.string.alpha(8),
        relation_to_user: faker.string.alpha(8),
        date_of_birth: new Date('1990-05-15'),
        profile_picture_file_name: faker.string.alpha(8),
        phone_number: faker.string.alpha(8),
        country: faker.string.alpha(8),
        state: faker.string.alpha(8),
        city: faker.string.alpha(8),
        address: faker.string.alpha(8),
        zip_code: faker.string.alpha(8),
        last_login_date: new Date('2024-11-22T13:31:45.837Z'),
        password_reset_token: faker.string.alpha(8),
        password_reset_token_expiry: new Date('2024-11-25T13:31:45.837Z'),
      };
      const res = await axios
        .post(userUrl, data, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${healThisBearerToken}`,
            req: mockRequest as any,
          },
        })
        .catch((e) => e);

      const addRoleID1 = {
        user_id: res.data.data.user_id,
        role_id: faker.number.int(2),
      };

      const addRoleID2 = {
        user_id: res.data.data.user_id,
        role_id: faker.number.int(3),
      };

      await axios
        .post(`${userUrl}/user-role`, addRoleID1, {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
          },
        })
        .catch((e) => e);

      await axios
        .post(`${userUrl}/user-role`, addRoleID2, {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
          },
        })
        .catch((e) => e);

      const getResponse = await axios
        .get(`${userUrl}/user-role`, {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
          },
        })
        .catch((e) => e);

      userIdsToCleanup.push(res.data.data.user_id);

      expect(getResponse.data.status_code).toBe(200);
      expect(getResponse.data.success).toBe(true);
      expect(getResponse.data.data.length).toBeGreaterThan(1);
    });
  });

  describe('Find', () => {
    it('should find user role by user role id', async () => {
      const email = faker.internet.email().toLowerCase();
      await whitelistEmail(email);
      const data = {
        profile_type: faker.string.alpha(8),
        email,
        password: faker.string.alpha(8),
        first_name: faker.string.alpha(8),
        last_name: faker.string.alpha(8),
        gender: faker.string.alpha(8),
        relation_to_user: faker.string.alpha(8),
        date_of_birth: new Date('1990-05-15'),
        profile_picture_file_name: faker.string.alpha(8),
        phone_number: faker.string.alpha(8),
        country: faker.string.alpha(8),
        state: faker.string.alpha(8),
        city: faker.string.alpha(8),
        address: faker.string.alpha(8),
        zip_code: faker.string.alpha(8),
        last_login_date: new Date('2024-11-22T13:31:45.837Z'),
        password_reset_token: faker.string.alpha(8),
        password_reset_token_expiry: new Date('2024-11-25T13:31:45.837Z'),
      };
      const res = await axios
        .post(userUrl, data, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${healThisBearerToken}`,
            req: mockRequest as any,
          },
        })
        .catch((e) => e);

      const addRoleID = {
        user_id: res.data.data.user_id,
        role_id: faker.number.int(2),
      };

      const response = await axios
        .post(`${userUrl}/user-role`, addRoleID, {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
          },
        })
        .catch((e) => e);

      const getResponse = await axios
        .get(`${userUrl}/user-role/${response.data.data.user_role_id}`, {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
          },
        })
        .catch((e) => e);

      userIdsToCleanup.push(res.data.data.user_id);

      expect(getResponse.data.status_code).toBe(200);
      expect(getResponse.data.success).toBe(true);
    });
  });

  describe('Update', () => {
    it('should update user_id and role_id record against user_role_id', async () => {
      const email = faker.internet.email().toLowerCase();
      await whitelistEmail(email);
      const data = {
        profile_type: faker.string.alpha(8),
        email,
        password: faker.string.alpha(8),
        first_name: faker.string.alpha(8),
        last_name: faker.string.alpha(8),
        gender: faker.string.alpha(8),
        relation_to_user: faker.string.alpha(8),
        date_of_birth: new Date('1990-05-15'),
        profile_picture_file_name: faker.string.alpha(8),
        phone_number: faker.string.alpha(8),
        country: faker.string.alpha(8),
        state: faker.string.alpha(8),
        city: faker.string.alpha(8),
        address: faker.string.alpha(8),
        zip_code: faker.string.alpha(8),
        last_login_date: new Date('2024-11-22T13:31:45.837Z'),
        password_reset_token: faker.string.alpha(8),
        password_reset_token_expiry: new Date('2024-11-25T13:31:45.837Z'),
      };
      const res = await axios
        .post(userUrl, data, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${healThisBearerToken}`,
            req: mockRequest as any,
          },
        })
        .catch((e) => e);

      const addRoleID = {
        user_id: res.data.data.user_id,
        role_id: 3,
      };

      const response = await axios
        .post(`${userUrl}/user-role`, addRoleID, {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
          },
        })
        .catch((e) => e);

      const updateRoleID = {
        user_id: res.data.data.user_id,
        role_id: 3,
      };

      const updateResponse = await axios.patch(
        `${userUrl}/user-role/${response.data.data.user_role_id}`,
        updateRoleID,
        {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
          },
        }
      );

      userIdsToCleanup.push(res.data.data.user_id);

      expect(updateResponse.data.status_code).toBe(200);
      expect(updateResponse.data.success).toBe(true);
    });
  });

  describe('Archive', () => {
    it('should delete user_role_id record', async () => {
      const email = faker.internet.email().toLowerCase();
      await whitelistEmail(email);
      const data = {
        profile_type: faker.string.alpha(8),
        email,
        password: faker.string.alpha(8),
        first_name: faker.string.alpha(8),
        last_name: faker.string.alpha(8),
        gender: faker.string.alpha(8),
        relation_to_user: faker.string.alpha(8),
        date_of_birth: new Date('1990-05-15'),
        profile_picture_file_name: faker.string.alpha(8),
        phone_number: faker.string.alpha(8),
        country: faker.string.alpha(8),
        state: faker.string.alpha(8),
        city: faker.string.alpha(8),
        address: faker.string.alpha(8),
        zip_code: faker.string.alpha(8),
        last_login_date: new Date('2024-11-22T13:31:45.837Z'),
        password_reset_token: faker.string.alpha(8),
        password_reset_token_expiry: new Date('2024-11-25T13:31:45.837Z'),
      };
      const res = await axios
        .post(userUrl, data, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${healThisBearerToken}`,
            req: mockRequest as any,
          },
        })
        .catch((e) => e);

      const addRoleID = {
        user_id: res.data.data.user_id,
        role_id: faker.number.int(2),
      };

      const response = await axios
        .post(`${userUrl}/user-role`, addRoleID, {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
          },
        })
        .catch((e) => e);
      const updateResponse = await axios.delete(
        `${userUrl}/user-role/${response.data.data.user_role_id}`,
        {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
          },
        }
      );
      userIdsToCleanup.push(res.data.data.user_id);

      expect(updateResponse.data.status_code).toBe(200);
      expect(updateResponse.data.success).toBe(true);
    });
  });

  describe('Create', () => {
    it('should create user device ', async () => {
      const data = {
        user_id: faker.number.int({ min: 1, max: 110 }),
        device_id: faker.string.uuid(),
        device_type: faker.helpers.arrayElement(['Android', 'iOS']),
        fcm_token: faker.string.alphanumeric(120),
        device_model: faker.helpers.arrayElement([
          'Oppo',
          'Samsung',
          'iPhone',
          'Redmi',
        ]),
        os_name: faker.helpers.arrayElement([
          'Kitkat',
          'Lollipop',
          'Marshmallow',
          'iOS',
        ]),
        os_version: faker.system.semver(),
        app_version: faker.system.semver(),
      };
      const res = await axios
        .post(`${userUrl}/user-device`, data, {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
            req: mockRequest as any,
          },
        })
        .catch((e) => e);

      expect(res.data.status_code).toBe(201);
      expect(res.data.data.user_device_id).toBeDefined();
      expect(res.data.data.user_id).toBeDefined();
      expect(res.data.data.device_type).toBeDefined();
      expect(res.data.data.device_id).toBeDefined();
      expect(res.data.data.device_model).toBeDefined();
      expect(res.data.data.os_name).toBeDefined();
      expect(res.data.data.os_version).toBeDefined();
      expect(res.data.data.app_version).toBeDefined();
      expect(res.data.data.fcm_token).toBeDefined();
      expect(res.data.data.created_by).toBeDefined();
      expect(res.data.data.created_at).toBeDefined();
      expect(res.data.data.updated_by).toBeDefined();
      expect(res.data.data.updated_at).toBeDefined();
      expect(res.data.data.deleted_by).toBeDefined();
      expect(res.data.data.deleted_at).toBeDefined();
      expect(res.data.data.is_deleted).toBeDefined();
    });
  });

  describe('Update', () => {
    it('should update user device ', async () => {
      const data = {
        user_id: faker.number.int({ min: 1, max: 120 }),
        device_id: faker.string.uuid(),
        device_type: faker.helpers.arrayElement(['Android', 'iOS']),
        fcm_token: faker.string.alphanumeric(120),
        device_model: faker.helpers.arrayElement([
          'Oppo',
          'Samsung',
          'iPhone',
          'Redmi',
        ]),
        os_name: faker.helpers.arrayElement([
          'Kitkat',
          'Lollipop',
          'Marshmallow',
          'iOS',
        ]),
        os_version: faker.system.semver(),
        app_version: faker.system.semver(),
      };
      const res = await axios
        .post(`${userUrl}/user-device`, data, {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
            req: mockRequest as any,
          },
        })
        .catch((e) => e);

      const updateQuery = {
        user_id: data.user_id,
        device_id: faker.string.uuid(),
        device_type: faker.helpers.arrayElement(['Android', 'iOS']),
        fcm_token: faker.string.alphanumeric(120),
        device_model: faker.helpers.arrayElement([
          'Oppo',
          'Samsung',
          'iPhone',
          'Redmi',
        ]),
        os_name: faker.helpers.arrayElement([
          'Kitkat',
          'Lollipop',
          'Marshmallow',
          'iOS',
        ]),
        os_version: faker.system.semver(),
        app_version: faker.system.semver(),
      };
      const updateResponse = await axios.patch(
        `${userUrl}/user-device/${res.data.data.user_device_id}`,
        updateQuery,
        {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
            req: mockRequest as any,
          },
        }
      );

      expect(updateResponse.data.status_code).toBe(200);
      expect(updateResponse.data.data.user_device_id).toBeDefined();
      expect(updateResponse.data.data.user_id).toBeDefined();
      expect(updateResponse.data.data.device_type).toBeDefined();
      expect(updateResponse.data.data.device_id).toBeDefined();
      expect(updateResponse.data.data.device_model).toBeDefined();
      expect(updateResponse.data.data.os_name).toBeDefined();
      expect(updateResponse.data.data.os_version).toBeDefined();
      expect(updateResponse.data.data.app_version).toBeDefined();
      expect(updateResponse.data.data.fcm_token).toBeDefined();
      expect(updateResponse.data.data.created_by).toBeDefined();
      expect(updateResponse.data.data.created_at).toBeDefined();
      expect(updateResponse.data.data.updated_by).toBeDefined();
      expect(updateResponse.data.data.updated_at).toBeDefined();
      expect(updateResponse.data.data.deleted_by).toBeDefined();
      expect(updateResponse.data.data.deleted_at).toBeDefined();
      expect(updateResponse.data.data.is_deleted).toBeDefined();
    });
  });

  describe('Create User Terms', () => {
    it('should should save user terms ', async () => {
      const terms = {
        title: faker.string.alpha(8),
        content: faker.string.alpha(8),
      };
      const termsRecord = await axios
        .post(`${termsUrl}`, terms, {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
            req: mockRequest as any,
          },
        })
        .catch((e) => e);

      const data = {
        user_id: faker.number.int({ min: 1, max: 300 }),
        terms_id: termsRecord.data.data.terms_id,
      };

      const res = await axios
        .post(`${userUrl}/save-user-terms`, data, {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
          },
        })
        .catch((e) => e);

      userIdsToCleanup.push(res.data.data.user_terms_agreement_id);

      expect(res.status).toBe(201);
      expect(res.data.data.user_id).toBeDefined();
      expect(res.data.data.terms_id).toBeDefined();
      expect(res.data.data.is_terms_accepted).toBeDefined();
    });
  });

  describe('Find', () => {
    it('should find terms by user id', async () => {
      const terms = {
        title: faker.string.alpha(8),
        content: faker.string.alpha(8),
      };
      const termsRecord = await axios
        .post(`${termsUrl}`, terms, {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
            req: mockRequest as any,
          },
        })
        .catch((e) => e);

      const data = {
        user_id: faker.number.int({ min: 1, max: 300 }),
        terms_id: termsRecord.data.data.terms_id,
      };

      const res = await axios
        .post(`${userUrl}/save-user-terms`, data, {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
          },
        })
        .catch((e) => e);

      await axios
        .get(`${userUrl}/terms/${res.data.data.user_id}`, {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
          },
        })
        .catch((e) => e);

      userIdsToCleanup.push(res.data.data.user_id);

      expect(res.data.status_code).toBe(201);
      expect(res.data.success).toBe(true);
    });
  });

  describe('Create', () => {
    it('should save user privacy policy ', async () => {
      const email = faker.internet.email().toLowerCase();
      await whitelistEmail(email);
      const data = {
        profile_type: faker.string.alpha(8),
        email,
        password: faker.string.alpha(8),
        first_name: faker.string.alpha(8),
        last_name: faker.string.alpha(8),
        gender: faker.string.alpha(8),
        relation_to_user: faker.string.alpha(8),
        date_of_birth: new Date('1990-05-15'),
        profile_picture_file_name: faker.string.alpha(8),
        phone_number: faker.string.alpha(8),
        country: faker.string.alpha(8),
        state: faker.string.alpha(8),
        city: faker.string.alpha(8),
        address: faker.string.alpha(8),
        zip_code: faker.string.alpha(8),
        last_login_date: new Date('2024-11-22T13:31:45.837Z'),
        password_reset_token: faker.string.alpha(8),
        password_reset_token_expiry: new Date('2024-11-25T13:31:45.837Z'),
      };
      const res = await axios
        .post(userUrl, data, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${healThisBearerToken}`,
            req: mockRequest as any,
          },
        })
        .catch((e) => e);

      const policyData = {
        user_id: res.data.data.user_id,
        privacy_policy_id: faker.number.int({ min: 1, max: 200 }),
      };

      userIdsToCleanup.push(res.data.data.user_id);
      const response = await axios
        .post(`${userUrl}/save-user-privacy-policy`, policyData, {
          headers: {
            Authorization: `Bearer ${healThisBearerToken}`,
          },
        })
        .catch((e) => e);
      expect(response.status).toBe(201);
    });
  });

  describe('Users Validation Errors', () => {
    it('should return validation errors for invalid inputs', async () => {
      const data = {
        email: 'invalid-email',
        password: '123', 
        profile_type: '', 
        gender: 'Unknown',
        date_of_birth: 'not-a-date', 
        country: '',
        relation_to_user:''
      }

      const response = await axios.post(`${userUrl}`, data, {headers: 
        {
          'Content-Type': 'application/json',
            Authorization: `Bearer ${healThisBearerToken}`,
        },
      }).catch((e) => e.response);
      expect(response.status).toBe(400);
      expect(response.data.message).toContain('email must be an email');
      expect(response.data.message).toContain('password must be longer than or equal to 8 characters');
      expect(response.data.message).toContain('profile_type should not be empty');
      expect(response.data.message).toContain('country should not be empty');
    });

    it('should return an error when email is not whitelisted', async () => {

      const data = {
        email: '<EMAIL>',
        password: 'StrongPassword123!',
        profile_type: 'Healer',
        first_name: 'John',
        gender: 'Male',
        date_of_birth: '1990-01-01',
        country: 'India',
      };

      const response = await axios.post(`${userUrl}`, data, {headers: 
        {
          'Content-Type': 'application/json',
            Authorization: `Bearer ${healThisBearerToken}`,
        },
      }).catch((e) => e.response);
    
      expect(response.status).toBe(201);
      expect(response.data.message).toBe('Email <EMAIL> is not registered in the whitelist.');
    });

    it('should return an error when email already exists', async () => {

      const data = {
        email: '<EMAIL>',
        password: 'StrongPassword123!',
        profile_type: 'Healer',
        first_name: 'John',
        gender: 'Male',
        date_of_birth: '1990-01-01',
        country: 'India',
      };

      const response = await axios.post(`${userUrl}`, data, {headers: 
        {
          'Content-Type': 'application/json',
            Authorization: `Bearer ${healThisBearerToken}`,
        },
      }).catch((e) => e.response);
      
      expect(response.status).toBe(409);
      expect(response.data.message).toBe('Email address already exists');
    });

    it('should return an error when password is missing', async () => {

      const data = {
        email: '<EMAIL>',
        profile_type: 'Healer',
        first_name: 'John',
        gender: 'Male',
        date_of_birth: '1990-01-01',
        country: 'India',
      };

      const response = await axios.post(`${userUrl}`, data, {headers: 
        {
          'Content-Type': 'application/json',
            Authorization: `Bearer ${healThisBearerToken}`,
        },
      }).catch((e) => e.response);
    
      expect(response.status).toBe(400);
      expect(response.data.message).toContain('password should not be empty');
    });
  });

  describe('Users Role Validation Errors', () => {
    it('should return validation errors for invalid user role input', async () => {
      const data = {
        user_id: 'not-a-number', 
        role_id: '',
      }

      const response = await axios.post(`${userUrl}/user-role`, data, {headers: 
        {
          'Content-Type': 'application/json',
            Authorization: `Bearer ${healThisBearerToken}`,
        },
      }).catch((e) => e.response);
      expect(response.status).toBe(400);
      expect(response.data.message).toContain('user_id must be an integer number');
      expect(response.data.message).toContain('role_id must be an integer number');
      expect(response.data.message).toContain('role_id should not be empty');
    });
  });

  describe('Users Device Validation Errors', () => {
    it('should return validation errors for invalid user device input', async () => {
      const data = {
        user_id: 'invalid-id', 
        device_id: 12345, 
        device_type: '',
        fcm_token: '',
        device_model: '',
        os_name: '',
        os_version: '',
        app_version: '',
      }

      const response = await axios.post(`${userUrl}/user-device`, data, {headers: 
        {
          'Content-Type': 'application/json',
            Authorization: `Bearer ${healThisBearerToken}`,
        },
      }).catch((e) => e.response);
      expect(response.status).toBe(400);
      expect(response.data.message).toContain('user_id must be an integer number');
      expect(response.data.message).toContain('device_id must be a string');
      expect(response.data.message).toContain('device_type should not be empty');
      expect(response.data.message).toContain('fcm_token should not be empty');
      expect(response.data.message).toContain('device_model should not be empty');
      expect(response.data.message).toContain('os_name should not be empty');
      expect(response.data.message).toContain('os_version should not be empty');
      expect(response.data.message).toContain('app_version should not be empty');
    });
  });
});
