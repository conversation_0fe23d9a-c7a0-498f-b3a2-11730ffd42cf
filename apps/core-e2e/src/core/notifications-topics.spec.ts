import axios from 'axios';
import { healThisToken } from '@core_be/global';
import { faker } from '@faker-js/faker';

const notificationsUrl = 'http://127.0.0.1:3000/api/notifications-topics';
const user_id = 1;
const healThisBearerToken = healThisToken();

describe('NotificationsTopics (e2e) with Axios', () => {
  it('should create a notification topic successfully', async () => {
    const requestBody = {
      name: faker.lorem.sentence(2),
      description: faker.lorem.paragraph(),
    };

    const res = await axios
      .post(`${notificationsUrl}`, requestBody, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      })
      .catch((e) => e.response);
    
    expect(res.status).toBe(201);
    expect(res.data.data.name).toBe(requestBody.name);
  });

  it('should return a validation error for missing name field', async () => {
    const requestBody = {
      description: faker.lorem.paragraph(),
    };

    const res = await axios
      .post(`${notificationsUrl}`, requestBody, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      })
      .catch((e) => e.response);

    expect(res.status).toBe(400);
    expect(res.data.message).toContain('name should not be empty');
  });

  it('should return a validation error for incorrect data types', async () => {
    const requestBody = {
      name: 123, 
      description: 456, 
    };

    const res = await axios
      .post(`${notificationsUrl}`, requestBody, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      })
      .catch((e) => e.response);

    expect(res.status).toBe(400);
    expect(res.data.message).toContain('name must be a string');
    expect(res.data.message).toContain('description must be a string');
  });

  it('should fetch all notification topics', async () => {
    const res = await axios.get(`${notificationsUrl}`, {
      headers: {
        Authorization: `Bearer ${healThisBearerToken}`,
      },
    });

    expect(res.status).toBe(200);
    expect(Array.isArray(res.data.data.notificationsTopics)).toBe(true);
  });

  it('should return 409 for non-existent topic', async () => {
    const res = await axios
      .get(`${notificationsUrl}/999`, {
        headers: {
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      })
      .catch((e) => e.response);

    expect(res.status).toBe(409);
    expect(res.data.message).toBe('Notifications Topics Id Not Found');
  });

  it('should update a notification topic successfully', async () => {
    const topicId = '1';
    const requestBody = {
      description: faker.lorem.paragraph(),
    };
    const resget = await axios
      .get(`${notificationsUrl}/${topicId}`, {
        headers: {
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      })
      .catch((e) => e.response);
    if(resget.status === 200) {
      const res = await axios
        .put(`${notificationsUrl}/${topicId}`, requestBody, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${healThisBearerToken}`,
          },
        })
        .catch((e) => e.response);
      
      expect(res.status).toBe(201);
      expect(res.data.data.description).toBe(requestBody.description);
    } else {
      expect(resget.status).toBe(409);
      expect(resget.data.message).toBe('Notifications Topics Id Not Found');
    }
  });

  it('should delete a notification topic successfully', async () => {
    const res = await axios
      .delete(`${notificationsUrl}/3`, {
        headers: {
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      })
      .catch((e) => e.response);

    expect(res.status).toBe(200);
    expect(res.data.data.is_deleted).toBe(true);
  });
});
