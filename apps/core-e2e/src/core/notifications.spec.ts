import axios from 'axios';
import {healThisToken} from '@core_be/global';
import { faker } from '@faker-js/faker';
import { NotificationStatus } from '@core/libs';

const notificationsUrl = 'http://127.0.0.1:3000/api/notifications';
const user_id = 1;
const healThisBearerToken = healThisToken();
const mockRequest: Request = {
  raw: {
    user: {
      user_id,
    },
  },
} as any;

describe('Notifications ', () => {
  let createdNotification;
  describe('Notifications (e2e) with Axios', () => {
    it('should send a notification by tokens and return the response', async () => {
      const requestBody = {
        tokens: ['Invalid_Token'],
        notification: {
          title: 'Test Message',
          body: 'This is a test notification',
        },
      };

      const res = await axios
        .post(`${notificationsUrl}/send-by-tokens`, requestBody, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${healThisBearerToken}`,
            req: mockRequest as any,
          },
        })
        .catch((e) => e);

      expect(res.status).toBe(201);
      expect(res.data.success).toBe(true);
      expect(res.data.data.responses[0].success).toBe(false);
      expect(res.data.data.responses[0].error.code).toBe(
        'messaging/invalid-argument'
      );
    });

    it('should send a notification by topic and return the response', async () => {
      const requestBody = {
        topic: 'test-topic',
        data: {
          notification: {
            title: 'Topic Notification',
            body: 'Topic body',
          },
        },
      };

      const res = await axios
        .post(`${notificationsUrl}/send-by-topic`, requestBody, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${healThisBearerToken}`,
            req: mockRequest as any,
          },
        })
        .catch((e) => e);

      expect(res.status).toBe(201);
      expect(res.data.success).toBe(true);
      expect(res.data.data).toBeDefined();
      expect(typeof res.data.data).toBe('string');
    });

    it('should fail when required fields are missing', async () => {
      const requestBody = {};
  
      const res = await axios
        .post(`${notificationsUrl}`, requestBody, { headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
          req: mockRequest as any,
        }, 
      }).catch((e) => e.response);
  
      expect(res.status).toBe(400);
      expect(res.data.message).toContain('title should not be empty');
      expect(res.data.message).toContain('body should not be empty');
      expect(res.data.message).toContain('status must be a valid enum value');
    });

    it('should fail with invalid data types', async () => {
      const requestBody = {
        title: 123,
        body: true,
        topic_id: 'invalid_id',
        user_ids: 'invalid_array',
        status: 'INVALID_STATUS',
      };
  
      const res = await axios
        .post(`${notificationsUrl}`, requestBody, { headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
          req: mockRequest as any,
        },
      }).catch((e) => e.response);
  
      expect(res.status).toBe(400);
      expect(res.data.message).toContain('title must be a string');
      expect(res.data.message).toContain('body must be a string');
      expect(res.data.message).toContain('topic_id must be an integer number');
      expect(res.data.message).toContain('user_ids must be an array');
      expect(res.data.message).toContain('status must be a valid enum value');
    });

    it('should fail if scheduled date is in the past', async () => {
      const requestBody = {
        title: faker.lorem.sentence(),
        body: faker.lorem.paragraph(),
        topic_id: 1,
        user_ids: [2,3],
        status: NotificationStatus.SCHEDULED,
        schedule_date_time: new Date(Date.now() - 86400000).toISOString()
      };
  
      const res = await axios
        .post(`${notificationsUrl}`, requestBody, { headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
          req: mockRequest as any,
        },
      }).catch((e) => e.response);
  
      expect(res.status).toBe(409);
      expect(res.data.message).toBe('Scheduled date must be a valid future date.');
    });

    it('should fail if topic_id or user_ids must be provided', async () => {
      const requestBody = {
        title: faker.lorem.sentence(),
        body: faker.lorem.paragraph(),
        status: NotificationStatus.DRAFT,
      };
  
      const res = await axios
        .post(`${notificationsUrl}`, requestBody, { headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
          req: mockRequest as any,
        },
      }).catch((e) => e.response);
  
      expect(res.status).toBe(409);
      expect(res.data.message).toBe('At least one of topic_id or user_ids must be provided.');
    });
  
    it('should create a notification successfully', async () => {
      const requestBody = {
        title: faker.lorem.sentence(),
        body: faker.lorem.paragraph(),
        topic_id:10,
        user_ids: [2,3],
        status: NotificationStatus.DRAFT,
      };
  
      const res = await axios.post(`${notificationsUrl}`, requestBody, { headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
          req: mockRequest as any,
        },
     }).catch((e) => e.response);
      if(res.status === 201) {
        expect(res.status).toBe(201);
        expect(res.data.data).toHaveProperty('notification_id');
        expect(res.data.data.title).toBe(requestBody.title);
        createdNotification = res.data.data;
      } else if(res.status === 409) {
        const validMessages = ['Invalid topic_id.', 'One or more user_ids are invalid.'];
        expect(res.status).toBe(409);
        expect(validMessages).toContain(res.data.message);
      } 
      
    });

    it('should get all notifications', async () => {
      const res = await axios.get(`${notificationsUrl}`, { headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${healThisBearerToken}`,
        req: mockRequest as any,
      },
     }).catch((e) => e.response);
  
      expect(res.status).toBe(200);
      expect(Array.isArray(res.data.data.users)).toBe(true);
    });
  
    it('should update a notification successfully', async () => {
      const updateData = {
        title: 'Updated Title',
        body: 'Updated Body',
        user_ids: [2,3],
        status: NotificationStatus.READY_TO_SEND,
      };
  
      const res = await axios.patch(`${notificationsUrl}/${createdNotification.notification_id}`, updateData, { headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${healThisBearerToken}`,
        req: mockRequest as any,
      },
      }).catch((e) => e.response);
      if(res.status === 200) {
        expect(res.status).toBe(200);
        expect(res.data.data.title).toBe(updateData.title);
        expect(res.data.data.body).toBe(updateData.body);
      } else {
        expect(res.status).toBe(404);
        expect(res.data.message).toBe('Notification not found.');
      }
      
    });
  
    it('should duplicate a notification', async () => {
      const duplicateData = { include_target: true };
  
      const res = await axios.post(`${notificationsUrl}/${createdNotification.notification_id}/duplicate`, duplicateData, { headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
          req: mockRequest as any,
        },
      }).catch((e) => e.response);
      
      if(res.status === 201) {
        expect(res.status).toBe(201);
        expect(res.data.data.notification_id).not.toBe(createdNotification.notification_id);
        expect(res.data.data.status).toBe(NotificationStatus.DRAFT);
      } else {
        expect(res.status).toBe(404);
        expect(res.data.message).toBe('Notification not found.');
      }
      
    });

    it('should delete a notification', async () => {
      const res = await axios.delete(`${notificationsUrl}/${createdNotification.notification_id}`, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${healThisBearerToken}`,
        },
      }).catch((e) => e.response);
      
      if(res.status === 200) {
        expect(res.status).toBe(200);
      } else {
        expect(res.status).toBe(404);
        expect(res.data.message).toBe('Notification not found.');
      }
    });
  });
});
