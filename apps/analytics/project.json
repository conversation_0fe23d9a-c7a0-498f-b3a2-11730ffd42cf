{"name": "analytics", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/analytics/src", "projectType": "application", "tags": [], "targets": {"serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "options": {"buildTarget": "analytics:build"}, "configurations": {"development": {"buildTarget": "analytics:build:development"}, "production": {"buildTarget": "analytics:build:production"}}}}}