import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsDate } from '@nestjs/class-validator';
import { IsInt, IsJSON, IsNumber } from 'class-validator';

export class AnalyticData {
  @ApiProperty({
    example: 'State',
    description: 'app state',
    required: true,
  })
  @IsString()
  app_state: string;

  @ApiProperty({
    example: '1.0.1',
    description: 'app version',
    required: true,
  })
  @IsString()
  app_version: string;

  @ApiProperty({
    example: 'navigation',
    description: 'click item',
    required: true,
  })
  @IsString()
  click_item: string;

  @ApiProperty({
    example: 'Healer section',
    description: 'destination page name',
    required: true,
  })
  @IsString()
  destination_page_name: string;

  @ApiProperty({
    example: 'USA',
    description: 'country name',
    required: true,
  })
  @IsString()
  device_country: string;

  @ApiProperty({
    example: '12345',
    description: 'Device Id',
    required: true,
  })
  @IsString()
  device_id: string;

  @ApiProperty({
    example: 'MODEL 12345',
    description: 'Device Name',
    required: true,
  })
  @IsString()
  device_name: string;

  @ApiProperty({
    example: 'iOS',
    description: 'device os',
    required: true,
  })
  @IsString()
  device_os: string;

  @ApiProperty({
    example: 37.4219983,
    description: 'latitude',
    required: true,
  })
  @IsNumber()
  latitude: number;

  @ApiProperty({
    example: -122.084,
    description: 'longitude',
    required: true,
  })
  @IsNumber()
  longitude: number;

  @ApiProperty({
    example: 'healer page',
    description: 'event name',
    required: true,
  })
  @IsString()
  event: string;

  @ApiProperty({
    example: '1.0.1',
    description: 'event details schema version',
    required: true,
  })
  @IsString()
  event_details_schema_version: string;

  @ApiProperty({
    example: {
      is_emergency: 'true or false',
      symptoms: 'true or false',
      mode_of_conversation: 'Audio or Text',
      number_of_msg: '',
    },
    description: 'session info details',
    required: true,
  })
  @IsJSON()
  session_info: string;

  @ApiProperty({
    example: '123',
    description: 'event id',
    required: true,
  })
  @IsString()
  event_id: string;

  @ApiProperty({
    example: 'Healers are available',
    description: 'notification body',
    required: true,
  })
  @IsString()
  notification_body: string;

  @ApiProperty({
    example: 'Event Title',
    description: 'Event title',
    required: true,
  })
  @IsString()
  notification_title: string;

  @ApiProperty({
    example: 'Alert',
    description: 'notification topic',
    required: true,
  })
  @IsString()
  notification_topic: string;

  @ApiProperty({
    example: 'Platform',
    description: 'Platform Name',
    required: true,
  })
  @IsString()
  platform: string;

  @ApiProperty({
    example: 1,
    description: 'Profile Id',
    required: true,
  })
  @IsNumber()
  profile_id: number;

  @ApiProperty({
    example: 'Patient',
    description: 'Profile Type',
    required: true,
  })
  @IsString()
  profile_type: string;

  @ApiProperty({
    example: 12345,
    description: 'time in milliseconds',
    required: true,
  })
  @IsNumber()
  session_duration: number;

  @ApiProperty({
    example: '2024-09-25 15:14:58.095 -0700',
    description: 'session end time',
    required: true,
  })
  @IsString()
  session_end: string;

  @ApiProperty({
    example: '2024-09-25 15:14:58.095 -0700',
    description: 'session start time',
    required: true,
  })
  @IsString()
  session_start: string;

  @ApiProperty({
    example: 'Source Page Name',
    description: 'Source Page Name',
    required: true,
  })
  @IsString()
  source_page_name: string;

  @ApiProperty({
    example: **********,
    description: 'time in epoch',
    required: true,
  })
  @IsNumber()
  timestamp: number;

  @ApiProperty({
    example: 1,
    description: 'user id',
    required: true,
  })
  @IsNumber()
  user_id: number;

  @ApiProperty({
    example: 'Version 1.0.1',
    description: 'version number',
    required: true,
  })
  @IsString()
  version: string;

  @ApiProperty({
    example: 'widget name',
    description: 'widget name',
    required: true,
  })
  @IsString()
  widgets: string;

  @ApiProperty({
    example: '28',
    description: 'Age of the user',
    required: true,
  })
  @IsString()
  age: string;

  @ApiProperty({
    example: 'Male',
    description: 'Gender of the user',
    required: true,
  })
  @IsString()
  gender: string;

  @ApiProperty({
    example: '1',
    description: 'Number of profiles linked',
    required: true,
  })
  @IsString()
  no_of_profile: string;

  @ApiProperty({
    example: 'Base Plan',
    description: 'Subscription plan name',
    required: true,
  })
  @IsString()
  subscription_plan: string;
}

export class CreateMetricsDto {
  @ApiProperty({
    type: AnalyticData, // Reference the class
    isArray: true, // Indicate it's an array
    description: 'Analytic Data',
    example: [
      {
        app_state: 'State',
        app_version: '1.0.1',
        click_item: 'navigation',
        destination_page_name: 'Healer section',
        device_country: 'USA',
        device_id: '12345',
        device_name: 'MODEL 12345',
        device_os: 'iOS',
        latitude: 37.4219983,
        longitude: -122.084,
        event: 'healer page',
        event_details_schema_version: '1.0.1',
        session_info: {
          is_emergency: 'true or false',
          symptoms: 'true or false',
          mode_of_conversation: 'Audio or Text',
          number_of_msg: '',
          session_id: '',
          session_status: 'INQUEUE',
          session_sub_status: '',
          queue_duration: '',
          queue_number: '',
          queue_end: '',
          queue_start: '',
          queue_status: '',
          availablity_response_time: '',
          summary: '',
          improvement: 'true or false',
          pain_level: '',
          healing_round_duration: '',
          healing_round_end: '',
          healing_round_pain_scale: '',
          healing_round_number: '',
          healing_round_start: '',
          healing_session_duration: '',
          healing_session_end: '',
          healing_session_start: '',
          reason: '',
          summary_duration: '',
          summary_end: '',
          summary_start: '',
          summary_status: '',
          abandoned_place: '',
          abandoned_by: 'Patient or Healer',
          no_of_feedback: '',
          app_crashed: '',
        },
        event_id: '123',
        notification_body: 'Healers are available',
        notification_title: 'Event Title',
        notification_topic: 'Alert',
        platform: 'Platform',
        profile_id: 1,
        profile_type: 'Patient',
        session_duration: 12345,
        session_end: '2024-09-25 15:14:58.095 -0700',
        session_start: '2024-09-25 15:14:58.095 -0700',
        source_page_name: 'Source Page Name',
        timestamp: **********,
        user_id: 1,
        version: 'Version 1.0.1',
        widgets: 'widget name',
        age: '28',
        gender: 'Male',
        no_of_profile: '1',
        subscription_plan: 'Base Plan',
      },
    ],
  })
  data: AnalyticData[];
}
