import { Controller, Get } from '@nestjs/common';
import { MetricsService } from '../service/metrics.service';
import { MessagePattern } from '@nestjs/microservices';
import { CreateMetricsDto } from '../dto/metric.dto';

@Controller()
export class MetricsController {
  constructor(private readonly metricsService: MetricsService) {}

  @MessagePattern('analytics_event')
  async analytics(data: CreateMetricsDto) {
    try {
      await this.metricsService.analytics(data);
      console.log('Metrics imported successfully.');
    } catch (e) {
      console.log('Metrics imported fail.', e);
    }
  }
}
