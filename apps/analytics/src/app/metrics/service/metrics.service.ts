import { Injectable } from '@nestjs/common';
import {
  MessagePattern,
  Ctx,
  Payload,
  RmqContext,
} from '@nestjs/microservices';
import { MetricsRepository } from '@core_be/data-access';
@Injectable()
export class MetricsService {
  constructor(private readonly metricsRepository: MetricsRepository) {}

  @MessagePattern('analytics_event')
  async analytics(data: any) {
    for (const metrics of data.data) {
      metrics.session_start = new Date(metrics.session_start);
      metrics.session_end = new Date(metrics.session_end);
      await this.metricsRepository.create(metrics);
    }
  }
}
