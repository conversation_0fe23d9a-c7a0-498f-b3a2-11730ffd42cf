import { Module } from '@nestjs/common';
import { MetricsController } from './controller/metrics.controller';
import { MetricsService } from './service/metrics.service';
import { MetricsRepository } from '@core/libs';
import { PrismaService } from '@core/prisma-client';
@Module({
  imports: [],
  controllers: [MetricsController],
  providers: [MetricsService, MetricsRepository, PrismaService],
})
export class MetricsModule {}
