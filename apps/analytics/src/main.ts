/**
 * This is not a production server yet!
 * This is only a minimal backend to get started.
 */

import { Logger, LogLevel } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { ConfigService } from '@nestjs/config';
import { Transport } from '@nestjs/microservices';

import { AppModule } from './app/app.module';

// Get log levels from environment
const getLogLevels = (): LogLevel[] => {
  const logLevel = process.env.LOG_LEVEL || 'error,warn,log';
  return logLevel.split(',').map((level) => level.trim()) as LogLevel[];
};

async function bootstrap() {
  const configService = new ConfigService();

  const rabbitMQUrl = configService.get<string>(
    'RABBITMQ_URL',
    'amqp://localhost:5672'
  );
  const rabbitMQQueue = configService.get<string>(
    'RABBITMQ_QUEUE',
    'analytics_queue'
  );

  const app = await NestFactory.createMicroservice(AppModule, {
    transport: Transport.RMQ,
    options: {
      urls: [rabbitMQUrl],
      queue: rabbitMQQueue,
      queueOptions: {
        durable: true,
      },
    },
    logger: getLogLevels(),
  });

  await app.listen();
  Logger.log(`🚀 RabbitMQ Microservice listening...`);
}

bootstrap();
