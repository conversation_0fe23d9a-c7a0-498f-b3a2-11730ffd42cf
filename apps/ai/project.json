{"name": "ai", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/ai/src", "projectType": "application", "tags": [], "targets": {"serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "options": {"buildTarget": "ai:build", "inspect": true, "port": 9230}, "configurations": {"development": {"buildTarget": "ai:build:development"}, "production": {"buildTarget": "ai:build:production"}}}, "deploy": {"command": "node ./apps/ai/ai/index.js"}}}