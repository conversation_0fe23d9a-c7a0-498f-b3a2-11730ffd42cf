/**
 * This is not a production server yet!
 * This is only a minimal backend to get started.
 */

import { DatadogTracer, setupSwagger } from '@core/libs';
import { ValidationPipe, LogLevel } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { Logger, LoggerErrorInterceptor } from 'nestjs-pino';
import { AppModule } from './app/api/app.module';
import helmet from 'helmet';
import qs from 'node:querystring';
import { FastifyReply, FastifyRequest } from 'fastify';
import {
  FastifyAdapter,
  NestFastifyApplication,
} from '@nestjs/platform-fastify';
//import { StandardExceptionsFilter } from '@core/libs';
import {
  ApiLoggingInterceptor,
  HttpExceptionFilter,
  ResponseInterceptor,
} from '@core_be/global';
import { Transport } from '@nestjs/microservices';
const tracer = new DatadogTracer({
  service: 'ai',
  env: process.env.NODE_ENV || 'unknown',
  logInjection: true,
});

// Get log levels from environment
const getLogLevels = (): LogLevel[] => {
  const logLevel = process.env.LOG_LEVEL || 'error,warn,log';
  return logLevel.split(',').map((level) => level.trim()) as LogLevel[];
};
// This is a workaround for BigInt serialization issues in JSON
(BigInt.prototype as any).toJSON = function () {
  return this.toString();
};

async function bootstrap() {
  const fastifyAdapter = new FastifyAdapter({
    logger: false,
    querystringParser: (str) => qs.parse(str),
  });

  fastifyAdapter.get('/', (req: FastifyRequest, res: FastifyReply) => {
    res.code(200);
    res.send({
      message: 'Welcome to ai',
    });
  });

  const app = await NestFactory.create<NestFastifyApplication>(
    AppModule,
    fastifyAdapter,
    {
      bufferLogs: true,
    }
  );
  const logger = app.get<Logger>(Logger);
  tracer.use('fastify', fastifyAdapter.getInstance()).use('pino', true);

  //const adapter = app.get<HttpAdapterHost>(HttpAdapterHost);

  app.useLogger(getLogLevels());
  const globalPrefix = 'api';
  app.setGlobalPrefix(globalPrefix);
  app.use(helmet());
  app.enableCors();

  // app.useGlobalInterceptors(new LoggerErrorInterceptor());
  // app.useGlobalFilters(
  //   new StandardExceptionsFilter(adapter.httpAdapter, logger)
  // );
  app.useGlobalInterceptors(
    new LoggerErrorInterceptor(),
    new ResponseInterceptor(),
    new ApiLoggingInterceptor(logger)
  );
  app.useGlobalFilters(new HttpExceptionFilter(logger));
  app.useGlobalPipes(new ValidationPipe());
  // Swagger setup

  await setupSwagger(
    app,
    fastifyAdapter,
    'HealThis AI API',
    'API documentation for HealThis BE AI',
    'api/ai/docs'
  );

  if (!process.env.REDIS_HOST || !process.env.REDIS_PORT) {
    throw new Error('Redis host and port are required');
  }
  app.connectMicroservice({
    transport: Transport.REDIS,
    options: {
      host: process.env.REDIS_HOST,
      port: parseInt(process.env.REDIS_PORT),
    },
  });

  const port = process.env.AI_PORT || 80;
  try {
    await app.startAllMicroservices();
    await app.listen(port, '0.0.0.0');
    logger.log(`Healthis-ai is running on: http://localhost:${port}`);
  } catch (err) {
    logger.error(err);
    process.exit(1);
  }
}

bootstrap();
