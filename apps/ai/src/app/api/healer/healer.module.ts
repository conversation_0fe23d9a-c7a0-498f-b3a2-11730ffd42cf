import { Module, NestModule, MiddlewareConsumer } from '@nestjs/common';
import { TokenMiddleware } from '@core_be/auth';
import { PrismaService } from '@core/prisma-client';
import { He<PERSON>r<PERSON>ontroller } from './controllers/healer.controller';
import { HealerService } from './services/healer.service';
import { LibProfileService, LibSessionService } from '@core/libs';
import { PublisherService } from 'libs/global/src/lib/queue/publisher.service';
import { LibNotificationService } from '@core_be/notifications';
import { HealerStatusRepository } from 'libs/data-access/src/lib/healer_status/healer_status.repository';

@Module({
  imports: [],
  controllers: [HealerController],
  providers: [
    HealerService,
    LibProfileService,
    PrismaService,
    PublisherService,
    LibSessionService,
    LibNotificationService,
    HealerStatusRepository,
  ],
})
export class HealerModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(TokenMiddleware).forRoutes(HealerController);
  }
}
