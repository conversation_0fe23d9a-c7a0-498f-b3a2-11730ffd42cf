import {
  BadRequestException,
  Controller,
  Get,
  NotFoundException,
  Param,
  HttpCode,
  Patch,
  Post,
  Req,
  UseGuards,
  Body,
} from '@nestjs/common';
import { ApiTags, ApiBearerAuth } from '@nestjs/swagger';
import {
  Role,
  Roles,
  RolesGuard,
  LibProfileService,
  ProfileType,
} from '@core/libs';
import { HealerService } from '../services/healer.service';
import { HealerIdParams } from '../dto/healer.dto';
import { TakeSessionDto } from '../../sessions/dto/session.dto';
import { SessionService } from '../../sessions/services/session.service';
import { ProfileAccessGuard } from 'libs/global/src/lib/guards/profile-access.guard';
import { ProfileTypes } from 'libs/global/src/lib/decorators/profile-types.decorator';

@ApiTags('Healer')
@ApiBearerAuth()
@Controller('healer')
@UseGuards(RolesGuard)
export class HealerController {
  constructor(
    private readonly healerService: HealerService,
    private readonly profileService: LibProfileService,
    private readonly sessionService: SessionService
  ) {}

  @Get(':profile_id/dashboard')
  @Roles(Role.Admin, Role.Healer)
  @ProfileTypes(ProfileType.HEALER)
  @UseGuards(ProfileAccessGuard)
  async getDashboard(@Req() req, @Param() params: HealerIdParams) {
    const { user_id, profiles } = req.raw.user;
    if (!params.profile_id || isNaN(Number(params.profile_id))) {
      throw new BadRequestException('Invalid profile_id');
    }
    const validateUserProfile = await this.profileService.validateUserProfile(
      profiles,
      user_id,
      +params.profile_id,
      ProfileType.HEALER
    );

    if (!validateUserProfile) {
      throw new NotFoundException('User profile not found.');
    }

    return await this.healerService.getDashboard(+params.profile_id);
  }

  @Get(':profile_id/session/:session_id')
  @Roles(Role.Admin, Role.Healer)
  @ProfileTypes(ProfileType.HEALER)
  @UseGuards(ProfileAccessGuard)
  async findOne(
    @Req() req,
    @Param('session_id') session_id: string,
    @Param('profile_id') profile_id: string
  ) {
    const { user_id, profiles } = req.raw.user;
    const profileID = ~~profile_id;
    const sessionID = ~~session_id;

    const validateUserProfile = await this.profileService.validateUserProfile(
      profiles,
      user_id,
      profileID
    );

    if (!validateUserProfile) {
      throw new NotFoundException('User profile not found.');
    }
    return await this.sessionService.findSessionDetails(sessionID, profileID);
  }

  @Get(':profile_id/session-assigned')
  @Roles(Role.Admin, Role.Healer)
  @UseGuards(ProfileAccessGuard)
  @ProfileTypes(ProfileType.HEALER)
  async getAssignedSession(
    @Req() req,
    @Param('profile_id') profile_id: string
  ) {
    const { user_id, profiles } = req.raw.user;
    const profileID = ~~profile_id;

    const validateUserProfile = await this.profileService.validateUserProfile(
      profiles,
      user_id,
      profileID
    );

    if (!validateUserProfile) {
      throw new NotFoundException('User profile not found.');
    }
    return this.sessionService.findHealerSession(profileID);
  }

  // DEPRECATED
  // @Patch('take-session')
  // @Roles(Role.Admin, Role.Healer)
  // async assignHealerToSession(@Req() req, @Body() body: TakeSessionDto) {
  //   const { user_id, profiles } = req.raw.user;

  //   const validateUserProfile = await this.profileService.validateUserProfile(
  //     profiles,
  //     user_id,
  //     +body.profile_id,
  //     ProfileType.HEALER
  //   );
  //   if (!validateUserProfile) {
  //     throw new BadRequestException('Invalid profile id');
  //   }

  //   return await this.libSessionService.sessionAssignedToHealer(body);
  // }

  @Post('request-session')
  @Roles(Role.Admin, Role.Healer)
  @UseGuards(ProfileAccessGuard)
  @ProfileTypes(ProfileType.HEALER)
  @HttpCode(200)
  async requestHealingSession(@Req() req, @Body() body: TakeSessionDto) {
    const { user_id, profiles } = req.raw.user;
    const validateUserProfile = await this.profileService.validateUserProfile(
      profiles,
      user_id,
      +body.profile_id,
      ProfileType.HEALER
    );
    if (!validateUserProfile) {
      throw new BadRequestException('Invalid profile id');
    }
    return await this.healerService.requestSession(body.profile_id);
  }
}
