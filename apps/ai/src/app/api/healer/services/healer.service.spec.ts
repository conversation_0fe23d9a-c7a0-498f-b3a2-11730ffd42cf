import { Test, TestingModule } from '@nestjs/testing';
import { HealerService } from './healer.service';
import { HealerIdParams } from '../dto/healer.dto';
import { SessionRepository } from '../../../../../../../libs/data-access/src';

describe('HealerService', () => {
  let healerService: HealerService;
  let sessionRepository: SessionRepository;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        HealerService,
        {
          provide: SessionRepository,
          useValue: {
            count: jest.fn(),
          },
        },
      ],
    }).compile();

    healerService = module.get<HealerService>(HealerService);
    sessionRepository = module.get<SessionRepository>(SessionRepository);
  });

  it('should return healer status as "Available" when there are no active sessions', async () => {
    const healerIdParams: HealerIdParams = { profile_id: 1 };

    // Mock count response for healer sessions
    jest.spyOn(sessionRepository, 'count').mockResolvedValueOnce(0);
    // Mock count response for patients in queue
    jest.spyOn(sessionRepository, 'count').mockResolvedValueOnce(0);

    const result = await healerService.getDashboard(healerIdParams);

    expect(result.healer_status).toBe('Available');
    expect(result.patient_count).toBe(0);
  });

  it('should return healer status as "Not Available" when there are active sessions', async () => {
    const healerIdParams: HealerIdParams = { profile_id: 1 };

    // Mock count response for healer sessions
    jest.spyOn(sessionRepository, 'count').mockResolvedValueOnce(1);
    // Mock count response for patients in queue
    jest.spyOn(sessionRepository, 'count').mockResolvedValueOnce(0);

    const result = await healerService.getDashboard(healerIdParams);

    expect(result.healer_status).toBe('Not Available');
    expect(result.patient_count).toBe(0);
  });

  it('should return the correct patient count in queue', async () => {
    const healerIdParams: HealerIdParams = { profile_id: 1 };
    // Mock count response for healer sessions
    jest.spyOn(sessionRepository, 'count').mockResolvedValueOnce(1);
    // Mock count response for patients in queue
    jest.spyOn(sessionRepository, 'count').mockResolvedValueOnce(5);

    const result = await healerService.getDashboard(healerIdParams);

    expect(result.healer_status).toBe('Not Available');
    expect(result.patient_count).toBe(5);
  });
});
