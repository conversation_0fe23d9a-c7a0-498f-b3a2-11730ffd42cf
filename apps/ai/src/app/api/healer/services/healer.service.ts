import { Injectable } from '@nestjs/common';
import {
  ProfileRepository,
  SessionSubStatusType,
  SessionRepository,
  SessionStatus,
  HealerStatusType,
} from '@core/libs';
import { PublisherService } from 'libs/global/src/lib/queue/publisher.service';
import { HEALER_REQUESTS_QUEUE } from 'libs/global/src/lib/config/rabbitmq.config';
import { HealerStatusRepository } from 'libs/data-access/src/lib/healer_status/healer_status.repository';
import { Logger } from 'nestjs-pino';

@Injectable()
export class HealerService {
  constructor(
    private readonly healerStatusRepository: HealerStatusRepository,
    private readonly healerSessionRepository: SessionRepository,
    private readonly profileRepository: ProfileRepository,
    private readonly publisherService: PublisherService,
    private readonly logger: Logger
  ) {}

  async requestSession(profile_id: number) {
    try {
      // Log method entry
      this.logger.log(
        {
          event: 'HEALER_REQUEST_SESSION_START',
          profile_id: profile_id,
          message: 'Starting healer session request process',
        },
        'Healer Session Request'
      );

      // Send message to queue
      const response = await this.publisherService.sendMessage({
        message: {
          profile_id,
        },
        queueRoutingKey: HEALER_REQUESTS_QUEUE.routingKey,
      });

      this.logger.log(
        {
          event: 'HEALER_REQUEST_SESSION_QUEUE_MESSAGE_SENT',
          profile_id: profile_id,
          message: 'Queue message sent successfully',
        },
        'Healer Session Request Queue'
      );

      // Update healer status
      await this.healerStatusRepository.upsert({
        where: { profile_id },
        create: {
          profile_id,
          status: HealerStatusType.PATIENT_REQUESTED,
          updated_at: new Date(),
        },
        update: {
          status: HealerStatusType.PATIENT_REQUESTED,
          updated_at: new Date(),
        },
      });

      this.logger.log(
        {
          event: 'HEALER_REQUEST_SESSION_SUCCESS',
          profile_id: profile_id,
          healer_status: HealerStatusType.PATIENT_REQUESTED,
          message: 'Healer session request completed successfully',
        },
        'Healer Session Request Success'
      );

      return response;
    } catch (error) {
      this.logger.error(
        {
          event: 'HEALER_REQUEST_SESSION_ERROR',
          profile_id: profile_id,
          error_message:
            error instanceof Error ? error.message : 'unknown error',
          message: 'Healer session request failed',
        },
        'Healer Session Request Error'
      );
      throw error;
    }
  }

  async getDashboard(profile_id: number) {
    const sessions = await this.healerSessionRepository.count({
      where: {
        is_deleted: false,
        profile_id,
      },
    });

    const profile = await this.profileRepository.findFirst({
      where: {
        is_deleted: false,
        profile_id,
      },
    });

    const healerStatus = sessions === 0 ? 'Available' : 'Not Available';
    const patientCount = await this.healerSessionRepository.count({
      where: {
        status: SessionStatus.IN_QUEUE,
        sub_status: SessionSubStatusType.AVAILABLE,
        is_deleted: false,
        OR: [{ healer_id: null }, { healer_id: { not: profile_id } }],
      },
    });

    return {
      healer_status: healerStatus,
      patient_count: patientCount,
      is_intro_complete: profile && profile.is_intro_complete,
    };
  }
}
