import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsString,
  IsNumber,
  IsBoolean,
  IsEnum,
  IsOptional,
  ValidateIf,
} from '@nestjs/class-validator';
import {
  SessionStatus,
  HealingRoundStatus,
  SessionSubStatusType,
  IsValidSubStatus,
  SessionDeclineTriggerSourceType,
} from '@core/libs';

export class CreateSessionDto {
  @ApiProperty({
    example: 1,
    description: 'Profile Id',
    required: true,
  })
  @IsNotEmpty()
  @IsNumber()
  profile_id: number;
}

export class QueueConfirmationDto {
  @ApiProperty({
    example: 1,
    description: 'Session Id',
    required: true,
  })
  @IsNotEmpty()
  @IsNumber()
  session_id: number;

  @ApiProperty({
    example: true,
    description: 'is_confirmed',
    required: true,
  })
  @IsNotEmpty()
  @IsBoolean()
  is_confirmed: boolean;
}
export class ActiveSessionDto {
  @ApiProperty({
    example: 1,
    description: 'Session Id',
    required: true,
  })
  @IsNotEmpty()
  @IsNumber()
  session_id: number;
}

export class TakeSessionDto {
  @ApiProperty({
    example: 1,
    description: 'Profile Id',
    required: true,
  })
  @IsNotEmpty()
  @IsNumber()
  profile_id: number;
}

export class UpdateSessionStatusDto {
  @ApiProperty({
    example: 'CANCELLED',
    description: 'Session Status',
    required: true,
    enum: SessionStatus,
  })
  @IsNotEmpty()
  @IsEnum(SessionStatus)
  status: SessionStatus;

  @ApiProperty({
    example: 'ENTER_IN_QUEUE',
    description: 'Session sub Status',
    required: false,
    enum: SessionSubStatusType,
  })
  @IsOptional()
  @ValidateIf((o) => o.sub_status !== null)
  @IsEnum(SessionSubStatusType, {
    message: 'Invalid sub_status for IN_QUEUE',
  })
  @IsString({ message: 'sub_status must be a string' })
  @IsValidSubStatus({ message: 'Invalid sub_status for the given status' })
  sub_status?: string | null;

  @ApiProperty({
    example: 1,
    description: 'Profile Id',
    required: true,
  })
  @IsNotEmpty()
  @IsNumber()
  profile_id: number;

  @ApiProperty({
    example: 2,
    description: 'Session Id',
    required: true,
  })
  @IsNotEmpty()
  @IsNumber()
  session_id: number;
}

export class UpdateSessionDto {
  @ApiProperty({
    example: 1,
    description: 'Profile Id',
    required: true,
  })
  @IsNotEmpty()
  @IsNumber()
  profile_id: number;

  @ApiProperty({
    example: true,
    description: 'is_follow_up',
    required: true,
  })
  @IsNotEmpty()
  @IsBoolean()
  is_follow_up: boolean;

  @ApiProperty({
    example: SessionStatus.COMPLETED,
    description: 'follow_up_frequency_days',
    required: true,
    default: new Date(),
  })
  @IsEnum(SessionStatus, { message: 'status must be a valid value' })
  @IsNotEmpty()
  @IsString()
  status: string;
}

export class ParamSessionDto {
  @ApiProperty({
    example: 1,
    description: 'session_id',
    required: true,
  })
  @IsNotEmpty()
  @IsNumber()
  session_id: number;
}

export class CreateHealingRoundDto {
  @ApiProperty({
    example: 1,
    description: 'Profile ID of the Healer or Patient',
    required: true,
  })
  @IsNotEmpty()
  @IsNumber()
  profile_id: number;

  @ApiProperty({
    example: 1,
    description: 'Session Id',
    required: true,
  })
  @IsNotEmpty()
  @IsNumber()
  session_id: number;

  @ApiProperty({
    example: false,
    description: 'async',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  async: boolean;
}

export class UpdateHealingRoundDto {
  @ApiProperty({
    example: 1,
    description: 'Profile Id',
    required: true,
  })
  @IsNotEmpty()
  @IsNumber()
  profile_id: number;

  @ApiProperty({
    example: 1,
    description: 'Session Id',
    required: true,
  })
  @IsNotEmpty()
  @IsNumber()
  session_id: number;

  @ApiProperty({
    example: 1,
    description: 'Round Id',
    required: true,
  })
  @IsNotEmpty()
  @IsNumber()
  round_id: number;

  @ApiProperty({
    example: true,
    description: 'async',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  async: boolean;
}

export class HealingSessionStatusDTo {
  @ApiProperty({
    example: SessionStatus.HEALING_ROUND,
    description: 'status',
    required: true,
  })
  @IsEnum(SessionStatus, { message: 'status must be a valid value' })
  @IsNotEmpty()
  @IsString()
  status: string;

  @ApiProperty({
    example: SessionStatus.HEALING_ROUND,
    description: 'status',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  sub_status: string;
}

export class StartHealingRoundDto {
  @ApiProperty({
    example: 1,
    description: 'Healer Id',
    required: true,
  })
  @IsNotEmpty()
  @IsNumber()
  healer_id: number;

  @ApiProperty({
    example: HealingRoundStatus.IN_PROGRESS,
    description: 'status',
    required: true,
  })
  @IsEnum(SessionStatus, { message: 'status must be a valid value' })
  @IsNotEmpty()
  @IsString()
  status: string;
}

export class FeedbackSummaryDto {
  @ApiProperty({
    example: 1,
    description: 'Session Id',
    required: true,
  })
  @IsNotEmpty()
  @IsNumber()
  session_id: number;

  @ApiProperty({
    example: 'Good!',
    description: 'Good!',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  content: string;

  @ApiProperty({
    example: 1,
    description: 'Score!',
    required: true,
  })
  @IsNotEmpty()
  @IsNumber()
  satisfaction_score: number;

  @ApiProperty({
    example: false,
    description: 'is_satisfied',
    required: true,
  })
  @IsNotEmpty()
  @IsBoolean()
  is_satisfied: boolean;

  @ApiProperty({
    example: HealingRoundStatus.COMPLETED,
    description: 'status',
    required: true,
  })
  @IsEnum(SessionStatus, { message: 'status must be a valid value' })
  @IsNotEmpty()
  @IsString()
  status: string;
}

export class SessionDto {
  session_id: number;
  profile_id: number;
  subscription_id?: number;
  thread_id?: string;
  session_start_at?: Date;
  session_end_at?: Date;
  status?: string;
  queue_number?: bigint;
  queue_start_time?: Date;
  satisfaction_score?: number;
  is_satisfied?: boolean;
  is_follow_up?: boolean;
  created_by?: number;
  created_at?: Date;
  updated_by?: number;
  updated_at?: Date;
  deleted_by?: number;
  deleted_at?: Date;
  is_deleted?: boolean;
  profiles?: unknown;
  subscriptions?: unknown;
  session_ailments?: unknown;
  session_summaries?: unknown;
  conversations?: unknown;
  healing_rounds?: unknown;
}

export type SessionResponseMainDto = {
  message: string;
  success: boolean;
  data: SessionDto[];
};

export type SessionResponseDto = {
  message: string;
  success: boolean;
  data: SessionDto[];
};

export class HealingSessionEndDto {
  @ApiProperty({
    example: 1,
    description: 'profile_id',
    required: true,
  })
  @IsNotEmpty()
  @IsNumber()
  profile_id: number;

  @ApiProperty({
    example: 1,
    description: 'session_id',
    required: true,
  })
  @IsNotEmpty()
  @IsNumber()
  session_id: number;
}

export class AcceptDeclineSessionDto {
  @ApiProperty({
    example: 1,
    description: 'Profile Id',
    required: true,
  })
  @IsNotEmpty()
  @IsNumber()
  profile_id: number;

  @ApiProperty({
    example: 1,
    description: 'Session Id',
    required: true,
  })
  @IsNotEmpty()
  @IsNumber()
  session_id: number;

  @ApiProperty({
    example: 'USER',
    description: 'Trigger source for session decline',
    required: false,
  })
  @IsOptional()
  @IsString()
  trigger_source?: SessionDeclineTriggerSourceType;
}
