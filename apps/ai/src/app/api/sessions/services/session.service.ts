import {
  BadGatewayException,
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { Prisma, PrismaService, healing_rounds } from '@core/prisma-client';
import {
  ConversationRepository,
  HealingRoundRepository,
  ProfileRepository,
  SessionAilmentRepository,
  SessionRepository,
  SummaryRepository,
} from 'libs/data-access/src';
import {
  // Import Services
  LibProfileService,

  // Import DTOs
  QueryDto,
  SessionDto,
  SessionResponseDto,
  UpdateSessionDto,

  // Import ENUMs
  HealingRoundStatus,
  SessionStatus,
  ProfileType,
  SessionSubStatusType,
  HealingRoundSubStatus,
  MessageType,
  SenderType,
  AI_MESSAGE_OUTGOING_TRIGGER,
  QueueConfirmationDto,
  CreateSessionDto,
  AI_MESSAGE_INCOMING_TRIGGER,
  AbandonmentSubStatus,
  HealerStatusType,
} from '@core/libs';
import { ConfigService } from '@nestjs/config';
import { LibOpenaiService } from '@core_be/aiml';
import { HealerStatusRepository } from 'libs/data-access/src/lib/healer_status/healer_status.repository';
import { Logger } from 'nestjs-pino';

@Injectable()
export class SessionService {
  private readonly summaryAssistantID: string;
  private readonly SESSION_CONFIRMATION_REQUIRED_TIMEOUT: number;
  private readonly AVERAGE_SESSION_DURATION: number;

  constructor(
    private readonly prisma: PrismaService,
    private readonly profileService: LibProfileService,
    private readonly sessionRepository: SessionRepository,
    private readonly conversationRepository: ConversationRepository,
    private readonly healingRoundRepository: HealingRoundRepository,
    private readonly summaryRepository: SummaryRepository,
    private readonly profileRepository: ProfileRepository,
    private readonly configService: ConfigService,
    private readonly openAIService: LibOpenaiService,
    private readonly sessionAilmentRepository: SessionAilmentRepository,
    private readonly healerStatusRepository: HealerStatusRepository,
    private readonly logger: Logger
  ) {
    this.summaryAssistantID = this.configService.getOrThrow<string>(
      'SUMMARY_ASSISTANT_ID'
    );

    this.SESSION_CONFIRMATION_REQUIRED_TIMEOUT =
      parseInt(
        '' + this.configService.get('SESSION_CONFIRMATION_REQUIRED_TIMEOUT')
      ) || 18000000;

    this.AVERAGE_SESSION_DURATION =
      parseInt('' + this.configService.get('AVERAGE_SESSION_DURATION')) ||
      600000;
  }
  async findAll(query: QueryDto): Promise<SessionResponseDto[] | unknown> {
    const {
      session_id,
      profile_id,
      sortBy = 'created_at',
      order = 'desc',
      page = 1,
      limit = 10,
    } = query;
    try {
      const finalQuery: {
        name?: RegExp;
        where: Prisma.healing_sessionsWhereInput;
        skip: number;
        take: number;
        orderBy?: { [sortBy: string]: string } | undefined;
      } = {
        where: { is_deleted: false },
        skip: (page - 1) * limit,
        take: typeof limit == 'string' ? parseInt(limit) : limit,
      };
      if (query.name) {
        finalQuery['name'] = new RegExp(query.name, 'i'); // Simple text filter
      }
      if (session_id) {
        finalQuery['where']['session_id'] = parseInt(session_id);
      }
      if (profile_id) {
        finalQuery['where']['profile_id'] = parseInt(profile_id);
      }

      if (sortBy) {
        finalQuery['orderBy'] = {
          [sortBy]: order,
        };
      }
      const sessions = await this.sessionRepository.findAll(finalQuery);
      return sessions;
    } catch (error) {
      throw new BadRequestException(error.message || 'Please try again');
    }
  }

  async create(body: CreateSessionDto, userId: number) {
    try {
      // Log method entry with request context
      this.logger.log(
        {
          event: 'SESSION_CREATE_START',
          user_id: userId,
          profile_id: body?.profile_id,
          message: 'Starting session creation process',
        },
        'Session Creation'
      );

      const checkProfile = await this.profileService.getProfileById(
        +body?.profile_id
      );

      if (
        checkProfile?.profile_type.toLocaleLowerCase() ==
        ProfileType.HEALER.toLocaleLowerCase()
      ) {
        this.logger.error(
          {
            event: 'SESSION_CREATE_HEALER_RESTRICTION',
            user_id: userId,
            profile_id: body?.profile_id,
            message: 'Healer profiles cannot start sessions',
          },
          'Session Creation Authorization Failed'
        );
        throw new BadRequestException('Healer can not start a session');
      }

      const userActiveHealingSession = await this.sessionRepository.findFirst({
        where: {
          AND: [
            { profile_id: body?.profile_id },
            {
              status: {
                notIn: [
                  SessionStatus.CANCELLED,
                  SessionStatus.COMPLETED,
                  SessionStatus.ABANDONED,
                ],
              },
            },
          ],
        },
      });

      if (userActiveHealingSession) {
        this.logger.log(
          {
            event: 'SESSION_CREATE_CANCELLING_ACTIVE_SESSION',
            user_id: userId,
            profile_id: body?.profile_id,
            active_session_id: userActiveHealingSession.session_id,
            message: 'Cancelling active session to create new one',
          },
          'Session Creation Active Session Cancellation'
        );

        await this.sessionRepository.update({
          where: { session_id: userActiveHealingSession.session_id },
          data: {
            status: SessionStatus.CANCELLED,
            updated_at: new Date().toISOString(),
          },
        });
      }

      // BE will not be initialising conversation
      // const assistantResponse =
      //   await this.openAIService.startSummaryAssistantThread(
      //     this.summaryAssistantID,
      //     true
      //   );

      const session = await this.sessionRepository.create({
        profile_id: body?.profile_id,
        status: SessionStatus.INTAKE,
        sub_status: SessionSubStatusType.IN_PROGRESS,
        // thread_id: assistantResponse.threadId,
      });

      // Log successful session creation
      this.logger.log(
        {
          event: 'SESSION_CREATE_SUCCESS',
          user_id: userId,
          profile_id: body?.profile_id,
          session_id: session.session_id,
          message: 'Session created successfully',
        },
        'Session Creation Success'
      );

      // const aiResponse: Prisma.conversationsCreateInput = {
      //   content: `${this.SESSION_INTAKE_GREETING_MESSAGE}`,
      //   sender_type: SenderType.AI,
      //   profiles: {
      //     connect: {
      //       profile_id: body?.profile_id,
      //     },
      //   },
      //   healing_sessions: {
      //     connect: {
      //       session_id: session.session_id,
      //     },
      //   },
      // };
      // await this.conversationRepository.create(aiResponse);

      return { session_id: session?.session_id };
    } catch (error: any) {
      // Log session creation failure
      this.logger.error(
        {
          event: 'SESSION_CREATE_ERROR',
          user_id: userId,
          profile_id: body?.profile_id,
          error_message: error.message || 'unknown error',
          message: 'Session creation failed',
        },
        'Session Creation Error'
      );

      throw new BadRequestException(error.message || 'Please try again');
    }
  }

  // Deprecated method
  async confirmAndRearrangeQueue(body: QueueConfirmationDto) {
    try {
      const { session_id } = body;
      let updatedStatus = '';

      const existSession = await this.sessionRepository.findFirst({
        where: {
          session_id: session_id,
          is_deleted: false,
          status: SessionStatus.IN_QUEUE,
          sub_status: {
            in: [SessionSubStatusType.AI_CONFIRMATION_REQUIRED],
          },
        },
      });

      if (!existSession?.thread_id) {
        throw new NotFoundException('Invalid Session');
      }
      const currentTime = new Date();

      const confirmationRequiredTime = new Date(
        existSession.sub_status_updated_at
      );

      const timeElapsed =
        currentTime.getTime() - confirmationRequiredTime.getTime();

      // If the session is in CONFIRMATION_REQUIRED state for more than 5 hours, move to AI_CONFIRMATION_REQUIRED
      if (
        existSession.status === SessionStatus.IN_QUEUE &&
        timeElapsed > this.SESSION_CONFIRMATION_REQUIRED_TIMEOUT
      ) {
        console.log('Session AI_CONFIRMATION_REQUIRED after 5 hours');
        const assistantResponse =
          await this.openAIService.continueConversationAssistant(
            existSession.thread_id,
            `${AI_MESSAGE_OUTGOING_TRIGGER.REVIEW_CASE_SUMMARY}`,
            this.summaryAssistantID,
            'user',
            false,
            undefined,
            undefined
          );

        this.conversationRepository.create({
          sender_type: SenderType.AI,
          profiles: {
            connect: {
              profile_id: existSession.profile_id,
            },
          },
          healing_sessions: {
            connect: {
              session_id,
            },
          },
          message_type: MessageType.Text,
          content: assistantResponse.output,
          responded_at: new Date(),
          is_null_response: false,
        });

        await this.sessionRepository.update({
          where: { session_id: existSession.session_id },
          data: {
            status: SessionStatus.IN_QUEUE,
            sub_status: SessionSubStatusType.AI_CONFIRMATION_REQUIRED,
            sub_status_updated_at: new Date(),
          },
        });
        updatedStatus = `Session AI_CONFIRMATION_REQUIRED after ${this.SESSION_CONFIRMATION_REQUIRED_TIMEOUT} hours`;
      }

      // if (
      //   is_confirmed &&
      //   existSession.sub_status == SessionSubStatusType.CONFIRMATION_REQUIRED &&
      //   timeElapsed < this.SESSION_CONFIRMATION_REQUIRED_TIMEOUT
      // ) {
      //   await this.sessionRepository.update({
      //     where: {
      //       session_id: session_id,
      //     },
      //     data: {
      //       status: SessionStatus?.IN_QUEUE,
      //       sub_status: SessionSubStatusType.AVAILABLE,
      //       updated_at: new Date(),
      //       sub_status_updated_at: new Date(),
      //     },
      //   });
      //   updatedStatus = is_confirmed
      //     ? 'Healing session confirmed successfully!'
      //     : 'Patient is not available to take session!';
      // }

      return {
        message: updatedStatus,
      };
    } catch (error) {
      throw new BadRequestException(error.message || 'Please try again');
    }
  }

  async findOne(
    session_id: number,
    profile_id?: number,
    isUserAccessing?: boolean,
    query: { limit: number } = { limit: 10 }
  ) {
    const session = await this.sessionRepository.findOneWithInclude({
      where: {
        session_id: session_id,
        is_deleted: false,
        ...(profile_id &&
          isUserAccessing && {
            OR: [{ profile_id: profile_id }, { healer_id: profile_id }],
          }),
      },
      include: {
        healing_rounds: {
          orderBy: {
            round_end_at: 'desc',
          },
          select: {
            round_id: true,
            session_id: true,
            healer_id: true,
            summary_id: true,
            round_number: true,
            round_start_at: true,
            round_end_at: true,
            status: true,
            check_in_count: true,
            max_time: true,
            is_healer_confirmed: true,
            is_patient_confirmed: true,
            is_positive_feedback: true,
          },
        },
        session_summaries: {
          orderBy: {
            created_at: 'desc',
          },
          select: {
            summary_id: true,
            content: true,
          },
        },
        conversations: {
          where: {
            session_id,
            profile_id,
            is_deleted: false,
          },
          orderBy: {
            created_at: 'desc',
          },
          take: query?.limit || 10,
          select: {
            conversation_id: true,
            profile_id: true,
            model_id: true,
            session_id: true,
            round_id: true,
            summary_id: true,
            follow_up_id: true,
            sender_type: true,
            message_type: true,
            content: true,
            created_at: true,
            is_null_response: true,
            responded_at: true,
          },
        },
      },
    });

    if (!session) {
      throw new NotFoundException('Session not found or access denied');
    }

    let lastSummary = null;
    let initialSummary = null;
    if (session?.session_summaries.length > 0) {
      lastSummary = session?.session_summaries[0];
      initialSummary =
        session?.session_summaries[session?.session_summaries.length - 1];
    }

    const ailments = await this.sessionAilmentRepository.findAilments({
      session_id,
    });
    const regex = new RegExp(
      `${AI_MESSAGE_INCOMING_TRIGGER.HEALING_ROUND_MESSAGE_TO_PATIENT}\\s*`,
      'g'
    );
    const filteredSummary = {
      ...initialSummary,
      content:
        initialSummary && initialSummary?.content?.length > 0
          ? initialSummary?.content.replace(regex, '')
          : '',
    };
    const responsePayload = {
      ...session,
      summary_info: filteredSummary || null,
      session_queue_info: null,
      healing_round:
        session.healing_rounds.length > 0 ? session.healing_rounds[0] : null,
      conversations: session.conversations?.length
        ? session.conversations.reverse()
        : null,
      ailments,
    };
    if (
      [SessionStatus.IN_QUEUE].includes(session?.status as SessionStatus) &&
      [
        // SessionAllSubStatusType.ENTER_IN_QUEUE,
        // SessionAllSubStatusType.IN_FRONT_OF_QUEUE,
        SessionSubStatusType.AVAILABLE,
        // SessionSubStatusType.CONFIRMATION_REQUIRED,
      ].includes(session?.sub_status as SessionSubStatusType)
    ) {
      const waitTime = await this.calculateWaitingTime(session?.queue_number);
      responsePayload['session_queue_info'] = {
        queue_number: session?.queue_number || null,
        queue_status: session?.status || null,
        queue_sub_status: session?.sub_status || null,
        queue_start_time: session?.queue_start_time || null,
        queue_waiting_time: waitTime,
      };
    }

    if (
      session.healing_rounds.length > 0 &&
      typeof lastSummary[0]?.content !== 'undefined'
    ) {
      session.healing_rounds[0]['last_round_feedback'] =
        lastSummary[0]?.content;
    }

    delete responsePayload['queue_number'];
    delete responsePayload['queue_start_time'];
    delete responsePayload['session_summaries'];
    delete responsePayload['healing_rounds'];

    return responsePayload;
  }

  async calculateWaitingTime(queueNumber: bigint): Promise<number | null> {
    // AVERAGE_SESSION_DURATION is in milliseconds
    const averageSessionDurationMs = this.AVERAGE_SESSION_DURATION;

    // Get all sessions currently in session (healing round, in progress or feedback required)
    const sessionsInSession = await this.prisma.healing_sessions.count({
      where: {
        status: SessionStatus.HEALING_ROUND,
        sub_status: {
          in: [
            SessionSubStatusType.IN_PROGRESS,
            SessionSubStatusType.FEEDBACK_REQUIRED,
            SessionSubStatusType.COMPLETED,
          ],
        },
      },
    });

    // Get the number of sessions in front of the given queue number
    const sessionsAhead = await this.prisma.healing_sessions.count({
      where: {
        status: SessionStatus.IN_QUEUE,
        sub_status: SessionSubStatusType.AVAILABLE,
        queue_number: {
          lt: queueNumber,
        },
      },
    });

    let estimatedSeconds = null;
    // Calculate estimated waiting time in seconds
    if (sessionsInSession > 0) {
      const estimatedMs =
        ((sessionsAhead + 1) * averageSessionDurationMs) / sessionsInSession;
      estimatedSeconds = Math.ceil(estimatedMs / 1000);
    }
    return estimatedSeconds;
  }

  async findHealerSession(profile_id: number) {
    const healer = await this.getAvailableHealer(profile_id);

    if (!healer) {
      throw new NotFoundException('No available Healer!');
    }

    const healer_status = await this.healerStatusRepository.findOne({
      profile_id,
      is_deleted: false,
    });

    const session = await this.sessionRepository.findOneWithInclude({
      where: {
        status: SessionStatus.IN_QUEUE,
        healer_id: profile_id,
        is_deleted: false,
        OR: [
          {
            sub_status: SessionSubStatusType.SESSION_CONFIRMED,
          },
        ],
      },
      include: {},
    });

    const responsePayload = {
      ...session,
      healer_status: healer_status?.status || null,
    };
    if (
      [SessionStatus.IN_QUEUE].includes(session?.status as SessionStatus) &&
      [SessionSubStatusType.SESSION_CONFIRMED].includes(
        session?.sub_status as SessionSubStatusType
      )
    ) {
      responsePayload['session_queue_info'] = {
        queue_number: session?.queue_number || null,
        queue_status: session?.status || null,
        queue_sub_status: session?.sub_status || null,
        queue_start_time: session?.queue_start_time || null,
        queue_waiting_time: null,
      };
    }
    delete responsePayload['queue_number'];
    delete responsePayload['queue_start_time'];

    delete responsePayload['session_summaries'];
    delete responsePayload['profiles'];
    delete responsePayload['healing_rounds'];
    return responsePayload;
  }

  async findSessionDetails(
    session_id: number,
    profile_id?: number,
    query: { limit: number } = { limit: 10 }
  ) {
    const session = await this.sessionRepository.findOneWithInclude({
      where: {
        session_id: session_id,
        is_deleted: false,
      },
      include: {
        healing_rounds: {
          orderBy: {
            round_end_at: 'desc',
          },
          select: {
            round_id: true,
            session_id: true,
            healer_id: true,
            summary_id: true,
            round_number: true,
            round_start_at: true,
            round_end_at: true,
            status: true,
            check_in_count: true,
            max_time: true,
            is_healer_confirmed: true,
            is_patient_confirmed: true,
            is_positive_feedback: true,
          },
        },
        session_summaries: {
          orderBy: {
            created_at: 'desc',
          },
          select: {
            summary_id: true,
            content: true,
          },
        },
        profiles: {
          include: {
            users: true,
          },
        },
        conversations: {
          where: {
            session_id,
            profile_id,
            is_deleted: false,
          },
          orderBy: {
            created_at: 'desc',
          },
          take: query?.limit || 10,
          select: {
            conversation_id: true,
            profile_id: true,
            model_id: true,
            session_id: true,
            round_id: true,
            summary_id: true,
            follow_up_id: true,
            sender_type: true,
            message_type: true,
            content: true,
            created_at: true,
            is_null_response: true,
            responded_at: true,
          },
        },
      },
    });

    if (session.healer_id !== profile_id) {
      throw new BadRequestException(
        'Healing Session is not assiged for this healer!'
      );
    }

    const healer = await this.getAvailableHealer(profile_id);

    if (!healer) {
      throw new NotFoundException('No available Healer!');
    }

    const ailments = await this.sessionAilmentRepository.findAilments({
      session_id,
    });

    let lastSummary = null;
    let initialSummary = null;
    if (session?.session_summaries.length > 0) {
      lastSummary = session?.session_summaries[0];
      initialSummary =
        session?.session_summaries[session?.session_summaries.length - 1];
    }

    const { users, ...rest } = session.profiles as any;
    const responsePayload = {
      ...session,
      summary_info: initialSummary,
      round_summary_info: lastSummary,
      session_queue_info: null,
      healing_round:
        session.healing_rounds.length > 0 ? session.healing_rounds[0] : null,
      conversations: session.conversations?.length
        ? session.conversations.reverse()
        : null,
      ailments,
      healer_info: healer,
      patient_info: {
        ...rest,
        age: users?.year_of_birth
          ? new Date().getFullYear() - users?.year_of_birth
          : null,
      },
    };
    if (
      [SessionStatus.IN_QUEUE].includes(session?.status as SessionStatus) &&
      [
        SessionSubStatusType.AVAILABLE,
        // SessionSubStatusType.CONFIRMATION_REQUIRED,
      ].includes(session?.sub_status as SessionSubStatusType)
    ) {
      const waitTime = await this.calculateWaitingTime(session?.queue_number);
      responsePayload['session_queue_info'] = {
        queue_number: session?.queue_number || null,
        queue_status: session?.status || null,
        queue_sub_status: session?.sub_status || null,
        queue_start_time: session?.queue_start_time || null,
        queue_waiting_time: waitTime,
      };
    }

    if (
      session.healing_rounds.length > 0 &&
      typeof lastSummary[0]?.content !== 'undefined'
    ) {
      session.healing_rounds[0]['last_round_feedback'] =
        lastSummary[0]?.content;
    }

    delete responsePayload['queue_number'];
    delete responsePayload['queue_start_time'];

    delete responsePayload['session_summaries'];
    delete responsePayload['profiles'];
    delete responsePayload['healing_rounds'];

    return responsePayload;
  }

  async update(session_id: number, body: UpdateSessionDto, userId: number) {
    try {
      const profile = await this.profileService.profile({
        profile_id: body?.profile_id,
      });

      if (userId === profile?.user_id) {
        throw new BadRequestException(
          'Not allowed to update a session, associated with other user id.'
        );
      }

      if (!profile) {
        throw new NotFoundException('Profile not found');
      }

      if (profile?.profile_type.toLocaleLowerCase() == ProfileType.HEALER) {
        throw new BadRequestException('Healer can not update a session');
      }

      const checkSession = await this.findOne(session_id);

      if (!checkSession) {
        throw new NotFoundException('Healing Session not found');
      }
      const session = await this.sessionRepository.update({
        where: { session_id, is_deleted: false },
        data: { ...body, sub_status_updated_at: new Date() },
      });
      return session;
    } catch (error) {
      throw new BadRequestException(error.message || 'Please try again');
    }
  }

  async softDelete(session_id: number) {
    try {
      const checkSession = await this.findOne(session_id);

      if (!checkSession) {
        throw new NotFoundException('Healing Session not found');
      }
      const result = await this.sessionRepository.update({
        where: { session_id, is_deleted: false },
        data: { is_deleted: true },
      });
      return result;
    } catch (error) {
      throw new BadRequestException(error.message || 'Please try again');
    }
  }

  async getAllSessions(profile_id: string) {
    try {
      const activeSession = await this.sessionRepository.findOneWithSelect({
        where: {
          profile_id: parseInt(profile_id),
          is_deleted: false,
          OR: [
            {
              status: {
                notIn: [
                  SessionStatus.CANCELLED,
                  SessionStatus.COMPLETED,
                  SessionStatus.ABANDONED,
                ],
              },
            },
          ],
        },
        select: {
          session_id: true,
          session_start_at: true,
          session_end_at: true,
          created_at: true,
          status: true,
          sub_status: true,
        },
      });

      const completedSessions = await this.sessionRepository.findAll({
        where: {
          profile_id: parseInt(profile_id),
          is_deleted: false,
          status: SessionStatus.COMPLETED,
        },
        select: {
          session_id: true,
          session_start_at: true,
          session_end_at: true,
          created_at: true,
          status: true,
          sub_status: true,
        },
        take: 5,
        orderBy: {
          updated_at: 'desc',
        },
      });

      const profile = await this.profileRepository.findFirst({
        where: {
          is_deleted: false,
          profile_id: parseInt(profile_id),
        },
      });

      return {
        active_session: activeSession || null,
        completed_session: completedSessions,
        is_intro_complete: profile && profile.is_intro_complete,
      };
    } catch (error) {
      throw new BadRequestException(error.message || 'Please try again');
    }
  }

  async updateHealingRoundById(
    round_id: number,
    body: Partial<healing_rounds>
  ) {
    return this.healingRoundRepository.update({
      where: { round_id, is_deleted: false },
      data: body,
    });
  }

  async getHealingRoundById(round_id: number) {
    return this.healingRoundRepository.findUnique({
      where: { round_id, is_deleted: false },
    });
  }

  async getLastHealingRoundBySessionId(
    session_id: number,
    query?: Prisma.healing_roundsWhereInput
  ) {
    return this.healingRoundRepository.findOneWithInclude({
      where: { session_id, is_deleted: false, ...query },
      orderBy: { round_id: 'desc' },
    });
  }

  async getHealingRoundNumber(session_id: number) {
    try {
      const round_info = await this.healingRoundRepository.aggregate({
        where: {
          session_id: session_id,
          is_deleted: false,
        },
        _max: {
          round_number: true,
        },
      });

      return round_info?._max?.round_number
        ? round_info?._max?.round_number + 1
        : 1;
    } catch (error) {
      throw new BadRequestException(error.message || 'Please try again');
    }
  }

  // Deprecated method
  async resumeSession(session: SessionDto) {
    const checkSession = await this.sessionRepository.findFirst({
      where: {
        session_id: session?.session_id,
        status: SessionStatus.IN_QUEUE,
      },
    });

    if (!checkSession) {
      throw new NotFoundException(
        'Healing Session not found for status CONFIRMATION_REQUIRED!'
      );
    }

    const findMaxQueueSessionNumber: number =
      Number(
        (
          await this.sessionRepository.aggregate({
            _max: {
              queue_number: true,
            },
          })
        )?._max?.queue_number
      ) || 0;

    const result = await this.sessionRepository.update({
      where: { session_id: session.session_id },
      data: {
        status: SessionStatus.INTAKE,
        queue_number: findMaxQueueSessionNumber + 1,
        queue_start_time: new Date().toISOString(),
      },
    });

    if (!result) {
      throw new BadGatewayException('Failed to resume healing session!');
    }

    return {
      message: 'Healing session resumed successfully!',
    };
  }

  async getFirstAvailablePosition() {
    const availableQueue = await this.sessionRepository.findAll({
      where: {
        status: SessionStatus.IN_QUEUE,
        sub_status: SessionSubStatusType.AVAILABLE,
      },
    });

    const occupiedPositions = availableQueue.map((p) => p.queue_number);
    let position = 1n;

    while (occupiedPositions.includes(position)) {
      position++;
    }

    return position;
  }

  // Deprecated method
  async swapQueuePositions(queueNumber: bigint) {
    const sessionAvailable = await this.sessionRepository.findAll({
      where: {
        status: SessionStatus.IN_QUEUE,
        sub_status: SessionSubStatusType.AVAILABLE,
      },
      orderBy: {
        queue_number: 'asc',
      },
    });

    const sessionConfirmationRequired = await this.sessionRepository.findAll({
      where: {
        status: SessionStatus.IN_QUEUE,
        // sub_status: SessionSubStatusType.CONFIRMATION_REQUIRED,
      },
      orderBy: {
        queue_number: 'asc',
      },
    });

    const sessionConfirmationRequiredFiltered =
      sessionConfirmationRequired.filter((s) => s.queue_number !== queueNumber);

    const sessionConfirmationToUpdate = sessionConfirmationRequired.filter(
      (s) => s.queue_number === queueNumber
    );

    sessionConfirmationToUpdate[0].status = SessionStatus.IN_QUEUE;
    sessionConfirmationToUpdate[0].sub_status = SessionSubStatusType.AVAILABLE;

    let reArrangeSessions = [
      ...sessionAvailable,
      ...sessionConfirmationToUpdate,
      ...sessionConfirmationRequiredFiltered,
    ];

    reArrangeSessions = reArrangeSessions.map((s, index) => {
      s.queue_number = BigInt(index + 1);
      return s;
    });

    return reArrangeSessions;
  }

  async updateSessionStatus(
    session_id: number,
    profile_id: number,
    status: string,
    subStatus?: string
  ) {
    const session = await this.sessionRepository.findFirst({
      where: {
        session_id,
        profile_id,
      },
    });
    if (!session) {
      throw new NotFoundException('Healing Session not found');
    }
    const sessionUpdate = await this.sessionRepository.update({
      where: { session_id },
      data: {
        status,
        sub_status: subStatus || null,
        sub_status_updated_at: new Date(),
        updated_at: new Date(),
      },
    });
    // Update healer status in case patient cancel the session after accepting
    if (session.healer_id && status === SessionStatus.CANCELLED) {
      await this.healerStatusRepository.upsert({
        where: { profile_id: session.healer_id },
        create: {
          profile_id: session.healer_id,
          status: HealerStatusType.NO_AVAILABLE_PATIENTS,
          updated_at: new Date(),
        },
        update: {
          status: HealerStatusType.NO_AVAILABLE_PATIENTS,
          updated_at: new Date(),
        },
      });
    }
    return sessionUpdate;
  }

  async updateById(
    session_id: number,
    data: Prisma.healing_sessionsUpdateInput
  ) {
    return this.sessionRepository.update({
      where: { session_id },
      data,
    });
  }

  async getAvailableHealer(profile_id: number) {
    return this.profileRepository.getHealerProfileInfo({
      where: {
        profile_type: ProfileType?.HEALER,
        is_active: true,
        profile_id,
      },
    });
  }

  // DEPRECATED METHOD
  // async getAvailablePatientOlder(healerId: number) {
  //   try {
  //     const occupiedPositions = await this.sessionRepository.findAll({
  //       where: {
  //         status: SessionStatus.IN_QUEUE,
  //         sub_status: SessionSubStatusType.AVAILABLE,
  //         OR: [
  //           {
  //             healer_id: {
  //               not: healerId,
  //             },
  //           },
  //           {
  //             healer_id: null,
  //           },
  //         ],
  //       },
  //       orderBy: {
  //         queue_number: 'asc',
  //       },
  //     });

  //     if (!occupiedPositions.length) {
  //       return null;
  //     }

  //     let availableQueueNumber = 1;

  //     for (const patient of occupiedPositions) {
  //       if (availableQueueNumber < patient.queue_number) {
  //         break;
  //       }
  //       availableQueueNumber++;
  //     }

  //     const sessionData = await this.sessionRepository.findFirst({
  //       where: {
  //         session_id: occupiedPositions[0]?.session_id,
  //         status: SessionStatus.IN_QUEUE,
  //         sub_status: SessionSubStatusType.AVAILABLE,
  //         queue_number: occupiedPositions[0]?.queue_number,
  //       },
  //     });

  //     const patientData = await this.profileRepository.getHealerProfileInfo({
  //       where: { profile_id: sessionData?.profile_id },
  //     });

  //     return {
  //       patient_info: patientData,
  //       session_info: sessionData,
  //     };
  //   } catch (error) {
  //     throw new BadRequestException('There are no available patients');
  //   }
  // }

  // async assignHealerToSession(data: { profile_id: number }) {
  //   const { profile_id } = data;

  //   const healer = await this.getAvailableHealer(profile_id);

  //   if (!healer) {
  //     throw new NotFoundException('No available Healer!');
  //   }

  //   const availableOlderPatient =
  //     await this.libSessionService.getAvailablePatientOlder(profile_id);
  //   if (!availableOlderPatient) {
  //     throw new NotFoundException('No patient found');
  //   }

  //   const sessionSummaryInfo = await this.getSessionSummaryInfo(
  //     availableOlderPatient?.session_info?.session_id
  //   );

  //   await this.sessionRepository.update({
  //     where: { session_id: availableOlderPatient?.session_info?.session_id },
  //     data: {
  //       status: SessionStatus.HEALING_ROUND,
  //       sub_status: HealingRoundSubStatus.HEALER_ASSIGNED,
  //       sub_status_updated_at: new Date(),
  //       session_start_at: new Date().toISOString(),
  //       updated_at: new Date().toISOString(),
  //       queue_number: null,
  //     },
  //   });

  //   const sessionInfo = await this.sessionRepository.getSessionInfo({
  //     where: { session_id: availableOlderPatient?.session_info?.session_id },
  //   });
  //   return {
  //     //message: 'Healing session started successfully',
  //     summary_info: sessionSummaryInfo,
  //     healer_info: healer,
  //     session_info: sessionInfo,
  //     patient_info: availableOlderPatient?.patient_info,
  //   };
  // }

  async getSessionSummaryInfo(session_id: number) {
    return this.summaryRepository.getLatestSessionSummary({
      where: { session_id },
      orderBy: {
        created_at: 'desc',
      },
    });
  }

  async getHealingRoundByUserId(user_id: number) {
    return this.healingRoundRepository.findOne({
      where: { user_id },
      include: {
        profiles: true,
        // healer: true,
        // patient: true,
        session_summaries: true,
        healing_sessions: true,
      },
    });
  }

  async getSessionById(id: number) {
    const session = this.sessionRepository.findFirst({
      where: {
        session_id: id,
      },
    });

    return session;
  }

  async getSessionByProfileId(profileId: string) {
    const session = await this.sessionRepository.findOneWithInclude({
      where: {
        profile_id: parseInt(profileId),
      },
      include: {
        conversations: true,
        session_summaries: true,
      },
    });

    if (session) {
      if (
        session.status === SessionStatus.HEALING_ROUND &&
        session.sub_status === HealingRoundSubStatus.FEEDBACK_REQUIRED
      ) {
        const healingRounds = await this.healingRoundRepository.findAll({
          where: { session_id: session.session_id },
          orderBy: { round_number: 'asc' },
        });

        return {
          ...session,
          healing_round_data: healingRounds.map((round) => ({
            round_number: round.round_number,
            duration: round.round_end_at
              ? round.round_end_at.getTime() - round?.round_start_at.getTime()
              : null,
            start: round.round_start_at,
            elapsed: round.round_start_at
              ? new Date().getTime() - round.round_start_at.getTime()
              : null,
            remaining: round.round_end_at
              ? round.round_end_at.getTime() - new Date().getTime()
              : null,
          })),
        };
      }
    }

    return session;
  }

  async endSession(sessionId: number, profileId: number) {
    let isHealer = false;
    const session = await this.sessionRepository.findFirst({
      where: {
        session_id: sessionId,
        OR: [
          {
            status: {
              notIn: [
                SessionStatus.CANCELLED,
                SessionStatus.COMPLETED,
                SessionStatus.ABANDONED,
              ],
            },
          },
        ],
      },
    });
    // Only patient or assigned healer can end the session
    if (
      !session ||
      (session?.healer_id !== profileId && session?.profile_id !== profileId)
    ) {
      throw new NotFoundException('Invalid Session');
    }

    if (session.healer_id === profileId) {
      isHealer = true;
    }

    if (session.status === SessionStatus.HEALING_ROUND) {
      const healingRounds = await this.healingRoundRepository.findAll({
        where: {
          session_id: session.session_id,
          status: {
            notIn: [HealingRoundStatus.CANCELLED, HealingRoundStatus.COMPLETED],
          },
        },
      });

      if (healingRounds && healingRounds.length > 0) {
        await this.healingRoundRepository.updateMany({
          where: {
            session_id: sessionId,
            OR: [
              {
                status: {
                  notIn: [
                    HealingRoundStatus.CANCELLED,
                    HealingRoundStatus.COMPLETED,
                  ],
                },
              },
            ],
          },
          data: {
            status: HealingRoundStatus.COMPLETED,
            round_end_at: new Date(),
            updated_at: new Date(),
          },
        });
      }
    }

    let sessionStatus;
    let sessionSubStatus;
    switch (session.status) {
      case SessionStatus.INTAKE:
      case SessionStatus.IN_QUEUE:
        sessionStatus = SessionStatus.CANCELLED;
        break;
      case SessionStatus.HEALING_ROUND:
        sessionStatus = SessionStatus.ABANDONED;
        sessionSubStatus = isHealer
          ? AbandonmentSubStatus.HEALER_ABANDONED
          : AbandonmentSubStatus.PATIENT_ABANDONED;
        break;
    }
    return this.sessionRepository.update({
      where: { session_id: sessionId },
      data: {
        status: sessionStatus,
        sub_status: sessionSubStatus,
        sub_status_updated_at: new Date(),
        session_end_at: new Date(),
      },
    });
  }

  // format minutes to hours and days
  formatMinutes(minutes: number): string {
    if (minutes < 60) {
      return `${minutes} minute${minutes !== 1 ? 's' : ''}`;
    } else if (minutes < 1440) {
      const hours = Math.floor(minutes / 60);
      return `${hours} hour${hours !== 1 ? 's' : ''}`;
    } else {
      const days = Math.floor(minutes / 1440);
      return `${days} day${days !== 1 ? 's' : ''}`;
    }
  }
}
