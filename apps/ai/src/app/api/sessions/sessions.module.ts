import { Module, NestModule, MiddlewareConsumer, Global } from '@nestjs/common';
import { TokenMiddleware } from '@core_be/auth';
import { ConfigService } from '@nestjs/config';
import { SessionController } from './controllers/sessions.controller';
import { HttpModule } from '@nestjs/axios';
import { ConversationService } from '../conversations/services/conversation.service';
import { LibProfileService } from '@core/libs';
import { SessionService } from './services/session.service';
import { ConversationModule } from '../conversations/conversation.module';
import { PrismaService } from '@core/prisma-client';
import { LibSessionService } from '@core_be/global';
import { LibNotificationService } from '@core_be/notifications';
import { PublisherService } from 'libs/global/src/lib/queue/publisher.service';
import { HealerStatusRepository } from 'libs/data-access/src/lib/healer_status/healer_status.repository';

@Global()
@Module({
  imports: [HttpModule, ConversationModule],
  controllers: [SessionController],
  providers: [
    ConfigService,
    ConversationService,
    SessionService,
    LibProfileService,
    PrismaService,
    LibSessionService,
    LibNotificationService,
    PublisherService,
    HealerStatusRepository
  ],
  exports: [SessionService],
})
export class SessionModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(TokenMiddleware).forRoutes(SessionController);
  }
}
