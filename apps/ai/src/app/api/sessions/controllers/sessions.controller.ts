import {
  Body,
  Controller,
  HttpCode,
  Post,
  UseGuards,
  Get,
  Patch,
  Param,
  Query,
  Delete,
  Req,
  NotFoundException,
  ForbiddenException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiBody,
  ApiResponse,
  ApiBearerAuth,
  ApiExcludeEndpoint,
} from '@nestjs/swagger';
import {
  Role,
  Roles,
  RolesGuard,
  LibProfileService,
  SessionRepository,
  AiHealingRoundStartEvent,
  AiHealingRoundCheckInEvent,
  HEALING_ROUND_LISTEN,
} from '@core/libs';
import {
  CreateSessionDto,
  UpdateSessionDto,
  CreateHealingRoundDto,
  UpdateHealingRoundDto,
  QueueConfirmationDto,
  HealingSessionStatusDTo,
  TakeSessionDto,
  UpdateSessionStatusDto,
  HealingSessionEndDto,
  AcceptDeclineSessionDto,
} from '../dto/session.dto';
import { AIHealingRoundService } from '@core_be/aiml';
import { healing_rounds } from '@core/prisma-client';
import { SessionService } from '../services/session.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { LibSessionService, ProfileType } from '@core_be/global';
import { ProfileAccessGuard } from 'libs/global/src/lib/guards/profile-access.guard';
import { ProfileTypes } from 'libs/global/src/lib/decorators/profile-types.decorator';

@ApiTags('Sessions')
@ApiBearerAuth()
@Controller('sessions')
@UseGuards(RolesGuard)
export class SessionController {
  constructor(
    private readonly sessionService: SessionService,
    private readonly libSessionService: LibSessionService,
    private readonly profileService: LibProfileService,
    private readonly sessionRepository: SessionRepository,
    private readonly aiHealingRoundService: AIHealingRoundService,
    private readonly eventEmitter: EventEmitter2
  ) {}

  @Post('')
  @HttpCode(201)
  @ApiBody({ type: CreateSessionDto })
  @Roles(Role.Patient)
  @UseGuards(ProfileAccessGuard)
  @ApiResponse({
    status: 201,
    description: 'Session created successfully',
  })
  async create(@Req() req, @Body() createSessionDto: CreateSessionDto) {
    const { user_id, profiles } = req.raw.user;

    const validateProfile = await this.profileService.validateUserProfile(
      profiles,
      user_id,
      createSessionDto?.profile_id
    );

    if (!validateProfile) {
      throw new NotFoundException('User profile not found!');
    }

    return await this.sessionService.create(createSessionDto, user_id);
  }

  @Get()
  @Roles(Role.Admin)
  @HttpCode(200)
  async findAll(@Req() req, @Query() query) {
    return await this.sessionService.findAll(query);
  }

  @Get(':session_id/:profile_id')
  @Roles(Role.Admin)
  @UseGuards(ProfileAccessGuard)
  @HttpCode(200)
  async findOne(
    @Req() req,
    @Param('session_id') session_id: string,
    @Param('profile_id') profile_id: string
  ) {
    const profileID = ~~profile_id;
    const sessionID = ~~session_id;
    const { user_id, profiles } = req.raw.user;
    let isUserAccessing = false;
    // Validate ownership unless admin
    if (!req.raw.user.roles?.includes(Role.Admin)) {
      isUserAccessing = true;
      const validateProfile = await this.profileService.validateUserProfile(
        profiles,
        user_id,
        profileID
      );
      if (!validateProfile) {
        throw new ForbiddenException(
          'You are not authorized to access this session.'
        );
      }
    }
    return await this.sessionService.findOne(
      sessionID,
      profileID,
      isUserAccessing
    );
  }

  @Patch(':id')
  @Roles(Role.Admin)
  @ApiBody({ type: UpdateSessionDto })
  @HttpCode(200)
  async update(
    @Req() req,
    @Body() updateSessionDto: UpdateSessionDto,
    @Param('session_id') session_id: string
  ) {
    const { user_id } = req.raw.user;
    const sessionID = ~~session_id;
    return await this.libSessionService.update(
      sessionID,
      updateSessionDto,
      user_id
    );
  }

  @Delete(':id')
  @Roles(Role.Admin)
  @HttpCode(200)
  async Delete(@Req() req, @Param('id') session_id: string) {
    const sessionID = ~~session_id;
    return await this.sessionService.softDelete(sessionID);
  }

  @Get('profile/:profile_id')
  @Roles(Role.Admin)
  @HttpCode(200)
  async getAllSessions(@Req() req, @Param('profile_id') profile_id: string) {
    return await this.sessionService.getAllSessions(profile_id);
  }

  // DEPRECATED
  // @Post('take-session')
  // @Roles(Role.Admin, Role.Healer, Role.Patient)
  // @HttpCode(200)
  // async assignHealerToSession(@Body() body: TakeSessionDto) {
  //   return await this.sessionService.assignHealerToSession(body);
  // }

  @Post('healing-round/start')
  @HttpCode(201)
  @Roles(Role.Admin, Role.Healer)
  @UseGuards(ProfileAccessGuard)
  @ProfileTypes(ProfileType.HEALER)
  @ApiBody({ type: CreateHealingRoundDto })
  @ApiResponse({
    status: 201,
    description: 'Healing Rounds created successfully',
  })
  async createHealingRound(
    @Req() req,
    @Body() createRoundInput: CreateHealingRoundDto
  ): Promise<healing_rounds | { message: string }> {
    const { user_id } = req.raw.user;
    createRoundInput.async = false;

    if (createRoundInput.async) {
      this.eventEmitter.emitAsync(
        HEALING_ROUND_LISTEN.HEALING_ROUND_START,
        new AiHealingRoundStartEvent(createRoundInput, user_id, false)
      );
      return { message: 'success' };
    }

    const response = this.aiHealingRoundService.processHealingRoundStart(
      {
        session_id: createRoundInput.session_id,
        profile_id: createRoundInput.profile_id,
      },
      null,
      true
    );
    return response;
  }

  @Post('healing-round/check-in')
  @Roles(Role.Admin, Role.Healer)
  @UseGuards(ProfileAccessGuard)
  @ProfileTypes(ProfileType.HEALER)
  @ApiBody({ type: UpdateHealingRoundDto })
  @HttpCode(200)
  async updateHealingRound(
    @Req() req,
    @Body() updateHealingRoundDto: UpdateHealingRoundDto
  ) {
    updateHealingRoundDto.async = false;
    const updateRoundData = {
      profiles: {
        connect: {
          profile_id: updateHealingRoundDto.profile_id,
        },
      },
      healing_sessions: {
        connect: {
          session_id: updateHealingRoundDto.session_id,
        },
      },
    };
    const { user_id } = req.raw.user;

    if (updateHealingRoundDto?.async) {
      this.eventEmitter.emitAsync(
        HEALING_ROUND_LISTEN.HEALING_ROUND_CHECK_IN,
        new AiHealingRoundCheckInEvent(updateRoundData)
      );

      return { message: 'Check in Success' };
    }

    const result = await this.aiHealingRoundService.processHealingRoundCheckIn(
      new AiHealingRoundCheckInEvent(updateRoundData)
    );
    return result;
  }

  // @Post('queue-confirmation')
  // @Roles(Role.Admin, Role.Healer, Role.Patient)
  // @HttpCode(200)
  // async queueConfirmation(@Body() body: QueueConfirmationDto) {
  //   return await this.sessionService.confirmAndRearrangeQueue(body);
  // }

  @Patch('update-status')
  @Roles(Role.Patient)
  @UseGuards(ProfileAccessGuard)
  @HttpCode(200)
  async updateStatus(@Req() req, @Body() body: UpdateSessionStatusDto) {
    const { session_id, profile_id, status, sub_status } = body;
    const { user_id, profiles } = req.raw.user;
    const validateUserProfile = await this.profileService.validateUserProfile(
      profiles,
      user_id,
      profile_id
    );

    if (!validateUserProfile) {
      throw new NotFoundException('User profile not found.');
    }

    return await this.sessionService.updateSessionStatus(
      session_id,
      profile_id,
      status,
      sub_status
    );
  }

  // @ApiExcludeEndpoint()
  // @Get('healing-profile/:profileId')
  // @Roles(Role.Admin, Role.Healer, Role.Patient)
  // @HttpCode(200)
  // async getSessionByProfileId(@Param('profileId') profileId: string) {
  //   return await this.sessionService.getSessionByProfileId(profileId);
  // }

  // @ApiExcludeEndpoint()
  // @Post(':sessionId/status')
  // async updateSessionStatus(
  //   @Param('sessionId') sessionId: string,
  //   @Body() body: HealingSessionStatusDTo
  // ) {
  //   const sessionIdInteger = ~~sessionId;

  //   return await this.sessionService.updateSessionStatus(
  //     sessionIdInteger,
  //     body.status,
  //     body.sub_status
  //   );
  // }

  @Post('end')
  @Roles(Role.Healer, Role.Patient)
  @UseGuards(ProfileAccessGuard)
  async endSession(@Req() req, @Body() body: HealingSessionEndDto) {
    const { profile_id, session_id } = body;
    return await this.sessionService.endSession(session_id, profile_id);
  }

  @Get('healing-videos')
  @Roles(Role.Admin)
  @HttpCode(200)
  async getHealingVideos() {
    return await this.sessionRepository.getHealingVideos();
  }

  @Post('accept')
  @Roles(Role.Admin, Role.Patient)
  @HttpCode(200)
  @ApiBody({ type: AcceptDeclineSessionDto })
  @ApiResponse({
    status: 200,
    description: 'Patient accepted the session offer successfully',
  })
  async AcceptSession(
    @Req() req,
    @Body() acceptSessionDto: AcceptDeclineSessionDto
  ) {
    const { user_id, profiles } = req.raw.user;
    const userProfileId = profiles[0]?.profile_id;

    const { session_id } = acceptSessionDto;
    const validateProfile = await this.profileService.validateUserProfile(
      profiles,
      user_id,
      acceptSessionDto?.profile_id
    );
    if (!validateProfile) {
      throw new NotFoundException('User profile not found!');
    }
    return this.libSessionService.acceptSessionOffer(userProfileId, session_id);
  }

  @Post('decline')
  @Roles(Role.Admin, Role.Patient)
  @HttpCode(200)
  @ApiBody({ type: AcceptDeclineSessionDto })
  @ApiResponse({
    status: 200,
    description: 'Patient declined the session offer successfully',
  })
  async DeclineSession(
    @Req() req,
    @Body() declineSessionDto: AcceptDeclineSessionDto
  ) {
    const { user_id, profiles } = req.raw.user;
    const userProfileId = profiles[0]?.profile_id;
    const { session_id, trigger_source } = declineSessionDto;
    const validateProfile = await this.profileService.validateUserProfile(
      profiles,
      user_id,
      declineSessionDto?.profile_id
    );
    if (!validateProfile) {
      throw new NotFoundException('User profile not found!');
    }
    return this.libSessionService.declineSessionOffer(
      user_id,
      userProfileId,
      session_id,
      trigger_source
    );
  }
}
