import { Test, TestingModule } from '@nestjs/testing';
import { SessionController } from './sessions.controller';
import { SessionService } from '../services/session.service';
import {
  ConversationRepository,
  HealingRoundStatus,
  LibProfileService,
  SenderType,
  SessionAilmentRepository,
  SessionRepository,
  SessionStatus,
  SummaryRepository,
  UsersDeviceRepository,
} from '@core/libs';
import { BadRequestException } from '@nestjs/common';
import { CreateSessionDto } from '@core/libs';
import { AIMessagingService, LibOpenaiService } from '@core_be/aiml';
import { PrismaService } from '@core/prisma-client';
import { LibFcmService } from '@core_be/notifications';
import { ConfigService } from '@nestjs/config';
import { faker } from '@faker-js/faker';

describe('SessionController', () => {
  let sessionController: SessionController;
  let sessionService: SessionService;
  let profileService: LibProfileService;

  const mockUserId = 1;
  const mockDto: CreateSessionDto = { profile_id: 1 };

  const mockSessionService = {
    create: jest.fn(),
    findAll: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    softDelete: jest.fn(),
  };

  const mockProfileService = {
    validateUserProfile: jest.fn(),
  };

  const mockOpenAiService = {
    someMethod: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [SessionController],
      providers: [
        { provide: SessionService, useValue: mockSessionService },
        { provide: LibProfileService, useValue: mockProfileService },
        { provide: LibOpenaiService, useValue: mockOpenAiService },
        AIMessagingService,
        ConversationRepository,
        PrismaService,
        LibFcmService,
        ConfigService,
        SessionAilmentRepository,
        SummaryRepository,
        UsersDeviceRepository,
        SessionRepository,
      ],
    }).compile();

    sessionController = module.get<SessionController>(SessionController);
    sessionService = module.get<SessionService>(SessionService);
    profileService = module.get<LibProfileService>(LibProfileService);
  });

  describe('create', () => {
    it('should create a session successfully', async () => {
      mockProfileService.validateUserProfile.mockResolvedValueOnce(true);
      mockSessionService.create.mockResolvedValueOnce({ session_id: 123 });

      const result = await sessionController.create(
        { raw: { user: { user_id: mockUserId, profiles: [] } } } as any,
        mockDto
      );

      expect(profileService.validateUserProfile).toHaveBeenCalledWith(
        [],
        mockUserId,
        mockDto.profile_id
      );
      expect(sessionService.create).toHaveBeenCalledWith(mockDto, mockUserId);
      expect(result).toEqual({ session_id: 123 });
    });
    it('should throw BadRequestException if session creation fails', async () => {
      mockProfileService.validateUserProfile.mockResolvedValueOnce(true);
      mockSessionService.create.mockRejectedValueOnce(
        new BadRequestException('Some error occurred')
      );

      await expect(
        sessionController.create(
          { raw: { user: { user_id: mockUserId, profiles: [] } } } as any,
          mockDto
        )
      ).rejects.toThrow(BadRequestException);

      expect(profileService.validateUserProfile).toHaveBeenCalledWith(
        [],
        mockUserId,
        mockDto.profile_id
      );
      expect(sessionService.create).toHaveBeenCalledWith(mockDto, mockUserId);
    });
  });

  describe('findAll', () => {
    it('should return an array of Sessions', async () => {
      const mockSessions = [
        {
          session_id: 1,
          profile_id: 1,
          healer_id: null,
          subscription_id: null,
          thread_id: 'thread123',
          session_start_at: new Date(),
          session_end_at: null,
          status: SessionStatus.INTAKE,
          sub_status: null,
          sub_status_updated_at: new Date(),
          queue_number: null,
          queue_start_time: null,
          satisfaction_score: null,
          is_satisfied: null,
          is_follow_up: null,
          created_by: null,
          created_at: new Date(),
          updated_by: null,
          updated_at: new Date(),
          deleted_by: null,
          deleted_at: null,
          is_deleted: false,
        },
      ];

      const query = {
        sortBy: 'created_at',
        order: 'desc',
        page: 1,
        limit: 10,
      };

      const mockReq = {
        raw: {
          user: {
            user_id: 1,
            profiles: [],
          },
        },
      };

      jest.spyOn(sessionService, 'findAll').mockResolvedValueOnce(mockSessions);

      const result = await sessionController.findAll(mockReq, query);

      expect(sessionService.findAll).toHaveBeenCalledWith(query);
      expect(result).toEqual(mockSessions);
    });
  });

  describe('findAll', () => {
    it('should return an array of Sessions', async () => {
      const mockSessions: any = [
        {
          session_id: 1,
          profile_id: 1,
          healer_id: null,
          subscription_id: null,
          thread_id: faker.string.alpha(8),
          session_start_at: new Date(),
          session_end_at: null,
          status: SessionStatus.INTAKE,
          sub_status: null,
          sub_status_updated_at: new Date(),
          queue_number: null,
          queue_start_time: null,
          satisfaction_score: null,
          is_satisfied: null,
          is_follow_up: null,
          created_by: null,
          created_at: new Date(),
          updated_by: null,
          updated_at: new Date(),
          deleted_by: null,
          deleted_at: null,
          is_deleted: false,
        },
      ];

      const query = {
        sortBy: 'created_at',
        order: 'desc',
        page: 1,
        limit: 10,
      };

      const mockReq = {
        raw: {
          user: {
            user_id: 1,
            profiles: [],
          },
        },
      };

      jest.spyOn(sessionService, 'findAll').mockResolvedValueOnce(mockSessions);

      const result = await sessionController.findAll(mockReq, query);

      expect(sessionService.findAll).toHaveBeenCalledWith(query);
      expect(result).toEqual(mockSessions);
    });
  });

  describe('findOne', () => {
    it('should return the session details with associated data', async () => {
      const session_id = '1';
      const profile_id = '1';

      const mockSessionData: any = {
        session_id: 1,
        profile_id: 1,
        subscription_id: null,
        thread_id: faker.string.alpha(8),
        session_start_at: new Date(),
        session_end_at: null,
        status: SessionStatus.ABANDONED,
        sub_status: null,
        created_by: null,
        sub_status_updated_at: new Date(),
        summary_info: null,
        session_queue_info: null,
        healing_round: {
          round_id: 1,
          session_id: 1,
          healer_id: 1,
          summary_id: 1,
          round_number: null,
          round_start_at: new Date(),
          round_end_at: null,
          status: null,
          check_in_count: 3,
          max_time: 300000,
          is_positive_feedback: null,
          is_patient_confirmed: false,
          is_healer_confirmed: false,
          last_round_feedback: null,
        },
        conversations: [
          {
            conversation_id: 1,
            profile_id: 1,
            model_id: null,
            session_id: 1,
            round_id: 1,
            summary_id: null,
            follow_up_id: null,
            sender_type: SenderType.Patient,
            message_type: null,
            content: faker.lorem.sentence(),
            file_name: null,
            metadata: null,
            created_at: new Date(),
            updated_at: null,
            deleted_at: null,
            is_deleted: false,
          },
          {
            conversation_id: 1,
            profile_id: 1,
            model_id: null,
            session_id: 1,
            round_id: 1,
            summary_id: null,
            follow_up_id: null,
            sender_type: SenderType.AI,
            message_type: null,
            content: faker.lorem.sentence(),
            file_name: null,
            metadata: null,
            created_at: new Date(),
            updated_at: null,
            deleted_at: null,
            is_deleted: false,
          },
        ],
        ailments: [],
      };

      jest.spyOn(sessionService, 'findOne').mockResolvedValue(mockSessionData);

      const mockReq = {
        user: {
          user_id: 1,
          profiles: [],
        },
      };

      const result = await sessionController.findOne(
        mockReq,
        session_id,
        profile_id
      );

      expect(sessionService.findOne).toHaveBeenCalledWith(1, 1);

      expect(result).toEqual(mockSessionData);
    });
  });

  describe('update', () => {
    it('should update a session successfully', async () => {
      const session_id = '1';
      const user_id = 10;
      const updateSessionDto = {
        profile_id: 2,
        is_follow_up: true,
        status: HealingRoundStatus.IN_PROGRESS,
      };

      const mockSessions = {
        session_id: 1,
        profile_id: 1,
        healer_id: null,
        subscription_id: null,
        thread_id: faker.string.alpha(8),
        session_start_at: new Date(),
        session_end_at: null,
        status: SessionStatus.INTAKE,
        sub_status: null,
        sub_status_updated_at: new Date(),
        queue_number: null,
        queue_start_time: null,
        satisfaction_score: null,
        is_satisfied: null,
        is_follow_up: null,
        created_by: null,
        created_at: new Date(),
        updated_by: null,
        updated_at: new Date(),
        deleted_by: null,
        deleted_at: null,
        is_deleted: false,
      };

      jest.spyOn(mockSessionService, 'update').mockResolvedValue(mockSessions);

      const mockReq = {
        raw: {
          user: {
            user_id,
          },
        },
      };

      const result = await sessionController.update(
        mockReq,
        updateSessionDto,
        session_id
      );

      expect(mockSessionService.update).toHaveBeenCalledWith(
        1,
        updateSessionDto,
        user_id
      );
      expect(result).toEqual(mockSessions);
    });
  });

  describe('softDelete', () => {
    it('should soft delete a session successfully', async () => {
      const session_id = '1';

      jest.spyOn(mockSessionService, 'softDelete').mockResolvedValue(1);

      const result = await sessionController.Delete({}, session_id);

      expect(mockSessionService.softDelete).toHaveBeenCalledWith(1);
      expect(result).toEqual(1);
    });
  });
});
