import { InternalServerErrorException, Module } from '@nestjs/common';
import { HumeModule } from './hume/hume.module';
import * as Jo<PERSON> from 'joi';
import { ConfigModule } from '@nestjs/config';
import { DatadogTraceModule } from 'nestjs-ddtrace';
import { LoggerModule } from 'nestjs-pino';
import { LibDataAccessModule } from '@core_be/data-access';
import { LibGlobalModule } from '@core/libs';
import { JwtGuard, JwtStrategy, LibAuthModule } from '@core_be/auth';
import { ConversationModule } from './conversations/conversation.module';
import { SessionModule } from './sessions/sessions.module';
import { LibNotificationsModule } from '@core_be/notifications';
import { APP_GUARD } from '@nestjs/core';
import { HealerModule } from './healer/healer.module';
import { PatientModule } from './patient/patient.module';
import { WsModule } from '../ws/ws.module';
import { LibAimlModule } from '@core_be/aiml';
import hyperid from 'hyperid';

import { Options } from 'pino-http';
import { DestinationStream } from 'pino';
import { AppController } from './app.controller';
import { AppService } from './app.service';

const instance = hyperid(true);
const pinoConfig: Options | DestinationStream | [Options, DestinationStream] = {
  customSuccessMessage: () => '',
  customErrorMessage: () => '',
  level: 'info',
  timestamp: true,
  genReqId: () => instance.uuid, // BUG: currently UUID is not being set in the request id
  transport: {
    target: 'pino-pretty',
    options: {
      json: true,
      colorize: false,
      singleLine: true,
      translateTime: 'SYS:standard',
      ignore: 'pid,hostname',
    },
  },
  serializers: {
    req: (req) => ({
      id: req.id,
      method: req.method,
      url: req.url,
      remoteAddress: req.remoteAddress,
      remotePort: req.remotePort,
      userAgent: req.headers['user-agent'],
    }),
    res: (res) => ({
      statusCode: res.statusCode,
    }),
    err: (err) => ({
      type: err.constructor.name,
      message: err.message,
      stack: err.stack,
      ...(err.code && { code: err.code }),
      ...(err.statusCode && { statusCode: err.statusCode }),
    }),
    // Custom serializer for any object - ensures JSON objects are properly logged
    data: (data) => {
      if (typeof data === 'object' && data !== null) {
        try {
          return JSON.parse(JSON.stringify(data)); // Ensures proper JSON serialization
        } catch {
          return data;
        }
      }
      return data;
    },
  },
  formatters: {
    level: (label: string) => {
      return { level: label };
    },
    log: (object: any) => {
      // Ensure JSON objects are properly formatted
      if (typeof object === 'object' && object !== null) {
        return object;
      }
      return { message: object };
    },
  },
};

if (!process.env.NODE_ENV) {
  throw new InternalServerErrorException(
    'NODE_ENV is not set. Expected values are "local" or "development" or "production"'
  );
}

if (
  ['development', 'local'].includes(process.env.NODE_ENV.trim().toLowerCase())
) {
  pinoConfig.level = 'debug';
  pinoConfig.transport.options.colorize = true;
}

@Module({
  imports: [
    WsModule,
    LibDataAccessModule,
    LibGlobalModule,
    LibAuthModule,
    LoggerModule.forRoot({
      pinoHttp: pinoConfig,
    }),
    DatadogTraceModule.forRoot(),
    HumeModule,
    LibNotificationsModule,
    ConversationModule,
    SessionModule,
    LibAimlModule,
    ConfigModule.forRoot({
      isGlobal: true,
      validationSchema: Joi.object({
        AI_PORT: Joi.string().required(),
        DATABASE_URL: Joi.string().required(),
        JWT_SECRET: Joi.string().required(),
        JWT_EXPIRATION: Joi.string().required(),
        JWT_ALGORITHM: Joi.string().required(),
        OPENAI_URL: Joi.string().required(),
        OPENAI_TOKEN: Joi.string().required(),
        // HUME_API_KEY: Joi.string().required(), Not in use
        // HUME_CLIENT_SECRET: Joi.string().required(), Not in use
        SENDGRID_API_KEY: Joi.string().required(),
        SENDGRID_FROM_EMAIL: Joi.string().required(),
        RABBITMQ_URL: Joi.string().required(),
        ELEVENLABS_API_KEY: Joi.string().required(),
        ELEVENLABS_INTAKE_AGENT_ID: Joi.string().required(),
        ELEVENLABS_REPORT_AGENT_ID: Joi.string().required(),
        ELEVENLABS_CHECK_IN_AGENT_ID: Joi.string().required(),
      }),
    }),
    HealerModule,
    PatientModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    JwtStrategy,
    {
      provide: APP_GUARD,
      useClass: JwtGuard,
    },
  ],
})
export class AppModule {}
