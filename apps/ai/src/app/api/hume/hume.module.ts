import { Module, NestModule, MiddlewareConsumer } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { LibHumeService } from '@core_be/aiml';
import { HumeController } from './hume.controller';
import { ConfigService } from '@nestjs/config';
import { TokenMiddleware } from '@core_be/auth';
import { PrismaService } from '@core/prisma-client';
import { LibDataAccessModule } from '@core_be/data-access';

@Module({
  imports: [HttpModule, LibDataAccessModule],
  controllers: [HumeController],
  providers: [LibHumeService, ConfigService, PrismaService],
})
export class HumeModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(TokenMiddleware).forRoutes(HumeController);
  }
}
