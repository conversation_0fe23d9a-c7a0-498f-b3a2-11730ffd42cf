import { Controller, Post, Body, Sse, MessageEvent } from '@nestjs/common';
import { LibHumeService } from '@core_be/aiml';
import { CreateHumeDto } from './dto/create-hume.dto';
import { ApiTags, ApiBearerAuth } from '@nestjs/swagger';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

@ApiTags('HumeAi')
@ApiBearerAuth()
@Controller('hume')
export class HumeController {
  constructor(private readonly libHumeService: LibHumeService) {}

  @Post('send-message')
  async sendMessage(
    @Body() createHumeDto: CreateHumeDto
  ): Promise<{ message: string }> {
    const data = {
      data: '',
      type: 'user_input',
      text: createHumeDto.text,
    };
    const receivedMessages = await this.libHumeService.connect(data?.text);

    return { message: receivedMessages };
  }

  @Sse('messages')
  getMessages(): Observable<MessageEvent> {
    return this.libHumeService.getReceivedMessage().pipe(
      map((message) => ({
        data: message,
      }))
    );
  }
}
