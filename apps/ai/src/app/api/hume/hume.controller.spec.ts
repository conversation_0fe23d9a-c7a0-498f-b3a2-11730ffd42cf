import { Test, TestingModule } from '@nestjs/testing';
import { <PERSON><PERSON><PERSON>roll<PERSON> } from './hume.controller';
import { LibHumeService } from '@core_be/aiml';

describe('HumeController', () => {
  let controller: HumeController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [HumeController],
      providers: [LibHumeService],
    }).compile();

    controller = module.get<HumeController>(HumeController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
