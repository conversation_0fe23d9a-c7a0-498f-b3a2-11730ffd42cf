import { Module, NestModule, MiddlewareConsumer } from '@nestjs/common';
import { TokenMiddleware } from '@core_be/auth';
import { ConfigService } from '@nestjs/config';
import { ConversationController } from './controllers/conversation.controller';
import { ConversationService } from './services/conversation.service';
import { HttpModule } from '@nestjs/axios';
import { LibFcmService, LibNotificationService } from '@core_be/notifications';
import {} from '@nestjs/microservices';
import {
  LibDataAccessModule,
  LibGlobalModule,
  LibProfileService,
  LibSessionService,
  MeasurePerformance,
} from '@core/libs';
import { PublisherService } from 'libs/global/src/lib/queue/publisher.service';
import { HealerStatusRepository } from 'libs/data-access/src/lib/healer_status/healer_status.repository';
import { ElevenLabsService } from 'libs/global/src/lib/services/elevenlabs.service';
@Module({
  imports: [LibDataAccessModule, HttpModule, LibGlobalModule],
  controllers: [ConversationController],
  providers: [
    LibSessionService,
    ConversationService,
    ConfigService,
    LibFcmService,
    LibProfileService,
    LibNotificationService,
    PublisherService,
    HealerStatusRepository,
    ElevenLabsService,
    MeasurePerformance,
  ],
})
export class ConversationModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(TokenMiddleware).forRoutes(ConversationController);
  }
}
