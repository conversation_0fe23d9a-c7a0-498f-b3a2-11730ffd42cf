import {
  BadRequestException,
  Injectable,
  NotImplementedException,
  NotFoundException,
  ForbiddenException,
  InternalServerErrorException,
  HttpException,
} from '@nestjs/common';
import { Prisma, conversations, healing_sessions } from '@core/prisma-client';
import {
  ConversationRepository,
  HealingRoundRepository,
  SessionAilmentRepository,
  SessionRepository,
  SortOrderDto,
  SummaryRepository,
} from '@core/libs';
import {
  ConversationStatus,
  ConversationType,
  HealingRoundStatus,
  LibSessionService,
  MessageType,
  SenderType,
  SessionSubStatusType,
  SessionStatus,
  AI_MESSAGE_INCOMING_TRIGGER,
} from '@core_be/global';
import {
  ConversationResponseDto,
  ConversationsQueryDto,
  CreateBulkConversationDto,
} from '../dto/conversation.dto';
import { SessionDto } from '../../sessions/dto/session.dto';
import {
  AIHealingRoundService,
  LibAIIntakeService,
  SummaryAilmentService,
  ContentFilterException,
} from '@core_be/aiml';
import _ from 'lodash';
import { Logger } from 'nestjs-pino';

@Injectable()
export class ConversationService {
  constructor(
    private readonly conversationRepository: ConversationRepository,
    private readonly sessionRepository: SessionRepository,
    private readonly libSessionService: LibSessionService,
    private readonly summaryRepository: SummaryRepository,
    private readonly aiIntakeService: LibAIIntakeService,
    private readonly summaryAilmentService: SummaryAilmentService,
    private readonly aiHealingRoundService: AIHealingRoundService,
    private readonly sessionAilmentRepository: SessionAilmentRepository,
    private readonly healingRoundRepository: HealingRoundRepository,
    private readonly logger: Logger
  ) {}

  async createConversationRecord(
    data: Prisma.conversationsCreateInput
  ): Promise<conversations> {
    return this.conversationRepository.create(data);
  }

  /*
   * This service is to handle conversation triggered from the bulk conversation API,
   * which is primarily used for handling voice input from the frontend.
   *
   * Text-based conversation transcript from the single conversation API is handled by the
   * ConversationListenerService in libs/aiml/src/shared/conversation-listener.service.ts
   */
  async createBulkConversation(
    bulkConversationDto: CreateBulkConversationDto
  ): Promise<conversations[]> {
    const startTime = Date.now();

    try {
      const sessionId = bulkConversationDto.session_id;
      const profileId = bulkConversationDto.profile_id;

      // Log method entry with request context
      this.logger.log({
        timestamp: new Date().toISOString(),
        event: 'BULK_CONVERSATION_START',
        session_id: sessionId,
        profile_id: profileId,
        conversation_type: bulkConversationDto.conversation_type,
        conversation_status: bulkConversationDto.conversation_status,
        message: 'Starting bulk conversation processing',
        elapsed_time: Date.now() - startTime,
      });

      // Find the session from the session_id
      const session: healing_sessions =
        await this.libSessionService.findFirstSession({
          session_id: sessionId,
        });

      this.logger.debug({
        timestamp: new Date().toISOString(),
        event: 'BULK_CONVERSATION_SESSION_DETAILS',
        session_id: sessionId,
        profile_id: profileId,
        message: 'Obtained session details',
        elapsed_time: Date.now() - startTime,
      });

      if (!session) {
        const errorMessage = 'session not found';
        this.logger.error({
          timestamp: new Date().toISOString(),
          event: 'BULK_CONVERSATION_SESSION_NOT_FOUND',
          session_id: sessionId,
          profile_id: profileId,
          message: errorMessage,
          elapsed_time: Date.now() - startTime,
        });
        throw new NotFoundException(errorMessage);
      } else if (session.profile_id !== profileId) {
        const errorMessage = 'profile id mismatch';
        this.logger.error({
          timestamp: new Date().toISOString(),
          event: 'BULK_CONVERSATION_PROFILE_MISMATCH',
          session_id: sessionId,
          profile_id: profileId,
          message: errorMessage,
          elapsed_time: Date.now() - startTime,
        });
        throw new ForbiddenException(errorMessage);
      }

      const sessionStatus = session.status;
      const sessionSubStatus = session.sub_status;

      /* Get the conversation type and status from the API Input
       * If not provided, it is assuming to be a conversation with in-take completed
       */
      const conversationType =
        bulkConversationDto.conversation_type || ConversationType.INTAKE;

      const conversationStatus =
        bulkConversationDto.conversation_status || ConversationStatus.COMPLETED;

      this.logger.debug({
        timestamp: new Date().toISOString(),
        event: 'BULK_CONVERSATION_SESSION_AND_CONVERSATION_DETAILS',
        session_id: sessionId,
        profile_id: profileId,
        status: {
          conversation: conversationType,
          session: sessionStatus,
        },
        sub_status: {
          conversation: conversationStatus,
          session: sessionSubStatus,
        },
        message: 'Obtained session and conversation details',
        elapsed_time: Date.now() - startTime,
      });

      // Starting from this point, the conversation is stored in the database
      // TODO: Improve Session State Validation
      /**
       * Update the conversation table with the conversation history
       * This is to store the conversation history for the patient
       */
      const conversations = await this.conversationRepository.createMany({
        data: bulkConversationDto.conversation.map((conversation) => ({
          content: conversation.content,
          sender_type: conversation.sender_type,
          profile_id: bulkConversationDto.profile_id,
          session_id: session.session_id,
          round_id: bulkConversationDto.round_id,
          message_type: conversation.message_type || MessageType.Text,
          created_at: new Date(),
          responded_at: conversation.responded_at,
        })),
        skipDuplicates: false, // Optional: skips duplicate records
      });

      if (!conversations) {
        const errorMessage = 'failed to save bulk conversation records';
        this.logger.error({
          timestamp: new Date().toISOString(),
          event: 'BULK_CONVERSATION_SAVE_FAILED',
          session_id: sessionId,
          profile_id: profileId,
          message: errorMessage,
          elapsed_time: Date.now() - startTime,
        });
        throw new InternalServerErrorException(errorMessage);
      }

      this.logger.debug({
        timestamp: new Date().toISOString(),
        event: 'BULK_CONVERSATION_SAVE_SUCCESS',
        session_id: sessionId,
        profile_id: profileId,
        message: 'Bulk conversation records saved successfully',
        elapsed_time: Date.now() - startTime,
      });

      /*
       * Transform the conversation history from object to string
       * and distinguish between the user's message and AI's message
       */
      const patientInfo: string[] = _.chain(bulkConversationDto.conversation)
        .orderBy(['responded_at'], ['asc'])
        .map((conversation) =>
          conversation.sender_type === SenderType.AI
            ? `Question: ${conversation.content}`
            : `Answer: ${conversation.content}`
        )
        .value();

      const conversationHistory: {
        role: 'user' | 'assistant';
        content: string;
      }[] = _.chain(bulkConversationDto.conversation)
        .orderBy(['responded_at'], ['asc'])
        .map((conversation) => ({
          role:
            conversation.sender_type === SenderType.AI ? 'assistant' : 'user',
          content: conversation.content,
        }))
        .value();

      if (
        conversationType === ConversationType.INTAKE &&
        sessionStatus === SessionStatus.INTAKE
      ) {
        /*
         * Handling intake conversation
         * Case: FLOW-INTAKE-*
         * sessionStatus === SessionStatus.INTAKE &&
         * conversationType === ConversationType.INTAKE &&
         * conversationStatus === ConversationStatus.COMPLETED
         */
        if (conversationStatus === ConversationStatus.COMPLETED) {
          this.logger.log({
            timestamp: new Date().toISOString(),
            event: 'BULK_CONVERSATION_INTAKE_COMPLETED_START',
            session_id: sessionId,
            profile_id: profileId,
            message: 'Processing completed intake conversation',
            elapsed_time: Date.now() - startTime,
          });

          /*
           * INTAKE - COMPLETED
           * When conversation status is completed, it is ready for summary extraction
           * and move the patient to the queue.
           *
           * Since the whole voice conversation was executed in FE, BE need to extract the summary
           * Create Intake summary if the conversation status is completed
           */
          try {
            const sanitizedResponse =
              await this.aiIntakeService.generateIntakeSummary(
                patientInfo,
                conversationStatus,
                session
              );

            await this.summaryAilmentService.createSummaryAndAilments(
              sanitizedResponse,
              sessionId,
              session,
              undefined,
              undefined
            );

            await this.aiIntakeService.enqueueHealingSession(
              session,
              profileId
            );

            this.logger.log({
              timestamp: new Date().toISOString(),
              event: 'BULK_CONVERSATION_INTAKE_COMPLETED_SUCCESS',
              session_id: sessionId,
              profile_id: profileId,
              message: 'Intake conversation completed and session enqueued',
              elapsed_time: Date.now() - startTime,
            });
          } catch (error) {
            if (error instanceof ContentFilterException) {
              this.logger.warn({
                timestamp: new Date().toISOString(),
                event: 'BULK_CONVERSATION_INTAKE_CONTENT_FILTER_ERROR',
                session_id: sessionId,
                profile_id: profileId,
                message: 'Content filter triggered during intake processing',
                elapsed_time: Date.now() - startTime,
              });
              throw new BadRequestException('failed due to content filter');
            } else {
              this.logger.error({
                timestamp: new Date().toISOString(),
                event: 'BULK_CONVERSATION_INTAKE_COMPLETED_ERROR',
                session_id: sessionId,
                profile_id: profileId,
                error_message:
                  error instanceof Error ? error.message : 'unknown error',
                message: 'Failed to process completed intake conversation',
                elapsed_time: Date.now() - startTime,
              });
              throw new InternalServerErrorException(
                'failed to process completed intake conversation'
              );
            }
          }
        } else if (conversationStatus === ConversationStatus.IN_PROGRESS) {
          /**
           * INTAKE - IN_PROGRESS
           * sessionStatus === SessionStatus.INTAKE &&
           * conversationType === ConversationType.INTAKE &&
           * conversationStatus === ConversationStatus.IN_PROGRESS
           * If Conversation Status is not completed,
           * This is for switching from voice to text chat, provide the context to AI assistant and continue chat with BE /conversations endpoint
           * This is for storing the existing conversation history in the database
           */
          try {
            this.logger.log({
              timestamp: new Date().toISOString(),
              event: 'BULK_CONVERSATION_INTAKE_IN_PROGRESS_START',
              session_id: sessionId,
              profile_id: profileId,
              message: 'Processing in-progress intake conversation',
              elapsed_time: Date.now() - startTime,
            });

            await this.aiIntakeService.ingestIntakeConversationHistory(
              session,
              conversationHistory
            );

            this.logger.log({
              timestamp: new Date().toISOString(),
              event: 'BULK_CONVERSATION_INTAKE_IN_PROGRESS_SUCCESS',
              session_id: sessionId,
              profile_id: profileId,
              message: 'In-progress intake conversation processed successfully',
              elapsed_time: Date.now() - startTime,
            });
          } catch (error) {
            const errorContext = {
              timestamp: new Date().toISOString(),
              event: 'BULK_CONVERSATION_INTAKE_IN_PROGRESS_ERROR',
              session_id: sessionId,
              profile_id: profileId,
              conversation_type: conversationType,
              conversation_status: conversationStatus,
              error_name: error instanceof Error ? error.name : 'UnknownError',
              error_message:
                error instanceof Error ? error.message : 'unknown error',
              message: 'Failed to process in-progress intake conversation',
              elapsed_time: Date.now() - startTime,
            };

            if (error instanceof ContentFilterException) {
              errorContext.event =
                'BULK_CONVERSATION_INTAKE_IN_PROGRESS_CONTENT_FILTER_ERROR';
              errorContext.message =
                'Content filter triggered during in-progress intake processing';
              this.logger.warn(errorContext);
              throw new BadRequestException('failed due to content filter');
            } else {
              this.logger.error(errorContext);
              throw new InternalServerErrorException(
                'failed to process in-progress intake conversation'
              );
            }
          }
        } else {
          this.logger.error({
            timestamp: new Date().toISOString(),
            event: 'BULK_CONVERSATION_INTAKE_INVALID_STATUS',
            session_id: sessionId,
            profile_id: profileId,
            conversation_type: conversationType,
            conversation_status: conversationStatus,
            message: 'Invalid conversation status for intake flow',
            elapsed_time: Date.now() - startTime,
            error_type: 'VALIDATION_ERROR',
          });
          throw new BadRequestException('invalid conversation status.');
        }
      } else if (
        conversationType === ConversationType.HEALING_ROUND_CHECK_IN &&
        sessionStatus === SessionStatus.HEALING_ROUND &&
        sessionSubStatus === SessionSubStatusType.FEEDBACK_REQUIRED
      ) {
        /**
         * Healing round check-in conversation
         * Case: FLOW-HEALING-ROUND-FEEDBACK-REQUIRED
         * sessionStatus === SessionStatus.HEALING_ROUND &&
         * sessionSubStatus === SessionSubStatusType.FEEDBACK_REQUIRED &&
         * conversationType === ConversationType.HEALING_ROUND_CHECK_IN &&
         */
        this.logger.log({
          timestamp: new Date().toISOString(),
          event: 'BULK_CONVERSATION_HEALING_ROUND_FLOW_START',
          session_id: sessionId,
          profile_id: profileId,
          conversation_type: conversationType,
          conversation_status: conversationStatus,
          session_status: sessionStatus,
          session_sub_status: sessionSubStatus,
          round_id: bulkConversationDto.round_id,
          message:
            'Starting healing round check-in conversation flow processing',
          elapsed_time: Date.now() - startTime,
        });

        /**
         * Retrieve healing round details from the database
         */
        const roundId = bulkConversationDto.round_id;
        const round = await this.healingRoundRepository.findUnique({
          where: { round_id: roundId, is_deleted: false },
        });
        this.logger.debug({
          timestamp: new Date().toISOString(),
          event: 'BULK_CONVERSATION_HEALING_ROUND_DETAILS',
          session_id: sessionId,
          profile_id: profileId,
          round_id: roundId,
          message: 'Obtained healing round details',
          elapsed_time: Date.now() - startTime,
        });

        const roundStatus = round?.status || null;

        if (
          roundStatus === null ||
          roundStatus !== HealingRoundStatus.FEEDBACK_REQUIRED
        ) {
          /**
           * sessionStatus === SessionStatus.HEALING_ROUND &&
           * sessionSubStatus === SessionSubStatusType.FEEDBACK_REQUIRED &&
           * conversationType === ConversationType.HEALING_ROUND_CHECK_IN &&
           * roundStatus !== HealingRoundStatus.FEEDBACK_REQUIRED
           */
          this.logger.error({
            timestamp: new Date().toISOString(),
            event: 'BULK_CONVERSATION_HEALING_ROUND_INVALID_STATUS',
            session_id: sessionId,
            profile_id: profileId,
            round_id: roundId,
            expected_status: HealingRoundStatus.FEEDBACK_REQUIRED,
            actual_status: roundStatus,
            conversation_type: conversationType,
            message: 'Invalid healing round status for check-in conversation',
            error_type: 'VALIDATION_ERROR',
            elapsed_time: Date.now() - startTime,
          });
          throw new BadRequestException('Invalid healing round status.');
        }

        this.logger.debug({
          timestamp: new Date().toISOString(),
          event: 'BULK_CONVERSATION_HEALING_ROUND_VALIDATED',
          session_id: sessionId,
          profile_id: profileId,
          round_id: roundId,
          round_status: round.status,
          check_in_count: round.check_in_count,
          conversation_type: conversationType,
          message:
            'Healing round validation successful, proceeding with check-in processing',
          elapsed_time: Date.now() - startTime,
        });

        /**
         * Retrieve latest session summary from the database
         */
        const patientSummaryDocument =
          await this.summaryRepository.getLatestSessionSummary({
            where: { session_id: session.session_id },
            orderBy: {
              created_at: 'desc',
            },
          });
        const summaryContent = patientSummaryDocument?.content;
        this.logger.debug({
          timestamp: new Date().toISOString(),
          event: 'BULK_CONVERSATION_HEALING_ROUND_PATIENT_SUMMARY_DETAILS',
          session_id: sessionId,
          profile_id: profileId,
          round_id: roundId,
          message: 'Obtained patient summary',
          elapsed_time: Date.now() - startTime,
        });

        if (conversationStatus === ConversationStatus.COMPLETED) {
          /**
           * When healing round is complete, create summary and ailments
           * Execute healing round thread to generate the AI response
           *
           * sessionStatus === SessionStatus.HEALING_ROUND &&
           * sessionSubStatus === SessionSubStatusType.FEEDBACK_REQUIRED &&
           * conversationType === ConversationType.HEALING_ROUND_CHECK_IN &&
           * conversationStatus === ConversationStatus.COMPLETED
           */
          try {
            this.logger.log({
              timestamp: new Date().toISOString(),
              event: 'BULK_CONVERSATION_HEALING_ROUND_COMPLETED_START',
              session_id: sessionId,
              profile_id: profileId,
              round_id: roundId,
              conversation_type: conversationType,
              conversation_status: conversationStatus,
              check_in_count: round.check_in_count,
              assistant_thread_id: round.assistant_thread_id,
              message:
                'Processing completed healing round check-in conversation',
              elapsed_time: Date.now() - startTime,
            });

            const { assistantResp } =
              await this.aiHealingRoundService.executeHealingRoundThread({
                sessionId,
                healing_round_id: roundId,
                patientInfo,
                assistant_thread_id: round.assistant_thread_id,
                summaryContent,
                conversationStatus,
                conversationType,
                wsEventOptions: undefined,
                region: round.cloud_region,
              });

            /**
             * Remove the healing round completed trigger from the AI response
             * Regex used: __ENERGY_HEALING_ROUND_COMPLETED__\s*
             */
            const regex = new RegExp(
              `${AI_MESSAGE_INCOMING_TRIGGER.HEALING_ROUND_COMPLETED}\\s*`,
              'g'
            );
            const aiResponse =
              assistantResp?.output.replace(regex, '').trim() || '';

            const ailmentResponse =
              await this.summaryAilmentService.createSummaryAndAilments(
                aiResponse,
                sessionId,
                session,
                roundId,
                summaryContent
              );

            /**
             * Round is successful if the patient has no pain or discomfort
             *   or
             * if reported pain level is lower than the previous round, handled in compareRoundFeedback
             *   * If there is no previous round, the round is considered to be failure
             */

            const improvedFeedback =
              await this.sessionAilmentRepository.compareRoundFeedback(
                session.session_id,
                +bulkConversationDto.round_id
              );
            const roundSuccess =
              ailmentResponse.is_zero_pain || improvedFeedback;

            const currDate = new Date();

            const inProgressCompleted =
              round.feedback_start_at?.getTime() || currDate.getTime();

            const inProgressElapsedTime =
              inProgressCompleted - round.round_start_at.getTime();

            const roundTimeUp = inProgressElapsedTime > round.max_time;

            const canHealerContinue =
              roundSuccess || (!roundTimeUp && round.check_in_count > 1);

            /**
             * Session Status is completed if
             *   * There is no pain or discomfort
             *   * The healer cannot continue, due to,
             *     * Pain level has decreased compared to the previous round
             *     * Elapsed Time > Max Time
             *     * It has already been checked more than once
             */
            const nextSessionStatus =
              ailmentResponse.is_zero_pain || !canHealerContinue
                ? SessionStatus.COMPLETED
                : sessionStatus;

            // Log comprehensive business metrics for healing round results
            this.logger.log({
              timestamp: new Date().toISOString(),
              event: 'BULK_CONVERSATION_HEALING_ROUND_BUSINESS_METRICS',
              session_id: sessionId,
              profile_id: profileId,
              round_id: roundId,
              conversation_type: conversationType,
              conversation_status: conversationStatus,
              business_metrics: {
                zero_pain: ailmentResponse.is_zero_pain,
                improved_feedback: improvedFeedback,
                round_success: roundSuccess,
                check_in_count: round.check_in_count,
                round_time_up: roundTimeUp,
                can_healer_continue: canHealerContinue,
                next_session_status: nextSessionStatus,
                in_progress_elapsed_time_ms: inProgressElapsedTime,
                max_time_ms: round.max_time,
              },
              message: 'Healing round business metrics calculated',
              elapsed_time: Date.now() - startTime,
            });
            /**
             * Update the database
             */
            await this.libSessionService.update(
              session.session_id,
              {
                status: nextSessionStatus,
                sub_status: SessionSubStatusType.COMPLETED,
                sub_status_updated_at: new Date(),
                updated_at: new Date(),
              },
              profileId
            );

            await this.healingRoundRepository.update({
              where: {
                round_id: bulkConversationDto.round_id,
                is_deleted: false,
              },
              data: {
                status: HealingRoundStatus.COMPLETED,
                is_positive_feedback: roundSuccess,
                round_end_at: new Date(),
                updated_at: new Date(),
              },
            });

            this.logger.log({
              timestamp: new Date().toISOString(),
              event: 'BULK_CONVERSATION_HEALING_ROUND_COMPLETED_SUCCESS',
              session_id: sessionId,
              profile_id: profileId,
              round_id: roundId,
              conversation_type: conversationType,
              conversation_status: conversationStatus,
              final_session_status: nextSessionStatus,
              round_success: roundSuccess,
              zero_pain_achieved: ailmentResponse.is_zero_pain,
              message:
                'Healing round check-in completed successfully and database updated',
              elapsed_time: Date.now() - startTime,
            });
          } catch (error) {
            const errorContext = {
              timestamp: new Date().toISOString(),
              event: 'BULK_CONVERSATION_HEALING_ROUND_COMPLETED_ERROR',
              session_id: sessionId,
              profile_id: profileId,
              round_id: roundId,
              conversation_type: conversationType,
              conversation_status: conversationStatus,
              error_name: error instanceof Error ? error.name : 'UnknownError',
              error_message:
                error instanceof Error ? error.message : 'unknown error',
              message:
                'Failed to process completed healing round check-in conversation',
              elapsed_time: Date.now() - startTime,
            };

            if (error instanceof ContentFilterException) {
              errorContext.event =
                'BULK_CONVERSATION_HEALING_ROUND_COMPLETED_CONTENT_FILTER_ERROR';
              errorContext.message =
                'Content filter triggered during healing round completed processing';
              this.logger.warn(errorContext);
              throw new BadRequestException('failed due to content filter');
            } else {
              this.logger.error(errorContext);
              throw new InternalServerErrorException(
                'failed to process completed healing round check-in conversation'
              );
            }
          }
        } else if (conversationStatus === ConversationStatus.IN_PROGRESS) {
          /**
           * Switch from voice to text chat, provide the context to AI assistant and continue chat with BE /conversations endpoint
           * This is for storing the existing conversation history in the database
           *
           * Execute healing round thread to generate the AI response
           *
           * sessionStatus === SessionStatus.HEALING_ROUND &&
           * sessionSubStatus === SessionSubStatusType.FEEDBACK_REQUIRED &&
           * conversationType === ConversationType.HEALING_ROUND_CHECK_IN &&
           * conversationStatus === ConversationStatus.IN_PROGRESS
           */
          try {
            const { assistantResp } =
              await this.aiHealingRoundService.executeHealingRoundThread({
                sessionId,
                healing_round_id: roundId,
                patientInfo,
                assistant_thread_id: round.assistant_thread_id,
                summaryContent,
                conversationStatus,
                conversationType,
                wsEventOptions: undefined,
                region: round.cloud_region,
              });

            /**
             * Remove the patient's message from the AI response
             * Regex used: __TO_PATIENT__:\s*
             */
            const regex = new RegExp(
              `${AI_MESSAGE_INCOMING_TRIGGER.HEALING_ROUND_MESSAGE_TO_PATIENT}\\s*`,
              'g'
            );
            const aiResponse =
              assistantResp?.output.replace(regex, '').trim() || '';

            await this.conversationRepository.create({
              sender_type: SenderType.AI,
              profiles: {
                connect: {
                  profile_id: bulkConversationDto.profile_id,
                },
              },
              healing_sessions: {
                connect: {
                  session_id: session.session_id,
                },
              },
              healing_rounds: {
                connect: {
                  round_id: bulkConversationDto.round_id,
                },
              },
              message_type: MessageType.Text,
              content: aiResponse,
              responded_at: new Date(),
              is_null_response: false,
            });
          } catch (error) {
            if (error instanceof ContentFilterException) {
              this.logger.warn({
                timestamp: new Date().toISOString(),
                event:
                  'BULK_CONVERSATION_HEALING_ROUND_IN_PROGRESS_CONTENT_FILTER_ERROR',
                session_id: sessionId,
                profile_id: profileId,
                message: 'content filter exception',
                elapsed_time: Date.now() - startTime,
              });
              throw new BadRequestException('failed due to content filter');
            }
            this.logger.error({
              timestamp: new Date().toISOString(),
              event: 'BULK_CONVERSATION_HEALING_ROUND_IN_PROGRESS_ERROR',
              session_id: sessionId,
              profile_id: profileId,
              message:
                'failed to process in-progress healing round check-in conversation',
              elapsed_time: Date.now() - startTime,
            });
            throw new InternalServerErrorException(
              'failed to process in-progress healing round check-in conversation'
            );
          }
        } else {
          this.logger.warn({
            timestamp: new Date().toISOString(),
            event: 'BULK_CONVERSATION_INVALID_CONVERSATION_STATUS',
            session_id: sessionId,
            profile_id: profileId,
            conversation_status: conversationStatus,
            message: 'invalid conversation status',
            elapsed_time: Date.now() - startTime,
          });
          throw new BadRequestException('Invalid conversation status.');
        }
      } else if (conversationType === ConversationType.REPORT_SYMPTOMS) {
        /**
         * Report symptoms triggered from the patient
         * BUG: This does not current support switching from voice to text chat
         *
         * conversationType === ConversationType.REPORT_SYMPTOMS
         *
         */
        const round = await this.healingRoundRepository.findUnique({
          where: { round_id: bulkConversationDto.round_id, is_deleted: false },
        });
        const roundId = bulkConversationDto.round_id;

        const { assistantResp } =
          await this.aiHealingRoundService.executeHealingRoundThread({
            sessionId,
            healing_round_id: roundId,
            patientInfo,
            assistant_thread_id: round.assistant_thread_id,
            summaryContent: undefined,
            conversationStatus,
            conversationType,
            wsEventOptions: undefined,
            region: round.cloud_region,
          });

        if (conversationStatus === ConversationStatus.COMPLETED) {
          /**
           * conversationType === ConversationType.REPORT_SYMPTOMS &&
           * conversationStatus === ConversationStatus.COMPLETED
           */
          const aiResponse = assistantResp?.output.trim() || '';
          await this.conversationRepository.create({
            sender_type: SenderType.AI,
            profiles: {
              connect: {
                profile_id: session.healer_id,
              },
            },
            healing_sessions: {
              connect: {
                session_id: session.session_id,
              },
            },
            healing_rounds: {
              connect: {
                round_id: bulkConversationDto.round_id,
              },
            },
            message_type: MessageType.Text,
            content: aiResponse,
            responded_at: new Date(),
            is_null_response: false,
          });
          this.logger.debug({
            timestamp: new Date().toISOString(),
            event: 'BULK_CONVERSATION_REPORT_SYMPTOMS_CONVERSATION_SAVED',
            session_id: sessionId,
            profile_id: profileId,
            round_id: roundId,
            message: 'saved report symptoms conversation',
            elapsed_time: Date.now() - startTime,
          });
        } else {
          /**
           * conversationType === ConversationType.REPORT_SYMPTOMS &&
           * conversationStatus === ConversationStatus.IN_PROGRESS (or any other status)
           */
          this.logger.warn({
            timestamp: new Date().toISOString(),
            event:
              'BULK_CONVERSATION_UNSUPPORTED_REPORT_SYMPTOMS_CONVERSATION_STATUS',
            session_id: sessionId,
            profile_id: profileId,
            conversation_type: conversationType,
            conversation_status: conversationStatus,
            message: 'unsupported report symptoms conversation status',
            elapsed_time: Date.now() - startTime,
          });
          throw new NotImplementedException(
            'report symptoms conversation is not supported.'
          );
        }
      } else {
        /**
         * conversationType (None of the above)
         */
        this.logger.warn({
          timestamp: new Date().toISOString(),
          event:
            'BULK_CONVERSATION_INVALID_CONVERSATION_TYPE_OR_SESSION_STATUS',
          session_id: sessionId,
          profile_id: profileId,
          session_status: sessionStatus,
          session_sub_status: sessionSubStatus,
          conversation_status: conversationStatus,
          conversation_type: conversationType,
          message: 'Invalid conversation type or session status.',
          elapsed_time: Date.now() - startTime,
        });
        throw new BadRequestException(
          'mismatched conversation and session states'
        );
      }
      // Log success
      this.logger.log({
        timestamp: new Date().toISOString(),
        event: 'BULK_CONVERSATION_SUCCESS',
        session_id: sessionId,
        profile_id: profileId,
        conversation_type: conversationType,
        conversation_status: conversationStatus,
        message: 'Bulk conversation processing completed successfully',
        elapsed_time: Date.now() - startTime,
      });
      return conversations;
    } catch (error) {
      // Log any errors at the top level
      this.logger.error({
        timestamp: new Date().toISOString(),
        event: 'BULK_CONVERSATION_ERROR',
        session_id: bulkConversationDto.session_id,
        profile_id: bulkConversationDto.profile_id,
        error_message:
          error instanceof HttpException
            ? error.message
            : `unknown error: ${error.message}`,
        message: 'Bulk conversation processing failed',
        elapsed_time: Date.now() - startTime,
      });

      throw error;
    }
  }

  async invalidateSession(sessionId: number) {
    await this.sessionRepository.update({
      where: { session_id: sessionId },
      data: {
        status: SessionStatus.ERROR,
        sub_status: SessionSubStatusType.ERROR_OCCURRED,
      },
    });
  }

  async findAll({
    query,
  }: {
    query: ConversationsQueryDto;
  }): Promise<ConversationResponseDto> {
    const {
      session_id,
      profile_id,
      sortBy = 'created_at',
      order = 'desc',
      skip = 0,
      limit = 5,
    } = query;

    const finalQuery = {
      take: limit,
      skip,
      where: { is_deleted: false, profile_id },
      select: {
        conversation_id: true,
        profile_id: true,
        model_id: true,
        session_id: true,
        round_id: true,
        summary_id: true,
        follow_up_id: true,
        sender_type: true,
        message_type: true,
        content: true,
        created_at: true,
        is_null_response: true,
        responded_at: true,
      },
      orderBy: {
        [sortBy]: order,
      },
    };

    if (session_id) {
      finalQuery['where']['session_id'] = session_id;
    }
    if (profile_id) {
      finalQuery['where']['profile_id'] = profile_id;
    }

    const data = await this.conversationRepository.findAll(finalQuery);
    const conversations = sortBy
      ? data.sort((a, b) =>
          order === SortOrderDto.ASC
            ? a[sortBy] > b[sortBy]
              ? 1
              : -1
            : a[sortBy] < b[sortBy]
            ? 1
            : -1
        )
      : data;
    return {
      conversations,
      total: 1,
      limit,
      skip,
    };
  }

  async findOne(session_id: string): Promise<conversations> {
    try {
      const checkSessionExist: SessionDto =
        await this.sessionRepository.findFirst({
          where: { session_id: parseInt(session_id), is_deleted: false },
        });

      if (!checkSessionExist) {
        throw new NotFoundException("session doesn't exist!, please try again");
      }

      const conversations: conversations =
        await this.conversationRepository.findFirst({
          where: { session_id: parseInt(session_id), is_deleted: false },
          include: {
            healing_sessions: true,
            healing_rounds: true,
            profiles: true,
          },
        });
      return conversations;
    } catch (error) {
      throw new Error(error);
    }
  }
}
