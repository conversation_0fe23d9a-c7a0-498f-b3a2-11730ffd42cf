import {
  BadRequestException,
  Injectable,
  NotImplementedException,
  NotFoundException,
  ForbiddenException,
  InternalServerErrorException,
} from '@nestjs/common';
import { Prisma, conversations, healing_sessions } from '@core/prisma-client';
import {
  ConversationRepository,
  HealingRoundRepository,
  SessionAilmentRepository,
  SessionRepository,
  SortOrderDto,
  SummaryRepository,
  MeasurePerformance,
} from '@core/libs';
import {
  ConversationStatus,
  ConversationType,
  HealingRoundStatus,
  LibSessionService,
  MessageType,
  SenderType,
  SessionSubStatusType,
  SessionStatus,
  AI_MESSAGE_INCOMING_TRIGGER,
} from '@core_be/global';
import {
  ConversationResponseDto,
  ConversationsQueryDto,
  CreateBulkConversationDto,
} from '../dto/conversation.dto';
import { SessionDto } from '../../sessions/dto/session.dto';
import {
  AIHealingRoundService,
  LibAIIntakeService,
  SummaryAilmentService,
  ContentFilterException,
} from '@core_be/aiml';
import _ from 'lodash';
import { Logger } from 'nestjs-pino';

@Injectable()
export class ConversationService {

  constructor(
    private readonly conversationRepository: ConversationRepository,
    private readonly sessionRepository: SessionRepository,
    private readonly libSessionService: LibSessionService,
    private readonly summaryRepository: SummaryRepository,
    private readonly aiIntakeService: LibAIIntakeService,
    private readonly summaryAilmentService: SummaryAilmentService,
    private readonly aiHealingRoundService: AIHealingRoundService,
    private readonly sessionAilmentRepository: SessionAilmentRepository,
    private readonly healingRoundRepository: HealingRoundRepository,
    private readonly performance:MeasurePerformance,
    private readonly logger: Logger
  ) {}

  async createConversationRecord(
    data: Prisma.conversationsCreateInput
  ): Promise<conversations> {
    return this.conversationRepository.create(data);
  }

  /*
   * This service is to handle conversation triggered from the bulk conversation API,
   * which is primarily used for handling voice input from the frontend.
   *
   * Text-based conversation transcript from the single conversation API is handled by the
   * ConversationListenerService in libs/aiml/src/shared/conversation-listener.service.ts
   */
  async createBulkConversation(
    bulkConversationDto: CreateBulkConversationDto
  ): Promise<conversations[]> {
    const perf = this.performance;

    try {
      const sessionId = bulkConversationDto.session_id;
      const profileId = bulkConversationDto.profile_id;

      // Log method entry with request context
      this.logger.log(
        {
          event: 'BULK_CONVERSATION_START',
          session_id: sessionId,
          profile_id: profileId,
          conversation_type: bulkConversationDto.conversation_type,
          conversation_status: bulkConversationDto.conversation_status,
          message: 'Starting bulk conversation processing',
        },
        'Bulk Conversation Processing'
      );

      // Find the session from the session_id
      const session: healing_sessions =
        await this.libSessionService.findFirstSession({
          session_id: sessionId,
        });

      perf.push(`obtained session`);

      if (!session) {
        const errorMessage = 'session not found';
        this.logger.error(
          {
            event: 'BULK_CONVERSATION_SESSION_NOT_FOUND',
            session_id: sessionId,
            profile_id: profileId,
            message: errorMessage,
          },
          'Bulk Conversation Validation Failed'
        );
        perf.warn(errorMessage, { session_id: sessionId });
        throw new NotFoundException(errorMessage);
      } else if (session.profile_id !== profileId) {
        const errorMessage = 'profile id mismatch';
        this.logger.error(
          {
            event: 'BULK_CONVERSATION_PROFILE_MISMATCH',
            session_id: sessionId,
            profile_id: profileId,
            message: errorMessage,
          },
          'Bulk Conversation Authorization Failed'
        );
        perf.warn(errorMessage, {
          session_id: sessionId,
          session_profile_id: session.profile_id,
          profile_id: profileId,
        });
        throw new ForbiddenException(errorMessage);
      }

      const sessionStatus = session.status;
      const sessionSubStatus = session.sub_status;

      /* Get the conversation type and status from the API Input
       * If not provided, it is assuming to be a conversation with in-take completed
       */
      const conversationType =
        bulkConversationDto.conversation_type || ConversationType.INTAKE;

      const conversationStatus =
        bulkConversationDto.conversation_status || ConversationStatus.COMPLETED;

      perf.debug('session and conversation details', {
        session_id: sessionId,
        status: {
          conversation: conversationType,
          session: sessionStatus,
        },
        sub_status: {
          conversation: conversationStatus,
          session: sessionSubStatus,
        },
      });

      // Starting from this point, the conversation is stored in the database
      // TODO: Improve Session State Validation
      /**
       * Update the conversation table with the conversation history
       * This is to store the conversation history for the patient
       */
      const conversations = await this.conversationRepository.createMany({
        data: bulkConversationDto.conversation.map((conversation) => ({
          content: conversation.content,
          sender_type: conversation.sender_type,
          profile_id: bulkConversationDto.profile_id,
          session_id: session.session_id,
          round_id: bulkConversationDto.round_id,
          message_type: conversation.message_type || MessageType.Text,
          created_at: new Date(),
          responded_at: conversation.responded_at,
        })),
        skipDuplicates: false, // Optional: skips duplicate records
      });

      if (!conversations) {
        const errorMessage = 'failed to save bulk conversation records';
        this.logger.error(
          {
            event: 'BULK_CONVERSATION_SAVE_FAILED',
            session_id: sessionId,
            profile_id: profileId,
            message: errorMessage,
          },
          'Bulk Conversation Database Error'
        );
        perf.warn(errorMessage, {
          session_id: sessionId,
          profile_id: profileId,
        });
        throw new InternalServerErrorException(errorMessage);
      }

      perf.push(`saved bulk conversation records`);

      /*
       * Transform the conversation history from object to string
       * and distinguish between the user's message and AI's message
       */
      const patientInfo: string[] = _.chain(bulkConversationDto.conversation)
        .orderBy(['responded_at'], ['asc'])
        .map((conversation) =>
          conversation.sender_type === SenderType.AI
            ? `Question: ${conversation.content}`
            : `Answer: ${conversation.content}`
        )
        .value();

      const conversationHistory: {
        role: 'user' | 'assistant';
        content: string;
      }[] = _.chain(bulkConversationDto.conversation)
        .orderBy(['responded_at'], ['asc'])
        .map((conversation) => ({
          role:
            conversation.sender_type === SenderType.AI ? 'assistant' : 'user',
          content: conversation.content,
        }))
        .value();

      if (
        conversationType === ConversationType.INTAKE &&
        sessionStatus === SessionStatus.INTAKE
      ) {
        /*
         * Handling intake conversation
         * Case: FLOW-INTAKE-*
         */
        if (conversationStatus === ConversationStatus.COMPLETED) {
          this.logger.log(
            {
              event: 'BULK_CONVERSATION_INTAKE_COMPLETED_START',
              session_id: sessionId,
              profile_id: profileId,
              message: 'Processing completed intake conversation',
            },
            'Bulk Conversation Intake Processing'
          );

          /*
           * INTAKE - COMPLETED
           * When conversation status is completed, it is ready for summary extraction
           * and move the patient to the queue.
           *
           * Since the whole voice conversation was executed in FE, BE need to extract the summary
           * Create Intake summary if the conversation status is completed
           */
          try {
            const sanitizedResponse =
              await this.aiIntakeService.generateIntakeSummary(
                patientInfo,
                conversationStatus,
                session,
                perf
              );

            await this.summaryAilmentService.createSummaryAndAilments(
              sanitizedResponse,
              sessionId,
              session,
              undefined,
              undefined,
              perf
            );

            await this.aiIntakeService.enqueueHealingSession(
              session,
              profileId,
              perf
            );

            this.logger.log(
              {
                event: 'BULK_CONVERSATION_INTAKE_COMPLETED_SUCCESS',
                session_id: sessionId,
                profile_id: profileId,
                message: 'Intake conversation completed and session enqueued',
              },
              'Bulk Conversation Intake Success'
            );

            perf.push('enqueued healing session');
          } catch (error) {
            if (error instanceof ContentFilterException) {
              this.logger.warn(
                {
                  event: 'BULK_CONVERSATION_INTAKE_CONTENT_FILTER_ERROR',
                  session_id: sessionId,
                  profile_id: profileId,
                  message: 'Content filter triggered during intake processing',
                },
                'Bulk Conversation Content Filter'
              );
              perf.warn('content filter exception', {
                session_id: sessionId,
                profile_id: profileId,
              });
            } else {
              this.logger.error(
                {
                  event: 'BULK_CONVERSATION_INTAKE_COMPLETED_ERROR',
                  session_id: sessionId,
                  profile_id: profileId,
                  error_message:
                    error instanceof Error ? error.message : 'unknown error',
                  message: 'Failed to process completed intake conversation',
                },
                'Bulk Conversation Intake Error'
              );
              perf.warn('failed to generate intake summary', {
                session_id: sessionId,
                profile_id: profileId,
                error: error instanceof Error ? error.message : 'unknown error',
              });
            }
            throw error;
          }
        } else if (conversationStatus === ConversationStatus.IN_PROGRESS) {
          /**
           * INTAKE - IN_PROGRESS
           * If Conversation Status is not completed,
           * This is for switching from voice to text chat, provide the context to AI assistant and continue chat with BE /conversations endpoint
           * This is for storing the existing conversation history in the database
           */
          try {
            this.logger.log(
              {
                event: 'BULK_CONVERSATION_INTAKE_IN_PROGRESS_START',
                session_id: sessionId,
                profile_id: profileId,
                message: 'Processing in-progress intake conversation',
              },
              'Bulk Conversation Intake In-Progress'
            );

            await this.aiIntakeService.ingestIntakeConversationHistory(
              session,
              conversationHistory,
              perf
            );

            this.logger.log(
              {
                event: 'BULK_CONVERSATION_INTAKE_IN_PROGRESS_SUCCESS',
                session_id: sessionId,
                profile_id: profileId,
                message:
                  'In-progress intake conversation processed successfully',
              },
              'Bulk Conversation Intake In-Progress Success'
            );
          } catch (error) {
            const errorContext = {
              timestamp: new Date().toISOString(),
              event: 'BULK_CONVERSATION_INTAKE_IN_PROGRESS_ERROR',
              session_id: sessionId,
              profile_id: profileId,
              conversation_type: conversationType,
              conversation_status: conversationStatus,
              error_name: error instanceof Error ? error.name : 'UnknownError',
              error_message:
                error instanceof Error ? error.message : 'unknown error',
              message: 'Failed to process in-progress intake conversation',
            };

            if (error instanceof ContentFilterException) {
              errorContext.event =
                'BULK_CONVERSATION_INTAKE_IN_PROGRESS_CONTENT_FILTER_ERROR';
              errorContext.message =
                'Content filter triggered during in-progress intake processing';
              this.logger.warn(
                errorContext,
                'Bulk Conversation Content Filter'
              );
              perf.warn('content filter exception', {
                session_id: sessionId,
                profile_id: profileId,
              });
            } else {
              this.logger.error(
                errorContext,
                'Bulk Conversation Intake In-Progress Error'
              );
            }
            throw error;
          }
        } else {
          const errorContext = {
            timestamp: new Date().toISOString(),
            event: 'BULK_CONVERSATION_INTAKE_INVALID_STATUS',
            session_id: sessionId,
            profile_id: profileId,
            conversation_type: conversationType,
            conversation_status: conversationStatus,
            message: 'Invalid conversation status for intake flow',
            error_type: 'VALIDATION_ERROR',
          };

          this.logger.error(
            errorContext,
            'Bulk Conversation Intake Validation Error'
          );
          perf.warn('invalid conversation status', {
            session_id: sessionId,
            profile_id: profileId,
            conversation_status: conversationStatus,
          });
          throw new BadRequestException('Invalid conversation status.');
        }
      } else if (
        conversationType === ConversationType.HEALING_ROUND_CHECK_IN &&
        sessionStatus === SessionStatus.HEALING_ROUND &&
        sessionSubStatus === SessionSubStatusType.FEEDBACK_REQUIRED
      ) {
        /**
         * Healing round check-in conversation
         * Case: FLOW-HEALING-ROUND-FEEDBACK-REQUIRED
         */
        this.logger.log(
          {
            timestamp: new Date().toISOString(),
            event: 'BULK_CONVERSATION_HEALING_ROUND_FLOW_START',
            session_id: sessionId,
            profile_id: profileId,
            conversation_type: conversationType,
            conversation_status: conversationStatus,
            session_status: sessionStatus,
            session_sub_status: sessionSubStatus,
            round_id: bulkConversationDto.round_id,
            message:
              'Starting healing round check-in conversation flow processing',
          },
          'Bulk Conversation Healing Round Flow'
        );

        /**
         * Retrieve healing round details from the database
         */
        const roundId = bulkConversationDto.round_id;
        const round = await this.healingRoundRepository.findUnique({
          where: { round_id: roundId, is_deleted: false },
        });
        perf.push(`obtained healing round`);

        if (round?.status !== HealingRoundStatus.FEEDBACK_REQUIRED) {
          const errorContext = {
            timestamp: new Date().toISOString(),
            event: 'BULK_CONVERSATION_HEALING_ROUND_INVALID_STATUS',
            session_id: sessionId,
            profile_id: profileId,
            round_id: roundId,
            expected_status: HealingRoundStatus.FEEDBACK_REQUIRED,
            actual_status: round?.status,
            conversation_type: conversationType,
            message: 'Invalid healing round status for check-in conversation',
            error_type: 'VALIDATION_ERROR',
          };

          this.logger.error(
            errorContext,
            'Bulk Conversation Healing Round Validation Error'
          );
          perf.warn('invalid healing round status', {
            session_id: sessionId,
            profile_id: profileId,
            round_id: roundId,
            round_status: round?.status,
          });
          throw new BadRequestException('Invalid healing round status.');
        }

        this.logger.log(
          {
            timestamp: new Date().toISOString(),
            event: 'BULK_CONVERSATION_HEALING_ROUND_VALIDATED',
            session_id: sessionId,
            profile_id: profileId,
            round_id: roundId,
            round_status: round.status,
            check_in_count: round.check_in_count,
            conversation_type: conversationType,
            message:
              'Healing round validation successful, proceeding with check-in processing',
          },
          'Bulk Conversation Healing Round Validation'
        );

        /**
         * Retrieve latest session summary from the database
         */
        const patientSummaryDocument =
          await this.summaryRepository.getLatestSessionSummary({
            where: { session_id: session.session_id },
            orderBy: {
              created_at: 'desc',
            },
          });
        const summaryContent = patientSummaryDocument?.content;
        perf.push(`obtained patient summary`);

        if (conversationStatus === ConversationStatus.COMPLETED) {
          /**
           * When healing round is complete, create summary and ailments
           *
           * Execute healing round thread to generate the AI response
           */
          try {
            this.logger.log(
              {
                timestamp: new Date().toISOString(),
                event: 'BULK_CONVERSATION_HEALING_ROUND_COMPLETED_START',
                session_id: sessionId,
                profile_id: profileId,
                round_id: roundId,
                conversation_type: conversationType,
                conversation_status: conversationStatus,
                check_in_count: round.check_in_count,
                assistant_thread_id: round.assistant_thread_id,
                message:
                  'Processing completed healing round check-in conversation',
              },
              'Bulk Conversation Healing Round Completed Processing'
            );

            const { assistantResp } =
              await this.aiHealingRoundService.executeHealingRoundThread(
                sessionId,
                roundId,
                patientInfo,
                round.assistant_thread_id,
                summaryContent,
                conversationStatus,
                conversationType,
                perf
              );

            /**
             * Remove to patient's message from the AI response
             * Regex used: __TO_PATIENT__:\s*
             */
            const regex = new RegExp(
              `${AI_MESSAGE_INCOMING_TRIGGER.HEALING_ROUND_MESSAGE_TO_PATIENT}\\s*`,
              'g'
            );
            const aiResponse =
              assistantResp?.output.replace(regex, '').trim() || '';

            const ailmentResponse =
              await this.summaryAilmentService.createSummaryAndAilments(
                aiResponse,
                sessionId,
                session,
                roundId,
                summaryContent,
                perf
              );

            /**
             * Round is successful if the patient has no pain or discomfort
             *   or
             * if reported pain level is lower than the previous round, handled in compareRoundFeedback
             *   * If there is no previous round, the round is considered to be failure
             */

            const improvedFeedback =
              await this.sessionAilmentRepository.compareRoundFeedback(
                session.session_id,
                +bulkConversationDto.round_id
              );
            const roundSuccess =
              ailmentResponse.is_zero_pain || improvedFeedback;

            const currDate = new Date();

            const inProgressCompleted =
              round.feedback_start_at?.getTime() || currDate.getTime();

            const inProgressElapsedTime =
              inProgressCompleted - round.round_start_at.getTime();

            const roundTimeUp = inProgressElapsedTime > round.max_time;

            const canHealerContinue =
              roundSuccess || (!roundTimeUp && round.check_in_count > 1);

            /**
             * Session Status is completed if
             *   * There is no pain or discomfort
             *   * The healer cannot continue, due to,
             *     * Pain level has decreased compared to the previous round
             *     * Elapsed Time > Max Time
             *     * It has already been checked more than once
             */
            const nextSessionStatus =
              ailmentResponse.is_zero_pain || !canHealerContinue
                ? SessionStatus.COMPLETED
                : sessionStatus;

            // Log comprehensive business metrics for healing round results
            this.logger.log(
              {
                timestamp: new Date().toISOString(),
                event: 'BULK_CONVERSATION_HEALING_ROUND_BUSINESS_METRICS',
                session_id: sessionId,
                profile_id: profileId,
                round_id: roundId,
                conversation_type: conversationType,
                conversation_status: conversationStatus,
                business_metrics: {
                  zero_pain: ailmentResponse.is_zero_pain,
                  improved_feedback: improvedFeedback,
                  round_success: roundSuccess,
                  check_in_count: round.check_in_count,
                  round_time_up: roundTimeUp,
                  can_healer_continue: canHealerContinue,
                  next_session_status: nextSessionStatus,
                  in_progress_elapsed_time_ms: inProgressElapsedTime,
                  max_time_ms: round.max_time,
                },
                message: 'Healing round business metrics calculated',
              },
              'Bulk Conversation Healing Round Business Metrics'
            );

            perf.info('healing round result', {
              session_id: sessionId,
              round_id: roundId,
              zero_pain: ailmentResponse.is_zero_pain,
              improved_feedback: improvedFeedback,
              round_success: roundSuccess,
              check_in_count: round.check_in_count,
              round_time_up: roundTimeUp,
              can_healer_continue: canHealerContinue,
              next_session_status: nextSessionStatus,
            });

            /**
             * Update the database
             */
            await this.libSessionService.update(
              session.session_id,
              {
                status: nextSessionStatus,
                sub_status: SessionSubStatusType.COMPLETED,
                sub_status_updated_at: new Date(),
                updated_at: new Date(),
              },
              profileId
            );

            await this.healingRoundRepository.update({
              where: {
                round_id: bulkConversationDto.round_id,
                is_deleted: false,
              },
              data: {
                status: HealingRoundStatus.COMPLETED,
                is_positive_feedback: roundSuccess,
                round_end_at: new Date(),
                updated_at: new Date(),
              },
            });

            this.logger.log(
              {
                timestamp: new Date().toISOString(),
                event: 'BULK_CONVERSATION_HEALING_ROUND_COMPLETED_SUCCESS',
                session_id: sessionId,
                profile_id: profileId,
                round_id: roundId,
                conversation_type: conversationType,
                conversation_status: conversationStatus,
                final_session_status: nextSessionStatus,
                round_success: roundSuccess,
                zero_pain_achieved: ailmentResponse.is_zero_pain,
                message:
                  'Healing round check-in completed successfully and database updated',
              },
              'Bulk Conversation Healing Round Completed Success'
            );

            perf.push('updated session and healing round');
          } catch (error) {
            const errorContext = {
              timestamp: new Date().toISOString(),
              event: 'BULK_CONVERSATION_HEALING_ROUND_COMPLETED_ERROR',
              session_id: sessionId,
              profile_id: profileId,
              round_id: roundId,
              conversation_type: conversationType,
              conversation_status: conversationStatus,
              error_name: error instanceof Error ? error.name : 'UnknownError',
              error_message:
                error instanceof Error ? error.message : 'unknown error',
              message:
                'Failed to process completed healing round check-in conversation',
            };

            if (error instanceof ContentFilterException) {
              errorContext.event =
                'BULK_CONVERSATION_HEALING_ROUND_COMPLETED_CONTENT_FILTER_ERROR';
              errorContext.message =
                'Content filter triggered during healing round completed processing';
              this.logger.warn(
                errorContext,
                'Bulk Conversation Content Filter'
              );
              perf.warn('content filter exception', {
                session_id: sessionId,
                profile_id: profileId,
              });
            } else {
              this.logger.error(
                errorContext,
                'Bulk Conversation Healing Round Completed Error'
              );
            }
            throw error;
          }
        } else if (conversationStatus === ConversationStatus.IN_PROGRESS) {
          /**
           * Switch from voice to text chat, provide the context to AI assistant and continue chat with BE /conversations endpoint
           * This is for storing the existing conversation history in the database
           *
           * Execute healing round thread to generate the AI response
           */
          try {
            const { assistantResp } =
              await this.aiHealingRoundService.executeHealingRoundThread(
                sessionId,
                roundId,
                patientInfo,
                round.assistant_thread_id,
                summaryContent,
                conversationStatus,
                conversationType,
                perf
              );

            /**
             * Remove the patient's message from the AI response
             * Regex used: __TO_PATIENT__:\s*
             */
            const regex = new RegExp(
              `${AI_MESSAGE_INCOMING_TRIGGER.HEALING_ROUND_MESSAGE_TO_PATIENT}\\s*`,
              'g'
            );
            const aiResponse =
              assistantResp?.output.replace(regex, '').trim() || '';

            await this.conversationRepository.create({
              sender_type: SenderType.AI,
              profiles: {
                connect: {
                  profile_id: bulkConversationDto.profile_id,
                },
              },
              healing_sessions: {
                connect: {
                  session_id: session.session_id,
                },
              },
              healing_rounds: {
                connect: {
                  round_id: bulkConversationDto.round_id,
                },
              },
              message_type: MessageType.Text,
              content: aiResponse,
              responded_at: new Date(),
              is_null_response: false,
            });
          } catch (error) {
            if (error instanceof ContentFilterException) {
              perf.warn('content filter exception', {
                session_id: sessionId,
                profile_id: profileId,
              });
            }
            throw error;
          }
        } else {
          perf.warn('invalid conversation status', {
            session_id: sessionId,
            profile_id: profileId,
            conversation_status: conversationStatus,
          });
          throw new BadRequestException('Invalid conversation status.');
        }
      } else if (conversationType === ConversationType.REPORT_SYMPTOMS) {
        /**
         * Report symptoms triggered from the patient
         * BUG: This does not current support switching from voice to text chat
         */
        const round = await this.healingRoundRepository.findUnique({
          where: { round_id: bulkConversationDto.round_id, is_deleted: false },
        });
        const roundId = bulkConversationDto.round_id;

        const { assistantResp } =
          await this.aiHealingRoundService.executeHealingRoundThread(
            sessionId,
            roundId,
            patientInfo,
            round.assistant_thread_id,
            undefined,
            conversationStatus,
            conversationType,
            perf
          );

        if (conversationStatus === ConversationStatus.COMPLETED) {
          const aiResponse = assistantResp?.output.trim() || '';
          await this.conversationRepository.create({
            sender_type: SenderType.AI,
            profiles: {
              connect: {
                profile_id: session.healer_id,
              },
            },
            healing_sessions: {
              connect: {
                session_id: session.session_id,
              },
            },
            healing_rounds: {
              connect: {
                round_id: bulkConversationDto.round_id,
              },
            },
            message_type: MessageType.Text,
            content: aiResponse,
            responded_at: new Date(),
            is_null_response: false,
          });
          perf.push('saved report symptoms conversation');
        } else {
          perf.warn('unsupported report symptoms conversation status', {
            session_id: sessionId,
            profile_id: profileId,
            conversation_type: conversationType,
            conversation_status: conversationStatus,
          });
          throw new NotImplementedException(
            'Report symptoms conversation is not supported.'
          );
        }
      } else {
        perf.warn('Invalid conversation type or session status.', {
          session_id: sessionId,
          session_status: sessionStatus,
          session_sub_status: sessionSubStatus,
          conversation_status: conversationStatus,
          conversation_type: conversationType,
        });
        throw new BadRequestException(
          'Mismatched conversation and session states'
        );
      }
      this.logger.log(
        {
          event: 'BULK_CONVERSATION_SUCCESS',
          session_id: sessionId,
          profile_id: profileId,
          conversation_type: conversationType,
          conversation_status: conversationStatus,
          message: 'Bulk conversation processing completed successfully',
        },
        'Bulk Conversation Success'
      );
      return conversations;
    } catch (error) {
      // Log any unhandled errors at the top level
      this.logger.error(
        {
          event: 'BULK_CONVERSATION_UNHANDLED_ERROR',
          session_id: bulkConversationDto.session_id,
          profile_id: bulkConversationDto.profile_id,
          error_message:
            error instanceof Error ? error.message : 'unknown error',
          message: 'Bulk conversation processing failed',
        },
        'Bulk Conversation Unhandled Error'
      );

      throw error;
    } finally {
      perf.logPerformance();
    }
  }

  async invalidateSession(sessionId: number) {
    await this.sessionRepository.update({
      where: { session_id: sessionId },
      data: {
        status: SessionStatus.ERROR,
        sub_status: SessionSubStatusType.ERROR_OCCURRED,
      },
    });
  }

  async findAll({
    query,
  }: {
    query: ConversationsQueryDto;
  }): Promise<ConversationResponseDto> {
    const {
      session_id,
      profile_id,
      sortBy = 'created_at',
      order = 'desc',
      skip = 0,
      limit = 5,
    } = query;

    const finalQuery = {
      take: limit,
      skip,
      where: { is_deleted: false, profile_id },
      select: {
        conversation_id: true,
        profile_id: true,
        model_id: true,
        session_id: true,
        round_id: true,
        summary_id: true,
        follow_up_id: true,
        sender_type: true,
        message_type: true,
        content: true,
        created_at: true,
        is_null_response: true,
        responded_at: true,
      },
      orderBy: {
        [sortBy]: order,
      },
    };

    if (session_id) {
      finalQuery['where']['session_id'] = session_id;
    }
    if (profile_id) {
      finalQuery['where']['profile_id'] = profile_id;
    }

    const data = await this.conversationRepository.findAll(finalQuery);
    const conversations = sortBy
      ? data.sort((a, b) =>
          order === SortOrderDto.ASC
            ? a[sortBy] > b[sortBy]
              ? 1
              : -1
            : a[sortBy] < b[sortBy]
            ? 1
            : -1
        )
      : data;
    return {
      conversations,
      total: 1,
      limit,
      skip,
    };
  }

  async findOne(session_id: string): Promise<conversations> {
    try {
      const checkSessionExist: SessionDto =
        await this.sessionRepository.findFirst({
          where: { session_id: parseInt(session_id), is_deleted: false },
        });

      if (!checkSessionExist) {
        throw new NotFoundException("session doesn't exist!, please try again");
      }

      const conversations: conversations =
        await this.conversationRepository.findFirst({
          where: { session_id: parseInt(session_id), is_deleted: false },
          include: {
            healing_sessions: true,
            healing_rounds: true,
            profiles: true,
          },
        });
      return conversations;
    } catch (error) {
      throw new Error(error);
    }
  }
}
