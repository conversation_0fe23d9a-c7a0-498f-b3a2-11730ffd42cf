import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsNumber } from '@nestjs/class-validator';
import { IsEnum, IsOptional, IsArray, ArrayNotEmpty } from 'class-validator';
import {
  ConversationStatus,
  ConversationType,
  SenderType,
  SessionDto,
} from '@core/libs';
import { IsInt, IsBoolean, IsDate, IsJSON } from 'class-validator';
import { Type } from '@nestjs/class-transformer';

export class CreateConversationDto {
  @ApiProperty({
    example: 1,
    description: 'Profile Id',
    required: true,
  })
  @IsNotEmpty()
  @IsNumber()
  profile_id: number;

  @ApiProperty({
    example: SenderType?.Patient,
    description: 'sender_type',
    required: false,
  })
  @IsEnum(SenderType, { message: 'status must be a valid value' })
  @IsNotEmpty()
  @IsString()
  sender_type: SenderType;

  @ApiProperty({
    example: 'Hi, How are you?',
    description: 'content',
    required: false,
  })
  @IsOptional()
  @IsString()
  content: string;

  @ApiProperty({
    example: 1,
    description: 'Session Id',
    required: true,
  })
  @IsNotEmpty()
  @IsNumber()
  session_id: number;

  @ApiProperty({
    example: 1,
    description: 'Round Id',
    required: true,
  })
  @IsOptional()
  @IsNumber()
  round_id?: number;
}

export class ConversationDto {
  @ApiProperty({
    example: SenderType?.Patient,
    description: 'sender_type',
    required: true,
  })
  @IsEnum(SenderType, { message: 'status must be a valid value' })
  @IsNotEmpty()
  @IsString()
  sender_type: SenderType;

  @ApiProperty({
    example: 'Hi, How are you?',
    description: 'content',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  content: string;

  @ApiProperty({
    example: 'TEXT',
    description: 'message type',
  })
  @IsOptional()
  @IsString()
  message_type?: string;

  @ApiProperty({
    example: '2024-10-31T12:59:59.000Z',
    description: 'Responded time stamp in UTC',
  })
  @IsNotEmpty()
  @IsString()
  responded_at: string;
}

export class CreateBulkConversationDto {
  @ApiProperty({
    example: 1,
    description: 'Profile Id',
    required: true,
  })
  @IsNotEmpty()
  @IsNumber()
  profile_id: number;

  @ApiProperty({
    example: 1,
    description: 'Session Id',
    required: true,
  })
  @IsNotEmpty()
  @IsNumber()
  session_id: number;

  @IsArray()
  @ArrayNotEmpty()
  @Type(() => ConversationDto)
  @ApiProperty({
    type: [ConversationDto],
    description: 'conversation between user and assistant',
    required: true,
  })
  conversation: ConversationDto[];

  @ApiProperty({
    example: 'COMPLETED',
    description: 'Conversation status',
    required: false,
  })
  @IsOptional()
  @IsEnum(ConversationStatus)
  conversation_status?: ConversationStatus;

  @ApiProperty({
    example: 'INTAKE',
    description: 'Conversation type',
    required: false,
  })
  @IsOptional()
  @IsEnum(ConversationType)
  conversation_type?: ConversationType;

  @ApiProperty({
    example: 1,
    description: 'Round Id',
  })
  @IsOptional()
  @IsNumber()
  round_id?: number;
}

export class ConversationsCreateDto {
  @IsOptional()
  @IsInt()
  summary_id?: number | null;

  @IsOptional()
  @IsInt()
  session_id?: number | null;

  @IsOptional()
  @IsString()
  sender_type?: string | null;

  @IsOptional()
  @IsString()
  message_type?: string | null;

  @IsOptional()
  @IsString()
  content?: string | null;

  @IsOptional()
  @IsString()
  file_name?: string | null;

  @IsOptional()
  @IsJSON()
  metadata?: any;

  @IsOptional()
  @IsDate()
  created_at?: Date | string | null;

  @IsOptional()
  @IsDate()
  updated_at?: Date | string | null;

  @IsOptional()
  @IsDate()
  deleted_at?: Date | string | null;

  @IsOptional()
  @IsBoolean()
  is_deleted?: boolean | null;

  @IsOptional()
  follow_ups?: any;

  @IsOptional()
  openai_models?: any;

  @IsInt()
  profile_id: number;

  @IsOptional()
  healing_rounds?: any;

  @IsOptional()
  healing_sessions?: any;

  @IsOptional()
  profiles?: any;
}

export class UpdateConversationDto {
  @ApiProperty({
    example: 1,
    description: 'Profile Id',
    required: true,
  })
  @IsNotEmpty()
  @IsNumber()
  profile_id: number;

  @ApiProperty({
    example: 1,
    description: 'session_id',
    required: true,
  })
  @IsNotEmpty()
  @IsNumber()
  session_id: number;

  @ApiProperty({
    example: SenderType?.Patient,
    description: 'sender_type',
    required: false,
  })
  @IsEnum(SenderType, { message: 'status must be a valid value' })
  @IsOptional()
  @IsString()
  sender_type: string;

  @ApiProperty({
    example: 1,
    description: 'content',
    required: false,
  })
  @IsOptional()
  @IsString()
  content: string;
}

export class ParamConversationDto {
  @ApiProperty({
    example: 1,
    description: 'healing_session_id',
    required: true,
  })
  @IsNotEmpty()
  @IsNumber()
  healing_session_id: number;
}

export class ChatOpenAiDto {
  @ApiProperty({
    example: 1,
    description: 'Profile Id',
    required: true,
  })
  @IsNotEmpty()
  @IsNumber()
  profile_id: number;

  @ApiProperty({
    example: 1,
    description: 'session_id',
    required: true,
  })
  @IsNotEmpty()
  @IsNumber()
  session_id: number;
}

export class AssistantDto {
  @ApiProperty({
    example: 'description',
    description: 'description',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  content: string;

  @ApiProperty({
    example: 1,
    description: 'Profile Id',
    required: true,
  })
  @IsNotEmpty()
  @IsNumber()
  profile_id: number;
}

export class ContinueAssistantChatDto {
  @ApiProperty({
    example: 1,
    description: 'Session Id',
    required: true,
  })
  @IsNotEmpty()
  @IsNumber()
  sessionId: number;

  @ApiProperty({
    example: 'description',
    description: 'description',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  content: string;

  @ApiProperty({
    example: 1,
    description: 'Profile Id',
    required: true,
  })
  @IsNotEmpty()
  @IsNumber()
  profile_id: number;
}

export class GetThreadMessagesDto {
  @ApiProperty({
    example: 'thread_QWAPsuHg88MKwMSjVJcNanOM',
    description: 'Thread Id',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  threadId: string;
}

export class Conversation {
  conversation_id: number;
  profile_id: number;
  model_id: number;
  session_id: number;
  round_id: number;
  summary_id: number;
  follow_up_id: number;
  sender_type: string;
  message_type: string;
  content: string;
  created_at: Date;
  updated_at: Date;
  is_deleted: boolean;
  deleted_at: Date;
  file_name?: string;
  metadata?: unknown;
  follow_ups?: unknown;
  openai_models?: unknown;
  profiles?: unknown;
  healing_rounds?: unknown;
  healing_sessions?: unknown;
}
export type ConversationResponseMainDto = {
  message: string;
  success: boolean;
  data: Conversation[];
};

export type ConversationResponseDto = {
  conversations: Conversation[];
  total: number;
  limit: number;
  skip: number;
};

export type ConversationsQueryDto = {
  session_id: number;
  profile_id: number;
  sortBy?: string;
  order?: string;
  offset?: string;
  skip?: number;
  limit?: number;
  name?: string;
};

export class SummaryConversationContentDto {
  content: string;
  assistant_id?: string;
  role?: 'user' | 'assistant';
  session?: SessionDto;
  profile_id: number;
}
