import {
  Body,
  Controller,
  Get,
  HttpCode,
  NotFoundException,
  Param,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiBody,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { ConversationService } from '../services/conversation.service';
import {
  CreateBulkConversationDto,
  CreateConversationDto,
} from '../dto/conversation.dto';
import {
  ConversationalAgentType,
  CreateConversationEvent,
  LibProfileService,
  Role,
  Roles,
  RolesGuard,
} from '@core/libs';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { ConversationListenerService } from '@core_be/aiml';
import { ProfileAccessGuard } from 'libs/global/src/lib/guards/profile-access.guard';
import { ElevenLabsService } from 'libs/global/src/lib/services/elevenlabs.service';

@ApiTags('Conversations')
@ApiBearerAuth()
@Controller('conversations')
@UseGuards(RolesGuard)
export class ConversationController {
  constructor(
    private readonly conversationService: ConversationService,
    private readonly conversationListenerService: ConversationListenerService,
    private readonly profileService: LibProfileService,
    private readonly eventEmitter: EventEmitter2,
    private readonly elevenlabsService: ElevenLabsService
  ) {}

  @Post('bulk')
  @Roles(Role.Patient)
  @UseGuards(ProfileAccessGuard)
  @HttpCode(201)
  @ApiBody({ type: CreateBulkConversationDto })
  @ApiResponse({
    status: 201,
    description: 'Bulk conversation created successfully',
  })
  async CreateBulkConversation(
    @Req() req,
    @Body() createBulkConversationDto: CreateBulkConversationDto
  ) {
    const { user_id, profiles } = req.raw.user;

    const validateProfile = await this.profileService.validateUserProfile(
      profiles,
      user_id,
      createBulkConversationDto?.profile_id
    );

    if (!validateProfile) {
      throw new NotFoundException('User profile not found!');
    }
    return this.conversationService.createBulkConversation(
      createBulkConversationDto
    );
  }

  @Post()
  @Roles(Role.Patient)
  @HttpCode(201)
  @UseGuards(ProfileAccessGuard)
  @ApiBody({ type: CreateConversationDto })
  @ApiResponse({
    status: 201,
    description: 'Conversation created successfully',
  })
  async CreateConversation(
    @Req() req,
    @Body() createConversationDto: CreateConversationDto
  ) {
    const { user_id, profiles } = req.raw.user;

    const validateProfile = await this.profileService.validateUserProfile(
      profiles,
      user_id,
      createConversationDto?.profile_id
    );

    if (!validateProfile) {
      throw new NotFoundException('User profile not found!');
    }
    return this.conversationListenerService.createConversation(
      new CreateConversationEvent(this.eventEmitter, createConversationDto)
    );
  }

  @Get()
  @Roles(Role.Patient)
  @HttpCode(200)
  @UseGuards(ProfileAccessGuard)
  @ApiQuery({
    name: 'session_id',
    required: true,
    example: 2,
  })
  @ApiQuery({
    name: 'profile_id',
    required: true,
    example: 2,
  })
  @ApiQuery({ name: 'sortBy', required: false, example: 'created_at' })
  @ApiQuery({ name: 'order', required: false, example: 'desc' })
  @ApiQuery({ name: 'skip', required: false, example: 0 })
  @ApiQuery({ name: 'offset', required: false, example: 0 })
  @ApiQuery({ name: 'limit', required: false, example: 5 })
  async findAll(@Query() query) {
    const sanitizedQuery = {
      ...query,
      profile_id: ~~query.profile_id,
      session_id: ~~query.session_id,
      sortBy: query.sortBy || 'created_at',
      order: query.order || 'desc',
      skip: ~~query.skip || 0,
      offset: ~~query.offset || 0,
      limit: ~~query.limit || 50,
    };
    return await this.conversationService.findAll({ query: sanitizedQuery });
  }

  @Get('agent-details/:agent_type')
  @Roles(Role.Patient)
  @HttpCode(200)
  @ApiQuery({
    name: 'agent_type',
    required: true,
    example: 'INTAKE_AGENT',
  })
  async getConversationalAgentDetails(
    @Req() req,
    @Param('agent_type') agent_type: ConversationalAgentType
  ) {
    return await this.elevenlabsService.getConversationalAgentDetails(
      agent_type
    );
  }
}
