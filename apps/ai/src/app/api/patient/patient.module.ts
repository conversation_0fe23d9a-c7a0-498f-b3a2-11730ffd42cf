import { Module, NestModule, MiddlewareConsumer } from '@nestjs/common';
import { TokenMiddleware } from '@core_be/auth';
import { PrismaService } from '@core/prisma-client';
import { PatientController } from './controllers/patient.controller';
import { PatientService } from './services/patient.service';
import { LibProfileService } from '@core/libs';

@Module({
  imports: [],
  controllers: [PatientController],
  providers: [PatientService, LibProfileService, PrismaService],
})
export class PatientModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(TokenMiddleware).forRoutes(PatientController);
  }
}
