import {
  BadRequestException,
  Controller,
  ForbiddenException,
  Get,
  NotFoundException,
  Param,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiBearerAuth } from '@nestjs/swagger';
import {
  Role,
  Roles,
  RolesGuard,
  LibProfileService,
  ProfileType,
} from '@core/libs';
import { PatientIdParams } from '../dto/patient.dto';
import { SessionService } from '../../sessions/services/session.service';
import { ProfileAccessGuard } from 'libs/global/src/lib/guards/profile-access.guard';
import { ProfileTypes } from 'libs/global/src/lib/decorators/profile-types.decorator';

@ApiTags('Patient')
@ApiBearerAuth()
@Controller('patient')
@UseGuards(RolesGuard)
export class PatientController {
  constructor(
    private readonly profileService: LibProfileService,
    private readonly sessionService: SessionService
  ) {}

  @Get(':profile_id/dashboard')
  @Roles(Role.Admin, Role.Patient)
  @UseGuards(ProfileAccessGuard)
  @ProfileTypes(ProfileType.PATIENT)
  async getDashboard(@Req() req, @Param() params: PatientIdParams) {
    const { user_id, profiles } = req.raw.user;
    if (!params.profile_id || isNaN(Number(params.profile_id))) {
      throw new BadRequestException('Invalid profile_id');
    }
    const validateUserProfile = await this.profileService.validateUserProfile(
      profiles,
      user_id,
      +params.profile_id,
      ProfileType.PATIENT
    );

    if (!validateUserProfile) {
      throw new NotFoundException('User profile not found.');
    }

    return await this.sessionService.getAllSessions(params.profile_id);
  }

  @Get(':profile_id/session/:session_id')
  @Roles(Role.Admin, Role.Patient)
  @UseGuards(ProfileAccessGuard)
  @ProfileTypes(ProfileType.PATIENT)
  async findOne(
    @Req() req,
    @Param('profile_id') profile_id: string,
    @Param('session_id') session_id: string
  ) {
    const { user_id, profiles } = req.raw.user;
    const profileID = ~~profile_id;
    const sessionID = ~~session_id;
    let isUserAccessing = false;
    // Validate ownership unless admin
    if (!req.raw.user.roles?.includes(Role.Admin)) {
      isUserAccessing = true;
      const validateUserProfile = await this.profileService.validateUserProfile(
        profiles,
        user_id,
        profileID,
        ProfileType.PATIENT
      );
      if (!validateUserProfile) {
        throw new ForbiddenException(
          'You are not authorized to access this session.'
        );
      }
    }

    return await this.sessionService.findOne(
      sessionID,
      profileID,
      isUserAccessing
    );
  }
}
