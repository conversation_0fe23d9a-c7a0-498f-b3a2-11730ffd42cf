import { Test, TestingModule } from '@nestjs/testing';
import { PatientController } from './patient.controller';
import { HealerService } from '../services/patient.service';
import { BadRequestException } from '@nestjs/common';

describe('PatientController', () => {
  let controller: PatientController;
  let healerService: PatientService;

  beforeEach(async () => {
    const mockHealerService = {
      getDashboard: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [PatientController],
      providers: [{ provide: HealerService, useValue: mockHealerService }],
    }).compile();

    controller = module.get<PatientController>(PatientController);
    healerService = module.get<HealerService>(PatientService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getDashboard', () => {
    it('should return healer dashboard data when profile_id is valid', async () => {
      const mockParams: HealerIdParams = { profile_id: 1 };
      const mockDashboardData = {
        healer_status: 'Available',
        patient_count: 5,
      };

      jest
        .spyOn(healerService, 'getDashboard')
        .mockResolvedValue(mockDashboardData);

      const result = await controller.getDashboard(mockParams);
      expect(result).toEqual(mockDashboardData);
      expect(healerService.getDashboard).toHaveBeenCalledWith({
        profile_id: 1,
      });
    });

    it('should throw BadRequestException if profile_id is invalid', async () => {
      const mockParams: HealerIdParams = { profile_id: NaN };

      await expect(controller.getDashboard(mockParams)).rejects.toThrow(
        BadRequestException
      );
    });
  });
});
