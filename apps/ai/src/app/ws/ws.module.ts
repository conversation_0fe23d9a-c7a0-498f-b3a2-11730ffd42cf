import { Global, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { ConversationService } from '../api/conversations/services/conversation.service';
import { CacheModule } from '@nestjs/cache-manager';
import { cacheConfigurationOptions, LibSessionService, MeasurePerformance } from '@core_be/global';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { WS_LISTEN } from '@core/libs';
import { LibNotificationService } from '@core_be/notifications';
import { PublisherService } from 'libs/global/src/lib/queue/publisher.service';
import { HealerStatusRepository } from 'libs/data-access/src/lib/healer_status/healer_status.repository';

@Global()
@Module({
  imports: [
    ConfigModule,
    JwtModule,
    CacheModule.registerAsync(cacheConfigurationOptions),
    EventEmitterModule.forRoot({
      delimiter: WS_LISTEN.DELIMITER,
      ignoreErrors: false,
      maxListeners: 1,
      wildcard: true,
      verboseMemoryLeak: true,
    }),
  ],
  providers: [
    ConversationService,
    LibSessionService,
    LibNotificationService,
    PublisherService,
    HealerStatusRepository,
    MeasurePerformance,
  ],
  exports: [CacheModule],
})
export class WsModule {}
