{"name": "integration-e2e", "$schema": "../../node_modules/nx/schemas/project-schema.json", "implicitDependencies": ["ai", "core"], "projectType": "application", "targets": {"e2e": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{e2eProjectRoot}"], "options": {"jestConfig": "apps/integration-e2e/jest.config.ts", "passWithNoTests": true}, "dependsOn": ["ai:build", "core:build"], "configurations": {}}, "lint": {"executor": "nx:run-commands", "cache": true, "inputs": ["default", "^default", "{workspaceRoot}/.eslintrc.json", "{projectRoot}/.eslintrc.json", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "options": {"cwd": "apps/integration-e2e", "command": "eslint ."}, "configurations": {}}}, "tags": []}