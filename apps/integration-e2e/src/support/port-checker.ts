#!/usr/bin/env node

import * as net from 'net';
import axios from 'axios';

// Function to check if a port is available
function isPortAvailable(port: number): Promise<boolean> {
  return new Promise((resolve) => {
    const server = net.createServer();

    server.listen(port, () => {
      server.close(() => {
        resolve(true); // Port is available
      });
    });

    server.on('error', () => {
      resolve(false); // Port is in use
    });
  });
}

// Function to check if service is responding
async function isServiceResponding(url: string): Promise<boolean> {
  try {
    await axios.get(url, { timeout: 2000 });
    return true;
  } catch (error) {
    return false;
  }
}

async function checkPorts() {
  const aiPort = parseInt(process.env.AI_PORT || '3001');
  const corePort = parseInt(process.env.PORT_CORE || '3000');
  const aiHealthUrl = `http://localhost:${aiPort}/`;
  const coreHealthUrl = `http://localhost:${corePort}/`;

  console.log('🔍 Checking port availability and service status...\n');

  // Check AI service
  console.log(`AI Service (Port ${aiPort}):`);
  const aiPortAvailable = await isPortAvailable(aiPort);
  if (aiPortAvailable) {
    console.log('  ✅ Port is available');
    console.log('  ℹ️  Service is not running');
  } else {
    console.log('  ❌ Port is occupied');
    const aiResponding = await isServiceResponding(aiHealthUrl);
    if (aiResponding) {
      console.log('  ✅ AI service is responding correctly');
    } else {
      console.log('  ⚠️  Port occupied but service not responding');
      console.log(`     Try: lsof -ti:${aiPort} | xargs kill`);
    }
  }

  console.log();

  // Check Core service
  console.log(`Core Service (Port ${corePort}):`);
  const corePortAvailable = await isPortAvailable(corePort);
  if (corePortAvailable) {
    console.log('  ✅ Port is available');
    console.log('  ℹ️  Service is not running');
  } else {
    console.log('  ❌ Port is occupied');
    const coreResponding = await isServiceResponding(coreHealthUrl);
    if (coreResponding) {
      console.log('  ✅ Core service is responding correctly');
    } else {
      console.log('  ⚠️  Port occupied but service not responding');
      console.log(`     Try: lsof -ti:${corePort} | xargs kill`);
    }
  }

  console.log('\n📋 Summary:');
  if (aiPortAvailable && corePortAvailable) {
    console.log('  Ready to run integration tests (both services will be auto-started)');
  } else if (!aiPortAvailable && !corePortAvailable) {
    const aiOk = await isServiceResponding(aiHealthUrl);
    const coreOk = await isServiceResponding(coreHealthUrl);
    if (aiOk && coreOk) {
      console.log('  Ready to run integration tests (both services already running)');
    } else {
      console.log('  ⚠️  Port conflicts detected - see messages above');
    }
  } else {
    console.log('  Mixed state - integration tests will start missing services');
  }
}

// Run the check
checkPorts().catch(console.error);
