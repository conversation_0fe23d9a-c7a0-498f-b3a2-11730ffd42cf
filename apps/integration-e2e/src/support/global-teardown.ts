/* eslint-disable */
import { ChildProcess } from 'child_process';

function killProcess(process: ChildProcess, serviceName: string): Promise<void> {
  return new Promise((resolve) => {
    if (!process || process.killed) {
      console.log(`${serviceName} process was already terminated`);
      resolve();
      return;
    }

    process.on('exit', () => {
      console.log(`${serviceName} service stopped`);
      resolve();
    });

    // Try graceful shutdown first
    process.kill('SIGTERM');

    // Force kill after 5 seconds if graceful shutdown fails
    setTimeout(() => {
      if (!process.killed) {
        console.log(`Force killing ${serviceName} service...`);
        process.kill('SIGKILL');
      }
    }, 5000);
  });
}

module.exports = async function () {
  console.log(globalThis.__TEARDOWN_MESSAGE__);

  const aiProcess: ChildProcess = globalThis.__AI_PROCESS__;
  const coreProcess: ChildProcess = globalThis.__CORE_PROCESS__;
  const startedAI: boolean = globalThis.__STARTED_AI__;
  const startedCore: boolean = globalThis.__STARTED_CORE__;

  try {
    // Only stop AI service if we started it
    if (aiProcess && startedAI) {
      console.log('Stopping AI service (started by test setup)...');
      await killProcess(aiProcess, 'AI');
    } else if (!startedAI) {
      console.log('AI service was already running externally - leaving it running');
    }

    // Only stop Core service if we started it
    if (coreProcess && startedCore) {
      console.log('Stopping Core service (started by test setup)...');
      await killProcess(coreProcess, 'Core');
    } else if (!startedCore) {
      console.log('Core service was already running externally - leaving it running');
    }

    console.log('Integration test environment cleaned up successfully\n');

  } catch (error) {
    console.error('Error during teardown:', error);

    // Force kill any remaining processes that we started
    if (aiProcess && !aiProcess.killed && startedAI) {
      console.log('Force killing AI service that we started...');
      aiProcess.kill('SIGKILL');
    }
    if (coreProcess && !coreProcess.killed && startedCore) {
      console.log('Force killing Core service that we started...');
      coreProcess.kill('SIGKILL');
    }
  }
};
