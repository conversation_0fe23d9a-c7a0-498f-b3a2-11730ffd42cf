/* eslint-disable */
import { spawn, ChildProcess } from 'child_process';
import axios from 'axios';
import * as net from 'net';

let aiProcess: ChildProcess;
let coreProcess: ChildProcess;

// Function to check if a port is available
function isPortAvailable(port: number): Promise<boolean> {
  return new Promise((resolve) => {
    const server = net.createServer();

    server.listen(port, () => {
      server.close(() => {
        resolve(true); // Port is available
      });
    });

    server.on('error', () => {
      resolve(false); // Port is in use
    });
  });
}

// Function to check if service is already running and responding
async function isServiceRunning(url: string): Promise<boolean> {
  try {
    await axios.get(url, { timeout: 2000 });
    return true;
  } catch (error) {
    return false;
  }
}

// Function to wait for service to be ready
async function waitForService(url: string, timeout = 60000): Promise<void> {
  const start = Date.now();

  while (Date.now() - start < timeout) {
    try {
      await axios.get(url);
      console.log(`Service at ${url} is ready`);
      return;
    } catch (error) {
      // Service not ready yet, wait and retry
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  throw new Error(`Service at ${url} did not start within ${timeout}ms`);
}

module.exports = async function () {
  console.log('\nSetting up integration test environment...\n');

  const aiPort = parseInt(process.env.AI_PORT || '3001');
  const corePort = parseInt(process.env.PORT_CORE || '3000');
  const aiHealthUrl = `http://localhost:${aiPort}/`;
  const coreHealthUrl = `http://localhost:${corePort}/`;

  try {
    // Check if services are already running
    console.log('Checking if services are already running...');
    const aiAlreadyRunning = await isServiceRunning(aiHealthUrl);
    const coreAlreadyRunning = await isServiceRunning(coreHealthUrl);

    let startedAI = false;
    let startedCore = false;

    // Handle AI service
    if (aiAlreadyRunning) {
      console.log(`✓ AI service is already running on port ${aiPort} (started externally)`);
    } else {
      const aiPortAvailable = await isPortAvailable(aiPort);
      if (!aiPortAvailable) {
        throw new Error(`Port ${aiPort} is occupied but AI service is not responding at ${aiHealthUrl}. Please free the port or start the AI service manually.`);
      }

      console.log(`Starting AI service on port ${aiPort}...`);
      aiProcess = spawn('npx', ['nx', 'serve', 'ai'], {
        stdio: ['ignore', 'pipe', 'pipe'],
        detached: false,
      });
      startedAI = true;
    }

    // Handle Core service
    if (coreAlreadyRunning) {
      console.log(`✓ Core service is already running on port ${corePort} (started externally)`);
    } else {
      const corePortAvailable = await isPortAvailable(corePort);
      if (!corePortAvailable) {
        throw new Error(`Port ${corePort} is occupied but Core service is not responding at ${coreHealthUrl}. Please free the port or start the Core service manually.`);
      }

      console.log(`Starting Core service on port ${corePort}...`);
      coreProcess = spawn('npx', ['nx', 'serve', 'core'], {
        stdio: ['ignore', 'pipe', 'pipe'],
        detached: false,
      });
      startedCore = true;
    }

    // Wait for services to be ready
    if (!aiAlreadyRunning) {
      console.log(`Waiting for AI service to be ready on port ${aiPort}...`);
      await waitForService(aiHealthUrl);
    }

    if (!coreAlreadyRunning) {
      console.log(`Waiting for Core service to be ready on port ${corePort}...`);
      await waitForService(coreHealthUrl);
    }

    console.log('All services are ready for integration testing!\n');

    // Store process references and flags for teardown
    globalThis.__AI_PROCESS__ = aiProcess;
    globalThis.__CORE_PROCESS__ = coreProcess;
    globalThis.__STARTED_AI__ = startedAI;
    globalThis.__STARTED_CORE__ = startedCore;
    globalThis.__TEARDOWN_MESSAGE__ = '\nTearing down integration test environment...\n';

  } catch (error) {
    console.error('Failed to setup integration test environment:', error);

    // Cleanup if startup failed
    if (aiProcess) {
      aiProcess.kill('SIGTERM');
    }
    if (coreProcess) {
      coreProcess.kill('SIGTERM');
    }

    throw error;
  }
};
