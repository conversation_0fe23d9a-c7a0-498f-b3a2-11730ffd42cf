import axios from 'axios';

describe('Ennie Integration Tests', () => {
  const AI_PORT = process.env.AI_PORT || '3001';
  const CORE_PORT = process.env.PORT_CORE || '3000';
  const AI_BASE_URL = `http://localhost:${AI_PORT}`;
  const CORE_BASE_URL = `http://localhost:${CORE_PORT}`;

  // Test data configuration via environment variables
  const PATIENT_LOGIN = process.env.PATIENT_LOGIN || '<EMAIL>';
  const PATIENT_PASSWORD = process.env.PATIENT_PASSWORD || 'TestPassword123!';

  beforeAll(() => {
    // Services should already be running from global setup
    console.log('Starting Ennie integration tests...');
  });

  describe('Health Checks', () => {
    it('should have AI service running', async () => {
      const response = await axios.get(`${AI_BASE_URL}/`);
      expect(response.status).toBe(200);
    });

    it('should have Core service running', async () => {
      const response = await axios.get(`${CORE_BASE_URL}/`);
      expect(response.status).toBe(200);
    });
  });

  describe('Authentication Tests', () => {
    describe('Patient Login', () => {
      it('should successfully login with patient role', async () => {
        const loginPayload = {
          username: PATIENT_LOGIN,
          password: PATIENT_PASSWORD,
        };

        const response = await axios.post(
          `${CORE_BASE_URL}/api/auth/login`,
          loginPayload,
          {
            headers: {
              'Content-Type': 'application/json',
            },
          }
        ).catch((e) => e.response);

        // Expecting successful login
        expect(response.status).toBe(200);
        expect(response.data).toBeDefined();
        expect(response.data.success).toBe(true);
        expect(response.data.data).toBeDefined();
        expect(response.data.data.access_token).toBeDefined();
        expect(response.data.data.user_id).toBeDefined();
        expect(response.data.data.email).toBeDefined();

        // Verify the response structure matches expected format
        expect(typeof response.data.data.access_token).toBe('string');
        expect(typeof response.data.data.user_id).toBe('number');
        expect(typeof response.data.data.email).toBe('string');

        // Verify additional fields that should be present
        expect(response.data.data.onboarding_status).toBeDefined();
        expect(Array.isArray(response.data.data.profiles)).toBe(true);
        expect(response.data.data.profiles.length).toBeGreaterThan(0);
      });

      it('should fail login with invalid patient credentials', async () => {
        const loginPayload = {
          username: PATIENT_LOGIN,
          password: 'wrong_password',
        };

        const response = await axios.post(
          `${CORE_BASE_URL}/api/auth/login`,
          loginPayload,
          {
            headers: {
              'Content-Type': 'application/json',
            },
          }
        ).catch((e) => e.response);

        // Expecting authentication failure with 401 status
        expect(response.status).toBe(401);
        expect(response.data.success).toBe(false);
        expect(response.data.message).toContain('Username or password does not match');
      });

      it('should fail login with missing credentials', async () => {
        const loginPayload = {
          username: '',
          password: '',
        };

        const response = await axios.post(
          `${CORE_BASE_URL}/api/auth/login`,
          loginPayload,
          {
            headers: {
              'Content-Type': 'application/json',
            },
          }
        ).catch((e) => e.response);

        // API returns 401 for missing credentials (treats as unauthorized)
        expect(response.status).toBe(401);
        expect(response.data.success).toBe(false);
        expect(response.data.message).toBeDefined();
      });
    });
  });
});
