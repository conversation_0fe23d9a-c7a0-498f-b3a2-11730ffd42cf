import axios from 'axios';

describe('Intake Integration Tests', () => {
  const AI_PORT = process.env.AI_PORT || '3001';
  const CORE_PORT = process.env.PORT_CORE || '3000';
  const AI_BASE_URL = `http://localhost:${AI_PORT}`;
  const CORE_BASE_URL = `http://localhost:${CORE_PORT}`;

  // Test data configuration via environment variables
  const PATIENT_LOGIN = process.env.PATIENT_LOGIN || '<EMAIL>';
  const PATIENT_PASSWORD = process.env.PATIENT_PASSWORD || 'TestPassword123!';

  let authToken: string;
  let patientProfileId: number;

  // Helper function to create a new session
  const createSession = async (token: string, profileId: number): Promise<string> => {
    const sessionPayload = {
      profile_id: profileId,
    };

    const sessionResponse = await axios.post(
      `${AI_BASE_URL}/api/sessions`,
      sessionPayload,
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      }
    );

    expect(sessionResponse.status).toBe(201);
    return sessionResponse.data.data.session_id;
  };

  // Helper function to get session data
  const getSession = async (token: string, profileId: number, sessionId: string): Promise<any> => {
    return await axios.get(
      `${AI_BASE_URL}/api/patient/${profileId}/session/${sessionId}`,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      }
    ).catch((e) => e.response);
  };

  beforeAll(async () => {
    // Login to get patient profile ID and access token
    const loginPayload = {
      username: PATIENT_LOGIN,
      password: PATIENT_PASSWORD,
    };

    const loginResponse = await axios.post(
      `${CORE_BASE_URL}/api/auth/login`,
      loginPayload,
      {
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    expect(loginResponse.status).toBe(200);
    authToken = loginResponse.data.data.access_token;
    patientProfileId = loginResponse.data.data.profiles[0].profile_id;
  });

  describe('Bulk Conversation Endpoint', () => {
    it('should successfully ingest intake conversation and validate session information', async () => {
      // Arrange
      // Create a new session for this test
      const inProgressSessionId = await createSession(authToken, patientProfileId);

      // Act
      const response = await axios.post(
        `${AI_BASE_URL}/api/conversations/bulk`,
        {
          profile_id: patientProfileId,
          session_id: inProgressSessionId,
          conversation_status: "IN_PROGRESS",
          conversation_type: "INTAKE",
          conversation: [
            {
              message_type: "Audio",
              content: "Hello, I'm Charlie Goldsmith. I'm here to help you get ready for your healing session. Can you tell me what's going on with you today?",
              sender_type: "AI"
            },
            {
              content: "I have a finger cramp",
              message_type: "Audio",
              sender_type: "Patient"
            },
            {
              message_type: "Audio",
              content: "Okay, I understand. Can you tell me a little bit more about your pain?  On a scale of zero to ten, with zero being no pain and ten being the worst pain you can imagine, how would you rate the severity of your pain right now? ",
              sender_type: "AI"
            }
          ]
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${authToken}`,
          },
        }
      ).catch((e) => e.response);

      // Assert - Endpoint response
      // Simple assertions as requested
      expect(response.status).toBe(201);
      expect(response.data).toBeDefined();
      expect(response.data.success).toBe(true);
      expect(response.data.data).toBeDefined();
      expect(response.data.data.count).toBe(3); // Should create 4 conversation records

      // Assert - Database Updates thru sessions endpoint
      // Call patient session endpoint to verify AI response has been inserted and see session info
      const getConversationResponse = await getSession(authToken, patientProfileId, inProgressSessionId);

      // Validate the patient session endpoint response
      expect(getConversationResponse.status).toBe(200);
      expect(getConversationResponse.data).toBeDefined();
      expect(getConversationResponse.data.success).toBe(true);
      expect(getConversationResponse.data.data).toBeDefined();

      const sessionData = getConversationResponse.data.data;

      // Validate session information
      expect(sessionData.session_id).toBe(inProgressSessionId);
      expect(sessionData.profile_id).toBe(patientProfileId);
      expect(sessionData.status).toBe('INTAKE');
      expect(sessionData.sub_status).toBe('IN_PROGRESS');
      expect(sessionData.thread_id).toBeDefined();
      expect(typeof sessionData.thread_id).toBe('string');

      // Verify that there are exactly 4 conversations
      const conversations = sessionData.conversations;
      expect(Array.isArray(conversations)).toBe(true);
      expect(conversations.length).toBe(3);

      // Verify that conversations contain the expected content
      const patientMessages = conversations.filter(conv => conv.sender_type === 'Patient');
      const aiMessages = conversations.filter(conv => conv.sender_type === 'AI');

      expect(patientMessages.length).toBe(1); // 2 patient messages
      expect(aiMessages.length).toBe(2); // 2 AI messages
    });

    it('should successfully process bulk conversation with COMPLETED intake conversation', async () => {
      // Arrange
      // Create a new session for the completed conversation test
      const completedSessionId = await createSession(authToken, patientProfileId);

      // Act
      const response = await axios.post(
        `${AI_BASE_URL}/api/conversations/bulk`,
        {
          profile_id: patientProfileId,
          session_id: completedSessionId,
          conversation_status: "COMPLETED",
          conversation_type: "INTAKE",
          conversation: [
            {
              message_type: "Audio",
              content: "Hello, I'm Charlie Goldsmith. I'm here to help you get ready for your healing session. Can you tell me what's going on with you today?",
              sender_type: "AI"
            },
            {
              content: "I have a finger cramp",
              message_type: "Audio",
              sender_type: "Patient"
            },
            {
              message_type: "Audio",
              content: "Okay, I understand. Can you tell me a little bit more about your pain?  On a scale of zero to ten, with zero being no pain and ten being the worst pain you can imagine, how would you rate the severity of your pain right now? ",
              sender_type: "AI"
            },
            {
              message_type: "Audio",
              content: "seven.",
              sender_type: "Patient"
            },
            {
              message_type: "Audio",
              content: "Any other symptoms ?",
              sender_type: "AI"
            },
            {
              message_type: "Audio",
              content: "No",
              sender_type: "Patient"
            }
          ]
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${authToken}`,
          },
        }
      ).catch((e) => e.response);

      // Assert - Endpoint response
      // Simple assertions for bulk conversation endpoint
      expect(response.status).toBe(201);
      expect(response.data).toBeDefined();
      expect(response.data.success).toBe(true);
      expect(response.data.data).toBeDefined();
      expect(response.data.data.count).toBe(6); // Should create 6 conversation records

      // Assert - Database Updates thru sessions endpoint
      // Call patient session endpoint to verify completed conversation processing
      const getSessionResponse = await getSession(authToken, patientProfileId, completedSessionId);
      // Validate the patient session endpoint response
      expect(getSessionResponse.status).toBe(200);
      expect(getSessionResponse.data).toBeDefined();
      expect(getSessionResponse.data.success).toBe(true);
      expect(getSessionResponse.data.data).toBeDefined();

      const sessionData = getSessionResponse.data.data;

      // Validate session information for completed conversation
      expect(sessionData.session_id).toBe(completedSessionId);
      expect(sessionData.profile_id).toBe(patientProfileId);
      expect(sessionData.status).toBe('IN_QUEUE'); // Completed conversation moves to IN_QUEUE
      expect(sessionData.sub_status).toBe('AVAILABLE'); // Sub status becomes AVAILABLE
      expect(sessionData.thread_id).toBeDefined();
      expect(typeof sessionData.thread_id).toBe('string');

      // Verify that there are exactly 6 conversations (all original messages)
      const conversations = sessionData.conversations;
      expect(Array.isArray(conversations)).toBe(true);
      expect(conversations.length).toBe(6); // Should have all 6 original messages

      // Verify that conversations contain the expected content
      const patientMessages = conversations.filter(conv => conv.sender_type === 'Patient');
      const aiMessages = conversations.filter(conv => conv.sender_type === 'AI');

      expect(patientMessages.length).toBe(3); // 3 patient messages
      expect(aiMessages.length).toBe(3); // 3 AI messages

      // Validate that summary was generated for completed conversation
      expect(sessionData.summary_info).toBeDefined();

      expect(sessionData.summary_info.summary_id).toBeDefined();
      expect(sessionData.summary_info.content).toBeDefined();
      expect(typeof sessionData.summary_info.content).toBe('string');
      expect(sessionData.summary_info.content.length).toBeGreaterThan(0);
      expect(sessionData.summary_info.content).toContain('Finger cramp'); // Should reference the ailment

      // Validate that ailments were extracted from completed conversation
      expect(sessionData.ailments).toBeDefined();
      expect(Array.isArray(sessionData.ailments)).toBe(true);
      expect(sessionData.ailments.length).toBeGreaterThan(0);

      const fingerCrampAilment = sessionData.ailments.find(ailment =>
        ailment.name.toLowerCase().includes('finger cramp')
      );
      expect(fingerCrampAilment).toBeDefined();
      expect(fingerCrampAilment.pain_level_before).toBe(7); // Should extract pain level from conversation
      expect(fingerCrampAilment.session_ailment_id).toBeDefined();

      // Validate queue information if session moved to IN_QUEUE
      expect(sessionData.session_queue_info).toBeDefined();
      expect(sessionData.session_queue_info.queue_status).toBe('IN_QUEUE');
      expect(sessionData.session_queue_info.queue_sub_status).toBe('AVAILABLE');
      expect(sessionData.session_queue_info.queue_number).toBeDefined();
    });
  });

  describe('Individual Conversation Endpoint', () => {
    it('should successfully process single conversation as an intermediate step of an intake conversation', async () => {
      // Arrange
      // Create a new session for the completed conversation test
      const sessionId = await createSession(authToken, patientProfileId);

      const prepBulkResponse = await axios.post(
        `${AI_BASE_URL}/api/conversations/bulk`,
        {
          profile_id: patientProfileId,
          session_id: sessionId,
          conversation_status: "IN_PROGRESS",
          conversation_type: "INTAKE",
          conversation: [
            {
              message_type: "Audio",
              content: "Hello, I'm Charlie Goldsmith. I'm here to help you get ready for your healing session. Can you tell me what's going on with you today?",
              sender_type: "AI"
            },
            {
              content: "I have a finger cramp",
              message_type: "Audio",
              sender_type: "Patient"
            },
            {
              message_type: "Audio",
              content: "Okay, I understand. Can you tell me a little bit more about your pain?  On a scale of zero to ten, with zero being no pain and ten being the worst pain you can imagine, how would you rate the severity of your pain right now? ",
              sender_type: "AI"
            }
          ]
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${authToken}`,
          },
        }
      ).catch((e) => e.response);

      // Assert - Endpoint response
      // Simple assertions for bulk conversation endpoint
      expect(prepBulkResponse.status).toBe(201);
      expect(prepBulkResponse.data).toBeDefined();
      expect(prepBulkResponse.data.success).toBe(true);
      expect(prepBulkResponse.data.data).toBeDefined();
      expect(prepBulkResponse.data.data.count).toBe(3); // Should create 3 conversation records after bulk endpoint

      // Act
      const response = await axios.post(
        `${AI_BASE_URL}/api/conversations`,
        {
          profile_id: patientProfileId,
          session_id: sessionId,
          content: "Seven.",
          message_type: "Text",
          sender_type: "Patient"
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${authToken}`,
          },
        }
      ).catch((e) => e.response);

      // Assert - Endpoint response
      expect(response.status).toBe(201);
      expect(response.data).toBeDefined();
      expect(response.data.success).toBe(true);
      expect(response.data.data).toBeDefined();

      // Assert - Database Updates thru sessions endpoint
      // Call patient session endpoint to verify completed conversation processing
      const getSessionResponse = await getSession(authToken, patientProfileId, sessionId);

      // Validate the patient session endpoint response
      expect(getSessionResponse.status).toBe(200);
      expect(getSessionResponse.data).toBeDefined();
      expect(getSessionResponse.data.success).toBe(true);
      expect(getSessionResponse.data.data).toBeDefined();

      const sessionData = getSessionResponse.data.data;

      // Validate session information for intermediate conversation
      expect(sessionData.session_id).toBe(sessionId);
      expect(sessionData.profile_id).toBe(patientProfileId);
      expect(sessionData.status).toBe('INTAKE');
      expect(sessionData.sub_status).toBe('IN_PROGRESS');
      expect(sessionData.thread_id).toBeDefined();
      expect(typeof sessionData.thread_id).toBe('string');

      // Verify that there are exactly 5 conversations (all original messages + 1 user response + 1 AI response)
      const conversations = sessionData.conversations;
      expect(Array.isArray(conversations)).toBe(true);
      expect(conversations.length).toBe(5);

      // Verify that conversations contain the expected content
      const patientMessages = conversations.filter(conv => conv.sender_type === 'Patient');
      const aiMessages = conversations.filter(conv => conv.sender_type === 'AI');

      expect(patientMessages.length).toBe(2);
      expect(aiMessages.length).toBe(3);
    });

    it('should successfully process single conversation as the last step of an intake conversation', async () => {
      // Arrange
      // Create a new session for the completed conversation test
      const sessionId = await createSession(authToken, patientProfileId);

      const prepBulkResponse = await axios.post(
        `${AI_BASE_URL}/api/conversations/bulk`,
        {
          profile_id: patientProfileId,
          session_id: sessionId,
          conversation_status: "IN_PROGRESS",
          conversation_type: "INTAKE",
          conversation: [
            {
              message_type: "Audio",
              content: "Hello, I'm Charlie Goldsmith. I'm here to help you get ready for your healing session. Can you tell me what's going on with you today?",
              sender_type: "AI"
            },
            {
              content: "I have a finger cramp",
              message_type: "Audio",
              sender_type: "Patient"
            },
            {
              message_type: "Audio",
              content: "Okay, I understand. Can you tell me a little bit more about your pain?  On a scale of zero to ten, with zero being no pain and ten being the worst pain you can imagine, how would you rate the severity of your pain right now? ",
              sender_type: "AI"
            },
            {
              message_type: "Audio",
              content: "seven.",
              sender_type: "Patient"
            },
            {
              message_type: "Audio",
              content: "Any other symptoms ?",
              sender_type: "AI"
            },
            {
              message_type: "Audio",
              content: "No",
              sender_type: "Patient"
            },
            {
              message_type: "Audio",
              content: "Thank you for sharing. To confirm, you are experiencing a finger cramp with a pain level of 7 out of 10, and there are no other symptoms. Is that correct?",
              sender_type: "AI"
            }
          ]
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${authToken}`,
          },
        }
      ).catch((e) => e.response);

      // Assert - Endpoint response
      // Simple assertions for bulk conversation endpoint
      expect(prepBulkResponse.status).toBe(201);
      expect(prepBulkResponse.data).toBeDefined();
      expect(prepBulkResponse.data.success).toBe(true);
      expect(prepBulkResponse.data.data).toBeDefined();
      expect(prepBulkResponse.data.data.count).toBe(7); // Should create 3 conversation records after bulk endpoint

      // Act
      const response = await axios.post(
        `${AI_BASE_URL}/api/conversations`,
        {
          profile_id: patientProfileId,
          session_id: sessionId,
          content: "Yes, that's correct.",
          message_type: "Text",
          sender_type: "Patient"
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${authToken}`,
          },
        }
      ).catch((e) => e.response);

      // Assert - Endpoint response
      expect(response.status).toBe(201);
      expect(response.data).toBeDefined();
      expect(response.data.success).toBe(true);
      expect(response.data.data).toBeDefined();

      // Assert - Database Updates thru sessions endpoint
      // Call patient session endpoint to verify completed conversation processing
      const getSessionResponse = await getSession(authToken, patientProfileId, sessionId);

      // Validate the patient session endpoint response
      expect(getSessionResponse.status).toBe(200);
      expect(getSessionResponse.data).toBeDefined();
      expect(getSessionResponse.data.success).toBe(true);
      expect(getSessionResponse.data.data).toBeDefined();

      const sessionData = getSessionResponse.data.data;

      // Validate session information for intermediate conversation
      expect(sessionData.session_id).toBe(sessionId);
      expect(sessionData.profile_id).toBe(patientProfileId);
      expect(sessionData.status).toBe('IN_QUEUE'); // Completed conversation moves to IN_QUEUE
      expect(sessionData.sub_status).toBe('AVAILABLE'); // Sub status becomes AVAILABLE
      expect(sessionData.thread_id).toBeDefined();
      expect(typeof sessionData.thread_id).toBe('string');

      // Verify that there are exactly 9 conversations (all original messages + 1 user response + 1 AI response)
      const conversations = sessionData.conversations;
      expect(Array.isArray(conversations)).toBe(true);
      expect(conversations.length).toBe(9);

      // Verify that conversations contain the expected content
      const patientMessages = conversations.filter(conv => conv.sender_type === 'Patient');
      const aiMessages = conversations.filter(conv => conv.sender_type === 'AI');

      expect(patientMessages.length).toBe(4);
      expect(aiMessages.length).toBe(5);

      // Validate that summary was generated for completed conversation
      expect(sessionData.summary_info).toBeDefined();

      expect(sessionData.summary_info.summary_id).toBeDefined();
      expect(sessionData.summary_info.content).toBeDefined();
      expect(typeof sessionData.summary_info.content).toBe('string');
      expect(sessionData.summary_info.content.length).toBeGreaterThan(0);
      expect(sessionData.summary_info.content).toContain('Finger cramp'); // Should reference the ailment

      // Validate that ailments were extracted from completed conversation
      expect(sessionData.ailments).toBeDefined();
      expect(Array.isArray(sessionData.ailments)).toBe(true);
      expect(sessionData.ailments.length).toBeGreaterThan(0);

      const fingerCrampAilment = sessionData.ailments.find(ailment =>
        ailment.name.toLowerCase().includes('finger cramp')
      );
      expect(fingerCrampAilment).toBeDefined();
      expect(fingerCrampAilment.pain_level_before).toBe(7); // Should extract pain level from conversation
      expect(fingerCrampAilment.session_ailment_id).toBeDefined();

      // Validate queue information if session moved to IN_QUEUE
      expect(sessionData.session_queue_info).toBeDefined();
      expect(sessionData.session_queue_info.queue_status).toBe('IN_QUEUE');
      expect(sessionData.session_queue_info.queue_sub_status).toBe('AVAILABLE');
      expect(sessionData.session_queue_info.queue_number).toBeDefined();
    });
  });
});
