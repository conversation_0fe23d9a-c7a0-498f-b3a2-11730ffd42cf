# Integration E2E Tests

This project contains automated integration tests that test the interaction between multiple services in the Healthis backend.

## Overview

The integration test suite automatically:

1. **Checks if services are already running** - If services are already started externally, it will use them
2. **Starts missing services** - Only starts services that aren't already running
3. Waits for them to be ready
4. Runs integration tests against both services
5. **Smart cleanup** - Only stops services that were started by the test suite, leaving externally started services running

This allows developers to start services manually during development and the tests will detect and use them.

## Prerequisites

- All dependencies should be installed (`npm install`)
- Docker services should be running (database, Redis, RabbitMQ, etc.)
- Services should be built (`npm run build`)

## Running Integration Tests

```bash
# Run integration tests
npx nx e2e integration-e2e

# Run with coverage
npx nx e2e integration-e2e --coverage

# Run in watch mode
npx nx e2e integration-e2e --watch
```

## Manual Service Management

### Starting Services Manually (Recommended for Development)

If you prefer to manage services manually during development:

```bash
# Terminal 1: Start Core service
npx nx serve core

# Terminal 2: Start AI service  
npx nx serve ai

# Terminal 3: Run integration tests (will detect running services)
npx nx e2e integration-e2e
```

### Automatic Service Management

The test suite will automatically handle services:

- **Port Check**: Checks if ports 3000 (Core) and 3001 (AI) are available
- **Service Detection**: Tests if services are responding at their health endpoints
- **Smart Startup**: Only starts services that aren't already running
- **Graceful Cleanup**: Only stops services that were started by the test suite

### Port Conflict Resolution

If a port is occupied but the service isn't responding:

```
Error: Port 3001 is occupied but AI service is not responding at http://localhost:3001/. 
Please free the port or start the AI service manually.
```

Solutions:

1. Kill the process using the port: `lsof -ti:3001 | xargs kill`
2. Start the proper service manually: `npx nx serve ai`
3. Use different ports via environment variables

### Port Checking Utility

A convenience script is provided to check port status:

```bash
# Check port availability and service status
npx ts-node apps/integration-e2e/src/support/port-checker.ts
```

This will show you:

- Which ports are available/occupied
- Whether services are responding correctly
- Suggested actions for resolving conflicts

## Test Structure

```
src/
├── integration/           # Integration test files
│   └── ai-core-integration.spec.ts
└── support/              # Test setup and utilities
    ├── global-setup.ts   # Starts services before tests
    ├── global-teardown.ts # Stops services after tests
    └── test-setup.ts     # Per-test setup
```

## Service URLs

- **Core Service**: <http://localhost:3000>
- **AI Service**: <http://localhost:3001>

## Writing Integration Tests

Integration tests should:

1. Test real interactions between services
2. Use actual HTTP requests (not mocked)
3. Verify end-to-end workflows
4. Test error handling across services

Example:

```typescript
describe('User Journey Integration', () => {
  it('should create user in core and process with AI', async () => {
    // 1. Create user session in Core
    const session = await axios.post(`${CORE_BASE_URL}/api/sessions`, {
      userId: 'test-user'
    });
    
    // 2. Process data with AI service
    const aiResult = await axios.post(`${AI_BASE_URL}/api/process`, {
      sessionId: session.data.id,
      data: 'test data'
    });
    
    // 3. Verify results are stored in Core
    const verification = await axios.get(`${CORE_BASE_URL}/api/sessions/${session.data.id}`);
    expect(verification.data.aiProcessed).toBe(true);
  });
});
```

## Configuration

The test configuration can be modified in:

- `jest.config.ts` - Jest test runner settings
- `src/support/global-setup.ts` - Service startup configuration
- `project.json` - Nx project configuration

## Troubleshooting

### Services Not Starting

- **Port conflicts**: Use `lsof -i :3000` and `lsof -i :3001` to check what's using the ports
- **Service detection**: Test manually: `curl http://localhost:3000/`
- Ensure Docker services are running (database, Redis, RabbitMQ)
- Verify service build status: `npx nx build core` and `npx nx build ai`

### Tests Timing Out

- Increase timeout in `jest.config.ts` (current: 120 seconds)
- Check service health endpoints manually
- Verify network connectivity
- If services were started manually, ensure they're fully ready

### Cleanup Issues

- **Auto-started services**: Will be automatically stopped after tests
- **Manually started services**: Will be left running (as intended)
- If services hang, they will be force-killed after 5 seconds
- Check for any remaining processes: `ps aux | grep "nx serve"`

### Mixed Service States

If you have one service running manually and want the test to start the other:

```bash
# Example: Core running manually, let test start AI
npx nx serve core  # Keep this running
npx nx e2e integration-e2e  # Will detect Core and start AI
```

## Environment Variables

### Service Configuration
- `AI_PORT` - Port for the AI service (default: 3001)
- `PORT_CORE` - Port for the Core service (default: 3000)

### Test Data Configuration

For authentication tests, you need to configure test credentials via environment variables:

- `PATIENT_LOGIN` - Username/email for patient login test (default: '<EMAIL>')
- `PATIENT_PASSWORD` - Password for patient login test (default: 'TestPassword123!')

### Setting Environment Variables

You can set these environment variables in several ways:

#### Option 1: Create a `.env` file
Create a `.env` file in the project root:
```bash
# Service ports
AI_PORT=3001
PORT_CORE=3000

# Test credentials
PATIENT_LOGIN=<EMAIL>
PATIENT_PASSWORD=YourTestPassword123!
```

#### Option 2: Export in your shell
```bash
export PATIENT_LOGIN="<EMAIL>"
export PATIENT_PASSWORD="YourTestPassword123!"
```

#### Option 3: Set inline with the test command
```bash
PATIENT_LOGIN="<EMAIL>" PATIENT_PASSWORD="TestPass123!" nx e2e integration-e2e
```

## Test Data Requirements

### Patient Account Setup

**Important**: The authentication tests require an existing patient account in your test database with the credentials specified in the environment variables. The tests do NOT create user accounts - they test against existing data.

You can set up the required test data in several ways:

1. **Database Seeding**: Add test data directly to your test database using database migration scripts or seed files
2. **Manual Creation**: Use the registration endpoint manually to create a test user before running the tests
3. **Existing Account**: Point the environment variables to an existing test account

### Creating Test Data (if needed)

If you need to create a test patient account, you can use the registration API:

```bash
curl -X POST http://localhost:3000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "<EMAIL>",
    "email": "<EMAIL>", 
    "password": "TestPassword123!",
    "profile_type": "Patient",
    "device_id": "test-device-123",
    "device_type": "Web",
    "fcm_token": "test-fcm-token"
  }'
```

**Note**: Make sure the credentials in the environment variables match the account that exists in your database.

## Running the Tests

### Run all integration tests
```bash
nx e2e integration-e2e
```

### Run with custom environment variables
```bash
PATIENT_LOGIN="<EMAIL>" PATIENT_PASSWORD="CustomPass123!" nx e2e integration-e2e
```

### Run specific test suites
```bash
# Run only authentication tests
nx e2e integration-e2e --testNamePattern="Authentication Tests"

# Run only patient login tests
nx e2e integration-e2e --testNamePattern="Patient Login"
```

## Test Structure

- **Health Checks**: Verify that both AI and Core services are running and responding
- **Authentication Tests**: Test login functionality for different user roles
  - Patient login with valid credentials
  - Failed login attempts with invalid credentials
  - Validation error handling

## Troubleshooting

### Services Not Starting
- Check if ports are already in use
- Ensure all dependencies are installed
- Verify database connectivity

### Authentication Test Failures
- **Verify the test patient account exists in your database** - This is the most common issue
- Check that the environment variables are set correctly
- Ensure the password meets the application's requirements
- Verify the database is accessible and properly seeded

### Port Conflicts
If you get port conflicts, you can specify different ports:
```bash
AI_PORT=3101 PORT_CORE=3100 nx e2e integration-e2e
```
