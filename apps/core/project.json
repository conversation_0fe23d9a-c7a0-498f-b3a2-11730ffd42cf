{"name": "core", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/core/src", "projectType": "application", "tags": [], "targets": {"serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "options": {"buildTarget": "core:build"}, "configurations": {"development": {"buildTarget": "core:build:development"}, "production": {"buildTarget": "core:build:production"}}}}}