import { Injectable } from '@nestjs/common';
import { Prisma, PrismaService } from '@core/prisma-client';
import { ANALYTICS_LISTENER, MetricsRepository } from '@core/libs';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { CreateMetricsDto } from '../dto/metrics.dto';
@Injectable()
export class MetricsService {
  constructor(
    private readonly metricsRepository: MetricsRepository,
    private readonly eventEmitter: EventEmitter2,
    private readonly prisma: PrismaService
  ) {}

  async create(data: CreateMetricsDto) {
    this.eventEmitter.emitAsync(ANALYTICS_LISTENER.CREATE, data);
  }

  //   async update(params: {
  //     where: Prisma.eventsWhereUniqueInput;
  //     data: Prisma.eventsUpdateInput;
  //   }): Promise<events> {
  //     const { where, data } = params;
  //     return this.eventsRepositoryService.update({ data, where });
  //   }

  //   async delete(where: Prisma.eventsWhereUniqueInput, data: Prisma.eventsUpdateInput): Promise<events> {
  //     return this.eventsRepositoryService.update({ data, where });
  //   }

  async metric(where: Prisma.metricsWhereUniqueInput) {
    return this.prisma.metrics.findUnique({ where });
  }

  async metrics(params: {
    skip?: number;
    page?: number;
    limit?: number;
    take?: number;
    cursor?: Prisma.metricsWhereUniqueInput;
    where?: Prisma.metricsWhereInput;
    orderBy?: Prisma.metricsOrderByWithAggregationInput;
  }) {
    //const { skip, page, take, limit, cursor, where, orderBy } = params;
    return this.prisma.metrics.findMany({});
  }
}
