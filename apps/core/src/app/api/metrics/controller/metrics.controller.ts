import { Controller, Get, Post, Body, Param, UseGuards, NotFoundException } from '@nestjs/common';
import { CreateMetricsDto } from '../dto/metrics.dto';
import { ApiTags } from '@nestjs/swagger';
import { MetricsService } from '../service/metrics.service';
import { Public } from '@core/libs';

@ApiTags('Metrics')
@Public()
@Controller('metrics')
export class MetricsController {
  constructor(
    private readonly metricsService: MetricsService,
  ) {}


  @Post()
  async create(@Body() createMetricsDto: CreateMetricsDto) {
    const createData = {
      ...createMetricsDto,
    };

    this.metricsService.create(createData);

    return { message:'Sent to queue'};
  }

  @Get()
  async findAll() {
    return this.metricsService.metrics({});
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    const metrics = await this.metricsService.metric({ metric_id: +id });
    if (!metrics) {
      throw new NotFoundException('Metric not found.');
    }
    return metrics;
  }
}

