import { Module, NestModule, MiddlewareConsumer } from '@nestjs/common';
import { TokenMiddleware } from '@core_be/auth';
import { ConfigService } from '@nestjs/config';
import { MetricsController } from './controller/metrics.controller';
import { ConfigModule } from '@nestjs/config';
import { MetricsService } from './service/metrics.service';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ANALYTICS_LISTENER } from '@core_be/global';
import { LibAnalyticsService } from '@core_be/aiml';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    EventEmitterModule.forRoot({
      delimiter: ANALYTICS_LISTENER.DELIMITER,
      ignoreErrors: false,
      maxListeners: 1,
      wildcard: true,
    }),
  ],
  controllers: [MetricsController],
  providers: [ConfigService, MetricsService, LibAnalyticsService],
})
export class MetricsModule {}
