import { ApiProperty } from '@nestjs/swagger';
import {
  IsInt,
  IsNotEmpty,
  IsString,
  IsOptional,
} from '@nestjs/class-validator';

export class CreateOnBoardingDto {
  @ApiProperty({
    example: 1,
    description: 'UserID',
    required: true,
  })
  @IsInt()
  @IsNotEmpty()
  user_id: number;

  @ApiProperty({
    example: 'f6cd7e22955d4d9c',
    description: 'DeviceID',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  device_id?: string;

  @ApiProperty({
    example: 'Android',
    description: 'Device Type',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  device_type?: string;

  @IsOptional()
  device_model?: string;

  @IsOptional()
  os_name?: string;

  @IsOptional()
  os_version?: string;

  @IsOptional()
  app_version?: string;

  @IsOptional()
  notes?: string;
}
