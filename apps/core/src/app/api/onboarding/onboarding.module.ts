import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { OnBoardingController } from './controllers/onboarding.controller';
import { TokenMiddleware } from '@core_be/auth';
import { UsersService } from '../users/services/users.service';
import { WaitlistRepository } from 'libs/data-access/src/lib/waitlist/waitlist.repository';

@Module({
  controllers: [OnBoardingController],
  providers: [UsersService, WaitlistRepository],
})
export class OnBoardingModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(TokenMiddleware).forRoutes(OnBoardingController);
  }
}
