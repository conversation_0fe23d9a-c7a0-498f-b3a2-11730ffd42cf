import { Test, TestingModule } from '@nestjs/testing';
import { OnBoardingController } from './onboarding.controller';
import { CreateOnBoardingDto } from '../dto/create-onboarding.dto';
import { OnBoardingRepository } from '../../../../../../../libs/data-access/src';
import { faker } from '@faker-js/faker';

describe('OnBoardingController', () => {
  const onboardingIdsToCleanup: number[] = [];

  afterEach(async () => {
    while (onboardingIdsToCleanup.length > 0) {
      const onboardingId = onboardingIdsToCleanup.pop();
      if (onboardingId) {
        try {
          await controller.delete(onboardingId);
        } catch (err) {
          console.error(
            `Failed to clean up onboarding with ID: ${onboardingId}`,
            err
          );
        }
      }
    }
  });
  let controller: OnBoardingController;
  let onBoardingRepository: OnBoardingRepository;

  beforeEach(async () => {
    const mockOnBoardingRepository = {
      createOnBoarding: jest.fn(),
      updateManyOnBoarding: jest.fn(),
      deleteOnBoarding: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [OnBoardingController],
      providers: [
        { provide: OnBoardingRepository, useValue: mockOnBoardingRepository },
      ],
    }).compile();

    controller = module.get<OnBoardingController>(OnBoardingController);
    onBoardingRepository =
      module.get<OnBoardingRepository>(OnBoardingRepository);
  });

  describe('create', () => {
    it('should create a new onboarding and return it', async () => {
      const mockCreateDto: CreateOnBoardingDto = {
        user_id: faker.number.int(2),
        device_id: faker.string.alpha(8),
        device_type: faker.helpers.arrayElement(['mobile', 'web', 'tablet']),
      };

      const mockResponse = {
        status_id: faker.number.int(22),
        user_id: faker.number.int(2),
        device_type: mockCreateDto.device_type,
        device_id: mockCreateDto.device_id,
        device_model: '',
        os_name: '',
        os_version: '',
        app_version: '',
        status: 'Active',
        notes: '',
        created_by: 123,
        created_at: new Date(),
        updated_by: 123,
        updated_at: new Date(),
        deleted_by: null,
        deleted_at: null,
        is_deleted: false,
      };

      jest
        .spyOn(onBoardingRepository, 'createOnBoarding')
        .mockResolvedValue(mockResponse);

      const req = { raw: { user: { user_id: 123 } } };

      const result = await controller.create(mockCreateDto, req);
      onboardingIdsToCleanup.push(result.status_id);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('update', () => {
    it('should update onboarding records and return the updated result', async () => {
      const mockUpdateDto = {
        user_id: faker.number.int(2),
        device_id: faker.string.alpha(8),
        device_type: faker.helpers.arrayElement(['mobile', 'web', 'tablet']),
        device_model: faker.string.alpha(8),
      };

      const mockResponse = {
        count: 1,
        data: {
          device_id: 'Device123',
          device_type: 'TypeA',
          device_model: 'ModelX',
          updated_by: 123,
          updated_at: new Date(),
        },
      };

      jest
        .spyOn(onBoardingRepository, 'updateManyOnBoarding')
        .mockResolvedValue(mockResponse);

      const req = { raw: { user: { user_id: 123 } } };

      const result = await controller.update(mockUpdateDto, req);

      expect(result).toEqual(mockResponse);

      expect(onBoardingRepository.updateManyOnBoarding).toHaveBeenCalledWith({
        where: {
          user_id: mockUpdateDto.user_id,
          device_id: mockUpdateDto.device_id,
          device_type: mockUpdateDto.device_type,
        },
        data: {
          device_id: mockUpdateDto.device_id,
          device_type: mockUpdateDto.device_type,
          device_model: mockUpdateDto.device_model,
          updated_by: req.raw.user.user_id,
          updated_at: expect.any(Date),
        },
      });
    });
  });
});
