import {
  Controller,
  Post,
  Body,
  Patch,
  UseGuards,
  Request,
  Param,
  Delete,
  NotFoundException,
} from '@nestjs/common';
import { CreateOnBoardingDto } from '../dto/create-onboarding.dto';
import { UpdateOnBoardingDto } from '../dto/update-onboarding.dto';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { AuthGuard } from '@nestjs/passport';
import { OnBoardingRepository, Role, Roles } from '@core/libs';
import { UsersService } from '../../users/services/users.service';

@ApiTags('User OnBoarding')
@ApiBearerAuth()
@Controller('user-onboarding')
@UseGuards(AuthGuard('jwt'))
export class OnBoardingController {
  constructor(
    private onBoardingRepository: OnBoardingRepository,
    private userService: UsersService
  ) {}

  @Post()
  async create(
    @Body() createOnBoardingDto: CreateOnBoardingDto,
    @Request() req
  ) {
    const user = await this.userService.user({
      user_id: +createOnBoardingDto.user_id,
    });
    if (!user) {
      throw new NotFoundException('User not found.');
    }
    const createData = {
      ...createOnBoardingDto,
      users: {
        connect: { user_id: createOnBoardingDto.user_id },
      },
      created_by: req.raw.user.user_id,
    };
    delete createData.user_id;
    return await this.onBoardingRepository.createOnBoarding(createData);
  }

  @Patch()
  async update(
    @Body() updateOnBoardingDto: UpdateOnBoardingDto,
    @Request() req
  ) {
    const updateData = {
      ...updateOnBoardingDto,
      updated_by: req.raw.user.user_id,
      updated_at: new Date(),
    };
    delete updateData.user_id;
    return await this.onBoardingRepository.updateManyOnBoarding({
      where: {
        user_id: updateOnBoardingDto.user_id,
        device_id: updateOnBoardingDto.device_id,
        device_type: updateOnBoardingDto.device_type,
      },
      data: updateData,
    });
  }

  @Delete('delete/:id')
  @Roles(Role.Admin)
  async delete(@Param('id') id: number) {
    return await this.onBoardingRepository.deleteOnBoarding({ status_id: +id });
  }
}
