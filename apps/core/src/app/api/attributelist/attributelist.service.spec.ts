import { Test, TestingModule } from '@nestjs/testing';
import { attributeService } from './attributelist.service';
import { PrismaService } from '@core/prisma-client';

describe('AttributelistService', () => {
  let service: attributeService;
  let prismaService: PrismaService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [attributeService, PrismaService],
    }).compile();

    service = module.get<attributeService>(attributeService);
    prismaService = module.get<PrismaService>(PrismaService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
