import { Injectable } from '@nestjs/common';
import { PrismaService, Prisma } from '@core/prisma-client';

@Injectable()
export class attributeService {
  constructor(private prisma: PrismaService) {}

  async createAttribute(data: Prisma.attributesCreateInput) {
    return await this.prisma.attributes.create({ data });
  }

  async updateAttribute(params: {
    where: Prisma.attributesWhereUniqueInput;
    data: Prisma.attributesUpdateInput;
  }) {
    const { where, data } = params;
    return await this.prisma.attributes.update({ data, where });
  }

  async deleteAttribute(where: Prisma.attributesWhereUniqueInput, data: any) {
    return await this.prisma.attributes.update({ data, where });
  }

  async attribute(where: Prisma.attributesWhereUniqueInput) {
    return await this.prisma.attributes.findUnique({
      where,
      include: { role_attributes: true },
    });
  }

  async attributes(params: {
    skip?: number;
    take?: number;
    cursor?: Prisma.attributesWhereUniqueInput;
    where?: Prisma.attributesWhereInput;
    orderBy?: Prisma.attributesOrderByWithAggregationInput;
  }) {
    const { skip, take, cursor, where, orderBy } = params;
    return await this.prisma.attributes.findMany({
      skip,
      take,
      cursor,
      where,
      orderBy,
      include: { role_attributes: true },
    });
  }
}
