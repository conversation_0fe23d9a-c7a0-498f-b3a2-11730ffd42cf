import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from '@nestjs/class-validator';

export class CreateAttributelistDto {
  @ApiProperty({
    example: 'AiSimpleAccess',
    description: 'Unique AttributeList',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    example: 'description',
    description: 'description',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  description: string;

  // @ApiProperty({
  //   example: { read: true, update: false, delete: false },
  //   description: 'Permissions object with specific attributes',
  //   required: true,
  // })
  // @IsNotEmpty()
  // @IsObject()
  // actions: Record<string, any>;
}
