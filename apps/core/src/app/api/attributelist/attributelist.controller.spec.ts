import { Test, TestingModule } from '@nestjs/testing';
import { AttributelistController } from './attributelist.controller';
import { attributeService } from './attributelist.service';
import { CreateAttributelistDto } from './dto/create-attributelist.dto';
import { UpdateAttributelistDto } from './dto/update-attributelist.dto';

const mockReq = {
  raw: {
    user: {
      user_id: 1,
    },
  },
};

describe('AttributelistController', () => {
  let controller: AttributelistController;
  let service: attributeService;

  const mockAttributeService = {
    createAttribute: jest.fn(),
    attributes: jest.fn(),
    attribute: jest.fn(),
    updateAttribute: jest.fn(),
    deleteAttribute: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AttributelistController],
      providers: [
        { provide: attributeService, useValue: mockAttributeService },
      ],
    }).compile();

    controller = module.get<AttributelistController>(AttributelistController);
    service = module.get<attributeService>(attributeService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create an attribute list', async () => {
      const createDto: CreateAttributelistDto = {
        name: 'Test Attribute',
        description: 'Test Description',
      };

      mockAttributeService.createAttribute.mockResolvedValueOnce({
        id: 1,
        ...createDto,
      });

      const result = await controller.create(createDto, mockReq);

      expect(service.createAttribute).toHaveBeenCalledWith({
        ...createDto,
        created_by: mockReq.raw.user.user_id,
      });
      expect(result).toEqual({ id: 1, ...createDto });
    });
  });

  describe('findAll', () => {
    it('should return all attributes', async () => {
      const mockAttributes = [
        { id: 1, name: 'Attribute 1', description: 'Description 1' },
        { id: 2, name: 'Attribute 2', description: 'Description 2' },
      ];

      mockAttributeService.attributes.mockResolvedValueOnce(mockAttributes);

      const result = await controller.findAll();

      expect(service.attributes).toHaveBeenCalledWith({
        where: { is_deleted: false },
      });
      expect(result).toEqual(mockAttributes);
    });
  });

  describe('findOne', () => {
    it('should return a single attribute by ID', async () => {
      const mockAttribute = {
        id: 1,
        name: 'Attribute 1',
        description: 'Description 1',
      };

      mockAttributeService.attribute.mockResolvedValueOnce(mockAttribute);

      const result = await controller.findOne('1');

      expect(service.attribute).toHaveBeenCalledWith({ attribute_id: 1 });
      expect(result).toEqual(mockAttribute);
    });
  });

  describe('update', () => {
    it('should update an attribute by ID', async () => {
      const updateDto: UpdateAttributelistDto = {
        name: 'Updated Attribute',
        description: 'Updated Description',
      };

      const mockUpdatedAttribute = { id: 1, ...updateDto };

      mockAttributeService.updateAttribute.mockResolvedValueOnce(
        mockUpdatedAttribute
      );

      const result = await controller.update('1', updateDto, mockReq);

      expect(service.updateAttribute).toHaveBeenCalledWith({
        where: { attribute_id: 1 },
        data: {
          ...updateDto,
          updated_by: mockReq.raw.user.user_id,
        },
      });
      expect(result).toEqual(mockUpdatedAttribute);
    });
  });

  describe('remove', () => {
    it('should delete an attribute by ID', async () => {
      const mockDeletedAttribute = {
        id: 1,
        is_deleted: true,
        deleted_by: mockReq.raw.user.user_id,
        deleted_at: new Date(),
      };

      mockAttributeService.deleteAttribute.mockResolvedValueOnce(
        mockDeletedAttribute
      );

      const result = await controller.remove('1', mockReq);

      expect(service.deleteAttribute).toHaveBeenCalledWith(
        { attribute_id: 1 },
        {
          is_deleted: true,
          deleted_by: mockReq.raw.user.user_id,
          deleted_at: expect.any(Date),
        }
      );
      expect(result).toEqual(mockDeletedAttribute);
    });
  });
});
