import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
  NotFoundException,
} from '@nestjs/common';
import { attributeService } from './attributelist.service';
import { CreateAttributelistDto } from './dto/create-attributelist.dto';
import { UpdateAttributelistDto } from './dto/update-attributelist.dto';
import { ApiTags, ApiBearerAuth } from '@nestjs/swagger';
import { Role, Roles, RolesGuard } from '@core/libs';

@ApiTags('AttributeList')
@ApiBearerAuth()
@Controller('attributelist')
@UseGuards(RolesGuard)
export class AttributelistController {
  constructor(
    private readonly attributelistService: attributeService
  ) {}

  @Post()
  @Roles(Role.Admin)
  async create(
    @Body() createAttributelistDto: CreateAttributelistDto,
    @Request() req
  ) {
    const createData = {
      ...createAttributelistDto,
      created_by: req.raw.user.user_id,
    };
    return await this.attributelistService.createAttribute(createData);
  }

  @Get()
  @Roles(Role.Admin)
  async findAll() {
    return await this.attributelistService.attributes({
      where: { is_deleted: false },
    });
  }

  @Get(':id')
  @Roles(Role.Admin)
  async findOne(@Param('id') id: string) {
    const attributelist = await this.attributelistService.attribute({attribute_id: +id});
    if (!attributelist) {
      throw new NotFoundException('AttributeList not found.');
    }
    return attributelist;
  }

  @Patch(':id')
  @Roles(Role.Admin)
  async update(
    @Param('id') id: string,
    @Body() updateAttributelistDto: UpdateAttributelistDto,
    @Request() req
  ) {
    const updateData = {
      ...updateAttributelistDto,
      updated_by: req.raw.user.user_id,
    };
    return await this.attributelistService.updateAttribute({
      where: { attribute_id: +id },
      data: updateData,
    });
  }

  @Delete(':id')
  @Roles(Role.Admin)
  async remove(@Param('id') id: string, @Request() req) {
    const deleteData = {
      is_deleted: true,
      deleted_by: req.raw.user.user_id,
      deleted_at: new Date(),
    };
    return await this.attributelistService.deleteAttribute(
      { attribute_id: +id },
      deleteData
    );
  }
}
