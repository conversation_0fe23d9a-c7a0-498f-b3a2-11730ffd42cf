import { Module, NestModule, MiddlewareConsumer } from '@nestjs/common';
import { attributeService } from './attributelist.service';
import { AttributelistController } from './attributelist.controller';
import { PrismaService } from '@core/prisma-client';
import { TokenMiddleware } from '@core_be/auth';
import { ConfigService } from '@nestjs/config';

@Module({
  controllers: [AttributelistController],
  providers: [attributeService, PrismaService, ConfigService],
})
export class AttributelistModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(TokenMiddleware).forRoutes(AttributelistController);
  }
}
