import { Module, NestModule, MiddlewareConsumer } from '@nestjs/common';
import { NotificationsController } from './controllers/notifications.controller';
import { TokenMiddleware } from '@core_be/auth';
import { ConfigService } from '@nestjs/config';
import { LibFcmService } from '@core_be/notifications';
import {
  NotificationRepository,
  NotificationsPreferencesRepository,
  NotificationsTopicsRepository,
} from '@core_be/data-access';
import { UsersService } from '../users/services/users.service';
import { NotificationsPreferencesService } from './services/notifications-preferences.service';
import { NotificationService } from './services/notification.service';
import { WaitlistRepository } from 'libs/data-access/src/lib/waitlist/waitlist.repository';
import { Logger } from 'nestjs-pino';

@Module({
  controllers: [NotificationsController],
  providers: [
    ConfigService,
    LibFcmService,
    UsersService,
    NotificationsPreferencesRepository,
    NotificationsPreferencesService,
    NotificationService,
    NotificationsTopicsRepository,
    NotificationRepository,
    WaitlistRepository,
    Logger,
  ],
})
export class NotificationsModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(TokenMiddleware).forRoutes(NotificationsController);
  }
}
