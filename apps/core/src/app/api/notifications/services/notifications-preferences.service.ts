import { Injectable } from '@nestjs/common';
import { NotificationPreferencesResponseDTO, UpdateNotificationsPreferenceseDTO } from '../dto/notifications.dto';
import { NotificationsPreferencesRepository,NotificationsTopicsRepository } from '@core/libs';

@Injectable()
export class NotificationsPreferencesService {
  constructor(
    private readonly notificationsPreferencesRepository:NotificationsPreferencesRepository,
    private readonly notificationsTopicsRepository:NotificationsTopicsRepository,
  ) {}

  async getUserPreferences(userId: number,createdByUser: number) : Promise<NotificationPreferencesResponseDTO[]> {
    const preferences = await this.notificationsPreferencesRepository.findMany(
      { user_id: userId },
      { notification_topic: true },
    );

    if (preferences.length === 0) {
      const allTopics = await this.notificationsTopicsRepository.findAll({});
      const createData = allTopics.map((topic) => ({
        user_id: userId,
        notification_topic_id: topic.notification_topic_id,
        is_subscribed: true,
        created_by: createdByUser,
      }));

      await this.notificationsPreferencesRepository.createMany(createData);
      
      return allTopics.map((topic) => ({
        topic_id: topic.notification_topic_id,
        name: topic.name,
        is_subscribed: true, 
      }));
    }

    return preferences.map((pref) => ({
      topic_id: pref.notification_topic.notification_topic_id,
      name: pref.notification_topic.name,
      is_subscribed: pref.is_subscribed,
    }));
  }

  async updateUserPreferences(
    userId: number,
    data: UpdateNotificationsPreferenceseDTO[]
  ): Promise<NotificationPreferencesResponseDTO[]> {
    const results = await Promise.all(
      data.map(async (preferenceData) => {
        const topic = await this.notificationsTopicsRepository.findOne({
          notification_topic_id: preferenceData.topic_id,
        });
  
        if (!topic) {
          return null; 
        }
  
        const createData = {
          user: { connect: { user_id: userId } },
          notification_topic: { connect: { notification_topic_id: topic.notification_topic_id } },
          is_subscribed: preferenceData.is_subscribed,
          created_by: preferenceData.updated_by,
        };
  
        const updateData = {
          is_subscribed: preferenceData.is_subscribed,
          updated_by: preferenceData.updated_by,
          updated_at: new Date(),
        };
  
        const preference = await this.notificationsPreferencesRepository.upsert(
          { user_id_notification_topic_id: { user_id: userId, notification_topic_id: preferenceData.topic_id } },
          updateData,
          createData
        );
  
        return {
          topic_id: preference.notification_topic_id,
          name: topic.name,
          is_subscribed: preference.is_subscribed,
        };
      })
    );
  
    return results.filter((result) => result !== null);
  }
}