import { Injectable } from '@nestjs/common';
import {
  CreateNotificationsDto,
  CreateNotificationsRecipientsDTO,
  CustomNotificationFindArgs,
  UpdateNotificationsRecipientsDTO,
} from '../dto/notifications.dto';
import { NotificationRepository } from '@core/libs';
import { Prisma } from '@core/prisma-client';
import { UpdateNotificationsDto } from '../dto/update-notifications.dto';

@Injectable()
export class NotificationService {
  constructor(
    private readonly notificationRepository: NotificationRepository
  ) {}

  async create(createNotificationsDto: CreateNotificationsDto) {
    return this.notificationRepository.create(createNotificationsDto);
  }

  async update(id: number, updateNotificationsDto: UpdateNotificationsDto) {
    return this.notificationRepository.update(
      { notification_id: +id },
      updateNotificationsDto
    );
  }

  async findOne(where: Prisma.notificationWhereUniqueInput) {
    return this.notificationRepository.findOne(where);
  }

  async deleteNotification(id:number,data:Prisma.notificationUpdateInput) {
    return this.notificationRepository.delete(
        { notification_id: +id },
        data
    );
  }

  async findAll(params: CustomNotificationFindArgs) {
      const { offset, limit, cursor, where, orderBy, include } = params;
  
      return await this.notificationRepository.findAll({
        skip: offset,
        limit,
        cursor,
        where,
        orderBy,
        include,
      });
    }

  async createManyRecipients(
    createNotificationsRecipientsDTO: CreateNotificationsRecipientsDTO[]
  ) {
    return this.notificationRepository.createManyRecipients(
      createNotificationsRecipientsDTO
    );
  }

  async upsertManyRecipients(
    updateRecipients: UpdateNotificationsRecipientsDTO
  ) {
    const existingRecipients = await this.notificationRepository.findManyRecipients(
      { notification_id: updateRecipients.notification_id },
      {},
      { user_id: true, notification_recipient_id: true}
    );

    const usersToDelete = existingRecipients.filter((r) => !updateRecipients.userIds.includes(r.user_id)).map((r) => r.notification_recipient_id);
    const usersToRestore = existingRecipients.filter((r) => updateRecipients.userIds.includes(r.user_id)).map((r) => r.notification_recipient_id);
    const usersToCreate = updateRecipients.userIds.filter(
      (id) => !existingRecipients.some((r) => r.user_id === id)
    );

    const updateRecipientData = {
      status: updateRecipients.status,
      updated_by: updateRecipients.updated_by,
      updated_at: new Date(),
      is_deleted: false,
      deleted_by: null,
      deleted_at: null,
    };

    const deleteRecipientData = {
      is_deleted: true,
      deleted_by: updateRecipients.updated_by,
      deleted_at: new Date(),
    };

    if (usersToDelete.length > 0) {
      await this.notificationRepository.deleteManyRecipients(
        { notification_recipient_id: { in: usersToDelete } },
        deleteRecipientData
      );
    }

    if (usersToRestore.length > 0) {
      await this.notificationRepository.updateManyRecipients(
        { notification_recipient_id: { in: usersToRestore } },
        updateRecipientData,
      );
    }

    if (usersToCreate.length > 0) {
      const newRecipients = usersToCreate.map((userId) => ({
        user_id: userId,
        notification_id: updateRecipients.notification_id,
        status: updateRecipients.status,
        created_by: updateRecipients.updated_by,
      }));
  
      await this.notificationRepository.createManyRecipients(newRecipients);
    }
    return { message: 'Recipients updated successfully' };
  }

  async findManyRecipients(
    where: Prisma.notification_recipientsWhereInput
  ) {
    return this.notificationRepository.findManyRecipients(where);
  }
  
  async deleteNotificationRecipient(notificationId: number,data:Prisma.notification_recipientsUpdateManyMutationInput) {
    const existingRecipients = await this.notificationRepository.findManyRecipients(
      { notification_id: notificationId },
      {},
      { notification_recipient_id: true }
    );

    if (existingRecipients.length > 0) {
      const recipientIdsToDelete = existingRecipients.map((r) => r.notification_recipient_id);
      await this.notificationRepository.deleteManyRecipients(
        { notification_recipient_id: { in: recipientIdsToDelete }},
        data
      );

      return { message: "Notification recipients deleted successfully." };
    }

    return { message: "No recipients found to delete." };
  }

  async notificationsCount(params: { where?: Prisma.notificationWhereInput }) {
    const { where } = params;
    return this.notificationRepository.count({ where });
  }
}
