import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsString,
  IsObject,
  IsInt,
  IsBoolean,
  IsArray,
  ArrayNotEmpty,
  IsOptional,
  IsEnum,
  IsIn,
  IsDateString,
} from '@nestjs/class-validator';
import { Type } from '@nestjs/class-transformer';
import { NotificationStatus } from '@core/libs';
import { Prisma } from '@core/prisma-client';

export class NotificationTopicDTO {
  @IsNotEmpty()
  @ApiProperty({
    example: 'Updates',
    description: 'The topic of the notification',
    required: true,
  })
  @IsString()
  topic: string;

  @IsNotEmpty()
  @ApiProperty({
    example: {
      notification: {
        title: 'Test',
        body: 'Here is the message',
      },
    },
    description: 'Tell you the score of a game',
    required: true,
  })
  @IsObject()
  data: object;
}

export class CreateNotificationsDto {
  @IsNotEmpty()
  @ApiProperty({
    example: 'title',
    description: 'The title of the notification',
    required: true,
  })
  @IsString()
  title: string;

  @IsNotEmpty()
  @ApiProperty({
    example: 'body',
    description: 'The body of the notification',
    required: true,
  })
  @IsString()
  body: string;

  @IsOptional()
  @IsInt()
  @ApiProperty({
    example: 1,
    description: 'The topic_id of the notification',
    required: true,
  })
  topic_id?: number;

  @IsOptional()
  @IsArray()
  @IsInt({ each: true })
  @ApiProperty({
    example: [1, 2, 3],
    description: 'The user_ids of the notification',
    required: true,
  })
  user_ids?: number[];

  @IsOptional()
  @IsString()
  @ApiProperty({
    example: 'sample.png',
    description: 'The image_url of the notification',
    required: true,
  })
  image_url?: string;

  @IsEnum(NotificationStatus)
  @IsIn([
    NotificationStatus.DRAFT,
    NotificationStatus.READY_TO_SEND,
    NotificationStatus.SCHEDULED,
  ])
  @ApiProperty({
    example: NotificationStatus.DRAFT,
    description: 'The Status of the notification',
    required: true,
  })
  status: NotificationStatus;

  @IsOptional()
  @IsString()
  @ApiProperty({
    example: new Date(),
    description: 'The schedule date of the notification',
    required: true,
  })
  schedule_date_time?: Date;
}

export class UpdateNotificationsPreferences {
  @IsNotEmpty()
  @ApiProperty({
    example: 1,
    description: 'The topic of the notification',
    required: true,
  })
  @IsInt()
  topic_id: number;

  @IsNotEmpty()
  @ApiProperty({
    example: true,
    description: 'The topic of the notification',
    required: true,
  })
  @IsBoolean()
  is_subscribed: boolean;
}

export class UpdateNotificationsPreferencesBulk {
  @IsArray()
  @ArrayNotEmpty()
  @Type(() => UpdateNotificationsPreferences)
  @ApiProperty({
    type: [UpdateNotificationsPreferences],
    description: 'List of notification preferences to update',
    required: true,
  })
  preferences: UpdateNotificationsPreferences[];
}

export class DuplicateNotifications{
  @IsOptional()
  @IsBoolean()
  @ApiPropertyOptional({
    example: true,
    description: 'Include topic and recipients of the notification',
  })
  include_target?: boolean;
}

export class NotificationQueryDto {
  @ApiPropertyOptional({
    example: 'notification_id',
    required: false,
  })
  @IsOptional()
  @IsString()
  orderBy?: string;

  @ApiPropertyOptional({
    description: 'Order direction',
    enum: ['asc', 'desc'],
    example: 'asc',
    required: false,
  })
  @IsOptional()
  @IsEnum(['asc', 'desc'])
  order: 'asc' | 'desc' = 'asc';

  @ApiPropertyOptional({
    example: 10,
    required: false,
  })
  @IsOptional()
  @IsString()
  limit: string;

  @ApiPropertyOptional({
    example: 0,
    required: false,
  })
  @IsOptional()
  @IsString()
  offset: string;

  @ApiPropertyOptional({
    example: new Date(),
    required: false,
  })
  @IsOptional()
  @IsDateString()
  sent_at_start_date?: string;

  @ApiPropertyOptional({
    example: new Date(),
    required: false,
  })
  @IsOptional()
  @IsDateString()
  sent_at_end_date ?: string;

  @ApiPropertyOptional({
    example: new Date(),
    required: false,
  })
  @IsOptional()
  @IsDateString()
  scheduled_at_start_date?: string;

  @ApiPropertyOptional({
    example: new Date(),
    required: false,
  })
  @IsOptional()
  @IsDateString()
  scheduled_at_end_date ?: string;

  @ApiPropertyOptional({
    example: '',
    required: false,
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({
    enum: NotificationStatus,
    example: NotificationStatus.DRAFT,
    required: false,
  })
  @IsOptional()
  @IsEnum(NotificationStatus)
  @IsString()
  status?: NotificationStatus;

  @ApiPropertyOptional({
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsString()
  topic_id?: string;
}

export class CreateNotificationsRecipientsDTO {
  user_id: number;
  notification_id: number;
  status: NotificationStatus;
  created_by: number;
}

export class UpdateNotificationsRecipientsDTO {
  notification_id: number;
  userIds: number[];
  status: NotificationStatus;
  updated_by: number;
}

export class NotificationPreferencesResponseDTO {
  topic_id: number;
  name: string;
  is_subscribed: boolean;
}

export class UpdateNotificationsPreferenceseDTO {
  topic_id: number;
  is_subscribed: boolean;
  updated_by: number;
}

export interface CustomNotificationFindArgs {
  offset?: number;
  limit?: number;
  cursor?: Prisma.notificationWhereUniqueInput;
  where?: Prisma.notificationWhereInput;
  orderBy?: Prisma.notificationOrderByWithAggregationInput;
  include?: Prisma.notificationInclude;
}