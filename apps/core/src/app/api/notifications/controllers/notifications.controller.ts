import {
  <PERSON>,
  Post,
  Body,
  Get,
  Param,
  Request,
  ConflictException,
  Patch,
  NotFoundException,
  Delete,
  Query,
} from '@nestjs/common';
import {
  CreateNotificationsDto,
  CustomNotificationFindArgs,
  DuplicateNotifications,
  NotificationQueryDto,
  NotificationTopicDTO,
  UpdateNotificationsPreferencesBulk,
} from '../dto/notifications.dto';
import { ApiTags, ApiBearerAuth } from '@nestjs/swagger';
import { LibFcmService } from '@core_be/notifications';
import {
  NotificationStatus,
  NotificationsTopicsRepository,
  Role,
  Roles,
} from '@core/libs';
import { NotificationsPreferencesService } from '../services/notifications-preferences.service';
import { UsersService } from '../../users/services/users.service';
import { NotificationService } from '../services/notification.service';
import { Logger } from 'nestjs-pino';
import { UpdateNotificationsDto } from '../dto/update-notifications.dto';
import { Prisma } from '@core/prisma-client';

@ApiTags('Notifications')
@ApiBearerAuth()
@Controller('notifications')
export class NotificationsController {
  constructor(
    private readonly fcmService: LibFcmService,
    private readonly notificationsPreferencesService: NotificationsPreferencesService,
    private usersService: UsersService,
    private readonly notificationService: NotificationService,
    private readonly notificationsTopicsRepository: NotificationsTopicsRepository,
    private readonly logger: Logger
  ) {}

  @Post('send-by-tokens')
  async testSendNotifications() {
    const result = await this.fcmService.sendMessageByTokens(
      ['Invalid_Token'],
      {
        notification: {
          title: 'Test Message',
          body: 'This message should fail',
        },
      },
      false
    );
    return result;
  }

  @Post('send-by-topic')
  async sendNotificationsByTopic(
    @Body() notificationTopic: NotificationTopicDTO
  ) {
    return await this.fcmService.sendMessageByTopic(
      notificationTopic.topic,
      notificationTopic.data
    );
  }

  @Post()
  @Roles(Role.Admin)
  async create(
    @Body() createNotificationsDto: CreateNotificationsDto,
    @Request() req
  ) {
    if (
      !createNotificationsDto.topic_id &&
      (!createNotificationsDto.user_ids ||
        createNotificationsDto.user_ids.length === 0)
    ) {
      throw new ConflictException(
        'At least one of topic_id or user_ids must be provided.'
      );
    }

    if (
      createNotificationsDto.status === NotificationStatus.SCHEDULED &&
      (!createNotificationsDto.schedule_date_time ||
        new Date(createNotificationsDto.schedule_date_time) <= new Date())
    ) {
      throw new ConflictException(
        'Scheduled date must be a valid future date.'
      );
    }

    if (createNotificationsDto.topic_id) {
      const topic = await this.notificationsTopicsRepository.findOne({
        notification_topic_id: createNotificationsDto.topic_id,
      });
      if (!topic) {
        throw new ConflictException('Invalid topic_id.');
      }
    }

    if (createNotificationsDto.user_ids) {
      const users = await this.usersService.users({
        where: { user_id: { in: createNotificationsDto.user_ids } },
      });
      if (users.length !== createNotificationsDto.user_ids.length) {
        throw new ConflictException('One or more user_ids are invalid.');
      }
    }

    const createData = {
      ...createNotificationsDto,
      notification_topic: createNotificationsDto.topic_id
        ? {
            connect: { notification_topic_id: createNotificationsDto.topic_id },
          }
        : undefined,
      scheduled_at:
        createNotificationsDto.status === NotificationStatus.SCHEDULED
          ? createNotificationsDto.schedule_date_time
          : null,
      created_by: req.raw.user.user_id,
    };
    delete createData.topic_id;
    delete createData.user_ids;
    delete createData.schedule_date_time;

    const notification = await this.notificationService.create(createData);

    this.logger.log({
      timestamp: new Date().toISOString(),
      event: 'NOTIFICATION_CREATED',
      userId: req.raw.user.user_id,
      notificationId: notification.notification_id,
      title: createNotificationsDto.title,
      status: createNotificationsDto.status,
      topicId: createNotificationsDto.topic_id,
      recipientCount: createNotificationsDto.user_ids?.length || 0,
      message: 'Notification created successfully'
    }, 'Notification Created');

    if (
      createNotificationsDto.user_ids &&
      createNotificationsDto.user_ids.length > 0
    ) {
      const recipientData = createNotificationsDto.user_ids.map((userId) => ({
        user_id: userId,
        notification_id: notification.notification_id,
        status: createNotificationsDto.status,
        created_by: req.raw.user.user_id,
      }));

      await this.notificationService.createManyRecipients(recipientData);
    }

    return notification;
  }

  @Patch(':notificationId')
  async updateNotification(
    @Param('notificationId') notificationId: string,
    @Body() updateNotificationDto: UpdateNotificationsDto,
    @Request() req
  ) {
    if (Object.keys(updateNotificationDto).length === 0) {
      throw new ConflictException(
        'At least one field must be provided for update.'
      );
    }

    const existingNotification = await this.notificationService.findOne({
      notification_id: +notificationId,
      is_deleted:false
    });

    if (!existingNotification) {
      throw new NotFoundException('Notification not found.');
    }

    if (existingNotification.status === NotificationStatus.SENT) {
      throw new ConflictException(
        'Cannot update a notification that has already been sent.'
      );
    }

    if (
      updateNotificationDto.status === NotificationStatus.SCHEDULED &&
      (!updateNotificationDto.schedule_date_time ||
        new Date(updateNotificationDto.schedule_date_time) <= new Date())
    ) {
      throw new ConflictException(
        'Scheduled date must be a valid future date.'
      );
    }

    if (updateNotificationDto.topic_id) {
      const topic = await this.notificationsTopicsRepository.findOne({
        notification_topic_id: updateNotificationDto.topic_id,
        is_deleted:false
      });
      if (!topic) {
        throw new ConflictException('Invalid topic_id.');
      }
    }

    if (updateNotificationDto.user_ids && updateNotificationDto.user_ids.length > 0) {
      const users = await this.usersService.users({
        where: { user_id: { in: updateNotificationDto.user_ids } },
      });
      if (users.length !== updateNotificationDto.user_ids.length) {
        throw new ConflictException('One or more user_ids are invalid.');
      }
    }

    const updateData = {
      ...updateNotificationDto,
      notification_topic: updateNotificationDto.topic_id
        ? { connect: { notification_topic_id: updateNotificationDto.topic_id } }
        : undefined,
      scheduled_at:
        updateNotificationDto.status === NotificationStatus.SCHEDULED
          ? updateNotificationDto.schedule_date_time
          : null,
      updated_by: req.raw.user.user_id,
      updated_at: new Date(),
    };
    delete updateData.topic_id;
    delete updateData.user_ids;
    delete updateData.schedule_date_time;

    const updateNotification = await this.notificationService.update(
      +notificationId,
      updateData
    );

    if (
      updateNotificationDto.user_ids &&
      updateNotificationDto.user_ids.length > 0
    ) {
      const updateRecipientData = {
        notification_id: +notificationId,
        userIds: updateNotificationDto.user_ids,
        status: updateNotificationDto.status,
        updated_by: req.raw.user.user_id,
      };
      await this.notificationService.upsertManyRecipients(updateRecipientData);
    }

    return updateNotification;
  }

  @Delete(':notificationId')
	@Roles(Role.Admin)
	async delete(@Param('notificationId') notificationId: string, @Request() req ){

    const existingNotification = await this.notificationService.findOne({
      notification_id: +notificationId,
      is_deleted:false
    });

    if (!existingNotification) {
      throw new NotFoundException('Notification not found.');
    }

    this.logger.log({
      timestamp: new Date().toISOString(),
      event: 'NOTIFICATION_DELETION',
      userId: req.raw.user.user_id,
      notificationId: +notificationId,
      notificationTitle: existingNotification.title,
      message: 'Notification deletion initiated'
    }, 'Notification Deletion');

		const deleteData = {
		  is_deleted: true,
		  deleted_by: req.raw.user.user_id,
		  deleted_at: new Date(),
		};

		const deleteNotification = await this.notificationService.deleteNotification(+notificationId,deleteData);

		await this.notificationService.deleteNotificationRecipient(+notificationId,deleteData);

		return deleteNotification
	}

  @Post(':notificationId/duplicate')
  async duplicateNotification(
    @Param('notificationId') notificationId: string,
    @Body() duplicateNotifications: DuplicateNotifications,
    @Request() req
  ) {

    const existingNotification = await this.notificationService.findOne({
      notification_id: +notificationId,
      is_deleted:false
    });

    if (!existingNotification) {
      throw new NotFoundException('Notification not found.');
    }

    const newNotificationData = {
      title: existingNotification.title,
      body: existingNotification.body,
      image_url: existingNotification.image_url,
      status: NotificationStatus.DRAFT,
      created_by: req.raw.user.user_id,
    };

    if (duplicateNotifications.include_target) {
      newNotificationData['notification_topic'] = { connect: { notification_topic_id: existingNotification.notification_topic_id } };
    }

    const newNotification = await this.notificationService.create(newNotificationData);

    if (duplicateNotifications.include_target) {

      const existingRecipients = await this.notificationService.findManyRecipients({ notification_id: +notificationId });

      if (existingRecipients.length > 0) {
        const newRecipients = existingRecipients.map((recipient) => ({
          user_id: recipient.user_id,
          notification_id: newNotification.notification_id,
          status: NotificationStatus.DRAFT,
          created_by: req.raw.user.user_id,
        }));

        await this.notificationService.createManyRecipients(newRecipients);
      }
    }

    return newNotification;
  }

  @Get()
  @Roles(Role.Admin)
  async findAll(@Query() query: NotificationQueryDto) {
    const {
      orderBy,
      order,
      limit,
      offset,
      sent_at_start_date,
      sent_at_end_date,
      scheduled_at_start_date,
      scheduled_at_end_date,
      search,
      status,
      topic_id
    } = query;
    const limitNumber = Number(limit) || 10;
    const offsetNumber = (Number(offset) * limitNumber) || 0;
    const orderByCondition: any = {};
    if(orderBy && orderBy !== 'notification_topic_name') {
      orderByCondition[orderBy] = order;
    }

    const queryPayload: CustomNotificationFindArgs = {
      orderBy: orderByCondition ?  orderByCondition : undefined,
      where: (() => {
        const where: Prisma.notificationWhereInput = {
          is_deleted: false,
        };
        if (status) {
          where.status = status;
        }
        if (topic_id) {
          where.notification_topic_id = +topic_id;
        }
        if (search) {
          where.OR = [
            { title: { contains: search, mode: 'insensitive' } },
            { body: { contains: search, mode: 'insensitive' } },
          ];
        }

        if (sent_at_start_date && sent_at_end_date) {
          where.sent_at = {
            gte: new Date(sent_at_start_date),
            lte: new Date(sent_at_end_date),
          };
        }

        if (scheduled_at_start_date && scheduled_at_end_date) {
          where.scheduled_at = {
            gte: new Date(scheduled_at_start_date),
            lte: new Date(scheduled_at_end_date),
          };
        }
        return where;
      })(),
      include: {
        notification_topic: {
          where: {
            is_deleted: false,
          },
        },
        notification_recipients: {
          where: {
            is_deleted: false,
          },
        },
      },
    };
    if (parseInt(limit)) {
      queryPayload['limit'] = limitNumber;
      queryPayload['offset'] = offsetNumber;
    }
    const totalItems = await this.notificationService.notificationsCount(queryPayload);

    const totalPages = Math.ceil(totalItems / limitNumber);

    const notificationData = await this.notificationService.findAll(queryPayload);

    return {
      users: notificationData,
      pagination: {
        total_pages: totalPages,
        total_items: totalItems,
        offset,
        limit,
      },
    };
  }

  @Get(':userId/preferences')
  @Roles(Role.Admin, Role.Patient, Role.Healer)
  async getUserNotificationPreferences(
    @Param('userId') userId: string,
    @Request() req
  ) {
    const existingUser = await this.usersService.user({
      user_id: +userId,
      is_deleted: false,
    });

    if (!existingUser) {
      throw new ConflictException('User not found');
    }
    const createdByUser = req.raw.user.user_id;
    return this.notificationsPreferencesService.getUserPreferences(
      +userId,
      createdByUser
    );
  }

  @Post(':userId/preferences')
  async updateUserNotificationPreferences(
    @Param('userId') userId: string,
    @Body()
    updateNotificationsPreferencesBulk: UpdateNotificationsPreferencesBulk,
    @Request() req
  ) {
    const existingUser = await this.usersService.user({
      user_id: +userId,
      is_deleted: false,
    });

    if (!existingUser) {
      throw new ConflictException('User not found');
    }
    const updateData = updateNotificationsPreferencesBulk.preferences.map(
      (preference) => ({
        ...preference,
        updated_by: req.raw.user.user_id,
      })
    );
    return await this.notificationsPreferencesService.updateUserPreferences(
      +userId,
      updateData
    );
  }
}
