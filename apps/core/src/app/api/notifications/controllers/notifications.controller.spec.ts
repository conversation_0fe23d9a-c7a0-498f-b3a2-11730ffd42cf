import { Test, TestingModule } from '@nestjs/testing';
import { NotificationsController } from './notifications.controller';
import { LibFcmService } from '@core_be/notifications';
import { NotificationTopicDTO } from './notifications.dto';

describe('NotificationsController', () => {
  let notificationsController: NotificationsController;
  let fcmService: LibFcmService;

  const mockNotificationsService = {
    sendMessageByTokens: jest.fn(),
    sendMessageByTopic: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [NotificationsController],
      providers: [
        { provide: LibFcmService, useValue: mockNotificationsService },
      ],
    }).compile();

    notificationsController = module.get<NotificationsController>(
      NotificationsController
    );
    fcmService = module.get<LibFcmService>(LibFcmService);
  });

  describe('testSendNotifications', () => {
    it('should call sendMessageByTokens and return the result', async () => {
      const mockResult = {
        responses: [
          {
            success: false,
            error: {
              code: 'messaging/invalid-argument',
              message:
                'The registration token is not a valid FCM registration token',
              toJSON: () => ({}), // Adding a mock toJSON method
            },
          },
        ],
        successCount: 0,
        failureCount: 1,
      };

      jest
        .spyOn(fcmService, 'sendMessageByTokens')
        .mockResolvedValue(mockResult);

      const result = await notificationsController.testSendNotifications();

      expect(result).toEqual(mockResult);
      expect(fcmService.sendMessageByTokens).toHaveBeenCalledWith(
        ['Invalid_Token'],
        {
          notification: {
            title: 'Test Message',
            body: 'This message should fail',
          },
        },
        false
      );
    });
  });

  describe('sendNotificationsByTopic', () => {
    it('should call sendMessageByTopic and return the result', async () => {
      const notificationTopic: NotificationTopicDTO = {
        topic: 'test-topic',
        data: {
          notification: {
            title: 'Topic Notification',
            body: 'Topic body',
          },
        },
      };
      const mockResult = '123456';
      jest
        .spyOn(fcmService, 'sendMessageByTopic')
        .mockResolvedValue(mockResult);

      const result = await notificationsController.sendNotificationsByTopic(
        notificationTopic
      );

      expect(result).toEqual(mockResult);
      expect(fcmService.sendMessageByTopic).toHaveBeenCalledWith(
        'test-topic',
        notificationTopic.data
      );
    });
  });
});
