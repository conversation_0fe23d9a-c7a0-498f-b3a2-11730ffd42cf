import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '@core/prisma-client';
import { AppDocumentName } from '@core_be/global';

@Injectable()
export class AppDocumentsService {
  constructor(private readonly prisma: PrismaService) {}

  async getDisclosure() {
    const disclosure = await this.prisma.app_documents.findFirst({
      where: {
        name: AppDocumentName.Disclosure,
        is_deleted: false,
        is_active: true,
      },
    });
    if (!disclosure) {
      throw new NotFoundException('Disclosure not found');
    }
    return disclosure;
  }
}
