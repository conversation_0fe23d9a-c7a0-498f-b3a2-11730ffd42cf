import { Controller, Get } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Public } from '@core/libs';
import { AppDocumentsService } from '../services/app-documents.service';

@ApiTags('App Documents')
@Public()
@Controller('app-documents')
export class AppDocumentsController {
  constructor(private appDocumentsService: AppDocumentsService) {}

  @Get('disclosure')
  @Public()
  async getDisclosure() {
    return await this.appDocumentsService.getDisclosure();
  }
}
