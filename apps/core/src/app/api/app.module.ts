import { InternalServerErrorException, Module } from '@nestjs/common';
import { UsersModule } from './users/users.module';
import { AuthModule } from './auth/auth.module';
import { APP_GUARD } from '@nestjs/core';
import { LibAuthModule, JwtGuard } from '@core_be/auth';
import * as <PERSON><PERSON> from 'joi';
import { ConfigModule } from '@nestjs/config';
import { LoggerModule } from 'nestjs-pino';
import { AttributelistModule } from './attributelist/attributelist.module';
import { RoleModule } from './role/role.module';
import { RoleattributeModule } from './roleattribute/roleattribute.module';
import { NotificationsModule } from './notifications/notifications.module';
import { WhitelistModule } from './whitelist/whitelist.module';
import { TermsModule } from './terms/terms.module';
import { OnBoardingModule } from './onboarding/onboarding.module';
import hyperid from 'hyperid';
import { LibDataAccessModule, LibGlobalModule } from '@core/libs';
import { PrismaClientModule } from '@core/prisma-client';
import { ProfileModule } from './profile/profile.module';
import { PrivacyPolicyModule } from './privacy-policy/privacy-policy..module';
import { FeedbackModule } from './feedback/feedback.module';
import { MetricsModule } from './metrics/metrics.module';
import { Options } from 'pino-http';
import { DestinationStream } from 'pino';
import { NotificationsTopicsModule } from './notifications-topics/notifications-topics.module';
import { QueueService } from 'libs/global/src/lib/queue/queue.service';
import { SurveyModule } from './survey/survey.module';
import { AppDocumentsModule } from './app-documents/app-documents.module';
import { ServiceStatusModule } from './service-status/service-status.module';

const instance = hyperid(true);
const pinoConfig: Options | DestinationStream | [Options, DestinationStream] = {
  customSuccessMessage: () => '',
  customErrorMessage: () => '',
  level: 'info',
  timestamp: true,
  genReqId: () => instance.uuid, // BUG: currently UUID is not being set in the request id
  transport: {
    target: 'pino-pretty',
    options: {
      json: true,
      colorize: false,
      singleLine: true,
      translateTime: 'SYS:standard',
      ignore: 'pid,hostname',
    },
  },
  serializers: {
    req: (req) => ({
      id: req.id,
      method: req.method,
      url: req.url,
      remoteAddress: req.remoteAddress,
      remotePort: req.remotePort,
      userAgent: req.headers['user-agent'],
    }),
    res: (res) => ({
      statusCode: res.statusCode,
    }),
    err: (err) => ({
      type: err.constructor.name,
      message: err.message,
      stack: err.stack,
      ...(err.code && { code: err.code }),
      ...(err.statusCode && { statusCode: err.statusCode }),
    }),
    // Custom serializer for any object - ensures JSON objects are properly logged
    data: (data) => {
      if (typeof data === 'object' && data !== null) {
        try {
          return JSON.parse(JSON.stringify(data)); // Ensures proper JSON serialization
        } catch {
          return data;
        }
      }
      return data;
    },
  },
  formatters: {
    level: (label: string) => {
      return { level: label };
    },
    log: (object: any) => {
      // Ensure JSON objects are properly formatted
      if (typeof object === 'object' && object !== null) {
        return object;
      }
      return { message: object };
    },
  },
};

if (!process.env.NODE_ENV) {
  throw new InternalServerErrorException(
    'NODE_ENV is not set. Expected values are "local" or "development" or "production"'
  );
}

if (
  ['development'].includes(process.env.NODE_ENV.trim().toLowerCase())
) {
  pinoConfig.level = 'debug';
} else if (
  ['local'].includes(process.env.NODE_ENV.trim().toLowerCase())
) {
  pinoConfig.level = 'debug';
  pinoConfig.transport.options.colorize = true;
}

@Module({
  imports: [
    PrismaClientModule,
    LibDataAccessModule,
    LibGlobalModule,
    LibAuthModule,
    LoggerModule.forRoot({
      pinoHttp: pinoConfig,
    }),
    AuthModule,
    ServiceStatusModule,
    AppDocumentsModule,
    UsersModule,
    AttributelistModule,
    RoleModule,
    RoleattributeModule,
    NotificationsModule,
    ConfigModule.forRoot({
      isGlobal: true,
      validationSchema: Joi.object({
        PORT_CORE: Joi.string().required(),
        DATABASE_URL: Joi.string().required(),
        AUTH_API_KEY: Joi.string().required(),
        JWT_SECRET: Joi.string().required(),
        JWT_EXPIRATION: Joi.string().required(),
        JWT_ALGORITHM: Joi.string().required(),
        SENDGRID_API_KEY: Joi.string().required(),
        SENDGRID_FROM_EMAIL: Joi.string().required(),
        RABBITMQ_URL: Joi.string().required(),
      }),
    }),
    ProfileModule,
    WhitelistModule,
    TermsModule,
    PrivacyPolicyModule,
    OnBoardingModule,
    FeedbackModule,
    SurveyModule,
    MetricsModule,
    NotificationsTopicsModule,
  ],
  // controllers: [AppController],
  providers: [
    QueueService,
    {
      provide: APP_GUARD,
      useClass: JwtGuard,
    },
  ],
})
export class AppModule {}
