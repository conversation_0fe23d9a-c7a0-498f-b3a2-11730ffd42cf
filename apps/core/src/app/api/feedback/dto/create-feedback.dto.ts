import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsNotEmpty, IsString } from '@nestjs/class-validator';

export class CreateFeedbackDto {
  @ApiProperty({
    example: 'Excellent service',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  comment: string;

  @ApiProperty({
    example: 1,
    description: 'Patient Id',
    required: true,
  })
  @IsNotEmpty()
  @IsInt()
  patient_id: number;

  @ApiProperty({
    example: 1,
    description: 'Healer Id',
    required: true,
  })
  @IsNotEmpty()
  @IsInt()
  healer_id: number;
}
