import {
  Controller,
  Post,
  Body,
  Request,
  UseGuards,
  NotFoundException,
} from '@nestjs/common';
import { FeedbackService } from '../services/feedback.service';
import { CreateFeedbackDto } from '../dto/create-feedback.dto';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import {
  RolesGuard,
  Role,
  Roles,
  LibProfileService,
  ProfileType,
} from '@core/libs';
import { Logger } from 'nestjs-pino';

@ApiTags('Feedback')
@ApiBearerAuth()
@Controller('feedback')
@UseGuards(RolesGuard)
export class FeedbackController {
  constructor(
    private readonly feedbackService: FeedbackService,
    private readonly profileService: LibProfileService,
    private readonly logger: Logger
  ) {}

  @Post()
  @Roles(Role.Healer, Role.Patient)
  async create(@Body() createFeedbackDto: CreateFeedbackDto, @Request() req) {
    const createData = {
      ...createFeedbackDto,
      created_by: req.raw.user.user_id,
    };
    const [patientProfile, healerProfile] = await Promise.all([
      this.profileService.profile({
        profile_id: +createFeedbackDto.patient_id,
      }),
      this.profileService.profile({ profile_id: +createFeedbackDto.healer_id }),
    ]);

    if (
      !patientProfile ||
      patientProfile.profile_type !== ProfileType.PATIENT
    ) {
      throw new NotFoundException('Patient profile not found!');
    }

    if (!healerProfile || healerProfile.profile_type !== ProfileType.HEALER) {
      throw new NotFoundException('Healer profile not found!');
    }

    if (!createFeedbackDto.comment) {
      this.logger.warn({
        timestamp: new Date().toISOString(),
        event: 'INVALID_FEEDBACK',
        patientId: createFeedbackDto.patient_id,
        healerId: createFeedbackDto.healer_id,
        message: 'Invalid feedback payload',
      });
      return {};
    }

    this.logger.log(
      {
        timestamp: new Date().toISOString(),
        event: 'FEEDBACK_CREATED',
        userId: req.raw.user.user_id,
        patientId: createFeedbackDto.patient_id,
        healerId: createFeedbackDto.healer_id,
        message: 'Feedback submitted',
      },
      'Feedback Created'
    );

    return this.feedbackService.create(createData);
  }
}
