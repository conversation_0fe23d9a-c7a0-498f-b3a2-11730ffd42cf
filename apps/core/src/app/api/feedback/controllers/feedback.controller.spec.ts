import { Test, TestingModule } from '@nestjs/testing';
import { FeedbackController } from './feedback.controller';
import { FeedbackService } from '../services/feedback.service';
import { LibProfileService, ProfileType } from '@core/libs';
import { NotFoundException } from '@nestjs/common';
import { CreateFeedbackDto } from '../dto/create-feedback.dto';

const mockReq = {
  raw: {
    user: {
      user_id: 1,
    },
  },
};

const createFeedbackDto: CreateFeedbackDto = {
  comment: 'Great service!',
  patient_id: 2,
  healer_id: 1,
};

const mockPatientProfile = {
  profile_id: createFeedbackDto.patient_id,
  profile_type: ProfileType.PATIENT,
};

const mockHealerProfile = {
  profile_id: createFeedbackDto.healer_id,
  profile_type: ProfileType.HEALER,
};

describe('FeedbackController', () => {
  let feedbackController: FeedbackController;
  let feedbackService: FeedbackService;
  let profileService: LibProfileService;

  const mockFeedbackService = {
    create: jest.fn(),
  };

  const mockProfileService = {
    profile: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [FeedbackController],
      providers: [
        { provide: FeedbackService, useValue: mockFeedbackService },
        { provide: LibProfileService, useValue: mockProfileService },
      ],
    }).compile();

    feedbackController = module.get<FeedbackController>(FeedbackController);
    feedbackService = module.get<FeedbackService>(FeedbackService);
    profileService = module.get<LibProfileService>(LibProfileService);
  });

  describe('create', () => {
    it('should create feedback successfully', async () => {
      mockProfileService.profile.mockResolvedValueOnce(mockPatientProfile);
      mockProfileService.profile.mockResolvedValueOnce(mockHealerProfile);
      mockFeedbackService.create.mockResolvedValueOnce({
        id: 1,
        ...createFeedbackDto,
      });

      const result = await feedbackController.create(
        createFeedbackDto,
        mockReq
      );

      expect(profileService.profile).toHaveBeenCalledTimes(2);
      expect(profileService.profile).toHaveBeenCalledWith({
        profile_id: createFeedbackDto.patient_id,
      });
      expect(profileService.profile).toHaveBeenCalledWith({
        profile_id: createFeedbackDto.healer_id,
      });
      expect(feedbackService.create).toHaveBeenCalledWith({
        ...createFeedbackDto,
        created_by: mockReq.raw.user.user_id,
      });
      expect(result).toEqual({ id: 1, ...createFeedbackDto });
    });

    it('should throw NotFoundException if patient profile is not found', async () => {
      mockProfileService.profile.mockResolvedValueOnce(null);

      await expect(
        feedbackController.create(createFeedbackDto, mockReq)
      ).rejects.toThrow(new NotFoundException('Patient profile not found!'));

      expect(profileService.profile).toHaveBeenCalledWith({
        profile_id: createFeedbackDto.patient_id,
      });
    });

    it('should throw NotFoundException if healer profile is not found', async () => {
      mockProfileService.profile.mockResolvedValueOnce(mockPatientProfile);
      mockProfileService.profile.mockResolvedValueOnce(null);

      await expect(
        feedbackController.create(createFeedbackDto, mockReq)
      ).rejects.toThrow(new NotFoundException('Healer profile not found!'));

      expect(profileService.profile).toHaveBeenCalledWith({
        profile_id: createFeedbackDto.patient_id,
      });
      expect(profileService.profile).toHaveBeenCalledWith({
        profile_id: createFeedbackDto.healer_id,
      });
    });
  });
});
