import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { FeedbackService } from './services/feedback.service';
import { FeedbackController } from './controllers/feedback.controller';
import { TokenMiddleware } from '@core_be/auth';
import { ConfigService } from '@nestjs/config';
import { Logger } from 'nestjs-pino';

@Module({
  controllers: [FeedbackController],
  providers: [FeedbackService, ConfigService, Logger],
})
export class FeedbackModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(TokenMiddleware).forRoutes(FeedbackController);
  }
}
