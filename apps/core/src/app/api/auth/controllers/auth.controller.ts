import {
  BadRequestException,
  Body,
  Controller,
  HttpCode,
  Post,
  Request,
  UseGuards,
  HttpException,
  UnauthorizedException,
} from '@nestjs/common';
import { TokenBlacklistService } from '@core_be/auth';
import { AuthGuard } from '@nestjs/passport';
import { ApiTags, ApiBody, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { Public } from '@core/libs';
import {
  LoginRequestDto,
  LoginResponseDto,
  ForgotPasswordDtoDto,
  UpdateUserDto,
  ResetPasswordDTO,
  RegisterResponseDto,
} from '../../users/dto';
import { LibEmailService } from '@core_be/email';
import { ConfigService } from '@nestjs/config';
import * as bcrypt from 'bcrypt';
import * as crypto from 'crypto';
import { UsersService } from '../../users/services/users.service';
import { CreateAuthDto, RegisterAuthDto } from '../dto';
import { AuthService } from '../services/auth.service';

@ApiTags('Authentications')
@Public()
@Controller('auth')
export class AuthController {
  private readonly siteUrl: string;
  private readonly forgotPasswordTemplateId: string;
  private readonly sendgridEmail: string;

  constructor(
    private authService: AuthService,
    private usersService: UsersService,
    private libEmailService: LibEmailService,
    private configService: ConfigService,
    private readonly tokenBlacklistService: TokenBlacklistService
  ) {
    this.siteUrl = this.configService.getOrThrow<string>('SITE_URL');
    this.forgotPasswordTemplateId = this.configService.getOrThrow<string>(
      'FORGOT_PASSWORD_SENDGRID_TEMPLATE_ID'
    );
    this.sendgridEmail = this.configService.getOrThrow<string>(
      'SENDGRID_FROM_EMAIL'
    );
  }

  @UseGuards(AuthGuard('local'))
  @Post('login')
  @HttpCode(200)
  @ApiBody({ type: CreateAuthDto })
  @ApiResponse({ status: 201, description: 'Login successful' })
  async login(
    @Body() loginRequestDto: LoginRequestDto
  ): Promise<LoginResponseDto> {
    return await this.authService.login(loginRequestDto);
  }

  @Post('register')
  @ApiBody({ type: RegisterAuthDto })
  @ApiResponse({ status: 201, description: 'Registers successful' })
  async register(
    @Body() registerAuthDto: RegisterAuthDto
  ): Promise<RegisterResponseDto> {
    return await this.authService.register(registerAuthDto);
  }

  @Post('forgot-password')
  async resetpassword(@Body() resetUserDto: ForgotPasswordDtoDto) {
    const user = await this.usersService.user({ email: resetUserDto.email });
    if (!user) {
      throw new BadRequestException('User not found');
    }

    const password_reset_token = crypto.randomBytes(64).toString('hex');
    const password_reset_token_expiry = new Date(Date.now() + 3600000); // 1 hour
    const resetTokenHash = await bcrypt.hash(password_reset_token, 10);

    const updateUserDto: UpdateUserDto = {
      password_reset_token: resetTokenHash,
      password_reset_token_expiry: password_reset_token_expiry,
    };

    const data = await this.usersService.updateUser({
      where: { user_id: user.user_id },
      data: updateUserDto,
    });

    if (!data) {
      throw new BadRequestException('Update forgot password failed');
    }

    const expiryLink = `${this.siteUrl}/reset-password?token=${password_reset_token}&email=${user.email}`;

    const forgotPassword = {
      to: user.email,
      from: `${this.sendgridEmail}`,
      templateId: `${this.forgotPasswordTemplateId}`,
      dynamic_template_data: {
        password_reset_url: expiryLink,
      },
      // Disable click tracking to avoid url conversion for the reset button
      tracking_settings: {
        click_tracking: {
          enable: false,
          enable_text: false,
        },
      },
    };

    await this.libEmailService.sendEmail(forgotPassword);
    return { message: 'Sent a password reset link on your email address.' };
  }

  @Post('reset-password')
  async updatePassword(@Body() updateUserPasswordDto: ResetPasswordDTO) {
    const { email, token, password } = updateUserPasswordDto;
    const userWithValidToken =
      await this.authService.findUserWithValidResetToken(email);
    if (!userWithValidToken || !userWithValidToken.password_reset_token) {
      throw new HttpException('Password reset token has expired', 403);
    }

    const isTokenValid = await bcrypt.compare(
      token,
      userWithValidToken.password_reset_token
    );
    if (!isTokenValid) {
      throw new UnauthorizedException('Invalid reset token.');
    }

    const hashedPassword = await bcrypt.hash(password, 10);
    const UserPassword = {
      password_hash: hashedPassword,
      password_reset_token: null,
      password_reset_token_expiry: null,
      updated_at: new Date(),
    };
    return await this.usersService.updateUser({
      where: { user_id: +userWithValidToken.user_id },
      data: UserPassword,
    });
  }

  // TODO: Not in use at the moment. Need to fix security issue
  // @ApiBody({ type: RefreshTokenDtoDto })
  // @Post('refresh-token')
  // async refreshToken(@Body() body: RefreshTokenDtoDto): Promise<string | null> {
  //   return await this.authService.refreshToken(body?.token);
  // }

  @ApiBearerAuth()
  @Post('logout')
  async logout(@Request() req) {
    const token = req.headers.authorization?.split(' ')[1];

    if (token) {
      this.tokenBlacklistService.addToken(token);
    }

    return { message: 'Logged out successfully.' };
  }
}
