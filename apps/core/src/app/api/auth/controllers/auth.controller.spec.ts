import { Test, TestingModule } from '@nestjs/testing';
import { AuthController } from './auth.controller';
import { AuthService } from '../services/auth.service';
import { UsersService } from '../../users/services/users.service';
import { ProfileType } from '@core/libs';
import { TokenBlacklistService } from '@core_be/auth';
import { LibEmailService } from '@core_be/email';
import { ConfigService } from '@nestjs/config';
import { RegisterAuthDto } from '../dto';
import {
  LoginRequestDto,
  ResetPasswordDTO,
  ForgotPasswordDtoDto,
} from '../../users/dto';
import { BadRequestException } from '@nestjs/common';

describe('AuthController', () => {
  let controller: AuthController;
  let authService: AuthService;
  let usersService: UsersService;
  let emailService: LibEmailService;
  let configService: ConfigService;

  const mockAuthService = {
    login: jest.fn(),
    register: jest.fn(),
    refreshToken: jest.fn(),
    findUserByToken: jest.fn(),
  };

  const mockUsersService = {
    user: jest.fn(),
    updateUser: jest.fn(),
  };

  const mockEmailService = {
    sendEmail: jest.fn(),
  };

  const mockConfigService = {
    getOrThrow: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AuthController],
      providers: [
        { provide: AuthService, useValue: mockAuthService },
        { provide: UsersService, useValue: mockUsersService },
        { provide: LibEmailService, useValue: mockEmailService },
        { provide: ConfigService, useValue: mockConfigService },
        TokenBlacklistService,
      ],
    }).compile();

    controller = module.get<AuthController>(AuthController);
    authService = module.get<AuthService>(AuthService);
    usersService = module.get<UsersService>(UsersService);
    emailService = module.get<LibEmailService>(LibEmailService);
    configService = module.get<ConfigService>(ConfigService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
    expect(authService).toBeDefined();
    expect(usersService).toBeDefined();
    expect(emailService).toBeDefined();
    expect(configService).toBeDefined();
  });

  describe('login', () => {
    it('should log in a user', async () => {
      const loginDto: LoginRequestDto = { username: 'test', password: '1234' };
      const loginResponse = { access_token: 'jwt-token', user_id: 1 };

      mockAuthService.login.mockResolvedValueOnce(loginResponse);

      const result = await controller.login(loginDto);
      expect(mockAuthService.login).toHaveBeenCalledWith(loginDto);
      expect(result).toEqual(loginResponse);
    });
  });

  describe('register', () => {
    it('should register a new user', async () => {
      const registerDto: RegisterAuthDto = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password',
        profile_type: ProfileType.PATIENT,
        gender: 'Female',
        date_of_birth: '2000-11-01',
        device_id: 'f6cd7e22955d4d9c',
        device_type: 'Android',
        fcm_token: 'token',
      };
      const registerResponse = { user_id: 1, access_token: 'jwt-token' };

      mockAuthService.register.mockResolvedValueOnce(registerResponse);

      const result = await controller.register(registerDto);
      expect(mockAuthService.register).toHaveBeenCalledWith(registerDto);
      expect(result).toEqual(registerResponse);
    });
  });

  describe('forgot-password', () => {
    it('should send a password reset email', async () => {
      const forgotPasswordDto: ForgotPasswordDtoDto = {
        email: '<EMAIL>',
      };
      const user = {
        user_id: 1,
        email: '<EMAIL>',
        profiles: [{ first_name: 'Test' }],
      };

      mockUsersService.user.mockResolvedValueOnce(user);
      mockUsersService.updateUser.mockResolvedValueOnce(true);
      mockEmailService.sendEmail.mockResolvedValueOnce(true);

      const result = await controller.resetpassword(forgotPasswordDto);

      expect(mockUsersService.user).toHaveBeenCalledWith({
        email: forgotPasswordDto.email,
      });
      expect(mockEmailService.sendEmail).toHaveBeenCalled();
      expect(result).toEqual({
        message: 'Sent a password reset link on your email address.',
      });
    });

    it('should throw an exception if the user is not found', async () => {
      const forgotPasswordDto: ForgotPasswordDtoDto = {
        email: '<EMAIL>',
      };
      mockUsersService.user.mockResolvedValueOnce(null);

      await expect(controller.resetpassword(forgotPasswordDto)).rejects.toThrow(
        BadRequestException
      );
    });
  });

  describe('reset-password', () => {
    it('should update the user password', async () => {
      const resetPasswordDto: ResetPasswordDTO = {
        token: 'reset-token',
        password: 'newpassword',
      };
      const userWithToken = {
        user_id: 1,
        password_reset_token_expiry: new Date(Date.now() + 3600),
      };

      mockAuthService.findUserByToken.mockResolvedValueOnce(userWithToken);
      mockUsersService.updateUser.mockResolvedValueOnce(true);

      const result = await controller.updatePassword(resetPasswordDto);

      expect(mockAuthService.findUserByToken).toHaveBeenCalledWith(
        resetPasswordDto.token
      );
      expect(mockUsersService.updateUser).toHaveBeenCalled();
      expect(result).toBe(true);
    });

    it('should throw an exception if the token is invalid', async () => {
      const resetPasswordDto: ResetPasswordDTO = {
        token: 'invalid-token',
        password: 'newpassword',
      };
      mockAuthService.findUserByToken.mockResolvedValueOnce(null);

      await expect(controller.updatePassword(resetPasswordDto)).rejects.toThrow(
        BadRequestException
      );
    });
  });

  describe('refresh-token', () => {
    it('should refresh the token', async () => {
      const refreshTokenDto = { token: 'old-token' };
      const newToken = 'new-token';

      mockAuthService.refreshToken.mockResolvedValueOnce(newToken);

      const result = await controller.refreshToken(refreshTokenDto);
      expect(mockAuthService.refreshToken).toHaveBeenCalledWith(
        refreshTokenDto.token
      );
      expect(result).toBe(newToken);
    });
  });

  describe('logout', () => {
    it('should log out a user', async () => {
      const mockReq = { headers: { authorization: 'Bearer token' } };

      const result = await controller.logout(mockReq as any);
      expect(result).toEqual({ message: 'Logged out successfully.' });
    });
  });
});
