// auth.module.ts
import { Module } from '@nestjs/common';
import { UsersModule } from '../users/users.module';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AuthController } from './controllers/auth.controller';
import { LibEmailService } from '@core_be/email';
import { JwtStrategy, LocalStrategy } from '@core_be/auth';
import { UsersService } from '../users/services/users.service';
import { AuthService } from './services/auth.service';
import { WaitlistRepository } from 'libs/data-access/src/lib/waitlist/waitlist.repository';
import { Logger } from 'nestjs-pino';

@Module({
  imports: [
    UsersModule,
    ConfigModule.forRoot(),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: {
          expiresIn: `${configService.get<string>('JWT_EXPIRATION')}s`,
        },
      }),
      inject: [ConfigService],
    }),
  ],
  controllers: [AuthController],
  providers: [
    LibEmailService,
    LocalStrategy,
    JwtStrategy,
    UsersService,
    AuthService,
    WaitlistRepository,
    Logger,
  ],
  exports: [JwtModule],
})
export class AuthModule {}
