import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsEmail,
  IsNotEmpty,
  IsString,
  MinLength,
  IsOptional,
} from '@nestjs/class-validator';
import { IsDateString, IsEnum, IsNumber } from 'class-validator';
import { ProfileType } from '@core/libs';

export class CreateAuthDto {
  @ApiProperty({
    example: '<EMAIL>',
    description: 'User UserName',
    required: true,
  })
  @IsEmail()
  @IsNotEmpty()
  username?: string;

  @ApiProperty({
    example: 'verySecurePassword123',
    description: 'Password',
    required: true,
    minLength: 8,
  })
  @IsString()
  @MinLength(8)
  password?: string;

  @ApiProperty({
    example: 'f6cd7e22955d4d9c',
    description: 'DeviceID',
    required: false,
  })
  @IsString()
  @IsOptional()
  device_id?: string;

  @ApiProperty({
    example: 'Android',
    description: 'Device Type',
    required: false,
  })
  @IsString()
  @IsOptional()
  device_type?: string;

  @ApiProperty({
    example:
      'f4-v18AzRdqLoaKRWZC6xs:APA91bEfIC9dN-_JLJGVzENoUbmGy-DvHhpyi-YIDBv7TRZlyel3WKoQsY2MQNA8SNqiRnEQ_T8CGvVHOP2ouaA5TVb2lmbZoNoEFEsE1tvr69TAxkxEHkCGCtomAvWCaKEx3fpJzQc4',
    description: 'FCM Token',
    required: false,
  })
  @IsString()
  @IsOptional()
  fcm_token?: string;
}

export class RegisterAuthDto {
  @ApiProperty({
    example: ProfileType.PATIENT,
    description: 'Profile type',
    required: true,
  })
  @IsEnum(ProfileType)
  @IsNotEmpty()
  profile_type: ProfileType;

  @ApiProperty({
    example: '<EMAIL>',
    description: 'User email address',
    required: true,
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  // @ApiProperty({
  //   example: 'johnss',
  //   description: 'User name',
  //   required: true,
  // })
  // @IsString()
  // @IsNotEmpty()
  // username: string;

  @ApiProperty({
    example: 'Male',
    description: 'Gender of the user',
    required: false,
  })
  @IsString()
  @IsOptional()
  gender: string;

  @ApiProperty({
    example: '2000-11-01',
    description: 'DOB of the user',
    required: false,
  })
  @IsString()
  @IsOptional()
  @IsNotEmpty({ message: 'Date of birth is required' })
  @IsDateString(
    {},
    { message: 'Date of birth must be in the format yyyy-MM-dd' }
  )
  date_of_birth: string;

  @ApiProperty({
    example: 'verySecurePassword123',
    description: 'Password',
    required: true,
    minLength: 8,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(8)
  password: string;

  @ApiProperty({
    example: 'f6cd7e22955d4d9c',
    description: 'DeviceID',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  device_id: string;

  @ApiProperty({
    example: 'Android',
    description: 'Device Type',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  device_type: string;

  @ApiProperty({
    example:
      'f4-v18AzRdqLoaKRWZC6xs:APA91bEfIC9dN-_JLJGVzENoUbmGy-DvHhpyi-YIDBv7TRZlyel3WKoQsY2MQNA8SNqiRnEQ_T8CGvVHOP2ouaA5TVb2lmbZoNoEFEsE1tvr69TAxkxEHkCGCtomAvWCaKEx3fpJzQc4',
    description: 'FCM Token',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  fcm_token: string;
}
