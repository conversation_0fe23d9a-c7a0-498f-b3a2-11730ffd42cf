import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsEmail,
  IsNotEmpty,
  IsString,
  MinLength,
  IsOptional,
} from '@nestjs/class-validator';

export class RefreshTokenDto {
  @ApiProperty({
    example:
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************.AfBqLQSFm6LefvrerjkkRH0imJTTiWOZS0-KZyWZ7OQ',
    description: 'token',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  token?: string;
}
