export interface RegisterReturnType {
  user_id: number;
  email: string;
  access_token: string;
  is_terms_accepted: boolean;
}

export interface LoginReturnType {
  user_id: number;
  email: string;
  access_token: string;
  onboarding_status: string;
  is_terms_accepted: boolean;
  profiles: Profiles[];
}

export interface Profiles {
  profile_id: number;
  profile_type: string;
  user_id: number;
  first_name: string;
  last_name: string;
  relation_to_user: string;
  gender: string;
  date_of_birth: Date;
  profile_picture_file_name: string;
  phone_number: string;
  country: string;
  state: string;
  city: string;
  address: string;
  zip_code: string;
  is_default: boolean;
  created_by: number;
  created_at: Date;
  updated_by: number;
  updated_at: Date;
  deleted_by: number;
  deleted_at: Date;
  is_active: boolean;
  is_deleted: boolean;
}
