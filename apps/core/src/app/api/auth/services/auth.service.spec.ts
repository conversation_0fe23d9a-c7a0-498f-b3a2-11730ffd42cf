import { Test, TestingModule } from '@nestjs/testing';
import { AuthService } from './auth.service';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '@core/prisma-client';
import {
  OnBoardingRepository,
  PrivacyPolicyRepository,
  TermsRepository,
  UsersDeviceRepository,
  UsersRepository,
  UsersRoleRepository,
} from 'libs/data-access/src';
import { LibProfileService } from '@core_be/global';
import { WhitelistRepository } from 'libs/data-access/src/lib/whitelist/whitelist.repository';
import { JwtService } from '@nestjs/jwt';
import { RoleRepository } from 'libs/data-access/src/lib/roles/role.repository';

describe('AuthService', () => {
  let service: AuthService;

  beforeEach(async () => {
    const mockConfigService = {
      getOrThrow: jest.fn().mockImplementation((key) => {
        if (key === 'JWT_EXPIRATION') return 3600;
        if (key === 'JWT_SECRET') return 'secretKey';
      }),
    };
    const mockTermsService = {};
    const mockUsersDeviceService = {};
    const mockUsersService = {};
    const mockUsersRolesService = {};
    const mockPrivacyPolicyService = {};
    const mockOnBoardingService = {};
    const mockLibProfileService = {};
    const mockWhitelistRepository = {};
    const mockRoleService = {};
    const mockJwtService = {
      sign: jest.fn().mockReturnValue('mockJwtToken'),
      decode: jest.fn(),
    };
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        { provide: ConfigService, useValue: mockConfigService },
        { provide: TermsRepository, useValue: mockTermsService },
        { provide: UsersDeviceRepository, useValue: mockUsersDeviceService },
        { provide: UsersRepository, useValue: mockUsersService },
        { provide: UsersRoleRepository, useValue: mockUsersRolesService },
        {
          provide: PrivacyPolicyRepository,
          useValue: mockPrivacyPolicyService,
        },
        { provide: OnBoardingRepository, useValue: mockOnBoardingService },
        { provide: WhitelistRepository, useValue: mockWhitelistRepository },
        { provide: JwtService, useValue: mockJwtService },
        { provide: LibProfileService, useValue: mockLibProfileService },
        { provide: RoleRepository, useValue: mockRoleService },
        AuthService,
        PrismaService,
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
