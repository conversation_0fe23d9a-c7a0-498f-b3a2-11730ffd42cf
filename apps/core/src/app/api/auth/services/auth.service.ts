import {
  BadRequestException,
  ConflictException,
  ForbiddenException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Algorithm } from 'jsonwebtoken';
import * as bcrypt from 'bcrypt';
import { users } from '@core/prisma-client';
import { ConfigService } from '@nestjs/config';
import { PrismaService, Prisma } from '@core/prisma-client';
import {
  OnBoardingRepository,
  PrivacyPolicyRepository,
  TermsRepository,
  UsersDeviceRepository,
  UsersRepository,
  UsersRoleRepository,
} from 'libs/data-access/src';
import {
  LibProfileService,
  ProfileType,
  WaitListUserType,
} from '@core_be/global';
import { Profiles, RegisterAuthDto } from '../dto';
import {
  LoginRequestDto,
  LoginResponseDto,
  RegisterResponseDto,
} from '../../users/dto';
import { WhitelistRepository } from 'libs/data-access/src/lib/whitelist/whitelist.repository';
import { WaitlistRepository } from 'libs/data-access/src/lib/waitlist/waitlist.repository';
import { LibEmailService } from '@core_be/email';
import { UsersService } from '../../users/services/users.service';

@Injectable()
export class AuthService {
  private readonly jwtExpiration: number;
  private readonly jwtSecret: string;
  private readonly jwtAlgorithm: Algorithm;
  private readonly sendgridEmail: string;
  private readonly patientWaitListedTemplateId: string;
  private readonly healerWaitListedTemplateId: string;
  private readonly healerWelcomeTemplateId: string;
  private readonly patientWelcomeTemplateId: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly usersRepository: UsersRepository,
    private readonly usersRoleRepository: UsersRoleRepository,
    private readonly usersDeviceRepository: UsersDeviceRepository,
    private readonly onBoardingRepository: OnBoardingRepository,
    private readonly termsRepository: TermsRepository,
    private readonly whitelistRepository: WhitelistRepository,
    private readonly jwtService: JwtService,
    private readonly prisma: PrismaService,
    private readonly profileService: LibProfileService,
    private readonly privacyPolicyRepo: PrivacyPolicyRepository,
    private readonly waitListRepository: WaitlistRepository,
    private readonly libEmailService: LibEmailService,
    private readonly usersService: UsersService
  ) {
    this.jwtExpiration =
      this.configService.getOrThrow<number>('JWT_EXPIRATION');
    this.jwtSecret = this.configService.getOrThrow<string>('JWT_SECRET');
    this.jwtAlgorithm = this.configService.getOrThrow<string>(
      'JWT_ALGORITHM'
    ) as Algorithm; // Ensure the algorithm is cast to the correct type
    this.sendgridEmail = this.configService.getOrThrow<string>(
      'SENDGRID_FROM_EMAIL'
    );
    this.patientWaitListedTemplateId = this.configService.getOrThrow<string>(
      'PATIENT_WAITLISTED_SENDGRID_TEMPLATE_ID'
    );
    this.healerWaitListedTemplateId = this.configService.getOrThrow<string>(
      'HEALER_WAITLISTED_SENDGRID_TEMPLATE_ID'
    );
    this.healerWelcomeTemplateId = this.configService.getOrThrow<string>(
      'HEALER_WELCOME_SENDGRID_TEMPLATE_ID'
    );
    this.patientWelcomeTemplateId = this.configService.getOrThrow<string>(
      'PATIENT_WELCOME_SENDGRID_TEMPLATE_ID'
    );
  }
  async validateUser(username: string, password: string): Promise<users> {
    const user = await this.usersRepository.findOne({
      username,
    });
    if (!user) {
      throw new BadRequestException('User not found');
    }
    if (!user.password_hash) {
      throw new BadRequestException('Password is required');
    }
    const isPasswordMatch: boolean = await bcrypt.compare(
      password,
      user.password_hash
    );
    if (!isPasswordMatch) {
      throw new BadRequestException('Password does not match');
    }
    return user;
  }

  async login(loginRequestDto: LoginRequestDto): Promise<LoginResponseDto> {
    const userData = await this.usersRepository.findOneByUsername(
      loginRequestDto.username
    );

    if (!userData) {
      throw new NotFoundException('User not found');
    }

    if (userData.is_deleted || !userData.is_active) {
      const message = userData.is_deleted
        ? 'Your account has been deleted. Please contact the administrator.'
        : 'Your account has been deactivated. Please contact the administrator.';
      throw new NotFoundException(message);
    }

    await this.usersRepository.update({
      where: { user_id: userData.user_id },
      data: { last_login_date: new Date() },
    });

    const roles = await this.usersRepository.findUserRole(userData?.user_id);

    if (!roles?.user_roles?.length) {
      throw new NotFoundException("Roles doesn't assign to current user!");
    }

    // // findOne should be enough to do the job - why is roles undefined - resolve it with findOne
    const roleNames = roles?.user_roles?.map((role) => role?.roles?.name);

    const tokenPayload = {
      username: loginRequestDto.username.toLowerCase(),
      roles: roleNames,
      user_id: userData?.user_id,
      profile_ids: userData?.profiles?.length
        ? userData?.profiles?.map((profile) => profile.profile_id)
        : [],
    };

    const token = await this.jwtService.signAsync(tokenPayload, {
      expiresIn: this.jwtExpiration,
      secret: this.jwtSecret,
      algorithm: this.jwtAlgorithm,
    });

    const userDefaultUserDevice =
      await this.usersDeviceRepository.getDefaultUserDevice({
        user_id: userData.user_id,
        is_deleted: false,
        device_id: loginRequestDto.device_id,
        device_type: loginRequestDto.device_type,
      });

    let userDefaultOnBoarding =
      await this.onBoardingRepository.getDefaultUserOnBoarding({
        user_id: userData.user_id,
        device_id: loginRequestDto.device_id,
        device_type: loginRequestDto.device_type,
        is_deleted: false,
      });

    if (!userDefaultUserDevice) {
      const createUserDevice = {
        users: { connect: { user_id: userData.user_id } },
        device_id: loginRequestDto.device_id || '',
        device_type: loginRequestDto.device_type || '',
        fcm_token: loginRequestDto.fcm_token || '',
        created_by: userData.user_id,
      };
      await this.usersDeviceRepository.createUserDevice(createUserDevice);
    } else {
      const updateUserDevice = {
        users: { connect: { user_id: userData.user_id } },
        device_id: loginRequestDto.device_id,
        device_type: loginRequestDto.device_type,
        fcm_token: loginRequestDto.fcm_token,
        updated_by: userData.user_id,
        updated_at: new Date(),
      };

      await this.usersDeviceRepository.updateUserDevice({
        where: { user_device_id: +userDefaultUserDevice.user_device_id },
        data: updateUserDevice,
      });
    }
    if (!userDefaultOnBoarding) {
      const createOnBoarding = {
        users: { connect: { user_id: userData.user_id } },
        device_id: loginRequestDto.device_id || '',
        device_type: loginRequestDto.device_type || '',
        created_by: userData.user_id,
      };
      userDefaultOnBoarding = await this.onBoardingRepository.createOnBoarding(
        createOnBoarding
      );
    }

    const userTermsStatus = await this.usersService.getUserTermsStatus(
      userData.user_id
    );

    const userDefaultProfile = await this.profileService.profiles({
      where: {
        user_id: userData.user_id,
        is_deleted: false,
      },
    });

    const privacyPolicyStatus = await this.usersService.getUserPrivacyPolicy(
      userData.user_id
    );

    return {
      user_id: userData.user_id,
      email: userData.email,
      access_token: token,
      onboarding_status: userDefaultOnBoarding.status,
      is_terms_accepted: !!userTermsStatus?.is_terms_accepted,
      is_privacy_policy_accepted:
        !!privacyPolicyStatus?.is_privacy_policy_accepted,
      is_plan_selected: false,
      profiles: userDefaultProfile,
    };
  }

  async register(user: RegisterAuthDto): Promise<RegisterResponseDto> {
    return this.prisma.$transaction(async (prisma) => {
      try {
        const whitelistedUser = await this.whitelistRepository.findOne(
          user.email
        );
        if (whitelistedUser) {
          const existingUser = await this.usersRepository.findOneByEmail(
            user.email
          );

          if (existingUser) {
            throw new ConflictException('Email address already exists');
          }
          if (!user.password) {
            throw new BadRequestException('Password is required');
          }
          const hashedPassword = await bcrypt.hash(user.password, 10);
          const email = user.email.toLowerCase();
          const newUser = {
            email,
            password_hash: hashedPassword,
            username: email,
            last_login_date: new Date(),
            year_of_birth: user.date_of_birth
              ? new Date(user.date_of_birth).getFullYear()
              : null,
          };
          const userData = await this.usersRepository.create(newUser, prisma);

          const roles = await this.prisma.roles.findFirst({
            where: { name: user.profile_type },
          });

          if (!roles?.role_id) {
            throw new BadRequestException('Role not found!');
          }

          const createUserRoleData = {
            roles: { connect: { role_id: roles.role_id } },
            users: { connect: { user_id: userData.user_id } },
          };

          await this.usersRoleRepository.createUserRole(
            createUserRoleData,
            prisma
          );

          const userDevice = {
            users: { connect: { user_id: userData.user_id } },
            device_id: user.device_id || '',
            device_type: user.device_type || '',
            fcm_token: user.fcm_token || '',
            created_by: userData.user_id,
          };
          await this.usersDeviceRepository.createUserDevice(userDevice, prisma);

          const onBoarding = {
            users: { connect: { user_id: userData.user_id } },
            device_id: user.device_id || '',
            device_type: user.device_type || '',
            created_by: userData.user_id,
          };
          await this.onBoardingRepository.createOnBoarding(onBoarding, prisma);

          const profilePayload = {
            first_name: '',
            last_name: '',
            profile_type: user.profile_type || '',
            relation_to_user: 'Self',
            profile_picture_file_name: '',
            phone_number: '',
            country: '',
            state: '',
            city: '',
            address: '',
            zip_code: '',
            created_by: userData.user_id,
            users: { connect: { user_id: userData.user_id } },
            gender: user.gender || '',
            is_default: true,
          };

          const profile: Profiles = await this.profileService.createProfile(
            profilePayload,
            prisma
          );

          const userRoles = await this.usersRepository.findUserRole(
            userData?.user_id,
            prisma
          );

          if (!userRoles?.user_roles?.length) {
            throw new ForbiddenException(
              "Roles doesn't assign to current user!"
            );
          }

          const roleNames = userRoles?.user_roles?.map(
            (role) => role?.roles?.name
          );

          const tokenPayload = {
            username: email,
            roles: roleNames,
            user_id: userData?.user_id,
            profile_ids: profile?.profile_id ? [profile?.profile_id] : [],
          };

          const token = this.jwtService.sign(tokenPayload, {
            expiresIn: this.jwtExpiration,
            secret: this.jwtSecret,
            algorithm: this.jwtAlgorithm,
          });
          // Accepting the terms and conditions
          await this.usersService.saveUserTerms(
            {
              is_terms_accepted: true,
              users: { connect: { user_id: userData.user_id } },
            },
            prisma
          );
          // Accepting the privacy policy
          await this.usersService.saveUserPrivacyPolicy(
            {
              is_privacy_policy_accepted: true,
              users: { connect: { user_id: userData.user_id } },
            },
            prisma
          );
          // Sending a welcome email upon successful registering.
          let validWelcomeTemplateId: string;
          if (user.profile_type === ProfileType.HEALER) {
            validWelcomeTemplateId = this.healerWelcomeTemplateId;
          } else if (user.profile_type === ProfileType.PATIENT) {
            validWelcomeTemplateId = this.patientWelcomeTemplateId;
          }
          if (validWelcomeTemplateId) {
            const welcomeEmail = {
              to: user.email,
              from: `${this.sendgridEmail}`,
              templateId: validWelcomeTemplateId,
            };
            await this.libEmailService.sendEmail(welcomeEmail);
          }

          return {
            user_id: userData.user_id,
            email: userData.email,
            access_token: token,
            is_terms_accepted: true,
            is_privacy_policy_accepted: true,
            is_plan_selected: false,
            onboarding_status: '',
            profiles: profile ? [profile] : [],
            is_wait_listed: false,
            is_white_listed: true,
          };
        } else {
          const waitlistUserType =
            user.profile_type === ProfileType.PATIENT
              ? WaitListUserType.PATIENT
              : WaitListUserType.HEALER;
          const waitListedUser =
            await this.waitListRepository.findByEmailAndType(
              user.email,
              waitlistUserType
            );
          if (!waitListedUser) {
            const waitListData = {
              email: user.email,
              user_type: user.profile_type,
              created_at: new Date(),
            };
            await this.waitListRepository.create(waitListData, prisma);
          }
          const waitListedEmail = {
            to: user.email,
            from: `${this.sendgridEmail}`,
            templateId:
              user.profile_type === ProfileType.PATIENT
                ? `${this.patientWaitListedTemplateId}`
                : `${this.healerWaitListedTemplateId}`,
          };
          await this.libEmailService.sendEmail(waitListedEmail);
          return {
            user_id: null,
            email: user.email,
            access_token: null,
            is_terms_accepted: null,
            is_privacy_policy_accepted: null,
            is_plan_selected: null,
            onboarding_status: '',
            profiles: [],
            is_wait_listed: true,
            is_white_listed: false,
          };
        }
      } catch (error) {
        throw new NotFoundException(error.message);
      }
    });
  }

  // DEPRECATED: This method is not used anymore.
  // async generateUniqueUsername() {
  //   // Retrieve the last user's username
  //   const lastUser = await this.usersRepository.findLast();
  //   const lastUsername = lastUser ? lastUser.username : 'U0000000000';

  //   // Extract the numeric part and increment it by 1
  //   const idChars = parseInt(lastUsername.slice(1)) + 1;

  //   // Generate the new username
  //   const newUsername = 'U' + idChars.toString().padStart(9, '0');

  //   return newUsername;
  // }

  async forgotPassword(params: {
    where: Prisma.usersWhereUniqueInput;
    data: Prisma.usersUpdateInput;
  }) {
    const { where, data } = params;
    return this.usersRepository.update({ where, data });
  }

  // async findUserByToken(token: string): Promise<users | null> {
  //   const where = { password_reset_token: token };
  //   return this.usersRepository.findFirst({ where });
  // }

  // async refreshToken(oldRefreshToken: string): Promise<string | null> {
  //   try {
  //     const decoded = this.jwtService.decode(oldRefreshToken);

  //     if (!decoded?.exp) {
  //       throw new Error('Invalid requested token');
  //     }

  //     let refreshedToken = oldRefreshToken;

  //     const timeNow = Math.floor(Date.now() / 1000);
  //     const isTokenExpired = decoded.exp < timeNow;

  //     if (isTokenExpired) {
  //       const user = await this.usersRepository.findOne({
  //         username: decoded?.username,
  //       });

  //       if (!user) throw new Error('Invalid user');

  //       const tokenPayload = {
  //         username: user.username,
  //       };

  //       refreshedToken = this.jwtService.sign(tokenPayload, {
  //         expiresIn: this.jwtExpiration,
  //         secret: this.jwtSecret,
  //       });
  //     }

  //     return refreshedToken;
  //   } catch (e) {
  //     throw new BadRequestException('Invalid refresh token');
  //   }
  // }

  async findUserByToken(token: string): Promise<users | null> {
    return await this.usersRepository.findUserByToken(token);
  }

  async findUserWithValidResetToken(email: string): Promise<users | null> {
    return await this.usersRepository.findUserWithValidResetToken(email);
  }
}
