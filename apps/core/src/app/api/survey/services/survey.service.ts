import { Injectable } from '@nestjs/common';
import { ProfileType, SurveryType } from '@core/libs';
import { SurveySubmissionDto } from '../dto/survey-submission.dto';
import { PrismaService } from '@core/prisma-client';

@Injectable()
export class SurveyService {
  constructor(private prisma: PrismaService) {}

  async storeSurvey(userType: ProfileType, dto: SurveySubmissionDto) {
    const survey = await this.prisma.survey_submissions.create({
      data: {
        user_type: userType,
        email: dto.surveyData?.email,
        type: SurveryType.Onboarding,
        data: dto.surveyData,
      },
    });

    return { survey_Id: survey.id };
  }
}
