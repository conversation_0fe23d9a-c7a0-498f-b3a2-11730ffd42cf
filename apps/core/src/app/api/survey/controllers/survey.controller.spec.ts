import { Test, TestingModule } from '@nestjs/testing';
import { SurveyController } from './survey.controller';
import { SurveyService } from '../services/survey.service';
import { SurveySubmissionDto } from '../dto/survey-submission.dto';
import { ProfileType } from '@core/libs';

const serveySubmissionDto: SurveySubmissionDto = {
  surveyData: {
    name: 'test',
  },
};

describe('ServeyController', () => {
  let serveyController: SurveyController;
  let serveyService: SurveyService;

  const mockServeyService = {
    storeSurvey: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [SurveyController],
      providers: [{ provide: SurveyService, useValue: mockServeyService }],
    }).compile();

    serveyController = module.get<SurveyController>(SurveyController);
    serveyService = module.get<SurveyService>(SurveyService);
  });

  it('should be defined', () => {
    expect(serveyController).toBeDefined();
  });

  it('should submit healer survey', async () => {
    const dto: SurveySubmissionDto = {
      surveyData: {
        email: '<EMAIL>',
        name: 'test',
      },
    };
    const result = { survey_Id: 2 };
    jest.spyOn(serveyService, 'storeSurvey').mockResolvedValue(result);

    expect(await serveyController.submitHealerSurvey(dto)).toBe(result);
    expect(serveyService.storeSurvey).toHaveBeenCalledWith(
      ProfileType.HEALER,
      dto
    );
  });

  it('should submit patient survey', async () => {
    const dto: SurveySubmissionDto = {
      surveyData: {
        email: '<EMAIL>',
        name: 'test',
      },
    };
    const result = { survey_Id: 1 };
    jest.spyOn(serveyService, 'storeSurvey').mockResolvedValue(result);

    expect(await serveyController.submitPatientSurvey(dto)).toBe(result);
    expect(serveyService.storeSurvey).toHaveBeenCalledWith(
      ProfileType.PATIENT,
      dto
    );
  });
});
