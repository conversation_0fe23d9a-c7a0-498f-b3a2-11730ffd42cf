import { Controller, Post, Body, UseGuards } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { ProfileType } from '@core/libs';
import { Logger } from 'nestjs-pino';
import { Public } from '@core/libs';
import { SurveySubmissionDto } from '../dto/survey-submission.dto';
import { SurveyService } from '../services/survey.service';
import { ApiKeyGuard } from 'libs/global/src/lib/guards/api-key.guard';

@ApiTags('Survey')
@Public() // Adding this to bypass global JWT token validation
@UseGuards(ApiKeyGuard)
@Controller('survey')
export class SurveyController {
  constructor(
    private readonly surveyService: SurveyService,
    private readonly logger: Logger
  ) {}

  @Post('healer/submission')
  async submitHealerSurvey(@Body() dto: SurveySubmissionDto) {
    this.logger.log({
      timestamp: new Date().toISOString(),
      event: 'SURVEY_SUBMISSION',
      profileType: ProfileType.HEALER,
      // surveyData: { questionsCount: dto.surveyData?.length || 0 },
      message: 'Healer survey submitted'
    }, 'Survey Submission');

    return this.surveyService.storeSurvey(ProfileType.HEALER, dto);
  }

  @Post('patient/submission')
  async submitPatientSurvey(@Body() dto: SurveySubmissionDto) {
    this.logger.log({
      timestamp: new Date().toISOString(),
      event: 'SURVEY_SUBMISSION',
      profileType: ProfileType.PATIENT,
      // surveyData: { questionsCount: dto.surveyData?.length || 0 },
      message: 'Patient survey submitted'
    }, 'Survey Submission');

    return this.surveyService.storeSurvey(ProfileType.PATIENT, dto);
  }
}
