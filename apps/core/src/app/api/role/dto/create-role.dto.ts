import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from '@nestjs/class-validator';

export class CreateRoleDto {
  @ApiProperty({
    example: 'Super Admin',
    description: 'Unique Role',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    example: 'description',
    description: 'description',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  description: string;
}
