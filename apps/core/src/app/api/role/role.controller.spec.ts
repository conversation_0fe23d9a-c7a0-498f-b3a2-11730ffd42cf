import { Test, TestingModule } from '@nestjs/testing';
import { RoleController } from './role.controller';
import { RoleService } from './role.service';
import { faker } from '@faker-js/faker';
import { CreateRoleDto } from './dto/create-role.dto';
import { UpdateRoleDto } from './dto/update-role.dto';

const user_id = 4;

const mockRequest: Request = {
  raw: {
    user: {
      user_id,
    },
  },
} as any;

// Create a mock for RoleService
const RoleStub = () => {
  return {
    name: faker.string.alpha(8),
    description: faker.string.alpha(20),
  };
};

class MockRoleService {
  Role = jest.fn();
  role = jest.fn();
  createRole = jest.fn();
  updateRole = jest.fn();
  deleteRole = jest.fn();
  deleteRoles = jest.fn();
}

describe('RoleController', () => {
  const roleIdsToCleanup: number[] = [];

  afterEach(async () => {
    while (roleIdsToCleanup.length > 0) {
      const roleId = roleIdsToCleanup.pop();
      if (roleId) {
        try {
          await service.deleteRoles({ role_id: roleId });
        } catch (err) {
          console.error(`Failed to clean up role with ID: ${roleId}`, err);
        }
      }
    }
  });

  let controller: RoleController;
  let service: RoleService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [RoleController],
      providers: [
        RoleService,
        { provide: RoleService, useClass: MockRoleService },
      ],
    }).compile();

    controller = module.get<RoleController>(RoleController);
    service = module.get<RoleService>(RoleService);
  });

  it('should create a role', async () => {
    const dto: CreateRoleDto = {
      name: RoleStub().name,
      description: RoleStub().description,
    };

    const expectedCreateData = {
      ...dto,
      created_by: user_id,
    };

    const mockResponse = {
      role_id: 1,
      name: dto.name,
      description: dto.description,
      created_by: user_id,
      created_at: new Date(),
      updated_by: null,
      updated_at: null,
      deleted_by: null,
      deleted_at: null,
      is_deleted: false,
    };

    jest.spyOn(service, 'createRole').mockResolvedValue(mockResponse);
    const role = await controller.create(dto, mockRequest);
    roleIdsToCleanup.push(role.role_id);
    expect(service.createRole).toHaveBeenCalledWith(expectedCreateData);
    expect(role).toEqual(mockResponse);
  });

  it('should return a list of roles', async () => {
    const mockRoles = [
      {
        role_id: faker.number.int(),
        name: faker.string.alpha(8),
        description: faker.string.alpha(8),
        created_by: faker.number.int(),
        created_at: faker.date.past(),
        updated_by: faker.number.int(),
        updated_at: faker.date.past(),
        deleted_by: faker.number.int(),
        deleted_at: faker.date.past(),
        is_deleted: false,
        role_attributes: [
          {
            role_attribute_id: faker.number.int(),
            role_id: faker.number.int(),
            attribute_id: faker.number.int(),
          },
        ],
        user_roles: [
          {
            user_role_id: faker.number.int(),
            user_id: faker.number.int(),
            role_id: faker.number.int(),
          },
        ],
      },
      {
        role_id: faker.number.int(),
        name: faker.string.alpha(8),
        description: faker.string.alpha(8),
        created_by: faker.number.int(),
        created_at: faker.date.past(),
        updated_by: faker.number.int(),
        updated_at: faker.date.past(),
        deleted_by: faker.number.int(),
        deleted_at: faker.date.past(),
        is_deleted: false,
        role_attributes: [
          {
            role_attribute_id: faker.number.int(),
            role_id: faker.number.int(),
            attribute_id: faker.number.int(),
          },
        ],
        user_roles: [
          {
            user_role_id: faker.number.int(),
            user_id: faker.number.int(),
            role_id: faker.number.int(),
          },
        ],
      },
    ];

    jest.spyOn(service, 'role').mockResolvedValue(mockRoles);
    const roles = await controller.findAll();
    expect(service.role).toHaveBeenCalled();
    expect(roles).toEqual(mockRoles);
  });

  it('should return a single role', async () => {
    const id = 1;
    const mockResponse = {
      role_id: 1,
      name: faker.string.alpha(8),
      description: faker.string.alpha(8),
      created_by: faker.number.int(),
      created_at: faker.date.past(),
      updated_by: faker.number.int(),
      updated_at: faker.date.past(),
      deleted_by: faker.number.int(),
      deleted_at: faker.date.past(),
      is_deleted: false,
      role_attributes: [
        {
          role_attribute_id: faker.number.int(),
          role_id: faker.number.int(),
          attribute_id: faker.number.int(),
        },
      ],
      user_roles: [
        {
          user_role_id: faker.number.int(),
          user_id: faker.number.int(),
          role_id: faker.number.int(),
        },
      ],
    };

    jest.spyOn(service, 'Role').mockResolvedValue(mockResponse);
    const role = await controller.findOne(String(id));
    expect(service.Role).toHaveBeenCalledWith({ role_id: id });
    expect(role).toEqual(mockResponse);
  });

  it('should create and then update a role', async () => {
    const id = 1;
    const user_id = 4;

    const createRoleDto = {
      name: faker.string.alpha(8),
      description: faker.string.alpha(8),
    };

    const createMockResponse = {
      role_id: id,
      name: createRoleDto.name,
      description: createRoleDto.description,
      created_by: 1,
      created_at: new Date(),
      updated_by: user_id,
      updated_at: new Date(),
      deleted_by: null,
      deleted_at: null,
      is_deleted: false,
      role_attributes: [],
      user_roles: [],
    };

    jest.spyOn(service, 'createRole').mockResolvedValue(createMockResponse);

    const createdRole = await controller.create(createRoleDto, {
      raw: { user: { user_id } },
    });
    roleIdsToCleanup.push(createdRole.role_id);
    const updateRoleDto: UpdateRoleDto = {
      name: faker.string.alpha(8),
      description: faker.string.alpha(8),
    };

    const updateMockResponse = {
      role_id: id,
      name: updateRoleDto.name,
      description: updateRoleDto.description,
      created_by: 1,
      created_at: new Date(),
      updated_by: user_id,
      updated_at: new Date(),
      deleted_by: null,
      deleted_at: null,
      is_deleted: false,
      role_attributes: [],
      user_roles: [],
    };

    jest.spyOn(service, 'updateRole').mockResolvedValue(updateMockResponse);

    const updatedRole = await controller.update(String(id), updateRoleDto, {
      raw: { user: { user_id } },
    });

    expect(service.updateRole).toHaveBeenCalledWith({
      where: { role_id: id },
      data: {
        ...updateRoleDto,
        updated_by: user_id,
      },
    });

    expect(updatedRole).toEqual(updateMockResponse);
  });

  it('should remove a role', async () => {
    const id = faker.number.int();
    const user_id = 4;

    const deleteData = {
      is_deleted: true,
      deleted_by: user_id,
      deleted_at: expect.any(Date),
    };

    const mockResponse = {
      role_id: id,
      name: faker.string.alpha(8),
      description: faker.string.alpha(8),
      is_deleted: true,
      deleted_by: user_id,
      deleted_at: new Date(),
      created_by: 1,
      created_at: new Date(),
      updated_by: 1,
      updated_at: new Date(),
    };

    jest.spyOn(service, 'deleteRole').mockResolvedValue(mockResponse);

    const removedRole = await controller.remove(String(id), {
      raw: { user: { user_id } },
    });

    expect(service.deleteRole).toHaveBeenCalledWith(
      { role_id: id },
      deleteData
    );

    expect(removedRole).toEqual(mockResponse);
  });
});
