import { Injectable } from '@nestjs/common';
import { PrismaService, Prisma } from '@core/prisma-client';
@Injectable()
export class RoleService {
  constructor(private prisma: PrismaService) {}

  async createRole(data: Prisma.rolesCreateInput) {
    return this.prisma.roles.create({ data });
  }

  async updateRole(params: {
    where: Prisma.rolesWhereUniqueInput;
    data: Prisma.rolesUpdateInput;
  }) {
    const { where, data } = params;
    return this.prisma.roles.update({ data, where });
  }

  async deleteRole(where: Prisma.rolesWhereUniqueInput, data: any) {
    return this.prisma.roles.update({ data, where });
  }

  async Role(where: Prisma.rolesWhereUniqueInput) {
    return this.prisma.roles.findUnique({
      where,
      include: { role_attributes: true, user_roles: true },
    });
  }

  async role(params: {
    skip?: number;
    take?: number;
    cursor?: Prisma.rolesWhereUniqueInput;
    where?: Prisma.rolesWhereInput;
    orderBy?: Prisma.rolesOrderByWithAggregationInput;
  }) {
    const { skip, take, cursor, where, orderBy } = params;
    return this.prisma.roles.findMany({
      skip,
      take,
      cursor,
      where,
      orderBy,
      include: { role_attributes: true, user_roles: true },
    });
  }

  async deleteRoles(where: Prisma.rolesWhereUniqueInput) {
    return this.prisma.roles.delete({ where });
  }
}
