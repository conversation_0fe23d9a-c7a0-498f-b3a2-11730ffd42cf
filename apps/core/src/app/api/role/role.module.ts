import { Module, NestModule, MiddlewareConsumer } from '@nestjs/common';
import { RoleService } from './role.service';
import { RoleController } from './role.controller';
import { TokenMiddleware } from '@core_be/auth';
import { ConfigService } from '@nestjs/config';
import { Logger } from 'nestjs-pino';

@Module({
  controllers: [RoleController],
  providers: [RoleService, ConfigService, Logger],
})
export class RoleModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(TokenMiddleware).forRoutes(RoleController);
  }
}
