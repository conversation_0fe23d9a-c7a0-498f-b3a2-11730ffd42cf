import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
  NotFoundException,
} from '@nestjs/common';
import { RoleService } from './role.service';
import { CreateRoleDto } from './dto/create-role.dto';
import { UpdateRoleDto } from './dto/update-role.dto';
import { ApiTags, ApiBearerAuth } from '@nestjs/swagger';
import { Role, Roles, RolesGuard } from '@core/libs';
import { Logger } from 'nestjs-pino';

@ApiTags('Roles')
@ApiBearerAuth()
@Controller('role')
@UseGuards(RolesGuard)
export class RoleController {
  constructor(
    private readonly roleService: RoleService,
    private readonly logger: Logger
  ) {}

  @Post()
  @Roles(Role.Admin)
  async create(@Body() createRoleDto: CreateRoleDto, @Request() req) {
    const createData = {
      ...createRoleDto,
      created_by: req.raw.user.user_id,
    };

    this.logger.log({
      timestamp: new Date().toISOString(),
      event: 'ROLE_CREATED',
      userId: req.raw.user.user_id,
      roleName: createRoleDto.name,
      roleDescription: createRoleDto.description,
      message: 'New role created'
    }, 'Role Created');

    return await this.roleService.createRole(createData);
  }

  @Get()
  @Roles(Role.Admin)
  async findAll() {
    return await this.roleService.role({ where: { is_deleted: false } });
  }

  @Get(':id')
  @Roles(Role.Admin)
  async findOne(@Param('id') id: string) {
    const role = await this.roleService.Role({ role_id: +id });
    if (!role) {
      throw new NotFoundException('Role not found.');
    }
    return role;
  }

  @Patch(':id')
  @Roles(Role.Admin)
  async update(
    @Param('id') id: string,
    @Body() updateRoleDto: UpdateRoleDto,
    @Request() req
  ) {
    const updateData = {
      ...updateRoleDto,
      updated_by: req.raw.user.user_id,
    };
    return await this.roleService.updateRole({
      where: { role_id: +id },
      data: updateData,
    });
  }

  @Delete(':id')
  @Roles(Role.Admin)
  async remove(@Param('id') id: string, @Request() req) {
    const deleteData = {
      is_deleted: true,
      deleted_by: req.raw.user.user_id,
      deleted_at: new Date(),
    };
    return await this.roleService.deleteRole({ role_id: +id }, deleteData);
  }

  @Delete('delete/:id')
  @Roles(Role.Admin)
  async delete(@Param('id') id: string) {
    return await this.roleService.deleteRoles({ role_id: +id });
  }
}
