import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  NotFoundException,
} from '@nestjs/common';
import { RoleattributeService } from './roleattribute.service';
import { CreateRoleattributeDto } from './dto/create-roleattribute.dto';
import { UpdateRoleattributeDto } from './dto/update-roleattribute.dto';
import { ApiTags, ApiBearerAuth } from '@nestjs/swagger';
import { Role, Roles, RolesGuard } from '@core/libs';

@ApiTags('RoleAttribute')
@ApiBearerAuth()
@Controller('roleattribute')
@UseGuards(RolesGuard)
export class RoleattributeController {
  constructor(private readonly roleattributeService: RoleattributeService) {}

  @Post()
  @Roles(Role.Admin)
  async create(@Body() createRoleattributeDto: CreateRoleattributeDto) {
    const createData = {
      roles: { connect: { role_id: createRoleattributeDto.role_id } },
      attributes: {
        connect: { attribute_id: createRoleattributeDto.attribute_id },
      },
    };
    return await this.roleattributeService.createRoleAttribute(createData);
  }

  @Get()
  @Roles(Role.Admin)
  async findAll() {
    return await this.roleattributeService.roleAttributes({});
  }

  @Get(':id')
  @Roles(Role.Admin)
  async findOne(@Param('id') id: string) {
    const roleAttribute = await this.roleattributeService.roleAttribute({
      role_attribute_id: +id,
    });
    if (!roleAttribute) {
      throw new NotFoundException('Role attribute not found.');
    }
    return roleAttribute;
  }

  @Patch(':id')
  @Roles(Role.Admin)
  async update(
    @Param('id') id: string,
    @Body() updateRoleattributeDto: UpdateRoleattributeDto
  ) {
    const updateData = {
      roles: { connect: { role_id: updateRoleattributeDto.role_id } },
      attributes: {
        connect: { attribute_id: updateRoleattributeDto.attribute_id },
      },
    };
    return await this.roleattributeService.updateRoleAttribute({
      where: { role_attribute_id: +id },
      data: updateData,
    });
  }

  @Delete(':id')
  @Roles(Role.Admin)
  async remove(@Param('id') id: string) {
    return await this.roleattributeService.deleteRoleAttribute({
      role_attribute_id: +id,
    });
  }
}
