import { Test, TestingModule } from '@nestjs/testing';
import { RoleattributeController } from './roleattribute.controller';
import { RoleattributeService } from './roleattribute.service';
import { CreateRoleattributeDto } from './dto/create-roleattribute.dto';
import { UpdateRoleattributeDto } from './dto/update-roleattribute.dto';
import { faker } from '@faker-js/faker';

const RoleAttributeStub = () => {
  return {
    role_id: faker.number.int(3),
    attribute_id: faker.number.int(3),
  };
};

class MockRoleAttributeService {
  createRoleAttribute = jest.fn();
  roleAttributes = jest.fn().mockResolvedValue([RoleAttributeStub()]);
  roleAttribute = jest.fn();
  updateRoleAttribute = jest.fn();
  deleteRoleAttribute = jest.fn();
}

describe('RoleattributeController', () => {
  let controller: RoleattributeController;
  let service: RoleattributeService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [RoleattributeController],
      providers: [
        RoleattributeService,
        {
          provide: RoleattributeService,
          useValue: new MockRoleAttributeService(),
        },
      ],
    }).compile();

    controller = module.get<RoleattributeController>(RoleattributeController);
    service = module.get<RoleattributeService>(RoleattributeService);
  });

  describe('create', () => {
    it('should call createRoleAttribute service with correct data', async () => {
      const dto: CreateRoleattributeDto = {
        role_id: RoleAttributeStub().role_id,
        attribute_id: RoleAttributeStub().attribute_id,
      };
      const createData = {
        roles: { connect: { role_id: dto.role_id } },
        attributes: { connect: { attribute_id: dto.attribute_id } },
      };
      const mockResponse = {
        role_attribute_id: 1,
        role_id: dto.role_id,
        attribute_id: dto.attribute_id,
      };

      jest
        .spyOn(service, 'createRoleAttribute')
        .mockResolvedValue(mockResponse);

      const result = await controller.create(dto);

      expect(service.createRoleAttribute).toHaveBeenCalledWith(createData);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('findAll', () => {
    it('should call roleAttributes service ', async () => {
      const mockResponse = [
        {
          role_attribute_id: 1,
          role_id: RoleAttributeStub().role_id,
          attribute_id: RoleAttributeStub().attribute_id,
          attributes: {
            attribute_id: 1,
            name: 'Attribute 1',
            description: 'Description for Attribute 1',
            created_by: 1,
            created_at: new Date(),
            updated_by: 1,
            updated_at: new Date(),
            deleted_by: null,
            deleted_at: null,
            is_deleted: false,
          },
          roles: {
            role_id: 1,
            name: 'Role 1',
            description: 'Description for Role 1',
            created_by: 1,
            created_at: new Date(),
            updated_by: 1,
            updated_at: new Date(),
            deleted_by: null,
            deleted_at: null,
            is_deleted: false,
          },
        },
      ];
      jest.spyOn(service, 'roleAttributes').mockResolvedValue(mockResponse);

      const result = await controller.findAll();

      expect(service.roleAttributes).toHaveBeenCalledWith({});
      expect(result).toEqual(mockResponse);
    });
  });

  describe('findOne', () => {
    it('should call roleAttribute service with role attribute id', async () => {
      const id = '1';
      const params = { id: String(id) };
      const mockResponse = {
        role_attribute_id: +id,
        role_id: RoleAttributeStub().role_id,
        attribute_id: RoleAttributeStub().attribute_id,
        attributes: {
          attribute_id: RoleAttributeStub().attribute_id,
          name: 'Test Attribute',
          description: 'Test Description',
          created_by: 1,
          created_at: new Date(),
          updated_by: 1,
          updated_at: new Date(),
          deleted_by: null,
          deleted_at: null,
          is_deleted: false,
        },
        roles: {
          role_id: RoleAttributeStub().role_id,
          name: 'Test Role',
          description: 'Test Description',
          created_by: 1,
          created_at: new Date(),
          updated_by: 1,
          updated_at: new Date(),
          deleted_by: null,
          deleted_at: null,
          is_deleted: false,
        },
      };

      jest.spyOn(service, 'roleAttribute').mockResolvedValue(mockResponse);
      const result = await controller.findOne(params.id);
      expect(service.roleAttribute).toHaveBeenCalledWith({
        role_attribute_id: +id,
      });
      expect(result).toEqual(mockResponse);
    });
  });

  describe('update', () => {
    it('should call updateRoleAttribute service with correct data', async () => {
      const id = '1';
      const dto: UpdateRoleattributeDto = {
        role_id: RoleAttributeStub().role_id,
        attribute_id: RoleAttributeStub().attribute_id,
      };
      const updateData = {
        roles: { connect: { role_id: dto.role_id } },
        attributes: { connect: { attribute_id: dto.attribute_id } },
      };
      const mockResponse = {
        role_attribute_id: +id,
        role_id: dto.role_id,
        attribute_id: dto.attribute_id,
      };
      jest
        .spyOn(service, 'updateRoleAttribute')
        .mockResolvedValue(mockResponse);
      const result = await controller.update(id, dto);
      expect(service.updateRoleAttribute).toHaveBeenCalledWith({
        where: { role_attribute_id: +id },
        data: updateData,
      });
      expect(result).toEqual(mockResponse);
    });
  });

  describe('remove', () => {
    it('should call deleteRoleAttribute service with role attribute id', async () => {
      const id = '1';
      const mockResponse = {
        role_attribute_id: +id,
        role_id: RoleAttributeStub().role_id,
        attribute_id: RoleAttributeStub().attribute_id,
      };

      jest
        .spyOn(service, 'deleteRoleAttribute')
        .mockResolvedValue(mockResponse);
      const result = await controller.remove(id);
      expect(service.deleteRoleAttribute).toHaveBeenCalledWith({
        role_attribute_id: +id,
      });

      expect(result).toEqual(mockResponse);
    });
  });
});
