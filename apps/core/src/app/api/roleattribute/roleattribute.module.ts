import { Module, NestModule, MiddlewareConsumer } from '@nestjs/common';
import { RoleattributeService } from './roleattribute.service';
import { RoleattributeController } from './roleattribute.controller';
import { TokenMiddleware } from '@core_be/auth';
import { ConfigService } from '@nestjs/config';

@Module({
  controllers: [RoleattributeController],
  providers: [RoleattributeService, ConfigService],
})
export class RoleattributeModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(TokenMiddleware).forRoutes(RoleattributeController);
  }
}
