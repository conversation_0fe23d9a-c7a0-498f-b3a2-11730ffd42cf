import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsInt } from '@nestjs/class-validator';

export class CreateRoleattributeDto {
  @ApiProperty({
    example: 1,
    description: 'Role ID',
    required: true,
  })
  @IsNotEmpty()
  @IsInt()
  role_id: number;

  @ApiProperty({
    example: 1,
    description: 'Attribute List ID',
    required: true,
  })
  @IsNotEmpty()
  @IsInt()
  attribute_id: number;
}
