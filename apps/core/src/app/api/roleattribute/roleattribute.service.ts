import { Injectable } from '@nestjs/common';
import { PrismaService, Prisma } from '@core/prisma-client';

@Injectable()
export class RoleattributeService {
  constructor(private prisma: PrismaService) {}

  async createRoleAttribute(data: Prisma.role_attributesCreateInput) {
    return this.prisma.role_attributes.create({ data });
  }

  async updateRoleAttribute(params: {
    where: Prisma.role_attributesWhereUniqueInput;
    data: Prisma.role_attributesUpdateInput;
  }) {
    const { where, data } = params;
    return this.prisma.role_attributes.update({ data, where });
  }

  async deleteRoleAttribute(where: Prisma.role_attributesWhereUniqueInput) {
    return this.prisma.role_attributes.delete({ where });
  }

  async roleAttribute(where: Prisma.role_attributesWhereUniqueInput) {
    return this.prisma.role_attributes.findUnique({
      where,
      include: {
        roles: true,
        attributes: true,
      },
    });
  }

  async roleAttributes(params: {
    skip?: number;
    take?: number;
    cursor?: Prisma.role_attributesWhereUniqueInput;
    where?: Prisma.role_attributesWhereInput;
    orderBy?: Prisma.role_attributesOrderByWithAggregationInput;
  }) {
    const { skip, take, cursor, where, orderBy } = params;
    return this.prisma.role_attributes.findMany({
      skip,
      take,
      cursor,
      where,
      orderBy,
      include: { roles: true, attributes: true },
    });
  }
}
