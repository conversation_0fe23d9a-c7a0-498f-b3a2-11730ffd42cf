import { Test, TestingModule } from '@nestjs/testing';
import { RoleattributeService } from './roleattribute.service';
import { PrismaService } from '@core/prisma-client';

describe('RoleattributeService', () => {
  let service: RoleattributeService;
  let prismaService: PrismaService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [RoleattributeService, PrismaService],
    }).compile();

    service = module.get<RoleattributeService>(RoleattributeService);
    prismaService = module.get<PrismaService>(PrismaService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
