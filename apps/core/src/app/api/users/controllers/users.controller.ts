import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Request,
  BadRequestException,
  ConflictException,
  Query,
  UseGuards,
} from '@nestjs/common';
import * as bcrypt from 'bcrypt';
import { ApiTags, ApiBearerAuth, ApiParam } from '@nestjs/swagger';
import { CreateEmailDto, LibEmailService } from '@core_be/email';
import { LibProfileService, Role, Roles, RolesGuard } from '@core/libs';
import { ConfigService } from '@nestjs/config';
import { Logger } from 'nestjs-pino';
import {
  CreateUserDto,
  ResetUserDto,
  UpdateUserDto,
  UpdateUserPasswordDto,
  CreateUserRoleDto,
  CreateUserdeviceDto,
  UpdateUserdeviceDto,
  SaveUserTermsDto,
  GetTermsUserParams,
  UserIdParams,
  ChangePasswordDto,
  UpdateUserStatusDto,
  CustomUserFindArgs,
  UserQueryDto,
  UserPingParams,
} from '../dto';

import { UsersService } from '../services/users.service';
import { Prisma } from '@core/prisma-client';
import { CreateUserPrivacyPolicyDto } from '../dto/create-user-privacy-policy.dto';
import { ProfileAccessGuard } from 'libs/global/src/lib/guards/profile-access.guard';
import { UserAccessGuard } from 'libs/global/src/lib/guards/user-access.guard';

@ApiTags('Users')
@ApiBearerAuth()
@Controller('users')
@UseGuards(RolesGuard)
export class UsersController {
  private readonly forgotPasswordTemplateId: string;
  private readonly sendgridEmail: string;

  constructor(
    private readonly usersService: UsersService,
    private readonly libEmailService: LibEmailService,
    private configService: ConfigService,
    private readonly profileService: LibProfileService,
    private readonly logger: Logger
  ) {
    this.forgotPasswordTemplateId = this.configService.getOrThrow<string>(
      'FORGOT_PASSWORD_SENDGRID_TEMPLATE_ID'
    );
    this.sendgridEmail = this.configService.getOrThrow<string>(
      'SENDGRID_FROM_EMAIL'
    );
  }

  @Get(':device_id/ping')
  @ApiParam({
    name: 'device_id',
    type: String,
    required: true,
    description: 'Device ID',
    example: 1,
  })
  @Roles(Role.Patient)
  async ping(@Request() req, @Param() params: UserPingParams) {
    const userId = req.raw.user.user_id;
    const deviceId = params.device_id;

    return await this.usersService.updateLastSeen(userId, deviceId);
  }

  @Post()
  @Roles(Role.Admin)
  async create(@Body() createUserDto: CreateUserDto, @Request() req) {
    const isEmailWhitelisted = await this.usersService.isEmailWhitelisted(
      createUserDto.email
    );
    if (!isEmailWhitelisted) {
      return {
        message: `Email ${createUserDto.email} is not registered in the whitelist.`,
      };
    }

    const existingUser = await this.usersService.user({
      email: createUserDto.email,
    });

    if (existingUser) {
      throw new ConflictException('Email address already exists');
    }
    if (!createUserDto.password) {
      throw new Error('Password is required');
    }
    const hashedPassword = await bcrypt.hash(createUserDto.password, 10);
    const createData = {
      email: createUserDto.email,
      password_hash: hashedPassword,
      username: createUserDto.email,
      created_by: req.raw.user.user_id,
    };
    const data = await this.usersService.createUser(createData);
    delete data.password_hash;
    const profile = {
      users: { connect: { user_id: data.user_id } },
      first_name: createUserDto.first_name || '',
      last_name: createUserDto.last_name || '',
      profile_type: createUserDto.profile_type || '',
      gender: createUserDto.gender || '',
      relation_to_user: createUserDto.relation_to_user || null,
      date_of_birth: createUserDto.date_of_birth
        ? new Date(createUserDto.date_of_birth)
        : null,
      profile_picture_file_name:
        createUserDto.profile_picture_file_name || null,
      phone_number: createUserDto.phone_number || null,
      country: createUserDto.country || null,
      state: createUserDto.state || null,
      city: createUserDto.city || null,
      address: createUserDto.address || null,
      zip_code: createUserDto.zip_code || null,
      created_by: req.raw.user.user_id,
      is_default: true,
    };
    await this.profileService.createProfile(profile);

    await this.usersService.createUserRolesWithUserId(data.user_id);

    return data;
  }

  @Get()
  @Roles(Role.Admin)
  async findAll(@Query() query: UserQueryDto) {
    const {
      orderBy,
      order,
      limit,
      offset,
      lastLoginStartDate,
      lastLoginEndDate,
      search,
      firstname,
      lastname,
      status,
      profileType,
    } = query;
    const limitNumber = Number(limit) || 10;
    const offsetNumber = Number(offset) * limitNumber;
    const is_active = status === 'active';
    const orderByCondition: any = {};
    if (orderBy && orderBy !== 'first_name' && orderBy !== 'last_name') {
      orderByCondition[orderBy] = order;
    }

    const queryPayload: CustomUserFindArgs = {
      orderBy: orderByCondition ? orderByCondition : undefined,
      where: (() => {
        const where: Prisma.usersWhereInput = {
          is_deleted: false,
        };
        if (status) {
          where.is_active = is_active;
        }
        if (search) {
          where.OR = [
            { username: { contains: search, mode: 'insensitive' } },
            { email: { contains: search, mode: 'insensitive' } },
          ];
        }

        if (lastLoginStartDate && lastLoginEndDate) {
          where.last_login_date = {
            gte: new Date(lastLoginStartDate),
            lte: new Date(lastLoginEndDate),
          };
        }

        if (firstname) {
          where.profiles = {
            some: {
              first_name: { contains: firstname, mode: 'insensitive' },
            },
          };
        }

        if (lastname) {
          where.profiles = {
            some: {
              last_name: { contains: lastname, mode: 'insensitive' },
            },
          };
        }

        if (profileType) {
          where.profiles = {
            some: {
              profile_type: profileType,
            },
          };
        }
        return where;
      })(),
      include: {
        profiles: {
          where: {
            is_default: true,
          },
        },
        user_roles: {
          include: {
            roles: {
              select: {
                name: true,
              },
            },
          },
        },
      },
    };
    const userData = await this.usersService.users(queryPayload);

    const users: any = userData.map((user) => {
      const roleNames = user.user_roles.map((role) => role.roles?.name);
      return {
        ...user,
        roleNames,
      };
    });

    //Direct cross-relation sorting is not supported in Prisma, so I have implemented it using JavaScript.
    if (orderBy === 'first_name' || orderBy === 'last_name') {
      users.sort((a, b) => {
        const profileA = a.profiles[0] || {};
        const profileB = b.profiles[0] || {};

        const aValue = profileA[orderBy]?.toString().toLowerCase() || '';
        const bValue = profileB[orderBy]?.toString().toLowerCase() || '';

        if (aValue === bValue) {
          return 0;
        }

        if (order === 'asc') {
          return aValue.localeCompare(bValue);
        } else {
          return bValue.localeCompare(aValue);
        }
      });
    }

    const paginatedUsers = users.slice(
      offsetNumber,
      offsetNumber + limitNumber
    );
    const totalItems = users.length;
    const totalPages = Math.ceil(totalItems / limitNumber);
    return {
      users: paginatedUsers,
      pagination: {
        total_pages: totalPages,
        total_items: totalItems,
        offset,
        limit,
      },
    };
  }

  @Get(':id')
  @ApiParam({
    name: 'id',
    type: Number,
    required: true,
    description: 'User ID',
    example: 1,
  })
  @Roles(Role.Admin)
  async findOne(@Param() params: UserIdParams) {
    return await this.usersService.user({ user_id: +params.id });
  }

  @Delete(':id')
  @ApiParam({
    name: 'id',
    type: Number,
    required: true,
    description: 'User ID',
    example: 1,
  })
  @Roles(Role.Healer, Role.Patient)
  async remove(@Param() params: UserIdParams, @Request() req) {
    const targetUserId = +params.id;
    const deleteData = {
      is_deleted: true,
      deleted_by: req.raw.user.user_id,
      deleted_at: new Date(),
    };

    this.logger.log({
      timestamp: new Date().toISOString(),
      event: 'USER_DELETION',
      userId: req.raw.user.user_id,
      targetUserId: targetUserId,
      message: 'User deletion initiated'
    }, 'User Deletion');

    return await this.usersService.deleteUser(
      { user_id: targetUserId },
      deleteData
    );
  }

  @Delete('myself')
  @Roles(Role.Admin, Role.Healer, Role.Patient)
  async deleteAccount(@Request() req) {
    const userId = req.raw.user.user_id;

    this.logger.log({
      timestamp: new Date().toISOString(),
      event: 'ACCOUNT_SELF_DELETION',
      userId: userId,
      message: 'User initiated self-account deletion'
    }, 'Account Self Deletion');

    return this.usersService.deleteAccount(userId);
  }

  @Patch(':id')
  @ApiParam({
    name: 'id',
    type: Number,
    required: true,
    description: 'User ID',
    example: 1,
  })
  @Roles(Role.Admin)
  @UseGuards(ProfileAccessGuard)
  async update(
    @Param() params: UserIdParams,
    @Body() updateUserDto: UpdateUserDto,
    @Request() req
  ) {
    const updateData = {
      email: updateUserDto.email,
      updated_by: req.raw.user.user_id,
      updated_at: new Date(),
    };

    const data = await this.usersService.updateUser({
      where: { user_id: +params.id },
      data: updateData,
    });
    delete data.password_hash;
    const updateProfile = {
      users: { connect: { user_id: data.user_id } },
      first_name: updateUserDto.first_name,
      last_name: updateUserDto.last_name,
      profile_type: updateUserDto.profile_type,
      gender: updateUserDto.gender,
      relation_to_user: updateUserDto.relation_to_user || null,
      ...(updateUserDto.date_of_birth && {
        date_of_birth: new Date(updateUserDto.date_of_birth),
      }),
      profile_picture_file_name:
        updateUserDto.profile_picture_file_name || null,
      phone_number: updateUserDto.phone_number || null,
      country: updateUserDto.country,
      state: updateUserDto.state || null,
      city: updateUserDto.city || null,
      address: updateUserDto.address || null,
      zip_code: updateUserDto.zip_code || null,
      created_by: data.user_id,
      is_default: true,
      updated_by: req.raw.user.user_id,
      updated_at: new Date(),
    };
    await this.profileService.updateProfile({
      where: { profile_id: +updateUserDto.profile_id },
      data: updateProfile,
    });
    return data;
  }

  // TODO: Not in use at the moment. Need to fix security issue
  // @Post('sendmail')
  // @Roles(Role.Admin, Role.Healer, Role.Patient)
  // async sendmail(@Body() createEmailDto: CreateEmailDto) {
  //   await this.libEmailService.sendEmail(createEmailDto);
  //   return {
  //     success: true,
  //     message: 'Email sent',
  //   };
  // }

  @Post('join-subscription')
  @Roles(Role.Admin, Role.Patient)
  async joinSubscriptionWaitList(@Request() req) {
    const userId = +req?.raw?.user?.user_id;
    await this.usersService.joinSubscriptionWaitList(userId);
    return {
      message: 'User successfully joined subscription waitlist',
    };
  }

  @Post('resetpassword')
  @Roles(Role.Admin)
  async resetPassword(@Body() resetUserDto: ResetUserDto) {
    const existingUser = await this.usersService.user({
      email: resetUserDto.email,
    });
    if (!existingUser) {
      throw new BadRequestException('User not found');
    }
    const resetPassword = {
      to: resetUserDto.email,
      from: `${this.sendgridEmail}`,
      templateId: `${this.forgotPasswordTemplateId}`,
      dynamic_template_data: {
        name:
          existingUser.profiles[0].first_name === null
            ? ' '
            : existingUser.profiles[0].first_name,
        action_url: resetUserDto.action_url,
      },
    };

    await this.libEmailService.sendEmail(resetPassword);
    return {
      message: 'Reset Password send in Email',
    };
  }

  @Post('updatepassword')
  @Roles(Role.Admin)
  async updatePassword(
    @Body() updateUserPasswordDto: UpdateUserPasswordDto,
    @Request() req
  ) {
    if (req.body.password !== req.body.confirmpassword) {
      throw new BadRequestException(
        'Password and Confirm Password does not match'
      );
    }
    const existingUser = await this.usersService.user({
      user_id: req.raw.user.user_id,
    });
    const isMatch: boolean = await bcrypt.compare(
      req.body.password,
      existingUser.password_hash
    );
    if (isMatch) {
      throw new BadRequestException(
        'The new password cannot be the same as the previous password.'
      );
    }
    const hashedPassword = await bcrypt.hash(req.body.password, 10);
    const UserPassword = {
      password_hash: hashedPassword,
    };
    return await this.usersService.updateUser({
      where: { user_id: +req.raw.user.user_id },
      data: UserPassword,
    });
  }

  @Post('user-role')
  @Roles(Role.Admin)
  async createUserRole(@Body() createUserRoleDto: CreateUserRoleDto) {
    const createUserRoleData = {
      roles: { connect: { role_id: createUserRoleDto.role_id } },
      users: { connect: { user_id: createUserRoleDto.user_id } },
    };
    return await this.usersService.createUserRole(createUserRoleData);
  }

  @Get('user-role')
  @Roles(Role.Admin)
  async findAllUserRole() {
    return await this.usersService.userRoles({});
  }

  @Get('user-role/:id')
  @Roles(Role.Admin)
  async findOneUserRole(@Param() params: UserIdParams) {
    return await this.usersService.userRole({ user_role_id: +params.id });
  }

  @Patch('user-role/:id')
  @Roles(Role.Admin)
  async updateUserRole(
    @Param() params: UserIdParams,
    @Body() createUserRoleDto: CreateUserRoleDto
  ) {
    const updateUserRoleData = {
      roles: { connect: { role_id: createUserRoleDto.role_id } },
      users: { connect: { user_id: createUserRoleDto.user_id } },
    };
    return await this.usersService.updateUserRole({
      where: { user_role_id: +params.id },
      data: updateUserRoleData,
    });
  }

  @Delete('user-role/:id')
  @Roles(Role.Admin)
  async removeUserRole(@Param() params: UserIdParams) {
    return await this.usersService.deleteUserRole({
      user_role_id: +params.id,
    });
  }

  @Post('user-device')
  @Roles(Role.Healer, Role.Patient)
  async createUserDevice(
    @Body() createUserdeviceDto: CreateUserdeviceDto,
    @Request() req
  ) {
    const createData = {
      ...createUserdeviceDto,
      users: {
        connect: { user_id: createUserdeviceDto.user_id },
      },
      created_by: req.raw.user.user_id,
    };
    delete createData.user_id;
    return await this.usersService.createUserDevice(createData);
  }

  @Patch('user-device/:id')
  @Roles(Role.Healer, Role.Patient)
  async updateUserDevice(
    @Param() params: UserIdParams,
    @Body() updateUserdeviceDto: UpdateUserdeviceDto,
    @Request() req
  ) {
    const updateData = {
      ...updateUserdeviceDto,
      updated_by: req.raw.user.user_id,
      updated_at: new Date(),
    };
    return await this.usersService.updateUserDevice({
      where: { user_device_id: +params.id },
      data: updateData,
    });
  }

  @Post('save-user-terms')
  @Roles(Role.Healer, Role.Patient)
  @UseGuards(UserAccessGuard)
  async saveUserTerms(@Body() saveUserTermsDto: SaveUserTermsDto) {
    const getUserTerms = await this.usersService.getByUserTerms({
      user_id: saveUserTermsDto.user_id,
      is_terms_accepted: true,
    });
    if (getUserTerms) {
      return {
        message:
          'This user has already viewed and accepted the terms and conditions.',
      };
    }
    // No longer maintaining terms and conditions in the database
    return await this.usersService.saveUserTerms({
      is_terms_accepted: true,
      users: { connect: { user_id: saveUserTermsDto.user_id } },
    });
  }

  @Get('terms/:user_id')
  @ApiParam({
    name: 'user_id',
    type: Number,
    required: true,
    description: 'User ID',
    example: 1,
  })
  @UseGuards(UserAccessGuard)
  @Roles(Role.Patient, Role.Healer)
  async getUserTerms(@Param() params: GetTermsUserParams) {
    return await this.usersService.getUserTerms(params.user_id);
  }

  @Get('privacy-policy/:user_id')
  @ApiParam({
    name: 'user_id',
    type: Number,
    required: true,
    description: 'User ID',
    example: 1,
  })
  @UseGuards(UserAccessGuard)
  @Roles(Role.Patient, Role.Healer)
  async getUserPrivacyPolicy(@Param() params: GetTermsUserParams) {
    return await this.usersService.getUserPrivacyPolicy(+params.user_id);
  }

  @Post('save-user-privacy-policy')
  @Roles(Role.Healer, Role.Patient)
  @UseGuards(UserAccessGuard)
  async saveUserPrivacyPolicy(
    @Body() saveUserPrivacyPolicyDto: CreateUserPrivacyPolicyDto
  ) {
    const getUserPrivacyPolicy = await this.usersService.getUserPrivacyPolicy(
      saveUserPrivacyPolicyDto.user_id
    );
    if (getUserPrivacyPolicy) {
      return {
        message: 'This user has already accepted privacy policy.',
      };
    }
    // No longer maintaining privacy policy in the database
    return await this.usersService.saveUserPrivacyPolicy({
      is_privacy_policy_accepted: true,
      users: { connect: { user_id: saveUserPrivacyPolicyDto.user_id } },
    });
  }

  @Patch('change-password')
  @Roles(Role.Admin, Role.Healer, Role.Patient)
  async changePassword(
    @Request() req,
    @Body() changePasswordDto: ChangePasswordDto
  ) {
    const existingUser = await this.usersService.getUserById(
      req.raw.user.user_id
    );

    if (!existingUser) {
      throw new BadRequestException('User not found.');
    }

    const isOldPasswordValid = await bcrypt.compare(
      changePasswordDto.old_password,
      existingUser.password_hash
    );
    if (!isOldPasswordValid) {
      throw new BadRequestException('Old password is incorrect.');
    }

    if (changePasswordDto.new_password === changePasswordDto.old_password) {
      throw new BadRequestException(
        'New password cannot be the same as the old password.'
      );
    }

    const hashedPassword = await bcrypt.hash(
      changePasswordDto.new_password,
      10
    );

    const updateData = {
      password_hash: hashedPassword,
      updated_by: req.raw.user.user_id,
      updated_at: new Date(),
    };
    const data = await this.usersService.updateUser({
      where: { user_id: req.raw.user.user_id },
      data: updateData,
    });
    if (!data) {
      this.logger.error({
        timestamp: new Date().toISOString(),
        event: 'PASSWORD_CHANGE_FAILED',
        userId: req.raw.user.user_id,
        email: existingUser.email,
        message: 'Password change operation failed'
      }, 'Password Change Failed');

      throw new BadRequestException('Change password failed.');
    }

    this.logger.log({
      timestamp: new Date().toISOString(),
      event: 'PASSWORD_CHANGED',
      userId: req.raw.user.user_id,
      email: existingUser.email,
      message: 'User password changed successfully'
    }, 'Password Changed');

    return { message: 'Password changed successfully.' };
  }

  @Patch(':user_id/:status')
  @Roles(Role.Admin)
  async updateUserStatus(@Param() params: UpdateUserStatusDto, @Request() req) {
    const { user_id, status } = params;

    const existingUser = await this.usersService.getUserById(+user_id);

    if (!existingUser) {
      throw new BadRequestException('User not found.');
    }
    const is_active = status === 'active';
    const updateData = {
      is_active,
      updated_by: req.raw.user.user_id,
      updated_at: new Date(),
    };
    return await this.usersService.updateUser({
      where: { user_id: +user_id },
      data: updateData,
    });
  }
}
