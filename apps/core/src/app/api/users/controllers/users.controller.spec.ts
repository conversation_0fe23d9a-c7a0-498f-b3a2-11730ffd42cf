import { Test, TestingModule } from '@nestjs/testing';
import { UsersController } from './users.controller';
import { UsersService } from '../services/users.service';
import {
  LibProfileService,
  UsersRepository,
} from '@core/libs';
import { ConfigService } from '@nestjs/config';
import { LibEmailService } from '@core_be/email';

describe('UsersController', () => {
  let controller: UsersController;
  const mockLibProfileService = {};
  const mockConfigService = {
    getOrThrow: jest.fn().mockImplementation((key) => {
      if (key === 'JWT_EXPIRATION') return 3600;
      if (key === 'JWT_SECRET') return 'secretKey';
    }),
  };
  const mockLibEmailService = {};
  const mockUsersService = {};
  const mockUsersRepositoryService = {};

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UsersController],
      providers: [
        { provide: UsersService, useValue: mockUsersService },
        { provide: LibProfileService, useValue: mockLibProfileService },
        { provide: UsersRepository, useValue: mockUsersRepositoryService },
        { provide: ConfigService, useValue: mockConfigService },
        { provide: LibEmailService, useValue: mockLibEmailService },
      ],
    }).compile();

    controller = module.get<UsersController>(UsersController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
