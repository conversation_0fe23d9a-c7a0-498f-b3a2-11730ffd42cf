import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import {
  LibProfileService,
  OnBoardingRepository,
  PrivacyPolicyRepository,
  TermsRepository,
  UsersDeviceRepository,
  UsersRepository,
  UsersRoleRepository,
  WaitListUserType,
  WhitelistRepository,
} from '@core/libs';
import { Prisma, PrismaService } from '@core/prisma-client';
import { CustomUserFindArgs } from '../dto';
import { WaitlistRepository } from 'libs/data-access/src/lib/waitlist/waitlist.repository';

@Injectable()
export class UsersService {
  constructor(
    private readonly usersRepository: UsersRepository,
    private readonly whitelistRepository: WhitelistRepository,
    private readonly usersRoleRepository: UsersRoleRepository,
    private readonly usersDeviceRepository: UsersDeviceRepository,
    private readonly onBoardingRepository: OnBoardingRepository,
    private readonly termsRepository: TermsRepository,
    private readonly privacyPolicyRepo: PrivacyPolicyRepository,
    private readonly profileService: LibProfileService,
    private readonly waitListRepository: WaitlistRepository,
    private readonly prisma: PrismaService
  ) {}

  async joinSubscriptionWaitList(userId: number) {
    const user = await this.usersRepository.findById(userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }
    let subscriptionWaitList = await this.waitListRepository.findByEmailAndType(
      user.email,
      WaitListUserType.SUBSCRIBER
    );
    if (!subscriptionWaitList) {
      const waitListData = {
        email: user.email,
        user_type: WaitListUserType.SUBSCRIBER,
        created_at: new Date(),
      };
      subscriptionWaitList = await this.waitListRepository.create(waitListData);
    }
    return subscriptionWaitList;
  }

  async updateLastSeen(userId: number, deviceId: string) {
    // const activeSession = await this.prisma.healing_sessions.findFirst({
    //   where: {
    //     status: SessionStatus.IN_QUEUE,
    //     sub_status: SessionSubStatusType.NOT_AVAILABLE,
    //     profiles: {
    //       users: {
    //         user_id: userId,
    //       },
    //     },
    //   },
    // });
    // // Update session status to AVAILABLE if NOT_AVAILABLE
    // if (activeSession) {
    //   await this.prisma.healing_sessions.update({
    //     where: { session_id: activeSession.session_id },
    //     data: {
    //       sub_status: SessionSubStatusType.AVAILABLE,
    //       sub_status_updated_at: new Date(),
    //       updated_at: new Date(),
    //     },
    //   });
    // }
    const userDevice = await this.prisma.user_devices.findFirst({
      where: {
        user_id: userId,
        device_id: deviceId,
      },
    });
    if (userDevice) {
      await this.prisma.user_devices.update({
        where: { user_device_id: userDevice.user_device_id! },
        data: {
          last_seen_at: new Date(),
          updated_at: new Date(),
        },
      });
    }
    return {};
  }

  async createUser(data: Prisma.usersCreateInput) {
    return this.usersRepository.create(data);
  }

  async updateUser(params: {
    where: Prisma.usersWhereUniqueInput;
    data: Prisma.usersUpdateInput;
  }) {
    const { where, data } = params;
    return this.usersRepository.update({ data, where });
  }

  async deleteUser(where: Prisma.usersWhereUniqueInput, data: any) {
    return this.usersRepository.delete(where, data);
  }

  async deleteAccount(user_id: number) {
    const profile = await this.profileService.getProfileByUserId(user_id);
    return this.prisma.$transaction(async (tx) => {
      const uuid = crypto.randomUUID();
      const timeStamp = new Date();
      await tx.profiles.update({
        where: { profile_id: profile.profile_id },
        data: {
          first_name: `deleted_${uuid}`,
          last_name: `deleted_${uuid}`,
          gender: `deleted_${uuid}`,
          deleted_by: user_id,
          updated_at: timeStamp,
          deleted_at: timeStamp,
          is_deleted: true,
          is_active: false,
        },
      });
      return tx.users.update({
        where: { user_id },
        data: {
          username: `deleted_${uuid}`,
          email: `deleted_${uuid}`,
          password_hash: `deleted_${uuid}`,
          password_reset_token: `deleted_${uuid}`,
          year_of_birth: null,
          updated_at: timeStamp,
          deleted_at: timeStamp,
          deleted_by: user_id,
          is_active: false,
          is_deleted: true,
        },
      });
    });
  }

  async user(where: Prisma.usersWhereUniqueInput) {
    return this.usersRepository.findOne(where);
  }

  async users(params: CustomUserFindArgs) {
    const { offset, limit, cursor, where, orderBy, include } = params;

    return await this.usersRepository.findAll({
      skip: offset,
      limit,
      cursor,
      where,
      orderBy,
      include,
    });
  }

  async userCount(params: { where?: Prisma.usersWhereInput }) {
    const { where } = params;
    return this.usersRepository.count({ where });
  }

  async getLastUser() {
    // Assuming your User entity has a createdAt field to order by
    return this.usersRepository.findLast();
  }

  async findUserByUsername(username: string) {
    const data = this.usersRepository.findOne({
      username,
      is_deleted: false,
    });
    return data;
  }

  // async createPatient(data: Prisma.patientCreateInput) {
  //   return this.usersRepository.patient.create({ data });
  // }

  async getUserProfiles(where: any) {
    return this.usersRepository.findOne(where, {
      profiles: true,
    });
  }

  // async userProfilesByUserId(params: {
  //   userId: Prisma.IntFilter<'UserProfile'>;
  // }) {
  //   const { userId } = params;
  //   // return this.usersRepository.patient.findMany({
  //   //   //where: { user_id: userId },
  //   // });
  // }

  async createUserRole(data: Prisma.user_rolesCreateInput) {
    return await this.usersRoleRepository.createUserRole(data);
  }

  async updateUserRole(params: {
    where: Prisma.user_rolesWhereUniqueInput;
    data: Prisma.user_rolesUpdateInput;
  }) {
    const { where, data } = params;
    return await this.usersRoleRepository.updateUserRole({ data, where });
  }

  async deleteUserRole(where: Prisma.user_rolesWhereUniqueInput) {
    return await this.usersRoleRepository.deleteUserRole(where);
  }

  async userRole(where: Prisma.user_rolesWhereUniqueInput) {
    return await this.usersRoleRepository.userRole(where);
  }

  async userRoleOne(where: any) {
    return await this.usersRepository.findOne(where, {
      user_roles: {
        include: {
          roles: true,
        },
      },
    });
  }

  async userRoles(params: {
    skip?: number;
    take?: number;
    cursor?: Prisma.user_rolesWhereUniqueInput;
    where?: Prisma.user_rolesWhereInput;
    orderBy?: Prisma.user_rolesOrderByWithAggregationInput;
  }) {
    const { skip, take, cursor, where, orderBy } = params;
    return await this.usersRoleRepository.userRoles({
      skip,
      take,
      cursor,
      where,
      orderBy,
    });
  }

  async isEmailWhitelisted(email: string): Promise<boolean> {
    return await this.whitelistRepository.findOne(email);
  }

  async createUserDevice(data: Prisma.user_devicesCreateInput) {
    return this.usersDeviceRepository.createUserDevice(data);
  }

  async updateUserDevice(params: {
    where: Prisma.user_devicesWhereUniqueInput;
    data: Prisma.user_devicesUpdateInput;
  }) {
    const { where, data } = params;
    return this.usersDeviceRepository.updateUserDevice({ data, where });
  }

  async getDefaultUserDevice(where: Prisma.user_devicesWhereInput) {
    return this.usersDeviceRepository.getDefaultUserDevice(where);
  }

  async createOnBoarding(data: Prisma.onboarding_statusCreateInput) {
    return this.onBoardingRepository.createOnBoarding(data);
  }

  async updateManyOnBoarding(params: {
    where: any;
    data: Prisma.onboarding_statusUpdateInput;
  }) {
    const { where, data } = params;
    return this.onBoardingRepository.updateManyOnBoarding({
      where,
      data,
    });
  }

  async getDefaultUserOnBoarding(where: Prisma.onboarding_statusWhereInput) {
    return this.onBoardingRepository.getDefaultUserOnBoarding(where);
  }

  async getUserById(user_id: number) {
    try {
      return await this.usersRepository.findById(user_id);
    } catch (error) {
      throw new BadRequestException(error || 'Please try again');
    }
  }

  async getUserDeviceInfo(user_id: number) {
    try {
      return await this.usersDeviceRepository.getUserDeviceInfo(user_id);
    } catch (error) {
      throw new BadRequestException(error || 'Please try again');
    }
  }

  async saveUserTerms(
    data: Prisma.user_terms_agreementCreateInput,
    prisma?: Prisma.TransactionClient
  ) {
    return this.termsRepository.saveUserTerms(data, prisma);
  }

  async getByUserTerms(where: Prisma.user_terms_agreementWhereInput) {
    return this.termsRepository.getByUserTerms(where);
  }

  async getUserTermsStatus(user_id: number) {
    return this.getByUserTerms({
      user_id: user_id,
      is_terms_accepted: true,
    });
  }

  async getUserTerms(user_id: number) {
    const userTerms = await this.termsRepository.getUserTerms(user_id);

    if (!userTerms) {
      throw new NotFoundException('User Terms not found');
    }

    return userTerms;
  }

  async createUserRolesWithUserId(user_id: number) {
    try {
      await this.usersRoleRepository.createUserRolesWithUserId(user_id);
    } catch (error) {
      throw new BadRequestException(error || 'Please try again');
    }
  }

  async getUserPrivacyPolicy(user_id: number) {
    return this.privacyPolicyRepo.getUserPrivacyPolicy(user_id);
  }

  async saveUserPrivacyPolicy(
    data: Prisma.user_privacy_policyCreateInput,
    prisma?: Prisma.TransactionClient
  ) {
    return this.privacyPolicyRepo.createPrivacyPolicy(data, prisma);
  }
}
