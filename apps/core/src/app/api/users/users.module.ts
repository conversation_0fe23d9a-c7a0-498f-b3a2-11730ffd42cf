import { Module, NestModule, MiddlewareConsumer } from '@nestjs/common';
import { UsersController } from './controllers/users.controller';
import { TokenMiddleware } from '@core_be/auth';
import { ConfigService } from '@nestjs/config';
import { LibEmailModule } from '@core_be/email';
import { FastifyMulterModule } from '@nest-lab/fastify-multer';
import { UsersService } from './services/users.service';
import { WaitlistRepository } from 'libs/data-access/src/lib/waitlist/waitlist.repository';
import { Logger } from 'nestjs-pino';

@Module({
  imports: [LibEmailModule, FastifyMulterModule],
  controllers: [UsersController],
  providers: [ConfigService, UsersService, WaitlistRepository, Logger],
})
export class UsersModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(TokenMiddleware).forRoutes(UsersController);
  }
}
