import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsInt, IsOptional } from '@nestjs/class-validator';

export class CreateUserPrivacyPolicyDto {
  @ApiProperty({
    example: 1,
    description: 'User ID',
    required: true,
  })
  @IsNotEmpty()
  @IsInt()
  user_id!: number;

  @ApiProperty({
    example: 1,
    description: 'Privacy Policy ID',
    required: true,
  })
  @IsOptional()
  @IsInt()
  privacy_policy_id?: number;
}
