import { Role } from '@core/libs';
import { Prisma } from '@core/prisma-client';
import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsInt,
  IsDateString,
} from 'class-validator';

export class UserIdParams {
  @IsNotEmpty()
  id: string;
}

export class UserPingParams {
  @IsNotEmpty()
  device_id: string;
}

export class GetTermsUserParams {
  @IsNotEmpty()
  user_id: number;
}

export interface CustomUserFindArgs {
  offset?: number;
  limit?: number;
  cursor?: Prisma.usersWhereUniqueInput;
  where?: Prisma.usersWhereInput;
  orderBy?: Prisma.usersOrderByWithAggregationInput;
  include?: Prisma.usersInclude;
}

export class UserQueryDto {
  @ApiPropertyOptional({
    example: 'user_id',
    required: false,
  })
  @IsOptional()
  @IsString()
  orderBy?: string;

  @ApiPropertyOptional({
    description: 'Order direction',
    enum: ['asc', 'desc'],
    example: 'asc',
    required: false,
  })
  @IsOptional()
  @IsEnum(['asc', 'desc'])
  order: 'asc' | 'desc' = 'asc';

  @ApiPropertyOptional({
    example: 10,
    required: false,
  })
  @IsOptional()
  @IsString()
  limit: string;

  @ApiPropertyOptional({
    example: 0,
    required: false,
  })
  @IsOptional()
  @IsString()
  offset: string;

  @ApiPropertyOptional({
    example: new Date(),
    required: false,
  })
  @IsOptional()
  @IsDateString()
  lastLoginStartDate?: string;

  @ApiPropertyOptional({
    example: new Date(),
    required: false,
  })
  @IsOptional()
  @IsDateString()
  lastLoginEndDate?: string;

  @ApiPropertyOptional({
    example: '',
    required: false,
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({
    example: '',
    required: false,
  })
  @IsOptional()
  @IsString()
  firstname?: string;

  @ApiPropertyOptional({
    example: '',
    required: false,
  })
  @IsOptional()
  @IsString()
  lastname?: string;

  @ApiPropertyOptional({
    enum: ['active', 'inactive'],
    example: 'active',
    required: false,
  })
  @IsOptional()
  @IsEnum(['active', 'inactive'])
  status: 'active' | 'inactive' = 'active';

  @ApiPropertyOptional({
    enum: Role,
    example: Role.Admin,
    required: false,
  })
  @IsOptional()
  @IsEnum(Role)
  @IsString()
  profileType?: string;
}
