import { <PERSON><PERSON>tatus, RelationToUser } from '@core/libs';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsOptional,
  IsEmail,
  IsNotEmpty,
  IsString,
  MinLength,
  IsInt,
  IsEnum,
} from 'class-validator';

export class CreateUserDto {
  @ApiProperty({ required: true })
  @IsNotEmpty()
  profile_type?: string;

  @ApiProperty({ required: true })
  @IsEmail()
  @IsNotEmpty()
  email?: string;

  @ApiProperty()
  @IsNotEmpty()
  @MinLength(8)
  password!: string;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  first_name?: string;

  @ApiProperty()
  @IsOptional()
  last_name?: string;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  gender?: string;

  @ApiPropertyOptional({
    example: RelationToUser.Self,
    description: 'Relation To User',
    default: RelationToUser.Self,
  })
  @IsString()
  @IsOptional()
  relation_to_user?: string;

  @ApiPropertyOptional({
    example: null,
    description: 'DOB',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  date_of_birth?: Date;

  @ApiPropertyOptional({
    example: null,
    description: 'Picture Name',
  })
  @IsString()
  @IsOptional()
  profile_picture_file_name?: string;

  @ApiPropertyOptional({
    example: null,
    description: 'phoneNumber',
  })
  @IsString()
  @IsOptional()
  phone_number?: string;

  @ApiPropertyOptional({
    example: null,
    description: 'country',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  country?: string;

  @ApiPropertyOptional({
    example: null,
    description: 'state',
  })
  @IsString()
  @IsOptional()
  state?: string;

  @ApiPropertyOptional({
    example: null,
    description: 'city',
  })
  @IsString()
  @IsOptional()
  city?: string;

  @ApiPropertyOptional({
    example: null,
    description: 'Address',
  })
  @IsString()
  @IsOptional()
  address?: string;

  @ApiPropertyOptional({
    example: null,
    description: 'zip_code',
  })
  @IsString()
  @IsOptional()
  zip_code?: string;

  @ApiProperty()
  @IsOptional()
  last_login_date?: Date;

  @ApiProperty()
  @IsOptional()
  password_reset_token?: string;

  @ApiProperty()
  @IsOptional()
  password_reset_token_expiry?: Date;
}

export class LoginRequestDto {
  @IsNotEmpty()
  @ApiProperty()
  username!: string;

  @IsNotEmpty()
  @ApiProperty()
  password!: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  device_id?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  device_type?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  fcm_token?: string;
}

export class Profiles {
  profile_id: number;
  profile_type: string;
  user_id: number;
  first_name: string;
  last_name: string;
  relation_to_user: string;
  gender: string;
  date_of_birth: Date;
  profile_picture_file_name: string;
  phone_number: string;
  country: string;
  state: string;
  city: string;
  address: string;
  zip_code: string;
  is_default: boolean;
  created_by: number;
  created_at: Date;
  updated_by: number;
  updated_at: Date;
  deleted_by: number;
  deleted_at: Date;
  is_active: boolean;
  is_deleted: boolean;
}

export class LoginResponseDto {
  user_id: number;
  email: string;
  access_token: string;
  onboarding_status: string;
  is_terms_accepted: boolean;
  is_privacy_policy_accepted: boolean;
  is_plan_selected: boolean;
  profiles: Profiles[];
}

export class RegisterResponseDto extends LoginResponseDto {
  is_wait_listed: boolean;
  is_white_listed: boolean;
}

export class ResetUserDto {
  @ApiProperty({
    example: '<EMAIL>',
    description: 'User email address',
    required: true,
  })
  @IsEmail()
  @IsNotEmpty()
  email!: string;

  @ApiProperty({
    example: 'http://localhost:3000/users/reset-password',
    description: 'Reset Password Link',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  action_url!: string;
}

export class UpdateUserPasswordDto {
  @ApiProperty({
    example: 'verySecurePassword123',
    description: 'Password',
    required: true,
    minLength: 8,
  })
  @IsString()
  @MinLength(8)
  password!: string;

  @ApiProperty({
    example: 'verySecurePassword123',
    description: 'Password',
    required: true,
    minLength: 8,
  })
  @IsString()
  @MinLength(8)
  confirmpassword!: string;
}

export class CreateUserRoleDto {
  @ApiProperty({
    example: 1,
    description: 'user_id',
    required: true,
  })
  @IsInt()
  @IsNotEmpty()
  user_id!: number;

  @ApiProperty({
    example: 1,
    description: 'Role',
    required: true,
  })
  @IsNotEmpty()
  @IsInt()
  role_id!: number;
}
export class BulkUpload {
  @ApiProperty({
    type: 'string',
    format: 'binary',
    description: 'whitelist Bulk Upload',
    required: true,
  })
  file!: any;
}

export class ForgotPasswordDtoDto {
  @ApiProperty({
    example: '<EMAIL>',
    description: 'User email address',
    required: true,
  })
  @IsEmail()
  @IsNotEmpty()
  email?: string;
}

export class RefreshTokenDtoDto {
  @ApiProperty({
    example:
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************.AfBqLQSFm6LefvrerjkkRH0imJTTiWOZS0-KZyWZ7OQ',
    description: 'token',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  token?: string;
}

export class ResetPasswordDTO {
  @ApiProperty({
    example: '<EMAIL>',
    description: 'email',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  email: string;

  @ApiProperty({
    example: 'verySecurePassword123',
    description: 'Password',
    required: true,
    minLength: 8,
  })
  @IsString()
  @MinLength(8)
  @IsNotEmpty()
  password: string;

  @ApiProperty({
    example: 'token12345t',
    description: 'token',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  token: string;
}

export class ChangePasswordDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @MinLength(8)
  old_password: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  @MinLength(8)
  new_password: string;
}

export class UpdateUserStatusDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  user_id: string;

  @ApiProperty({
    enum: ProfileStatus,
    example: ProfileStatus.ACTIVE,
    description: 'active/inactive',
    required: true,
  })
  @IsEnum(ProfileStatus, { message: 'status must be a valid value' })
  @IsNotEmpty()
  @IsString()
  status: ProfileStatus;
}
