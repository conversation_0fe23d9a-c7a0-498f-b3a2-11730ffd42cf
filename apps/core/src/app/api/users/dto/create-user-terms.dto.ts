import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsInt, IsOptional } from '@nestjs/class-validator';

export class SaveUserTermsDto {
  @ApiProperty({
    example: 1,
    description: 'User ID',
    required: true,
  })
  @IsNotEmpty()
  @IsInt()
  user_id!: number;

  @ApiProperty({
    example: 1,
    description: 'Terms ID',
    required: true,
  })
  @IsOptional()
  @IsInt()
  terms_id?: number;
}
