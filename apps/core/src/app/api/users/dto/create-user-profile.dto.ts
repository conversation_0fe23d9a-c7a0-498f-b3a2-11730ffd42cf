import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsOptional,
  IsPhoneNumber,
  IsString,
} from '@nestjs/class-validator';

export class CreateUserProfileDto {
  @ApiProperty({
    example: 'John',
    description: 'First name',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  firstName: string;

  @ApiProperty({
    example: 'Doe',
    description: 'Last name',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  lastName: string;

  @ApiPropertyOptional({
    example: 'Loves coding and biking',
    description: 'Short biography',
  })
  @IsString()
  @IsOptional()
  bio?: string;

  @ApiPropertyOptional({
    example: '+1234567890',
    description: 'Phone number',
  })
  @IsPhoneNumber(null)
  @IsOptional()
  phoneNumber?: string;

  @ApiPropertyOptional({
    example: 'US',
    description: 'Country code',
  })
  @IsString()
  @IsOptional()
  countryCode?: string;
}
