import { ApiProperty } from '@nestjs/swagger';
import { IsObject, IsOptional, IsString } from 'class-validator';

export class CreateUserOnBoardingDto {
  @ApiProperty({
    example: 'Fcm-Token',
  })
  @IsString()
  @IsOptional()
  device_id?: string;

  @ApiProperty({
    example: 'Andriod',
  })
  @IsString()
  @IsOptional()
  device_type?: string;

  @ApiProperty({
    example: 'Oppo',
  })
  @IsString()
  @IsOptional()
  device_model?: string;

  @ApiProperty({
    example: 'jelly Bean',
  })
  @IsString()
  @IsOptional()
  os_name?: string;

  @ApiProperty({
    example: '14',
  })
  @IsString()
  @IsOptional()
  os_version?: string;

  @ApiProperty({
    example: 'App-version',
  })
  @IsString()
  @IsOptional()
  app_version?: string;

  @ApiProperty({
    example: 'App-version',
  })
  @ApiProperty({
    example: 'Android',
  })
  @IsString()
  @IsOptional()
  platform?: string;

  @ApiProperty({
    example: { latitude: 40.712776, longitude: -74.005974 },
    description: 'Coordinates ',
    required: true,
  })
  @IsObject()
  @IsOptional()
  location?: string;
}
