import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsInt, IsNotEmpty, IsString } from '@nestjs/class-validator';

export class CreateUserdeviceDto {
  @ApiProperty({
    example: 1,
    description: 'UserID',
    required: true,
  })
  @IsInt()
  @IsNotEmpty()
  user_id: number;

  @ApiProperty({
    example: 'f6cd7e22955d4d9c',
    description: 'DeviceID',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  device_id?: string;

  @ApiProperty({
    example: 'Android',
    description: 'Device Type',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  device_type?: string;

  @ApiProperty({
    example:
      'f4-v18AzRdqLoaKRWZC6xs:APA91bEfIC9dN-_JLJGVzENoUbmGy-DvHhpyi-YIDBv7TRZlyel3WKoQsY2MQNA8SNqiRnEQ_T8CGvVHOP2ouaA5TVb2lmbZoNoEFEsE1tvr69TAxkxEHkCGCtomAvWCaKEx3fpJzQc4',
    description: 'FCM Token',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  fcm_token?: string;

  @ApiProperty({
    example: 'Oppo',
    description: 'Device Model',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  device_model?: string;

  @ApiProperty({
    example: 'Kitkat',
    description: 'Os Name',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  os_name?: string;

  @ApiProperty({
    example: '14',
    description: 'Os Version',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  os_version?: string;

  @ApiProperty({
    example: 'lolly pop',
    description: 'App Version',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  app_version?: string;
}
