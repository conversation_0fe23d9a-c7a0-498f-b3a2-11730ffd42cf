import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsString } from '@nestjs/class-validator';
import { IsEnum, IsOptional } from 'class-validator';
import { Prisma } from '@core/prisma-client';

export class CreateNotificationsTopicsDto {
  @ApiProperty({
    example: 'name',
    description: 'name',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    example: 'description',
    description: 'description',
    required: false,
  })
  @IsOptional()
  @IsString()
  description: string;
}

export class UpdateNotificationsTopicsDto {
  @ApiProperty({
    example: 'Updated description',
    description: 'The updated description of the notification topic.',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;
}

export class NotificationsTopicsResponseDTO {
  notification_topic_id: number;
  name: string;
  description: string | null;
  created_by: number | null;
  created_at: Date;
  updated_by: number | null;
  updated_at: Date;
  deleted_by: number | null;
  deleted_at: Date;
  is_deleted: boolean;
}

export class NotificationsTopicsQueryDto {
  @ApiPropertyOptional({
    example: 'name',
    required: false,
  })
  @IsOptional()
  @IsString()
  orderBy?: string;

  @ApiPropertyOptional({
    description: 'Order direction',
    enum: ['asc', 'desc'],
    example: 'asc',
    required: false,
  })
  @IsOptional()
  @IsEnum(['asc', 'desc'])
  order: 'asc' | 'desc' = 'asc';

  @ApiPropertyOptional({
    example: 10,
    required: false,
  })
  @IsOptional()
  @IsString()
  limit: string;

  @ApiPropertyOptional({
    example: 0,
    required: false,
  })
  @IsOptional()
  @IsString()
  offset: string;

  @ApiPropertyOptional({
    example: '',
    required: false,
  })
  @IsOptional()
  @IsString()
  search?: string;
}

export interface NotificationsTopicsFindArgs {
  offset?: number;
  limit?: number;
  cursor?: Prisma.notification_topicsWhereUniqueInput;
  where?: Prisma.notification_topicsWhereInput;
  orderBy?: Prisma.notification_topicsOrderByWithAggregationInput;
  include?: Prisma.notification_topicsInclude;
}