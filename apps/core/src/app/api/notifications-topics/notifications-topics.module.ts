import { Module, NestModule, MiddlewareConsumer } from '@nestjs/common';
import { NotificationsTopicsController } from './controllers/notifications-topics.controller';
import { TokenMiddleware } from '@core_be/auth';
import { ConfigService } from '@nestjs/config';
import { LibFcmService } from '@core_be/notifications';
import { NotificationsTopicsService } from './services/notifications-topics.service';

@Module({
  controllers: [NotificationsTopicsController],
  providers: [NotificationsTopicsService, ConfigService, LibFcmService],
})
export class NotificationsTopicsModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(TokenMiddleware).forRoutes(NotificationsTopicsController);
  }
}
