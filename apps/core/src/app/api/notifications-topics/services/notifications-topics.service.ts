import { Injectable } from '@nestjs/common';
import {
  CreateNotificationsTopicsDto,
  NotificationsTopicsFindArgs,
  NotificationsTopicsResponseDTO,
  UpdateNotificationsTopicsDto,
} from '../dto/notifications-topics.dto';
import { NotificationsTopicsRepository } from '@core/libs';
import { Prisma } from '@core/prisma-client';

@Injectable()
export class NotificationsTopicsService {
  constructor(
    private readonly notificationsTopicsRepository: NotificationsTopicsRepository
  ) {}

  async findAll(params: NotificationsTopicsFindArgs): Promise<NotificationsTopicsResponseDTO[]> {
    const { offset, limit, cursor, where, orderBy, include } = params;
    return this.notificationsTopicsRepository.findAll({
        skip: offset,
        limit,
        cursor,
        where,
        orderBy,
        include,
    });
  }

  async findOne(where: Prisma.notification_topicsWhereUniqueInput): Promise<NotificationsTopicsResponseDTO> {
    return this.notificationsTopicsRepository.findOne(where);
  }

  async create(
    createNotificationsTopicsDto: CreateNotificationsTopicsDto
  ): Promise<NotificationsTopicsResponseDTO> {
    return this.notificationsTopicsRepository.create(createNotificationsTopicsDto);
  }

  async update(
    id:number,
    updateNotificationsTopicsDto: UpdateNotificationsTopicsDto
  ): Promise<NotificationsTopicsResponseDTO> {
    return this.notificationsTopicsRepository.update(
     { notification_topic_id: +id },
      updateNotificationsTopicsDto
    );
  }

  async deleteNotificationTopics(id:number,data:any): Promise<NotificationsTopicsResponseDTO> {
    return this.notificationsTopicsRepository.delete(
        { notification_topic_id: +id },
        data
    );
  }

  async notificationsTopicsCount(params: { where?: Prisma.notification_topicsWhereInput }) {
    const { where } = params;
    return this.notificationsTopicsRepository.count({ where });
  }
}
