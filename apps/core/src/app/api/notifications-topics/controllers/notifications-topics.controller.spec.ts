import { Test, TestingModule } from '@nestjs/testing';
import { NotificationsTopicsController } from './notifications-topics.controller';
import { NotificationsTopicsService } from '../services/notifications-topics.service';
import {
  CreateNotificationsTopicsDto,
  NotificationsTopicsQueryDto,
  NotificationsTopicsResponseDTO,
  UpdateNotificationsTopicsDto,
} from '../dto/notifications-topics.dto';
import { faker } from '@faker-js/faker';

describe('NotificationsTopicsController', () => {
  let controller: NotificationsTopicsController;
  let notificationsTopicsService: NotificationsTopicsService;

  const mockNotificationsTopicsService = {
    findAll: jest.fn(),
    findOne: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    deleteNotificationTopics: jest.fn(),
    notificationsTopicsCount: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [NotificationsTopicsController],
      providers: [
        { provide: NotificationsTopicsService, useValue: mockNotificationsTopicsService },
      ],
    }).compile();

    controller = module.get<NotificationsTopicsController>(NotificationsTopicsController);
    notificationsTopicsService =
      module.get<NotificationsTopicsService>(NotificationsTopicsService);
  });

  describe('findAll', () => {
    it('should return an array of notification topics', async () => {
      const mockQuery: NotificationsTopicsQueryDto = {
        orderBy: 'name',
        order: 'asc',
        limit: '10',
        offset: '0',
        search: '',
    };
      const mockTopics: NotificationsTopicsResponseDTO[] = [
        {
          notification_topic_id: 1,
          name: faker.lorem.words(3),
          description: faker.lorem.sentence(),
          created_by: null,
          created_at: new Date(),
          updated_by: null,
          updated_at: new Date(),
          deleted_by: null,
          deleted_at: new Date(),
          is_deleted: false,
        },
      ];

      jest.spyOn(notificationsTopicsService, 'findAll').mockResolvedValue(mockTopics);
      jest.spyOn(mockNotificationsTopicsService, 'notificationsTopicsCount').mockResolvedValue(1);

      const result = await controller.findAll(mockQuery);
      expect(result.notificationsTopics).toEqual(mockTopics);
      expect(result.pagination).toEqual({
          total_pages: 1,
          total_items: 1,
          offset: '0',
          limit: '10',
      });
      expect(mockNotificationsTopicsService.findAll).toHaveBeenCalled();
      expect(mockNotificationsTopicsService.notificationsTopicsCount).toHaveBeenCalled();
    });
  });

  describe('findOne', () => {
    it('should return a single notification topic', async () => {
      const mockTopic: NotificationsTopicsResponseDTO = {
        notification_topic_id: 1,
        name: faker.lorem.words(3),
        description: faker.lorem.sentence(),
        created_by: null,
        created_at: new Date(),
        updated_by: null,
        updated_at: new Date(),
        deleted_by: null,
        deleted_at: new Date(),
        is_deleted: false,
      };

      jest.spyOn(notificationsTopicsService, 'findOne').mockResolvedValue(mockTopic);

      const result = await controller.findOne('1');
      expect(result).toEqual(mockTopic);
      expect(notificationsTopicsService.findOne).toHaveBeenCalledWith({ notification_topic_id: 1, is_deleted: false });
    });
  });

  describe('create', () => {
    it('should create and return a new notification topic', async () => {
        const mockDto: CreateNotificationsTopicsDto = {
            name: faker.lorem.words(3),
            description: faker.lorem.sentence(),
        };

        const mockResponse: NotificationsTopicsResponseDTO = {
            notification_topic_id: 1,
            name: mockDto.name,
            description: mockDto.description,
            created_by: null,
            created_at: new Date(),
            updated_by: null,
            updated_at: new Date(),
            deleted_by: null,
            deleted_at: new Date(),
            is_deleted: false,
        };

        jest.spyOn(notificationsTopicsService, 'findOne').mockResolvedValue(null);

        jest.spyOn(notificationsTopicsService, 'create').mockResolvedValue(mockResponse);

        const result = await controller.create(mockDto, { raw: { user: { user_id: 1 } } });

        expect(result).toEqual(mockResponse);
        expect(notificationsTopicsService.findOne).toHaveBeenCalledWith({
            name: mockDto.name,
            is_deleted: false,
        });
        expect(notificationsTopicsService.create).toHaveBeenCalledWith({
            ...mockDto,
            created_by: 1,
        });
    });
  });

  describe('update', () => {
    it('should update and return the updated notification topic', async () => {
      const mockDto: UpdateNotificationsTopicsDto = {
        description: faker.lorem.sentence() ,
      };

      const mockResponse: NotificationsTopicsResponseDTO = {
        notification_topic_id: 1,
        name: "Updated Policy",
        description: mockDto.description ?? null,
        created_by: null,
        created_at: new Date(),
        updated_by: null,
        updated_at: new Date(),
        deleted_by: null,
        deleted_at: new Date(),
        is_deleted: false,
      };

      jest
        .spyOn(notificationsTopicsService, 'update')
        .mockResolvedValue(mockResponse);

      const result = await controller.update(1, mockDto, { raw: { user: { user_id: 1 } } });
      expect(result).toEqual(mockResponse);
      expect(notificationsTopicsService.update).toHaveBeenCalledWith(1, {
        ...mockDto,
        updated_by: 1,
        updated_at: expect.any(Date),
      });
    });
  });

  describe('delete', () => {
    it('should delete a notification topic and return it', async () => {
      const mockResponse: NotificationsTopicsResponseDTO = {
        notification_topic_id: 1,
        name: faker.lorem.words(3),
        description: faker.lorem.sentence(),
        created_by: null,
        created_at: new Date(),
        updated_by: null,
        updated_at: new Date(),
        deleted_by: null,
        deleted_at: new Date(),
        is_deleted: false,
      };

      jest
        .spyOn(notificationsTopicsService, 'deleteNotificationTopics')
        .mockResolvedValue(mockResponse);

      const result = await controller.delete(1, { raw: { user: { user_id: 1 } } });
      expect(result).toEqual(mockResponse);
      expect(notificationsTopicsService.deleteNotificationTopics).toHaveBeenCalledWith(1, {
        is_deleted: true,
        deleted_by: 1,
        deleted_at: expect.any(Date),
      });
    });
  });
});
