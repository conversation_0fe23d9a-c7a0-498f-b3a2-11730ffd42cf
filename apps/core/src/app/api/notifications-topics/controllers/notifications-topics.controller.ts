import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  Put,
  Request,
  Query,
  ConflictException
} from '@nestjs/common';
import { NotificationsTopicsService } from '../services/notifications-topics.service';
import { ApiTags, ApiBearerAuth } from '@nestjs/swagger';
import {
  CreateNotificationsTopicsDto,
  UpdateNotificationsTopicsDto,
  NotificationsTopicsResponseDTO,
  NotificationsTopicsQueryDto,
  NotificationsTopicsFindArgs,
} from '../dto/notifications-topics.dto';
import { Role, Roles, RolesGuard } from '@core/libs';
import { Prisma } from '@core/prisma-client';

@ApiTags('Notifications Topics')
@ApiBearerAuth()
@Controller('notifications-topics')
@UseGuards(RolesGuard)
export class NotificationsTopicsController {
  constructor(private readonly notificationsTopicsService: NotificationsTopicsService) {}

  @Get()
  @Roles(Role.Admin)
  async findAll(@Query() query: NotificationsTopicsQueryDto) {
    const { orderBy, order, limit, offset,search} = query;
    const limitNumber = Number(limit) || 10;
    const offsetNumber = Number(offset) * limitNumber;
    const orderByCondition: any = {};
    if(orderBy) {
      orderByCondition[orderBy] = order;
    } 
    const queryPayload: NotificationsTopicsFindArgs = {
      orderBy: orderByCondition ?  orderByCondition : undefined,
      where: (() => {
        const where: Prisma.notification_topicsWhereInput = {
          is_deleted: false,
        };
        if (search) {
          where.OR = [
            { name: { contains: search, mode: 'insensitive' } },
            { description: { contains: search, mode: 'insensitive' } },
          ];
        }
        return where;
      })(),
      include: {
        user_notification_preferences: true,
        notification:true
      },
    };

    if (parseInt(limit)) {
      queryPayload['limit'] = limitNumber;
      queryPayload['offset'] = offsetNumber;
    }

    const totalItems = await this.notificationsTopicsService.notificationsTopicsCount(queryPayload);

    const totalPages = Math.ceil(totalItems / limitNumber);

    const notificationsTopics =  await this.notificationsTopicsService.findAll(queryPayload);

    return {
      notificationsTopics,
      pagination: {
        total_pages: totalPages,
        total_items: totalItems,
        offset,
        limit,
      },
    };

  }

  @Get(':id')
  @Roles(Role.Admin)
  async findOne(@Param('id') id: string): Promise<NotificationsTopicsResponseDTO> {
    const existingNotificationTopics = await this.notificationsTopicsService.findOne({
      notification_topic_id: +id,
      is_deleted:false
    });

    if (!existingNotificationTopics) {
      throw new ConflictException('Notifications Topics Id Not Found');
    }
    return this.notificationsTopicsService.findOne({ notification_topic_id: +id });
  }

  @Post()
  @Roles(Role.Admin)
  async create(@Body() createNotificationsTopicsDto: CreateNotificationsTopicsDto, @Request() req ): Promise<NotificationsTopicsResponseDTO> {
    const existingNotificationTopics = await this.notificationsTopicsService.findOne({
      name: createNotificationsTopicsDto.name,
      is_deleted:false
    });

    if (existingNotificationTopics) {
      throw new ConflictException('Notifications Topics already exists');
    }
    const createData = {
      ...createNotificationsTopicsDto,
      created_by: req.raw.user.user_id,
    };
    return this.notificationsTopicsService.create(createData);
  }

  @Put(':id')
  @Roles(Role.Admin)
  async update(
    @Param('id') id: number,
    @Body() updateNotificationsTopicsDto: UpdateNotificationsTopicsDto,
    @Request() req 
  ): Promise<NotificationsTopicsResponseDTO> {
    const updateData = {
      ...updateNotificationsTopicsDto,
      updated_by: req.raw.user.user_id,
      updated_at:new Date()
    };
    return this.notificationsTopicsService.update(id,updateData);
  }

  @Delete(':id')
  @Roles(Role.Admin)
  async delete(@Param('id') id: number, @Request() req ): Promise<NotificationsTopicsResponseDTO> {
    const deleteData = {
      is_deleted: true,
      deleted_by: req.raw.user.user_id,
      deleted_at: new Date(),
    };
    return this.notificationsTopicsService.deleteNotificationTopics(id,deleteData);
  }
}
