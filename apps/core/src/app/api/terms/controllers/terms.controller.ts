import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
  NotFoundException,
} from '@nestjs/common';
import { TermsService } from '../services/terms.service';
import { CreateTermsDto, UpdateTermsDto, GetTermsIdParams } from '../dto';
import { ApiTags, ApiBearerAuth, ApiParam } from '@nestjs/swagger';
import { AuthGuard } from '@nestjs/passport';
import { Role, Roles } from '@core/libs';

@ApiTags('Terms Condition')
@ApiBearerAuth()
@Controller('terms')
@UseGuards(AuthGuard('jwt'))
export class TermsController {
  constructor(private readonly termsService: TermsService) {}

  @Post()
  async create(@Body() createTermsDto: CreateTermsDto, @Request() req) {
    const createData = {
      ...createTermsDto,
      created_by: req.raw.user.user_id,
    };

    return await this.termsService.create(createData);
  }

  @Get()
  async findAll() {
    return await this.termsService.terms({
      where: { is_deleted: false },
    });
  }

  @Get(':id')
  @ApiParam({
    name: 'id',
    type: Number,
    required: true,
    example: 1,
  })
  async findOne(@Param() params: GetTermsIdParams) {
    const term = await this.termsService.term({ terms_id: +params.id });
    if (!term) {
      throw new NotFoundException('Terms not found.');
    }
    return term;
  }

  @Patch(':id')
  @ApiParam({
    name: 'id',
    type: Number,
    required: true,
    example: 1,
  })
  async update(
    @Param() params: GetTermsIdParams,
    @Body() updateTermsDto: UpdateTermsDto,
    @Request() req
  ) {
    const updateData = {
      ...updateTermsDto,
      updated_by: req.raw.user.user_id,
      updated_at: new Date(),
    };
    return await this.termsService.update({
      where: { terms_id: +params.id },
      data: updateData,
    });
  }

  @Delete(':id')
  @ApiParam({
    name: 'id',
    type: Number,
    required: true,
    example: 1,
  })
  @Roles(Role.Admin)
  async remove(@Param() params: GetTermsIdParams, @Request() req) {
    const deleteData = {
      is_deleted: true,
      deleted_by: req.raw.user.user_id,
      deleted_at: new Date(),
    };
    return await this.termsService.delete({ terms_id: +params.id }, deleteData);
  }
}
