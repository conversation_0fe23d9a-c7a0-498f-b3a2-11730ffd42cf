import { Test, TestingModule } from '@nestjs/testing';
import { TermsController } from './terms.controller';
import { TermsService } from '../services/terms.service';
import { faker } from '@faker-js/faker';
import { CreateTermsDto, GetTermsIdParams, UpdateTermsDto } from '../dto';
const user_id = '4';

const mockRequest: Request = {
  raw: {
    user: {
      user_id,
    },
  },
} as any;
// Create a mock for TermAndConditionsRepository
const TermsStub = () => {
  return {
    title: faker.string.alpha(8),
    content: faker.string.alpha(8),
  };
};

class MockTermsService {
  terms = jest.fn();
  term = jest.fn();
  create = jest.fn();
  update = jest.fn();
  delete = jest.fn();
}

describe('TermsController', () => {
  let controller: TermsController;
  let service: TermsService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [TermsController],
      providers: [
        TermsService,
        { provide: TermsService, useClass: MockTermsService },
      ],
    }).compile();

    controller = module.get<TermsController>(TermsController);
    service = module.get<TermsService>(TermsService);
  });

  it('should create terms', async () => {
    const dto: CreateTermsDto = {
      title: TermsStub().title,
      content: TermsStub().content,
    };

    const expectedCreateData = {
      ...dto,
      created_by: user_id,
    };

    const mockResponse = {
      terms_id: 1,
      title: 'Test Title',
      content: 'Test Content',
      is_active: true,
      effective_date: new Date(),
      created_by: parseInt(user_id, 10),
      created_at: new Date(),
      updated_by: null,
      updated_at: null,
      deleted_by: null,
      deleted_at: null,
      is_deleted: false,
    };

    jest.spyOn(service, 'create').mockResolvedValue(mockResponse);
    const terms = await controller.create(dto, mockRequest);
    expect(service.create).toHaveBeenCalledWith(expectedCreateData);
    expect(terms).toEqual(mockResponse);
  });

  it('should return a list of terms', async () => {
    const mockTerms = [
      {
        terms_id: 1,
        title: 'Term 1',
        content: 'Content for Term 1',
        is_active: true,
        effective_date: new Date('2024-01-01'),
        created_by: 1,
        created_at: new Date('2023-12-01'),
        updated_by: 1,
        updated_at: new Date('2023-12-02'),
        deleted_by: null,
        deleted_at: null,
        is_deleted: false,
      },
      {
        terms_id: 2,
        title: 'Term 2',
        content: 'Content for Term 2',
        is_active: true,
        effective_date: new Date('2024-02-01'),
        created_by: 2,
        created_at: new Date('2023-12-05'),
        updated_by: 2,
        updated_at: new Date('2023-12-06'),
        deleted_by: null,
        deleted_at: null,
        is_deleted: false,
      },
    ];

    jest.spyOn(service, 'terms').mockResolvedValue(mockTerms);
    const terms = await controller.findAll();
    expect(service.terms).toHaveBeenCalledWith({
      where: { is_deleted: false },
    });
    expect(terms).toEqual(mockTerms);
  });

  it('should return a single term', async () => {
    const id = 1;
    const params: GetTermsIdParams = { id: String(id) };
    const mockResponse = {
      terms_id: 1,
      title: 'Sample Title',
      content: 'Sample Content',
      is_active: true,
      effective_date: new Date(),
      created_by: 123,
      created_at: new Date(),
      updated_by: null,
      updated_at: null,
      deleted_by: null,
      deleted_at: null,
      is_deleted: false,
    };

    jest.spyOn(service, 'term').mockResolvedValue(mockResponse);
    const term = await controller.findOne(params);
    expect(service.term).toHaveBeenCalledWith({ terms_id: id });
    expect(term).toEqual(mockResponse);
  });

  it('should update a term', async () => {
    const id = 1;
    const params: GetTermsIdParams = { id: String(id) };
    const updateTermsDto: UpdateTermsDto = {
      title: 'Updated Title',
      content: 'Updated Content',
    };
    const mockResponse = {
      terms_id: id,
      title: updateTermsDto.title,
      content: updateTermsDto.content,
      is_active: true,
      effective_date: new Date('2024-01-01'),
      updated_by: parseInt(user_id, 10),
      updated_at: new Date(),
      created_by: 123,
      created_at: new Date(),
      deleted_by: null,
      deleted_at: null,
      is_deleted: false,
    };

    jest.spyOn(service, 'update').mockResolvedValue(mockResponse);
    const updatedTerm = await controller.update(
      params,
      updateTermsDto,
      mockRequest
    );
    expect(service.update).toHaveBeenCalledWith({
      where: { terms_id: id },
      data: {
        ...updateTermsDto,
        updated_by: user_id,
        updated_at: expect.any(Date),
      },
    });
    expect(updatedTerm).toEqual(mockResponse);
  });

  it('should remove a term', async () => {
    const id = 1;
    const params: GetTermsIdParams = { id: String(id) };
    const deleteData = {
      is_deleted: true,
      deleted_by: user_id,
      deleted_at: expect.any(Date),
    };
    const mockResponse = {
      terms_id: id,
      title: 'Example Title',
      content: 'Example Content',
      is_active: true,
      effective_date: new Date('2024-01-01'),
      created_by: parseInt(user_id, 10),
      created_at: new Date(),
      updated_by: parseInt(user_id, 10),
      is_deleted: true,
      deleted_by: parseInt(user_id, 10),
      deleted_at: new Date(),
      updated_at: new Date(),
    };

    jest.spyOn(service, 'delete').mockResolvedValue(mockResponse);
    const removedTerm = await controller.remove(params, mockRequest);
    expect(service.delete).toHaveBeenCalledWith({ terms_id: id }, deleteData);
    expect(removedTerm).toEqual(mockResponse);
  });
});
