import { Module, NestModule, MiddlewareConsumer } from '@nestjs/common';
import { TermsService } from './services/terms.service';
import { TermsController } from './controllers/terms.controller';
import { TokenMiddleware } from '@core_be/auth';
import { ConfigService } from '@nestjs/config';

@Module({
  controllers: [TermsController],
  providers: [TermsService, ConfigService],
})
export class TermsModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(TokenMiddleware).forRoutes(TermsController);
  }
}
