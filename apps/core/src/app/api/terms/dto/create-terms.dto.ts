import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from '@nestjs/class-validator';

export class CreateTermsDto {
  @ApiProperty({
    example: 'title',
    description: 'title',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  title: string;

  @ApiProperty({
    example: 'content',
    description: 'content',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  content: string;

  @ApiProperty({
    example: true,
    description: 'deleted flag set to true',
    required: false,
  })
  is_deleted?: boolean;

  @ApiProperty({
    example: 1,
    description: 'user_id',
    required: false,
  })
  deleted_by?: number;

  @ApiProperty({
    example: 'date',
    description: 'timestamp',
    required: false,
  })
  deleted_at?: Date;
}
