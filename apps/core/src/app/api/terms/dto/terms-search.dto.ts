import { PartialType } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsNumber,
  IsBoolean,
  IsDate,
} from 'class-validator';

export class GetTermsDto {
  @IsBoolean()
  is_deleted: boolean;
}

export class CursorTermsDto {
  @IsNumber()
  id?: number;

  @IsNumber()
  terms_id: number;

  @IsString()
  title?: string;

  @IsString()
  content?: string;

  @IsBoolean()
  is_active?: boolean;

  @IsDate()
  effective_date?: Date;

  @IsNumber()
  created_by?: number;

  @IsDate()
  created_at?: Date;

  @IsNumber()
  updated_by?: number;

  @IsDate()
  updated_at?: Date;

  @IsNumber()
  deleted_by?: number;

  @IsDate()
  deleted_at?: Date;

  @IsBoolean()
  is_deleted?: boolean;
}

export class OrderTermsDto {
  @IsString()
  created_at?: 'asc' | 'desc';
}
