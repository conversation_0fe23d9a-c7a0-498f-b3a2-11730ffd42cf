import { Injectable } from '@nestjs/common';
import { TermsRepository } from 'libs/data-access/src/lib/terms/terms.repository';
import { Prisma, terms_and_conditions } from '@core/prisma-client';
import { CreateTermsDto } from '../dto/create-terms.dto';
import { UpdateTermsDto, UpdateTermsWhereDto } from '../dto/update-terms.dto';
import {
  GetTermsDto,
  OrderTermsDto,
  CursorTermsDto,
} from '../dto/terms-search.dto';

@Injectable()
export class TermsService {
  constructor(private readonly termAndConditionsRepository: TermsRepository) {}

  async create(data: CreateTermsDto): Promise<terms_and_conditions> {
    return this.termAndConditionsRepository.create(data);
  }

  async update(params: {
    where: UpdateTermsWhereDto;
    data: UpdateTermsDto;
  }): Promise<terms_and_conditions> {
    const { where, data } = params;
    return this.termAndConditionsRepository.update({ data, where });
  }

  async delete(
    where: UpdateTermsWhereDto,
    data: UpdateTermsDto
  ): Promise<terms_and_conditions> {
    return this.termAndConditionsRepository.update({ data, where });
  }

  async term(where: UpdateTermsWhereDto): Promise<terms_and_conditions> {
    return this.termAndConditionsRepository.findOne(where);
  }

  async terms(params: {
    skip?: number;
    take?: number;
    cursor?: CursorTermsDto;
    where?: GetTermsDto;
    orderBy?: OrderTermsDto;
  }): Promise<terms_and_conditions[]> {
    const { skip, take, cursor, where, orderBy } = params;
    return this.termAndConditionsRepository.findAll({
      skip,
      cursor,
      where,
      orderBy,
    });
  }
}
