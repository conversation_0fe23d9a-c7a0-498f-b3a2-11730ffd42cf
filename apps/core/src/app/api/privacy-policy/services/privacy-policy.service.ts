import { Injectable } from '@nestjs/common';
import {
  CreatePrivacyPolicyDto,
  PrivacyPolicyResponseDTO,
  UpdatePrivacyPolicyDto,
} from '../dto/privacy-policy.dto';
import { PrivacyPolicyRepository } from '@core/libs';

@Injectable()
export class PrivacyPolicyService {
  constructor(
    private readonly privacyPolicyRepository: PrivacyPolicyRepository
  ) {}

  async findAll(): Promise<PrivacyPolicyResponseDTO[]> {
    return this.privacyPolicyRepository.findAll();
  }

  async findOne(id: number): Promise<PrivacyPolicyResponseDTO> {
    return this.privacyPolicyRepository.findOne({ privacy_policy_id: id });
  }

  async create(
    createPrivacyPolicyDto: CreatePrivacyPolicyDto
  ): Promise<PrivacyPolicyResponseDTO> {
    return this.privacyPolicyRepository.create({
      title: createPrivacyPolicyDto.title,
      content: createPrivacyPolicyDto.content,
      language: 'en',
    });
  }

  async update(
    updatePrivacyPolicyDto: UpdatePrivacyPolicyDto
  ): Promise<PrivacyPolicyResponseDTO> {
    return this.privacyPolicyRepository.update(
      { privacy_policy_id: updatePrivacyPolicyDto.privacy_policy_id },
      {
        title: updatePrivacyPolicyDto.title,
        content: updatePrivacyPolicyDto.content,
        language: 'en',
      }
    );
  }

  async deletePolicy(id: number): Promise<PrivacyPolicyResponseDTO> {
    return this.privacyPolicyRepository.delete(
      { privacy_policy_id: id },
      { is_deleted: true }
    );
  }
}
