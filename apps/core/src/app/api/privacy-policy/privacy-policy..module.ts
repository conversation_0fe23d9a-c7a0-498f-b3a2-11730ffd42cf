import { Module, NestModule, MiddlewareConsumer } from '@nestjs/common';
import { PrivacyPolicyController } from './controllers/privacy-policy.controller';
import { TokenMiddleware } from '@core_be/auth';
import { ConfigService } from '@nestjs/config';
import { PrivacyPolicyService } from './services/privacy-policy.service';

@Module({
  controllers: [PrivacyPolicyController],
  providers: [PrivacyPolicyService, ConfigService],
})
export class PrivacyPolicyModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(TokenMiddleware).forRoutes(PrivacyPolicyController);
  }
}
