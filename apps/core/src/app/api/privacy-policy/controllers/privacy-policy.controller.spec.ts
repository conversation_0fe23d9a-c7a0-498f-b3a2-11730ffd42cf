import { Test, TestingModule } from '@nestjs/testing';
import { PrivacyPolicyController } from './privacy-policy.controller';
import { PrivacyPolicyService } from '../services/privacy-policy.service';
import {
  CreatePrivacyPolicyDto,
  PrivacyPolicyResponseDTO,
  UpdatePrivacyPolicyDto,
} from '../dto/privacy-policy.dto';

describe('PrivacyPolicyController', () => {
  let controller: PrivacyPolicyController;
  let privacyPolicyService: PrivacyPolicyService;

  const mockPrivacyPolicyService = {
    findAll: jest.fn(),
    findOne: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    deletePolicy: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [PrivacyPolicyController],
      providers: [
        { provide: PrivacyPolicyService, useValue: mockPrivacyPolicyService },
      ],
    }).compile();

    controller = module.get<PrivacyPolicyController>(PrivacyPolicyController);
    privacyPolicyService =
      module.get<PrivacyPolicyService>(PrivacyPolicyService);
  });

  describe('findAll', () => {
    it('should return an array of privacy policies', async () => {
      const mockPolicies: PrivacyPolicyResponseDTO[] = [
        {
          privacy_policy_id: 1,
          version: '1.0',
          title: 'Policy 1',
          content: 'Content 1',
          language: 'en',
          is_active: true,
          effective_date: new Date(),
          created_by: null,
          created_at: new Date(),
          updated_by: null,
          updated_at: new Date(),
          deleted_by: null,
          deleted_at: new Date(),
          is_deleted: false,
        },
      ];

      jest
        .spyOn(privacyPolicyService, 'findAll')
        .mockResolvedValue(mockPolicies);

      const result = await controller.findAll();
      expect(result).toEqual(mockPolicies);
      expect(privacyPolicyService.findAll).toHaveBeenCalled();
    });
  });

  describe('findOne', () => {
    it('should return a single privacy policy', async () => {
      const mockPolicy: PrivacyPolicyResponseDTO = {
        privacy_policy_id: 1,
        version: '1.0',
        title: 'Policy 1',
        content: 'Content 1',
        language: 'en',
        is_active: true,
        effective_date: new Date(),
        created_by: null,
        created_at: new Date(),
        updated_by: null,
        updated_at: new Date(),
        deleted_by: null,
        deleted_at: new Date(),
        is_deleted: false,
      };

      jest.spyOn(privacyPolicyService, 'findOne').mockResolvedValue(mockPolicy);

      const result = await controller.findOne('1');
      expect(result).toEqual(mockPolicy);
      expect(privacyPolicyService.findOne).toHaveBeenCalledWith(1);
    });
  });

  describe('create', () => {
    it('should create and return a new privacy policy', async () => {
      const mockDto: CreatePrivacyPolicyDto = {
        title: 'Policy 1',
        content: 'Content 1',
        language: 'en',
      };

      const mockResponse: PrivacyPolicyResponseDTO = {
        privacy_policy_id: 1,
        version: '1.0',
        title: 'Policy 1',
        content: 'Content 1',
        language: 'en',
        is_active: true,
        effective_date: new Date(),
        created_by: null,
        created_at: new Date(),
        updated_by: null,
        updated_at: new Date(),
        deleted_by: null,
        deleted_at: new Date(),
        is_deleted: false,
      };

      jest
        .spyOn(privacyPolicyService, 'create')
        .mockResolvedValue(mockResponse);

      const result = await controller.create(mockDto);
      expect(result).toEqual(mockResponse);
      expect(privacyPolicyService.create).toHaveBeenCalledWith(mockDto);
    });
  });

  describe('update', () => {
    it('should update and return the updated privacy policy', async () => {
      const mockDto: UpdatePrivacyPolicyDto = {
        privacy_policy_id: 1,
        title: 'Updated Policy',
        content: 'Updated Content',
        language: 'en',
      };

      const mockResponse: PrivacyPolicyResponseDTO = {
        privacy_policy_id: 1,
        version: '1.1',
        title: 'Updated Policy',
        content: 'Updated Content',
        language: 'en',
        is_active: true,
        effective_date: new Date(),
        created_by: null,
        created_at: new Date(),
        updated_by: null,
        updated_at: new Date(),
        deleted_by: null,
        deleted_at: new Date(),
        is_deleted: false,
      };

      jest
        .spyOn(privacyPolicyService, 'update')
        .mockResolvedValue(mockResponse);

      const result = await controller.update(1, mockDto);
      expect(result).toEqual(mockResponse);
      expect(privacyPolicyService.update).toHaveBeenCalledWith(mockDto);
    });
  });

  describe('delete', () => {
    it('should delete a privacy policy and return it', async () => {
      const mockResponse: PrivacyPolicyResponseDTO = {
        privacy_policy_id: 1,
        version: '1.1',
        title: 'Policy to Delete',
        content: 'Content',
        language: 'en',
        is_active: true,
        effective_date: new Date(),
        created_by: null,
        created_at: new Date(),
        updated_by: null,
        updated_at: new Date(),
        deleted_by: null,
        deleted_at: new Date(),
        is_deleted: true,
      };

      jest
        .spyOn(privacyPolicyService, 'deletePolicy')
        .mockResolvedValue(mockResponse);

      const result = await controller.delete('1');
      expect(result).toEqual(mockResponse);
      expect(privacyPolicyService.deletePolicy).toHaveBeenCalledWith(1);
    });
  });
});
