import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  Put,
  NotFoundException,
} from '@nestjs/common';
import { PrivacyPolicyService } from '../services/privacy-policy.service';
import { ApiTags, ApiBearerAuth } from '@nestjs/swagger';
import { AuthGuard } from '@nestjs/passport';
import {
  CreatePrivacyPolicyDto,
  UpdatePrivacyPolicyDto,
  PrivacyPolicyResponseDTO,
} from '../dto/privacy-policy.dto';
import { Role, Roles } from '@core_be/global';

@ApiTags('Privacy Policy Condition')
@ApiBearerAuth()
@Controller('privacy-policy')
@UseGuards(AuthGuard('jwt'))
export class PrivacyPolicyController {
  constructor(private readonly privacyPolicyService: PrivacyPolicyService) {}

  @Get()
  async findAll(): Promise<PrivacyPolicyResponseDTO[]> {
    return this.privacyPolicyService.findAll();
  }

  @Get(':id')
  async findOne(@Param('id') id: string): Promise<PrivacyPolicyResponseDTO> {
    const privacyPolicy = await this.privacyPolicyService.findOne(parseInt(id));
    if (!privacyPolicy) {
      throw new NotFoundException('Privacy policy not found.');
    }
    return privacyPolicy;
  }

  @Post()
  async create(
    @Body() createPrivacyPolicyDto: CreatePrivacyPolicyDto
  ): Promise<PrivacyPolicyResponseDTO> {
    return this.privacyPolicyService.create(createPrivacyPolicyDto);
  }

  @Put(':id')
  async update(
    @Param('id') id: number,
    @Body() updatePrivacyPolicyDto: UpdatePrivacyPolicyDto
  ): Promise<PrivacyPolicyResponseDTO> {
    return this.privacyPolicyService.update(updatePrivacyPolicyDto);
  }

  @Delete(':id')
  @Roles(Role.Admin)
  async delete(@Param('id') id: string): Promise<PrivacyPolicyResponseDTO> {
    return this.privacyPolicyService.deletePolicy(parseInt(id));
  }
}
