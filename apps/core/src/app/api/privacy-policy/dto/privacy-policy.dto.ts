import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsString } from '@nestjs/class-validator';
import { IsOptional } from 'class-validator';

export class CreatePrivacyPolicyDto {
  @ApiProperty({
    example: 'en',
    description: 'en',
    required: false,
  })
  @IsOptional()
  @IsString()
  language: string;

  @ApiProperty({
    example: 'title',
    description: 'title',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  title: string;

  @ApiProperty({
    example: 'content',
    description: 'content',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  content: string;

  @ApiProperty({
    example: true,
    description: 'deleted flag set to true',
    required: false,
  })
  is_deleted?: boolean;

  @ApiProperty({
    example: 1,
    description: 'user_id',
    required: false,
  })
  deleted_by?: number;

  @ApiProperty({
    example: 'date',
    description: 'timestamp',
    required: false,
  })
  deleted_at?: Date;
}

export class UpdatePrivacyPolicyDto extends CreatePrivacyPolicyDto {
  @IsNumber()
  privacy_policy_id: number;
}

export class PrivacyPolicyResponseDTO {
  privacy_policy_id: number;
  version: string | null;
  title: string;
  content: string;
  language: string;
  is_active: boolean;
  effective_date: Date;
  created_by: number | null;
  created_at: Date;
  updated_by: number | null;
  updated_at: Date;
  deleted_by: number | null;
  deleted_at: Date;
  is_deleted: boolean;
}
