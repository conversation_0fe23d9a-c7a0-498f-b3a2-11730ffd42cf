import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Request,
  ConflictException,
  BadGatewayException,
  UseGuards,
  BadRequestException,
  NotFoundException,
  ForbiddenException,
} from '@nestjs/common';
import { CreateProfileDto } from './dto/create-profile.dto';
import { UpdateProfileDto } from './dto/update-profile.dto';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { PrismaService } from '@core/prisma-client';
import {
  RolesGuard,
  LibProfileService,
  ProfileType,
  RelationToUser,
  Role,
  Roles,
  UsersRoleRepository,
} from '@core/libs';
import { ProfileAccessGuard } from 'libs/global/src/lib/guards/profile-access.guard';
import { UserAccessGuard } from 'libs/global/src/lib/guards/user-access.guard';
import { UsersService } from '../users/services/users.service';
import { Logger } from 'nestjs-pino';

@ApiTags('Profiles')
@ApiBearerAuth()
@Controller('profile')
@UseGuards(RolesGuard)
export class ProfileController {
  constructor(
    private usersRoleRepository: UsersRoleRepository,
    private readonly profileService: LibProfileService,
    private usersService: UsersService,
    private readonly prisma: PrismaService,
    private readonly logger: Logger
  ) {}

  // No usage from app side, limiting access to admin only
  @Post()
  @Roles(Role.Admin)
  async create(@Body() createProfileDto: CreateProfileDto, @Request() req) {
    if (createProfileDto.user_id !== req.raw?.user?.user_id) {
      throw new BadRequestException(
        'You are not authorized to create this profile.'
      );
    }
    if (
      createProfileDto?.profile_type == ProfileType.HEALER &&
      createProfileDto?.relation_to_user != RelationToUser.Self
    ) {
      throw new BadGatewayException(
        `A profile type ${ProfileType.HEALER} can be created with relation type ${RelationToUser.Self}.`
      );
    }

    if (
      createProfileDto?.relation_to_user == RelationToUser.Self ||
      createProfileDto?.profile_type == ProfileType.HEALER
    ) {
      const userProfiles = await this.profileService.checkUserProfile(
        createProfileDto
      );

      if (userProfiles?.profiles.length > 0) {
        throw new ConflictException(
          `You already have a ${userProfiles?.profiles[0]?.relation_to_user} relation as a ${userProfiles?.profiles[0]?.profile_type}.`
        );
      }
    }

    const createData: any = {
      ...createProfileDto,
      created_by: req.raw.user.user_id,
      users: { connect: { user_id: createProfileDto.user_id } },
      gender: createProfileDto.gender || '',
      date_of_birth: null,
    };

    const getDefaultProfile = await this.profileService.getDefaultProfile({
      user_id: createProfileDto.user_id,
      is_default: true,
      is_deleted: false,
    });

    if (!getDefaultProfile) {
      createData.is_default = true;
      createData.relation_to_user = RelationToUser.Self;
    }

    delete createData.user_id;

    this.logger.log(
      {
        timestamp: new Date().toISOString(),
        event: 'PROFILE_CREATION',
        userId: req.raw.user.user_id,
        targetUserId: createProfileDto.user_id,
        profileType: createProfileDto.profile_type,
        relationToUser: createProfileDto.relation_to_user,
        message: 'Profile creation initiated',
      },
      'Profile Creation'
    );

    const data = await this.profileService.createProfile(createData);
    const userRoles = await this.prisma.user_roles.findFirst({
      where: { user_id: createProfileDto.user_id },
    });

    const roles = await this.prisma.roles.findFirst({
      where: { name: createProfileDto?.profile_type },
    });

    if (!roles?.role_id) {
      throw new BadRequestException('Role not found!');
    }

    if (userRoles?.role_id !== roles?.role_id && roles.name in Role) {
      const createUserRoleData = {
        roles: { connect: { role_id: roles.role_id } },
        users: { connect: { user_id: createProfileDto.user_id } },
      };

      await this.usersRoleRepository.createUserRole(createUserRoleData);
    }
    return data;
  }

  // Limit access to admin only
  @Get()
  @Roles(Role.Admin)
  async findAll() {
    return await this.profileService.profiles({});
  }

  // Allow only the user to access their own profile
  @Get(':profile_id')
  @Roles(Role.Healer, Role.Patient)
  @UseGuards(ProfileAccessGuard)
  async findOne(@Param('profile_id') profile_id: string, @Request() req) {
    const profile = await this.profileService.profile({
      profile_id: +profile_id,
    });
    if (!profile) {
      throw new NotFoundException('Profile not found.');
    }
    return profile;
  }

  @Get('userid/:user_id')
  @Roles(Role.Admin)
  @UseGuards(UserAccessGuard)
  async findOneByUserId(@Param('user_id') userId: string, @Request() req) {
    return await this.profileService.profileByUserId({
      user_id: +userId,
    });
  }

  @Patch(':profile_id')
  @Roles(Role.Healer, Role.Patient)
  @UseGuards(ProfileAccessGuard)
  async update(
    @Param('profile_id') profile_id: string,
    @Body() updateProfileDto: UpdateProfileDto,
    @Request() req
  ) {
    if (
      updateProfileDto?.profile_type == ProfileType.HEALER &&
      updateProfileDto?.relation_to_user != RelationToUser.Self
    ) {
      throw new BadGatewayException(
        `A profile type ${ProfileType.HEALER} can be created with relation type ${RelationToUser.Self}.`
      );
    }

    if (
      updateProfileDto?.relation_to_user == RelationToUser.Self ||
      updateProfileDto?.profile_type == ProfileType.HEALER
    ) {
      const userProfiles = await this.profileService.checkUserProfile(
        updateProfileDto
      );

      if (userProfiles?.profiles.length > 0) {
        throw new ConflictException(
          `You already have a ${userProfiles?.profiles[0]?.relation_to_user} relation as a ${userProfiles?.profiles[0]?.profile_type}.`
        );
      }
    }

    const updateData = {
      ...updateProfileDto,
      updated_by: req.raw.user.user_id,
      updated_at: new Date(),
    };
    await this.validateUpdatePermissions(updateData);

    const userId = req.raw?.user?.user_id;
    if (!userId) {
      this.logger.error(
        {
          timestamp: new Date().toISOString(),
          event: 'PROFILE_UPDATE_NO_USER',
          profileId: +profile_id,
          message: 'User not found for profile update',
        },
        'Profile Update Failed'
      );
      throw new BadRequestException('User not found.');
    }
    this.logger.log(
      {
        timestamp: new Date().toISOString(),
        event: 'PROFILE_UPDATE',
        userId,
        profileId: +profile_id,
        changes: Object.keys(updateData),
        message: 'Profile update initiated',
      },
      'Profile Update'
    );

    if (updateProfileDto.date_of_birth) {
      await this.usersService.updateUser({
        where: { user_id: userId },
        data: {
          year_of_birth: new Date(updateProfileDto.date_of_birth).getFullYear(),
        },
      });
    }

    return await this.profileService.updateProfile({
      where: { profile_id: +profile_id },
      data: updateData,
    });
  }

  // DEPRECATED: This endpoint is not used in the application
  // @Delete(':profile_id')
  // @Roles(Role.Admin)
  // @UseGuards(ProfileAccessGuard)
  // async remove(@Param('profile_id') profile_id: string, @Request() req) {
  //   const deleteData = {
  //     is_deleted: true,
  //     deleted_by: req.raw.user.user_id,
  //     deleted_at: new Date(),
  //   };
  //   return await this.profileService.deleteProfile(
  //     { profile_id: +profile_id },
  //     deleteData
  //   );
  // }

  private async validateUpdatePermissions(
    updateData: UpdateProfileDto
  ): Promise<void> {
    // Prevent unauthorized field updates
    const restrictedFields = [
      'profile_id',
      'profile_type',
      'user_id',
      'created_at',
      'is_deleted',
    ];
    for (const field of restrictedFields) {
      if (updateData.hasOwnProperty(field)) {
        throw new ForbiddenException(
          `Cannot update restricted field: ${field}`
        );
      }
    }
  }
}
