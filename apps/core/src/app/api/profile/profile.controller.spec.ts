import { Test, TestingModule } from '@nestjs/testing';
import { ProfileController } from './profile.controller';
import { PrismaService } from '@core/prisma-client';
import {
  LibProfileService,
  UsersRoleRepository,
} from '@core/libs';
import { UsersService } from '../users/services/users.service';

describe('ProfileController', () => {
  let controller: ProfileController;

  beforeEach(async () => {
    const mockLibProfileService = {};
    const mockUsersService = {};
    const mockUsersRolesService = {};
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ProfileController],
      providers: [
        { provide: LibProfileService, useValue: mockLibProfileService },
        { provide: UsersService, useValue: mockUsersService },
        { provide: UsersRoleRepository, useValue: mockUsersRolesService },
        PrismaService,
      ],
    }).compile();

    controller = module.get<ProfileController>(ProfileController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
