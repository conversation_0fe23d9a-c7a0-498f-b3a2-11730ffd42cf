import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsOptional,
  IsString,
  IsNumber,
  IsEnum,
} from '@nestjs/class-validator';
import { ProfileType, RelationToUser } from '@core/libs';
import { IsBoolean } from 'class-validator';

export class CreateProfileDto {
  @ApiProperty({
    example: 'John',
    description: 'First name',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  first_name: string;

  @ApiProperty({
    example: 'Doe',
    description: 'Last name',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  last_name: string;

  @ApiPropertyOptional({
    example: ProfileType.HEALER,
    description: 'Healer/Patient',
    required: true,
  })
  @IsEnum(ProfileType, { message: 'profile_type must be a valid value' })
  @IsString()
  @IsNotEmpty()
  profile_type: string;

  @ApiProperty({
    example: 123,
    description: 'User ID',
    required: true,
  })
  @IsNumber()
  @IsNotEmpty()
  user_id: number;

  @ApiPropertyOptional({
    example: RelationToUser.Self,
    description: 'Relation To User',
    default: RelationToUser.Self,
    required: true,
  })
  @IsEnum(RelationToUser, { message: 'relation_to_user must be a valid value' })
  @IsString()
  @IsNotEmpty()
  relation_to_user?: string;

  @ApiPropertyOptional({
    example: 'female',
    description: 'Gender',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  gender?: string;

  @ApiPropertyOptional({
    example: '2024-07-09',
    description: 'DOB',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  date_of_birth?: Date;

  @ApiPropertyOptional({
    example: 'Profile.jpg',
    description: 'Picture Name',
  })
  @IsString()
  @IsOptional()
  profile_picture_file_name?: string;

  @ApiPropertyOptional({
    example: '9876543210',
    description: 'phoneNumber',
  })
  @IsString()
  @IsOptional()
  phone_number?: string;

  @ApiPropertyOptional({
    example: 'India',
    description: 'country',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  country?: string;

  @ApiPropertyOptional({
    example: 'Tamil Nadu',
    description: 'state',
  })
  @IsString()
  @IsOptional()
  state?: string;

  @ApiPropertyOptional({
    example: 'Chennai',
    description: 'city',
  })
  @IsString()
  @IsOptional()
  city?: string;

  @ApiPropertyOptional({
    example: 'address',
    description: 'Address',
  })
  @IsString()
  @IsOptional()
  address?: string;

  @ApiPropertyOptional({
    example: '600078',
    description: 'zip_code',
  })
  @IsString()
  @IsOptional()
  zip_code?: string;

  @ApiPropertyOptional({
    example: true,
    description: 'is_intro_complete',
  })
  @IsBoolean()
  @IsOptional()
  is_intro_complete?: boolean;
}
