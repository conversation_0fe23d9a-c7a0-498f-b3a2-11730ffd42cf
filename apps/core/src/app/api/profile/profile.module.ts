import { Global, MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { ProfileController } from './profile.controller';
import { TokenMiddleware } from 'libs/auth/src/lib/middleware';
import { LibProfileService } from '@core/libs';
import { UsersService } from '../users/services/users.service';
import { WaitlistRepository } from 'libs/data-access/src/lib/waitlist/waitlist.repository';
import { Logger } from 'nestjs-pino';

@Global()
@Module({
  controllers: [ProfileController],
  providers: [LibProfileService, UsersService, WaitlistRepository, Logger],
  exports: [LibProfileService],
})
export class ProfileModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(TokenMiddleware).forRoutes(ProfileController);
  }
}
