import { Injectable } from '@nestjs/common';
import { PrismaService } from '@core/prisma-client';
import { SystemConfigService, SystemConfigType } from '@core_be/global';

@Injectable()
export class ServiceStatusService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly systemConfigService: SystemConfigService
  ) {}

  async getServiceStatus() {
    const maintananceModeConfig = await this.prisma.system_configs.findFirst({
      where: {
        name: SystemConfigType.MaintainanceMode,
        is_deleted: false,
        is_active: true,
      },
    });
    const notificationMessage =
      await this.systemConfigService.getNotificationMessage();
    const notificationTitle =
      await this.systemConfigService.getNotificationTitle();
    return {
      isOutage: maintananceModeConfig?.value === 'ENABLED' ? true : false,
      outageMessage: maintananceModeConfig?.description || '',
      lastUpdated: maintananceModeConfig?.updated_at || '',
      notificationMessage: notificationMessage || '',
      notificationTitle: notificationTitle || '',
    };
  }
}
