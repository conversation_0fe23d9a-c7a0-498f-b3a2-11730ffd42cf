import { Controller, Get } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Public } from '@core/libs';
import { ServiceStatusService } from '../services/service-status.service';

@ApiTags('service Status')
@Public()
@Controller('service-status')
export class ServiceStatusController {
  constructor(private serviceStatusService: ServiceStatusService) {}

  @Get()
  @Public()
  async getServiceStatus() {
    return await this.serviceStatusService.getServiceStatus();
  }
}
