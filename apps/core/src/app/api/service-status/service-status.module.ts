// app-documents.module.ts
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ServiceStatusController } from './controllers/service-status.controller';
import { SystemConfigService } from '@core_be/global';
import { ServiceStatusService } from './services/service-status.service';

@Module({
  imports: [ConfigModule.forRoot()],
  controllers: [ServiceStatusController],
  providers: [ServiceStatusService, SystemConfigService],
  exports: [],
})
export class ServiceStatusModule {}
