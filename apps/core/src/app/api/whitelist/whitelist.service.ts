import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService, Prisma } from '@core/prisma-client';
import csvParser from 'csv-parser';
import { Readable } from 'stream';
import validator from 'validator';
import { WhitelistFindArgs } from './dto/create-whitelist.dto';

@Injectable()
export class WhitelistService {
  private whitelistedEmails: Set<string> = new Set();
  constructor(private prisma: PrismaService) {}

  async createWhiteList(data: Prisma.whitelistCreateInput) {
    return this.prisma.whitelist.create({ data });
  }

  async validateWhitelist(where: Prisma.whitelistWhereInput) {
    return this.prisma.whitelist.findFirst({ where });
  }

  async uploadCSV(file: any, user: any): Promise<any> {
    try {
      if (!file || !file.originalname || !file.originalname.endsWith('.csv')) {
        throw new NotFoundException('Invalid File. Please upload a CSV file.');
      }

      const bufferStream = new Readable();
      bufferStream.push(file.buffer);
      bufferStream.push(null);
      return new Promise((resolve, reject) => {
        const validEmails: string[] = [];
        const duplicateEmails: string[] = [];
        const invalidEmails: string[] = [];
        bufferStream
          .pipe(csvParser({ headers: ['email'] }))
          .on('data', (data: any) => {
            const email = data.email.trim();
            if (!validator.isEmail(email)) {
              invalidEmails.push(email);
            } else {
              validEmails.push(email);
            }
          })
          .on('end', async () => {
            try {
              const existingWhitelist = await Promise.all(
                validEmails.map((email) => this.validateWhitelist({ entity_value: email }))
              );
              const newWhitelistData = validEmails.filter((email, index) => {
                if (existingWhitelist[index]) {
                  duplicateEmails.push(email);
                  return false;
                }
                return true;
              }).map((email) => ({
                entity_type: 'email',
                entity_value: email,
                created_by: user.user_id,
                status: 'active',
              }));
              if (newWhitelistData.length > 0) {
                await this.createMany(newWhitelistData);
                newWhitelistData.forEach((data) => this.whitelistedEmails.add(data.entity_value));
              }
  
              resolve({
                whitelistedEmails: newWhitelistData.map((d) => d.entity_value),
                duplicateEmails,
                invalidEmails,
              });
            } catch (error: any) {
              reject(new BadRequestException(error.message));
            }
          });
      });
    } catch (error: any) {
      throw new BadRequestException(error.message);
    }
  }

  async createMany(data: Prisma.whitelistCreateManyInput[]) {
    return this.prisma.whitelist.createMany({data});
  }

  async findAll(params: WhitelistFindArgs) {
    const { offset, limit, cursor, where, orderBy } = params;
    return this.prisma.whitelist.findMany({
      skip:offset,
      take: limit,
      cursor,
      where,
      orderBy,
    });
  }

  async count(params: { where?: Prisma.whitelistWhereInput }) {
    const { where } = params;
    return this.prisma.whitelist.count({ where });
  }
}
