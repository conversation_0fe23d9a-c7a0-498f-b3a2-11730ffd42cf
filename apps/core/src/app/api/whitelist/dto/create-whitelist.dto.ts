import { Prisma } from '@core/prisma-client';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ArrayNotEmpty, ArrayUnique, IsArray, IsEnum, IsOptional, IsString } from 'class-validator';

export class CreateWhitelistDto {
  @ApiProperty({
    type: 'string',
    format: 'binary',
    description: 'whitelist Bulk Upload',
    required: true,
  })
  file: any;
}


export class WhitelistDto {
  @IsArray()
  @ArrayNotEmpty()
  @ArrayUnique()
  @ApiProperty({
    example: ['<EMAIL>'],
    description: 'The emails of the whitelist',
    required: true,
  })
  emails: string[];
}

export class WhitelistQueryDto {
  @ApiPropertyOptional({
    example: 'entity_value',
    required: false,
  })
  @IsOptional()
  @IsString()
  orderBy?: string;

  @ApiPropertyOptional({
    description: 'Order direction',
    enum: ['asc', 'desc'],
    example: 'asc',
    required: false,
  })
  @IsOptional()
  @IsEnum(['asc', 'desc'])
  order: 'asc' | 'desc' = 'asc';

  @ApiPropertyOptional({
    example: 10,
    required: false,
  })
  @IsOptional()
  @IsString()
  limit: string;

  @ApiPropertyOptional({
    example: 0,
    required: false,
  })
  @IsOptional()
  @IsString()
  offset: string;

  @ApiPropertyOptional({
    example: '',
    required: false,
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({
    description: 'Status of the whitelist',
    enum: ['active', 'inactive'],
    example: 'active',
    required: false,
  })
  @IsOptional()
  @IsEnum(['active', 'inactive'])
  status: 'active' | 'inactive' = 'active';
}

export interface WhitelistFindArgs {
  offset?: number;
  limit?: number;
  cursor?: Prisma.whitelistWhereUniqueInput;
  where?: Prisma.whitelistWhereInput;
  orderBy?: Prisma.whitelistOrderByWithAggregationInput;
}

export class WhitelistResponse {
  whitelistedEmails: string[];
  duplicateEmails: string[];
  invalidEmails: string[];
}