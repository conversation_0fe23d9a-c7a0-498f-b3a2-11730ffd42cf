import { Test, TestingModule } from '@nestjs/testing';
import { WhitelistController } from './whitelist.controller';
import { WhitelistService } from './whitelist.service';
import { CreateWhitelistDto } from './dto/create-whitelist.dto';
import { faker } from '@faker-js/faker';

const user_id = '4';
const mockRequest = {
  raw: {
    user: {
      user_id,
    },
  },
};
describe('WhitelistController', () => {
  let controller: WhitelistController;
  let service: WhitelistService;

  beforeEach(async () => {
    const mockWhitelistService = {
      uploadCSV: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [WhitelistController],
      providers: [
        {
          provide: WhitelistService,
          useValue: mockWhitelistService,
        },
      ],
    }).compile();

    controller = module.get<WhitelistController>(WhitelistController);
    service = module.get<WhitelistService>(WhitelistService);
  });

  describe('bulkUpload', () => {
    it('should call WhitelistService and do the bulkUpload', async () => {
      const email = faker.internet.email().toLowerCase();
      const csvContent = `email\n${email}`;
      const mockFile = {
        buffer: Buffer.from(csvContent, 'utf-8'),
        originalname: 'emails.csv',
        mimetype: 'text/csv',
        size: csvContent.length,
        fieldname: 'file',
        encoding: '7bit',
      };
      const dto: CreateWhitelistDto = { file: 'file' };
      const mockResponse = {
        status_code: 201,
        success: true,
        message: 'Whitelist Uploaded.',
      };

      jest.spyOn(service, 'uploadCSV').mockResolvedValue(mockResponse);
      const result = await controller.uploadCSV(mockFile, dto, mockRequest);
      expect(service.uploadCSV).toHaveBeenCalledWith(
        mockFile,
        mockRequest.raw.user
      );
      expect(result.message).toEqual(mockResponse.message);
    });
  });
});
