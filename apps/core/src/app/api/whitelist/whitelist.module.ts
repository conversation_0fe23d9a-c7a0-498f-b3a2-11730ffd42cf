import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { WhitelistController } from './whitelist.controller';
import { TokenMiddleware } from '@core_be/auth';
import { ConfigService } from '@nestjs/config';
import { WhitelistService } from './whitelist.service';
import { Logger } from 'nestjs-pino';

@Module({
  controllers: [WhitelistController],
  providers: [WhitelistService, ConfigService, Logger],
})
export class WhitelistModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(TokenMiddleware).forRoutes(WhitelistController);
  }
}
