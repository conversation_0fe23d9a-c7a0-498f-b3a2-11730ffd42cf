import {
  Controller,
  Post,
  Body,
  UseInterceptors,
  UploadedFile,
  Request,
  BadRequestException,
  UseGuards,
  Get,
  Query,
} from '@nestjs/common';
import { CreateWhitelistDto ,WhitelistDto, WhitelistFindArgs, WhitelistQueryDto, WhitelistResponse} from './dto/create-whitelist.dto';
import { ApiBearerAuth, ApiConsumes, ApiTags } from '@nestjs/swagger';
import { FileInterceptor } from '@nest-lab/fastify-multer';
import type { File } from '@nest-lab/fastify-multer';
import { WhitelistService } from './whitelist.service';
import { Role, Roles, RolesGuard } from '@core/libs';
import validator from 'validator';
import { Prisma } from '@core/prisma-client';
import { Logger } from 'nestjs-pino';

@ApiTags('Whitelist')
@ApiBearerAuth()
@Controller('whitelist')
@UseGuards(RolesGuard)
export class WhitelistController {
  private whitelistedEmails: Set<string> = new Set();
  constructor(
    private readonly whitelistService: WhitelistService,
    private readonly logger: Logger
  ) {}

  @Post('bulk-upload')
  @Roles(Role.Admin)
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('file'))
  async uploadCSV(
    @UploadedFile() file: File,
    @Body() createWhitelistDto: CreateWhitelistDto,
    @Request() req
  ): Promise<WhitelistResponse> {
    try {
      this.logger.log({
        timestamp: new Date().toISOString(),
        event: 'WHITELIST_BULK_UPLOAD',
        userId: req.raw.user.user_id,
        fileName: file?.originalname,
        fileSize: file?.size,
        message: 'Bulk whitelist upload initiated'
      }, 'Whitelist Bulk Upload');

      return this.whitelistService.uploadCSV(file, req.raw.user);
    } catch (error) {
      this.logger.error({
        timestamp: new Date().toISOString(),
        event: 'WHITELIST_BULK_UPLOAD_FAILED',
        userId: req.raw.user.user_id,
        fileName: file?.originalname,
        error: error.message,
        message: 'Bulk whitelist upload failed'
      }, 'Whitelist Bulk Upload Failed');

      throw new BadRequestException(error.message);
    }
  }

  @Post()
  @Roles(Role.Admin)
  async addEmails(
    @Body() whitelistDto : WhitelistDto ,
    @Request() req
  ): Promise<WhitelistResponse> {
    try {
      const validEmails: string[] = [];
      const duplicateEmails: string[] = [];
      const invalidEmails: string[] = [];

      whitelistDto.emails.forEach((email) => {
        if (!validator.isEmail(email)) {
          invalidEmails.push(email);
        } else {
          validEmails.push(email);
        }
      });

      const existingWhitelist = await Promise.all(
        validEmails.map((email) => this.whitelistService.validateWhitelist({ entity_value: email }))
      );

      const newWhitelistData = validEmails.filter((email, index) => {
        if (existingWhitelist[index]) {
          duplicateEmails.push(email);
          return false;
        }
        return true;
      }).map((email) => ({
        entity_value: email,
        entity_type: 'email',
        created_by: req.raw.user.user_id,
        status: 'active',
      }));

      if (newWhitelistData.length > 0) {
        await this.whitelistService.createMany(newWhitelistData);
        newWhitelistData.forEach((data) => this.whitelistedEmails.add(data.entity_value));
      }

      return { whitelistedEmails: newWhitelistData.map((d) => d.entity_value), duplicateEmails, invalidEmails };

    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Get()
  @Roles(Role.Admin)
  async findAll(@Query() query: WhitelistQueryDto) {
    const { orderBy, order, limit, offset,search} = query;
    const limitNumber = Number(limit) || 10;
    const offsetNumber = (Number(offset) * limitNumber) || 0;
    const orderByCondition: any = {};
    if(orderBy) {
      orderByCondition[orderBy] = order;
    }
    const queryPayload: WhitelistFindArgs = {
      orderBy: orderByCondition ?  orderByCondition : undefined,
      where: (() => {
        const where: Prisma.whitelistWhereInput = {
          is_deleted: false,
        };
        if (search) {
          where.OR = [
            { entity_value: { contains: search, mode: 'insensitive' } }
          ];
        }
        if (query.status) {
          where.status = query.status;
        }
        return where;
      })(),
    };

    if (parseInt(limit)) {
      queryPayload['limit'] = limitNumber;
      queryPayload['offset'] = offsetNumber;
    }

    const totalItems = await this.whitelistService.count(queryPayload);

    const totalPages = Math.ceil(totalItems / limitNumber);

    const whitelist =  await this.whitelistService.findAll(queryPayload);

    return {
      whitelist,
      pagination: {
        total_pages: totalPages,
        total_items: totalItems,
        offset,
        limit,
      },
    };

  }
}
