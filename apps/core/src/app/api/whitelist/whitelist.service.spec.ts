import { Test, TestingModule } from '@nestjs/testing';
import { WhitelistService } from './whitelist.service';
import { PrismaService } from '@core/prisma-client';

describe('WhitelistService', () => {
  let service: WhitelistService;
  let prismaService: PrismaService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [WhitelistService, PrismaService],
    }).compile();

    service = module.get<WhitelistService>(WhitelistService);
    prismaService = module.get<PrismaService>(PrismaService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
