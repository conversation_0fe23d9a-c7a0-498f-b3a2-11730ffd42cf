/**
 * This is not a production server yet!
 * This is only a minimal backend to get started.
 */

import { ValidationPipe, LogLevel } from '@nestjs/common';
import { HttpAdapterHost, NestFactory } from '@nestjs/core';
import { Logger, LoggerErrorInterceptor } from 'nestjs-pino';
import { AppModule } from './app/api/app.module';
import helmet from 'helmet';
import {
  FastifyAdapter,
  NestFastifyApplication,
} from '@nestjs/platform-fastify';
import { FastifyReply, FastifyRequest } from 'fastify';
import qs from 'node:querystring';
import { ResponseInterceptor, HttpExceptionFilter, ApiLoggingInterceptor } from '@core_be/global';
import { DatadogTracer, setupSwagger } from '@core/libs';

const tracer = new DatadogTracer({
  service: 'core_be',
  env: process.env.NODE_ENV || 'unknown',
  logInjection: true,
});

// Get log levels from environment
const getLogLevels = (): LogLevel[] => {
  const logLevel = process.env.LOG_LEVEL || 'error,warn,log';
  return logLevel.split(',').map((level) => level.trim()) as LogLevel[];
};

// This is a workaround for BigInt serialization issues in JSON
(BigInt.prototype as any).toJSON = function () {
  return this.toString();
};

async function bootstrap() {
  const fastifyAdapter = new FastifyAdapter({
    logger: false,
    querystringParser: (str) => qs.parse(str),
  });

  fastifyAdapter.get('/', (req: FastifyRequest, res: FastifyReply) => {
    res.code(200);
    res.send({
      message: 'Welcome to Heal Dev Env',
    });
  });

  const app = await NestFactory.create<NestFastifyApplication>(
    AppModule,
    fastifyAdapter
  );
  const logger = app.get<Logger>(Logger);
  const adapter = app.get<HttpAdapterHost>(HttpAdapterHost);

  tracer.use('fastify', fastifyAdapter.getInstance()).use('pino', true);

  app.useLogger(getLogLevels());
  // See: https://github.com/nestjs/nest/issues/964#issuecomment-413563009
  //app.useGlobalInterceptors(new LoggerErrorInterceptor());
  // app.useGlobalFilters(
  //   new StandardExceptionsFilter(adapter.httpAdapter, logger)
  // );
  app.useGlobalInterceptors(
    new LoggerErrorInterceptor(),
    new ApiLoggingInterceptor(logger),
    new ResponseInterceptor()
  );
  app.useGlobalFilters(new HttpExceptionFilter());
  const globalPrefix = 'api';
  app.setGlobalPrefix(globalPrefix);
  app.use(helmet());
  app.enableCors();
  app.useGlobalPipes(new ValidationPipe());
  // Swagger setup
  await setupSwagger(
    app,
    fastifyAdapter,
    'Healthis API',
    'API documentation for Healthis',
    'api/docs'
  );

  const port = process.env.PORT_CORE || 80;
  try {
    await app.listen(port, '0.0.0.0');
    logger.log(`Healthis-core is running on: http://localhost:${port}`);
  } catch (err) {
    logger.error(err);
    process.exit(1);
  }
}

bootstrap();
