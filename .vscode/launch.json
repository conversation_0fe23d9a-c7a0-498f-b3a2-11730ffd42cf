{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "type": "node",
      "request": "launch",
      "name": "Debug Core Nest Framework",
      "args": ["${workspaceFolder}/apps/core/src/main.ts"],
      "runtimeArgs": [
        "--nolazy",
        "-r",
        "ts-node/register",
        "-r",
        "tsconfig-paths/register"
      ],
      "envFile": "${workspaceFolder}/.env",
      "env": {
        "NODE_ENV": "local",
        "PORT_CORE": "3000",
        "TS_NODE_PROJECT": "tsconfig.base.json", // Specify the tsconfig to use. See content of it below.
        "IS_DEBUG_MODE": "true" // Custom env variable to detect debug mode
      },
      "sourceMaps": true,
      "cwd": "${workspaceRoot}",
      "console": "internalConsole",
      "outputCapture": "std",
      "resolveSourceMapLocations": [
        "${workspaceFolder}/**",
        "!**/node_modules/**" // Disable the "could not read source map" error for node_modules
      ]
    },
    {
      "type": "node",
      "request": "launch",
      "name": "Debug HealThisAI Nest Framework",
      "args": ["${workspaceFolder}/apps/ai/src/main.ts"],
      "runtimeArgs": [
        "--nolazy",
        "-r",
        "ts-node/register",
        "-r",
        "tsconfig-paths/register"
      ],
      "envFile": "${workspaceFolder}/.env",
      "env": {
        "NODE_ENV": "local",
        "AI_PORT": "3001",
        "TS_NODE_PROJECT": "tsconfig.base.json", // Specify the tsconfig to use. See content of it below.
        "IS_DEBUG_MODE": "true" // Custom env variable to detect debug mode
      },
      "sourceMaps": true,
      "cwd": "${workspaceRoot}",
      "console": "internalConsole",
      "outputCapture": "std",
      "resolveSourceMapLocations": [
        "${workspaceFolder}/**",
        "!**/node_modules/**" // Disable the "could not read source map" error for node_modules
      ]
    },
    {
      "type": "node",
      "request": "launch",
      "name": "Debug Scheduler Nest Framework",
      "args": ["${workspaceFolder}/apps/scheduler/src/main.ts"],
      "runtimeArgs": [
        "--nolazy",
        "-r",
        "ts-node/register",
        "-r",
        "tsconfig-paths/register"
      ],
      "envFile": "${workspaceFolder}/.env",
      "env": {
        "NODE_ENV": "local",
        "SCHEDULER_PORT": "4000",
        "TS_NODE_PROJECT": "tsconfig.base.json", // Specify the tsconfig to use. See content of it below.
        "IS_DEBUG_MODE": "true" // Custom env variable to detect debug mode
      },
      "sourceMaps": true,
      "cwd": "${workspaceRoot}",
      "console": "internalConsole",
      "outputCapture": "std",
      "resolveSourceMapLocations": [
        "${workspaceFolder}/**",
        "!**/node_modules/**" // Disable the "could not read source map" error for node_modules
      ]
    }
  ]
}
