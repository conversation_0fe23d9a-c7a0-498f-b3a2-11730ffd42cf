events {}

http {
    upstream core {
        server host.docker.internal:3000;
    }

    upstream healthis-ai {
        server host.docker.internal:3001;
    }

    server {
        listen 5005;

        # Routes for healthis-ai
        location ~ ^/api/(ailments|sessions|conversations|healer|patient|ai).* {
            proxy_pass http://healthis-ai;
            # Only for local development
            proxy_connect_timeout 300s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
        }

        location ~ ^/socket.io/ {
            proxy_pass http://healthis-ai;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "Upgrade";
        }

        # Default route (everything else goes to core)
        location / {
            proxy_pass http://core;
        }
    }
}
