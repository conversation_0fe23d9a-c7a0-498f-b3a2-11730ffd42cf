{"root": true, "ignorePatterns": ["**/*"], "plugins": ["@nx"], "overrides": [{"files": ["*.ts", "*.tsx", "*.js", "*.jsx"], "rules": {"@nx/enforce-module-boundaries": "off"}}, {"files": ["*.ts", "*.tsx"], "extends": ["plugin:@nx/typescript"], "rules": {}}, {"files": ["*.js", "*.jsx"], "extends": ["plugin:@nx/javascript"], "rules": {}}, {"files": ["*.spec.ts", "*.spec.tsx", "*.spec.js", "*.spec.jsx"], "env": {"jest": true}, "rules": {}}]}