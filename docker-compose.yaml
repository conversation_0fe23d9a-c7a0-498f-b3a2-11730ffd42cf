services:
  redis:
    image: 'redis:7.4-alpine'
    ports:
      - '6379:6379'
    networks:
      - healthis-main

  database:
    restart: always
    container_name: database
    image: 'postgres:latest'
    ports:
      - '5432:5432'
    env_file:
      - .env
    networks:
      - healthis-main
    volumes:
      - ./db-data:/var/lib/postgresql/data:rw
  #      - ./db-init.sql:/docker-entrypoint-initdb.d/init.sql

  rabbitmq:
    restart: always
    image: rabbitmq:4.1.1-management
    ports:
      - 5672:5672
      - 15672:15672
    env_file:
      - .env
    networks:
      - healthis-main

  reverse-proxy:
    image: nginx:latest
    container_name: nginx-proxy
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    ports:
      - '5005:5005'

  core:
    restart: always
    ports:
      - '3000:80'
    env_file:
      - .env
    networks:
      - healthis-main
    depends_on:
      - database
    build:
      context: .
      dockerfile: ./docker/microservices/DOCKERFILE
      target: prod
      args:
        - APP_NAME=core
    healthcheck:
      test: wget --no-verbose --tries=1 --spider http://localhost || exit 1
      interval: 60s
      timeout: 30s
      retries: 5
      start_period: 30s

  ai:
    restart: always
    ports:
      - '3001:80'
    env_file:
      - .env
    networks:
      - healthis-main
    depends_on:
      - database
    #entrypoint: ["tail","-f","/dev/null"]
    build:
      context: .
      dockerfile: ./docker/microservices/DOCKERFILE
      target: prod
      args:
        - APP_NAME=ai
    healthcheck:
      test: wget --no-verbose --tries=1 --spider http://localhost || exit 1
      interval: 60s
      timeout: 30s
      retries: 5
      start_period: 30s

  scheduler:
    restart: always
    ports:
      - '4000:80'
    env_file:
      - .env
    networks:
      - healthis-main
    depends_on:
      - database
    build:
      context: .
      dockerfile: ./docker/microservices/DOCKERFILE
      target: prod
      args:
        - APP_NAME=scheduler
    healthcheck:
      test: wget --no-verbose --tries=1 --spider http://localhost || exit 1
      interval: 60s
      timeout: 30s
      retries: 5
      start_period: 30s
networks:
  healthis-main:
    driver: bridge
