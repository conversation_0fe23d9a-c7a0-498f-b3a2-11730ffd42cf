name: AKS Deployment

on:
  push:
    branches:
      - develop
      - qa
      - staging
      - main
    tags:
      - v*

jobs:
  build_containers:
    name: Container build and push to ACR
    runs-on: ${{ github.ref_name }}
    environment:
      name: ${{ github.ref_name }}
    strategy:
      matrix:
        microservice: [ai, core, scheduler]
    steps:
      # Checkout the code
      - name: Checkout code
        uses: actions/checkout@v4

      # Create .env file from GitHub Secrets
      - name: Create .env file
        run: |
          echo "${{ secrets.ENV }}" | awk '{gsub(/\r/, ""); print}' > .env

      # Login to ACR
      - name: Login to ACR and push container
        uses: Azure/docker-login@v2
        with:
            login-server: ${{ vars.AZURE_CONTAINER_REGISTRY }}.azurecr.io
            username: ${{ secrets.ACR_USERNAME }}
            password: ${{ secrets.ACR_PASSWORD }}

      - run: |
          docker compose build ${{ matrix.microservice }}
          docker tag healthis_be-${{ matrix.microservice }}:latest ${{ vars.AZURE_CONTAINER_REGISTRY }}.azurecr.io/ennie-api/${{ matrix.microservice }}:${{ github.sha }}
          docker push ${{ vars.AZURE_CONTAINER_REGISTRY }}.azurecr.io/ennie-api/${{ matrix.microservice }}:${{ github.sha }}


  deploy_db_migration:
    name: Database migration
    needs: [build_containers]
    environment:
      name: ${{ github.ref_name }}
    runs-on: ubuntu-latest

    steps:
      # Checkout the code
      - name: Checkout code
        uses: actions/checkout@v4

      # Set up Node.js (ensure the right version is used)
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '22'

      # Create .env file from GitHub Secrets - only contains DB connection string for `yarn db`
      - name: Create .env file
        run: |
          echo "${{ secrets.ENV }}" | awk '{gsub(/\r/, ""); print}' > .env

      # Install dependencies
      - name: Install dependencies
        run: yarn --ignore-scripts --frozen-lockfile

      - name: Azure login
        uses: azure/login@v2
        with:
          creds: ${{ secrets.AZURE_GITHUB_ACTION_CREDS }}

      - name: Whitelist GitHub Runner IP
        uses: azure/cli@v2
        with:
          inlineScript: |
            set -eu
            agentIP=$(curl -s https://api.ipify.org/)
            az postgres flexible-server firewall-rule create -g "${{ vars.PG_RESOURCE_GROUP }}" -n "${{ vars.PG_NAME }}" -r ${{ vars.GH_RUNNER_FIREWALL_RULE_NAME }} --start-ip-address $agentIP --end-ip-address $agentIP

      # Prisma database deployment
      - name: Deploy database schema
        run: yarn db

      - name: Remove GitHub Runner IP
        uses: azure/cli@v2
        with:
          inlineScript: |
            az postgres flexible-server firewall-rule delete -g "${{ vars.PG_RESOURCE_GROUP }}" -n "${{ vars.PG_NAME }}" -r ${{ vars.GH_RUNNER_FIREWALL_RULE_NAME }} -y

  deploy_containers:
    name: Deploy to AKS
    needs: [build_containers]
    environment:
      name: ${{ github.ref_name }}
    runs-on: ${{ github.ref_name }}

    steps:
    # Checkout the code
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Echo current working directory
      run: pwd

    - name: Azure login
      uses: azure/login@v2
      with:
        creds: ${{ secrets.AZURE_GITHUB_ACTION_CREDS }}
    - name: Gets K8s context
      uses: azure/aks-set-context@v4
      with:
          resource-group: ${{ vars.RESOURCE_GROUP }}
          cluster-name: ${{ vars.AKS_CLUSTER_NAME }}
      id: login

    - uses: azure/k8s-bake@v3
      with:
        renderEngine: 'helm'
        helmChart: './charts/ennie-api-services-secure'
        overrideFiles: './charts/ennie-api-services-secure/values-${{ github.ref_name }}.yaml'
        releaseName: 'ennie-app-services'
        # overrides: |
        #   replicas: 2
        helm-version: 'latest'
      id: bake

    - uses: Azure/k8s-deploy@v5.0.3
      with:
        action: deploy
        pull-images: false
        manifests: ${{ steps.bake.outputs.manifestsBundle }}
        images: |
            ${{ vars.AZURE_CONTAINER_REGISTRY }}.azurecr.io/ennie-api/ai:${{ github.sha }}
            ${{ vars.AZURE_CONTAINER_REGISTRY }}.azurecr.io/ennie-api/core:${{ github.sha }}
            ${{ vars.AZURE_CONTAINER_REGISTRY }}.azurecr.io/ennie-api/scheduler:${{ github.sha }}
        private-cluster: true
        resource-group: ${{ vars.RESOURCE_GROUP }}
        name: ${{ vars.AKS_CLUSTER_NAME }}
