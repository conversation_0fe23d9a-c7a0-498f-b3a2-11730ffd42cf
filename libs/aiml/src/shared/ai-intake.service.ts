import {
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import {
  AssistantResponse,
  LibOpenaiService,
  ThreadRunOptions,
} from '../openai/openai.service';
import {
  AI_MESSAGE_INCOMING_TRIGGER,
  ConversationDto,
  LibSessionService,
  QueryParamsDto,
  SessionSubStatusType,
  SenderType,
  SESSION_STATUS,
  SessionStatus,
  WS_SEND,
  ConversationStatus,
  SystemConfigService,
} from '@core_be/global';
import {
  ConversationRepository,
  SessionFollowUpRepository,
  SessionRepository,
  UsersRepository,
} from '@core_be/data-access';
import { healing_sessions, Prisma, PrismaService } from '@core/prisma-client';
import { ConfigService } from '@nestjs/config';
import { SummaryAilmentService } from './summary-ailment.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { WsSendEvent } from '@core_be/global';
import { ClientProxy } from '@nestjs/microservices';
import { Logger } from 'nestjs-pino';
import { v4 as uuidv4 } from 'uuid';

// type AilmentObject = {
//   name: string;
//   level: number;
//   location: string;
//   situation: string;
//   is_situational: boolean;
//   is_pain: boolean;
//   description: string;
// };

@Injectable()
export class LibAIIntakeService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly conversationRepository: ConversationRepository,
    private readonly libOpenAiService: LibOpenaiService,
    private readonly sessionRepository: SessionRepository,
    private readonly configService: ConfigService,
    private readonly summaryAilmentService: SummaryAilmentService,
    private readonly eventEmitter: EventEmitter2,
    private readonly userRepository: UsersRepository,
    private readonly sessionFollowUpRepository: SessionFollowUpRepository,
    private readonly libSessionService: LibSessionService,
    private readonly logger: Logger,
    private readonly systemConfigService: SystemConfigService
  ) {}

  /**
   * This function is used to handle single conversation message from the frontend with endpoint `/conversations`
   *
   * Depending on AI response, it will continue the conversation in `IN_PROGRESS` state, or if the agent decide
   * to end the conversation, it will update the session status to `INTAKE` and `COMPLETED` and generate summary and ailment
   */
  public async handleIntakeTextMessage(
    payload: ConversationDto,
    session: healing_sessions,
    emitter: EventEmitter2 | ClientProxy = this.eventEmitter
  ): Promise<Prisma.$conversationsPayload['scalars']> {
    const startTime = Date.now();
    const intakeTextMessageTraceId = uuidv4();
    const sessionId = session.session_id;
    const profileId = payload.profile_id;

    // Get dynamic conversation agent ID
    const conversationAgentId =
      await this.systemConfigService.getIntakeConversationAgentId(
        session.cloud_region!
      );
    if (!conversationAgentId) {
      throw new Error(
        'Intake conversation agent ID not found in system configuration'
      );
    }

    let assistantResponse: AssistantResponse;
    let threadId: string;

    if (!session.thread_id) {
      this.logger.debug({
        event: 'INTAKE_TEXT_MESSAGE_START_NEW_INTAKE_THREAD',
        session_id: sessionId,
        profile_id: profileId,
        message: 'starting new intake thread',
        elapsed_time: Date.now() - startTime,
        trace_id: intakeTextMessageTraceId,
      });
      const { assistantResp, sessionUpdate } = await this.newIntake(
        payload.content,
        conversationAgentId,
        session,
        {
          wsEvent: WS_SEND.CONVERSATION_AI_DELTA,
          profileId: payload.profile_id,
          sessionId: session.session_id,
          emitter,
        }
      );
      assistantResponse = assistantResp;
      threadId = assistantResp.threadId;

      this.logger.log({
        event: 'INTAKE_TEXT_MESSAGE_CREATED_NEW_INTAKE_THREAD',
        session_id: sessionId,
        profile_id: profileId,
        thread_id: threadId,
        elapsed_time: Date.now() - startTime,
        trace_id: intakeTextMessageTraceId,
      });

      if (!sessionUpdate) {
        throw new NotFoundException('Healing Session failed to update.');
      }
    } else {
      threadId = session.thread_id;
      this.logger.debug({
        event: 'INTAKE_TEXT_MESSAGE_CONTINUE_INTAKE_THREAD',
        session_id: sessionId,
        profile_id: profileId,
        thread_id: threadId,
        elapsed_time: Date.now() - startTime,
        trace_id: intakeTextMessageTraceId,
      });

      const assistantResp = await this.continueIntake(
        threadId,
        payload.content,
        conversationAgentId,
        {
          wsEvent: WS_SEND.CONVERSATION_AI_DELTA,
          profileId: profileId,
          sessionId: sessionId,
          emitter,
        },
        session.cloud_region!
      );
      assistantResponse = assistantResp;
      this.logger.log({
        event: 'INTAKE_TEXT_MESSAGE_RECEIVED_ASSISTANT_RESPONSE',
        session_id: sessionId,
        profile_id: profileId,
        thread_id: threadId,
        elapsed_time: Date.now() - startTime,
        trace_id: intakeTextMessageTraceId,
      });
    }

    let conversation: any;
    if (!assistantResponse) {
      conversation = await this.conversationRepository.update({
        where: { conversation_id: payload.conversation_id },
        data: {
          responded_at: new Date(),
          is_null_response: true,
        },
      });

      this.logger.error({
        event: 'INTAKE_TEXT_MESSAGE_RECEIVED_NULL_RESPONSE',
        session_id: sessionId,
        profile_id: profileId,
        thread_id: threadId,
        conversation_id: payload.conversation_id,
        elapsed_time: Date.now() - startTime,
        trace_id: intakeTextMessageTraceId,
      });

      throw new InternalServerErrorException(
        'received a null response from agent'
      );
    }

    const aiRawResponse = assistantResponse?.output.trim() || '';
    const keywordToTrack =
      AI_MESSAGE_INCOMING_TRIGGER.CONVERSATION_COMPLETED.replace(/_/gi, '\\_');
    const isConversationCompleted = assistantResponse?.trigger.includes(
      AI_MESSAGE_INCOMING_TRIGGER.CONVERSATION_COMPLETED
    );

    const aiResponseToUser = isConversationCompleted
      ? aiRawResponse.replace(
          new RegExp(
            `[\\_\\:\\n\\s]*(${keywordToTrack})[\\w\\W\\_\\s\\n\\*\\:]*$`,
            'igm'
          ),
          ''
        )
      : aiRawResponse;

    // Store AI response to user
    const aiResponse: Prisma.conversationsCreateInput = {
      content: aiResponseToUser,
      sender_type: SenderType.AI,
      profiles: {
        connect: {
          profile_id: payload.profile_id,
        },
      },
      healing_sessions: {
        connect: {
          session_id: session.session_id,
        },
      },
      responded_at: new Date(),
      is_null_response: aiResponseToUser === '',
    };

    // Tech Debt: sort out conversation update scenario and logic
    conversation = await this.conversationRepository.update({
      where: { conversation_id: payload.conversation_id },
      data: {
        responded_at: new Date(),
        is_null_response: aiResponse.content === '',
      },
    });

    if (aiResponse.content !== '') {
      conversation = await this.conversationRepository.create(aiResponse);
      this.logger.debug({
        event: 'INTAKE_TEXT_MESSAGE_STORED_ASSISTANT_RESPONSE_TO_DB',
        session_id: sessionId,
        profile_id: profileId,
        thread_id: threadId,
        conversation_id: payload.conversation_id,
        elapsed_time: Date.now() - startTime,
        trace_id: intakeTextMessageTraceId,
      });
    }

    if (isConversationCompleted) {
      const summaryStartTime = Date.now();
      this.logger.log({
        event: 'INTAKE_TEXT_MESSAGE_AGENT_DECIDED_TO_END_INTAKE_CONVERSATION',
        session_id: sessionId,
        profile_id: profileId,
        thread_id: threadId,
        elapsed_time: Date.now() - startTime,
        summary_elapsed_time: Date.now() - summaryStartTime,
        trace_id: intakeTextMessageTraceId,
      });
      // Update session status in database
      await this.libSessionService.update(
        session.session_id,
        {
          status: SessionStatus.INTAKE,
          sub_status: SessionSubStatusType.COMPLETED,
          sub_status_updated_at: new Date(),
          updated_at: new Date(),
        },
        payload.profile_id
      );
      this.logger.debug({
        event: 'INTAKE_TEXT_MESSAGE_UPDATED_SESSION_STATUS_TO_INTAKE_COMPLETED',
        session_id: sessionId,
        profile_id: profileId,
        thread_id: threadId,
        elapsed_time: Date.now() - startTime,
        summary_elapsed_time: Date.now() - summaryStartTime,
        trace_id: intakeTextMessageTraceId,
      });

      // Get dynamic summary agent ID
      const intakeSummaryAgentId =
        await this.systemConfigService.getIntakeSummarizationAgentId(
          session.cloud_region!
        );
      if (!intakeSummaryAgentId) {
        throw new Error(
          'Intake summarization agent ID not found in system configuration'
        );
      }

      const assistantResp =
        await this.libOpenAiService.continueConversationAssistant(
          threadId,
          'conversation_status:COMPLETED',
          intakeSummaryAgentId,
          'user',
          false,
          undefined,
          session.cloud_region!
        );

      this.logger.log({
        event: 'INTAKE_TEXT_MESSAGE_SUMMARIZED_CONVERSATION',
        session_id: sessionId,
        profile_id: profileId,
        thread_id: threadId,
        elapsed_time: Date.now() - startTime,
        summary_elapsed_time: Date.now() - summaryStartTime,
        trace_id: intakeTextMessageTraceId,
      });

      if (assistantResp?.output) {
        const regex = new RegExp(
          `${AI_MESSAGE_INCOMING_TRIGGER.HEALING_SESSION_START}\\s*`,
          'g'
        );

        const sanitizedResponse =
          assistantResp.output.replace(regex, '').trim() || '';

        await this.summaryAilmentService.createSummaryAndAilments(
          sanitizedResponse,
          session.session_id,
          session
        );

        await this.enqueueHealingSession(session, session.profile_id);

        this.logger.log({
          event: 'INTAKE_TEXT_MESSAGE_ENQUEUED_HEALING_SESSION',
          session_id: sessionId,
          profile_id: profileId,
          thread_id: threadId,
          elapsed_time: Date.now() - startTime,
          summary_elapsed_time: Date.now() - summaryStartTime,
          trace_id: intakeTextMessageTraceId,
        });
      } else {
        const errorMessage = 'failed to generate summary';
        this.logger.error({
          event: 'INTAKE_TEXT_MESSAGE_FAILED_TO_GENERATE_SUMMARY',
          session_id: sessionId,
          profile_id: profileId,
          thread_id: threadId,
          elapsed_time: Date.now() - startTime,
          summary_elapsed_time: Date.now() - summaryStartTime,
          trace_id: intakeTextMessageTraceId,
        });
        throw new InternalServerErrorException(errorMessage);
      }
    }
    this.logger.log({
      event: 'INTAKE_TEXT_MESSAGE_CONVERSATION_COMPLETED',
      session_id: sessionId,
      profile_id: profileId,
      thread_id: threadId,
      elapsed_time: Date.now() - startTime,
      trace_id: intakeTextMessageTraceId,
    });
    return conversation;
  }

  public async handleAIConfirmation(
    payload: ConversationDto,
    session: healing_sessions,
    query: QueryParamsDto = {
      skip: 0,
      limit: 5,
      order: 'desc',
    },
    emitter: EventEmitter2 | ClientProxy = this.eventEmitter,
    region?: string
  ): Promise<Prisma.$conversationsPayload['scalars']> {
    const sessionId = session.session_id;
    const profileId = payload.profile_id;
    const startTime = Date.now();
    const aiConfirmationTraceId = uuidv4();

    // Get dynamic conversation agent ID
    const conversationAgentId =
      await this.systemConfigService.getIntakeConversationAgentId(region);
    if (!conversationAgentId) {
      throw new Error(
        'Intake conversation agent ID not found in system configuration'
      );
    }

    let assistantResponse: AssistantResponse;
    let threadId: string;

    if (!session.thread_id) {
      this.logger.debug({
        event: 'INTAKE_TEXT_MESSAGE_START_NEW_AI_CONFIRMATION_THREAD',
        session_id: sessionId,
        profile_id: profileId,
        elapsed_time: Date.now() - startTime,
        trace_id: aiConfirmationTraceId,
      });
      const { assistantResp, sessionUpdate } = await this.newIntake(
        payload.content,
        conversationAgentId, // TODO: determine which agent to use for ai confirmation
        session,
        {
          wsEvent: WS_SEND.CONVERSATION_AI_DELTA,
          profileId: payload.profile_id,
          sessionId: session.session_id,
          emitter,
        }
      );
      assistantResponse = assistantResp;
      threadId = assistantResp.threadId;

      this.logger.log({
        event: 'INTAKE_TEXT_MESSAGE_CREATED_NEW_AI_CONFIRMATION_THREAD',
        session_id: sessionId,
        profile_id: profileId,
        thread_id: threadId,
        elapsed_time: Date.now() - startTime,
        trace_id: aiConfirmationTraceId,
      });
      if (!sessionUpdate) {
        throw new NotFoundException('Healing Session failed to update.');
      }
    } else {
      threadId = session.thread_id;

      this.logger.debug({
        event: 'INTAKE_TEXT_MESSAGE_CONTINUE_AI_CONFIRMATION_THREAD',
        session_id: sessionId,
        profile_id: profileId,
        thread_id: threadId,
        elapsed_time: Date.now() - startTime,
        trace_id: aiConfirmationTraceId,
      });
    }

    const assistantResp = await this.continueIntake(
      threadId,
      payload.content,
      conversationAgentId, // TODO: determine which agent to use for ai confirmation
      {
        wsEvent: WS_SEND.CONVERSATION_AI_DELTA,
        profileId: payload.profile_id,
        sessionId: session.session_id,
        emitter,
      },
      region
    );
    assistantResponse = assistantResp;
    this.logger.log({
      event: 'INTAKE_TEXT_MESSAGE_RECEIVED_ASSISTANT_RESPONSE',
      session_id: sessionId,
      profile_id: profileId,
      thread_id: threadId,
      elapsed_time: Date.now() - startTime,
      trace_id: aiConfirmationTraceId,
    });

    let conversation: any;
    if (!assistantResponse) {
      conversation = await this.conversationRepository.update({
        where: { conversation_id: payload.conversation_id },
        data: {
          responded_at: new Date(),
          is_null_response: true,
        },
      });

      this.logger.error({
        event: 'INTAKE_TEXT_MESSAGE_RECEIVED_NULL_RESPONSE',
        session_id: sessionId,
        profile_id: profileId,
        conversation_id: payload.conversation_id,
        elapsed_time: Date.now() - startTime,
        trace_id: aiConfirmationTraceId,
      });

      throw new InternalServerErrorException('Received a null response');
    }

    const aiRawResponse = assistantResponse?.output.trim() || '';
    const keywordToTrack =
      AI_MESSAGE_INCOMING_TRIGGER.HEALING_SESSION_START.replace(/_/gi, '\\_');
    const isHealingStart = assistantResponse?.trigger.includes(
      AI_MESSAGE_INCOMING_TRIGGER.HEALING_SESSION_START
    );
    const aiResponseToUser = isHealingStart
      ? aiRawResponse.replace(
          new RegExp(
            `[\\_\\:\\n\\s]*(${keywordToTrack})[\\w\\W\\_\\s\\n\\*\\:]*$`,
            'igm'
          ),
          ''
        )
      : aiRawResponse;

    // Store AI response to user
    const aiResponse: Prisma.conversationsCreateInput = {
      content: aiResponseToUser,
      sender_type: SenderType.AI,
      profiles: {
        connect: {
          profile_id: payload.profile_id,
        },
      },
      healing_sessions: {
        connect: {
          session_id: session.session_id,
        },
      },
      responded_at: new Date(),
      is_null_response: aiResponseToUser === '',
    };

    // Tech Debt: sort out conversation update scenario and logic
    conversation = await this.conversationRepository.update({
      where: { conversation_id: payload.conversation_id },
      data: {
        responded_at: new Date(),
        is_null_response: aiResponse.content === '',
      },
    });

    if (aiResponse.content !== '') {
      conversation = await this.conversationRepository.create(aiResponse);
      this.logger.debug({
        event: 'INTAKE_TEXT_MESSAGE_STORED_ASSISTANT_RESPONSE_TO_DB',
        session_id: sessionId,
        profile_id: profileId,
        thread_id: threadId,
        elapsed_time: Date.now() - startTime,
        trace_id: aiConfirmationTraceId,
      });
    }

    if (isHealingStart) {
      this.logger.log({
        event: 'INTAKE_TEXT_MESSAGE_AGENT_DECIDED_TO_START_HEALING',
        session_id: sessionId,
        profile_id: profileId,
        thread_id: threadId,
        elapsed_time: Date.now() - startTime,
        trace_id: aiConfirmationTraceId,
      });

      const extractedSummaryResponse = aiRawResponse.replace(
        new RegExp(
          `^[\\w\\W\\:\\_\\n\\s]*(${keywordToTrack})[\\_\\s\\n]*`,
          'igm'
        ),
        ''
      );

      this.startHealing(extractedSummaryResponse, session);
    }

    return conversation;
  }

  async generateIntakeSummary(
    patientInfo: string[],
    conversationStatus: ConversationStatus,
    session: healing_sessions,
    region?: string
  ) {
    const startTime = Date.now();
    const intakeSummaryTraceId = uuidv4();

    this.logger.debug({
      event: 'GENERATE_INTAKE_SUMMARY',
      session_id: session.session_id,
      elapsed_time: Date.now() - startTime,
      trace_id: intakeSummaryTraceId,
    });

    const { assistantResp } = await this.summarizeIntakeConversation(
      [...patientInfo, `conversation_status:${conversationStatus}`].join('\n'),
      session,
      undefined,
      region
    );
    const sanitizedResponse = assistantResp?.output;

    this.logger.log({
      event: 'GENERATE_INTAKE_SUMMARY_SUCCESS',
      session_id: session.session_id,
      elapsed_time: Date.now() - startTime,
      trace_id: intakeSummaryTraceId,
    });

    return sanitizedResponse;
  }

  async ingestIntakeConversationHistory(
    session: healing_sessions,
    conversation: { role: 'user' | 'assistant'; content: string }[]
  ) {
    const startTime = Date.now();

    this.logger.debug({
      event: 'INGEST_INTAKE_CONVERSATION_HISTORY',
      session_id: session.session_id,
      elapsed_time: Date.now() - startTime,
    });

    await this.InsertIntakeConversation(conversation, session);

    this.logger.log({
      event: 'INGEST_INTAKE_CONVERSATION_HISTORY_SUCCESS',
      session_id: session.session_id,
      elapsed_time: Date.now() - startTime,
    });
  }

  async updateInQueueStatus(
    session: healing_sessions,
    emitter: EventEmitter2 | ClientProxy = this.eventEmitter
  ) {
    const queueNumber = await this.libSessionService.getNextQueueNumber();
    const data: Prisma.healing_sessionsUpdateInput = {
      status: SessionStatus.IN_QUEUE,
      sub_status: SessionSubStatusType.AVAILABLE,
      queue_number: queueNumber,
      sub_status_updated_at: new Date(),
      queue_start_time: new Date(),
      updated_at: new Date(),
    };
    const updatedSession = await this.sessionRepository.update({
      where: { session_id: session.session_id },
      data,
    });

    const responsePayload = await this.libSessionService.findOne(
      session.session_id
    );

    new WsSendEvent(
      emitter,
      SESSION_STATUS.STATUS,
      responsePayload,
      updatedSession?.profile_id
    ).emit();
  }

  async newIntake(
    content: string,
    assistantId: string,
    session: healing_sessions,
    wsEventOptions?: ThreadRunOptions['wsEventOptions']
  ): Promise<{
    assistantResp: AssistantResponse;
    sessionUpdate: Prisma.healing_sessionsUpdateInput;
  }> {
    const assistantResp =
      await this.libOpenAiService.createConversationAssistant(
        content,
        assistantId,
        'user',
        false,
        wsEventOptions
      );
    const region = await this.systemConfigService.getActiveRegion();

    const sessionUpdate = await this.sessionRepository.update({
      where: { session_id: session.session_id },
      data: {
        thread_id: assistantResp.threadId,
        cloud_region: region,
      },
    });
    return { assistantResp, sessionUpdate };
  }

  async summarizeIntakeConversation(
    content: string,
    session: healing_sessions,
    wsEventOptions?: ThreadRunOptions['wsEventOptions'],
    region?: string
  ): Promise<{
    assistantResp: AssistantResponse;
    sessionUpdate: Prisma.healing_sessionsUpdateInput;
  }> {
    // Get dynamic summary agent ID
    const summaryAgentId =
      await this.systemConfigService.getIntakeSummarizationAgentId(region);
    if (!summaryAgentId) {
      throw new Error(
        'Intake summarization agent ID not found in system configuration'
      );
    }

    const assistantResp =
      await this.libOpenAiService.createConversationAssistant(
        content,
        summaryAgentId,
        'user',
        false,
        wsEventOptions
      );

    if (assistantResp?.output) {
      const regex = new RegExp(
        `${AI_MESSAGE_INCOMING_TRIGGER.HEALING_SESSION_START}\\s*`,
        'g'
      );

      let sanitizedResponse =
        assistantResp.output.replace(regex, '').trim() || '';
      sanitizedResponse = sanitizedResponse.trim();

      assistantResp.output = sanitizedResponse;
    }
    if (!region) {
      region = await this.systemConfigService.getActiveRegion();
    }
    const sessionUpdate = await this.sessionRepository.update({
      where: { session_id: session.session_id },
      data: {
        thread_id: assistantResp.threadId,
        cloud_region: region,
      },
    });
    return { assistantResp, sessionUpdate };
  }

  async InsertIntakeConversation(
    conversation: { role: 'user' | 'assistant'; content: string }[],
    session: healing_sessions
  ): Promise<{
    sessionUpdate: Prisma.healing_sessionsUpdateInput | null;
  }> {
    let threadId: string;
    0;
    let sessionUpdate: Prisma.healing_sessionsUpdateInput | null = null;
    let region = session.cloud_region;
    if (!session.thread_id) {
      // Current limitation: Bulk endpoint will always inject all messages to the thread, so we need to use new thread for each bulk conversation
      const thread = await this.libOpenAiService.startNewThread();
      region = await this.systemConfigService.getActiveRegion();
      threadId = thread.id;
      sessionUpdate = await this.sessionRepository.update({
        where: { session_id: session.session_id },
        data: {
          thread_id: threadId,
          cloud_region: region,
        },
      });
    } else {
      threadId = session.thread_id;
    }

    for (const message of conversation) {
      await this.libOpenAiService.addMessageToThread(
        threadId,
        message.role,
        message.content,
        region!
      );
    }

    return { sessionUpdate: sessionUpdate };
  }

  private async continueIntake(
    thread_id: string,
    content: string,
    assistantId: string,
    wsEventOptions?: ThreadRunOptions['wsEventOptions'],
    region?: string
  ) {
    return this.libOpenAiService.continueConversationAssistant(
      thread_id,
      content,
      assistantId,
      'user',
      false,
      wsEventOptions,
      region
    );
  }

  public async enqueueHealingSession(
    session: healing_sessions,
    profileId: number
  ) {
    this.logger.debug({
      event: 'ENQUEUE_HEALING_SESSION',
      session_id: session.session_id,
      profile_id: profileId,
    });

    const queueNumber = await this.libSessionService.getNextQueueNumber();

    await this.libSessionService.update(
      session.session_id,
      {
        status: SessionStatus.IN_QUEUE,
        sub_status: SessionSubStatusType.AVAILABLE,
        queue_number: queueNumber,
        sub_status_updated_at: new Date(),
        queue_start_time: new Date(),
        updated_at: new Date(),
      },
      profileId
    );

    this.logger.log({
      event: 'ENQUEUE_HEALING_SESSION_SUCCESS',
      session_id: session.session_id,
      profile_id: profileId,
      queue_number: queueNumber,
    });
  }

  /**
   * The legacy start healing function flow
   * Need to validate if this is still needed
   */
  private async startHealing(
    summaryResponse: string,
    session: healing_sessions
  ) {
    const startTime = Date.now();
    const startHealingTraceId = uuidv4();

    this.logger.log({
      event: 'INTAKE_START_HEALING_SESSION',
      session_id: session.session_id,
      profile_id: session.profile_id,
      elapsed_time: Date.now() - startTime,
      trace_id: startHealingTraceId,
    });

    await this.updateInQueueStatus(session);

    this.logger.debug({
      event: 'INTAKE_UPDATED_IN_QUEUE_STATUS',
      session_id: session.session_id,
      profile_id: session.profile_id,
      elapsed_time: Date.now() - startTime,
      trace_id: startHealingTraceId,
    });

    if (summaryResponse.trim() !== '') {
      const ailmentResponse =
        await this.summaryAilmentService.createSummaryAndAilments(
          summaryResponse,
          session.session_id,
          session,
          undefined,
          undefined
        );

      const userDetail = await this.userRepository.findFirst({
        where: { is_deleted: false, is_active: true },
        include: {
          profiles: {
            where: {
              profile_id: session.profile_id,
            },
          },
        },
      });

      this.logger.log({
        event: 'INTAKE_CREATED_SUMMARY_AND_AILMENTS',
        session_id: session.session_id,
        profile_id: session.profile_id,
        elapsed_time: Date.now() - startTime,
        trace_id: startHealingTraceId,
      });

      if (ailmentResponse.is_zero_pain) {
        await this.sessionRepository.update({
          where: { session_id: session.session_id },
          data: {
            status: SessionStatus.COMPLETED,
            sub_status: SessionStatus.COMPLETED,
            sub_status_updated_at: new Date(),
            updated_at: new Date(),
            session_end_at: new Date(),
            is_follow_up: null,
          },
        });

        if (userDetail?.user_id) {
          await this.sessionFollowUpRepository.create({
            session_id: session.session_id,
            user_id: userDetail.user_id,
            status: 'PENDING',
            follow_up_at: new Date(),
          });
        }

        this.logger.log({
          event: 'INTAKE_COMPLETED_HEALING_SESSION_ZERO_PAIN',
          session_id: session.session_id,
          profile_id: session.profile_id,
          elapsed_time: Date.now() - startTime,
          trace_id: startHealingTraceId,
        });
      }
    }
  }
}
