import { Injectable, InternalServerErrorException, NotFoundException } from '@nestjs/common';
import {
  AssistantResponse,
  LibOpenaiService,
  ThreadRunOptions,
} from '../openai/openai.service';
import {
  AI_MESSAGE_INCOMING_TRIGGER,
  ConversationDto,
  LibSessionService,
  MeasurePerformance,
  QueryParamsDto,
  SessionSubStatusType,
  SenderType,
  SESSION_STATUS,
  SessionStatus,
  WS_SEND,
  ConversationStatus,
} from '@core_be/global';
import {
  ConversationRepository,
  SessionFollowUpRepository,
  SessionRepository,
  UsersRepository,
} from '@core_be/data-access';
import { healing_sessions, Prisma, PrismaService } from '@core/prisma-client';
import { ConfigService } from '@nestjs/config';
import { SummaryAilmentService } from './summary-ailment.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { WsSendEvent } from '@core_be/global';
import { ClientProxy } from '@nestjs/microservices';

// type AilmentObject = {
//   name: string;
//   level: number;
//   location: string;
//   situation: string;
//   is_situational: boolean;
//   is_pain: boolean;
//   description: string;
// };

@Injectable()
export class LibAIIntakeService {
  private readonly assistantId?: string;
  private readonly summaryAgentId?: string;
  private readonly conversationAgentId?: string;

  constructor(
    private readonly prisma: PrismaService,
    private readonly conversationRepository: ConversationRepository,
    private readonly libOpenAiService: LibOpenaiService,
    private readonly sessionRepository: SessionRepository,
    private readonly configService: ConfigService,
    private readonly summaryAilmentService: SummaryAilmentService,
    private readonly eventEmitter: EventEmitter2,
    private readonly userRepository: UsersRepository,
    private readonly sessionFollowUpRepository: SessionFollowUpRepository,
    private readonly libSessionService: LibSessionService
  ) {
    this.assistantId = this.configService.getOrThrow<string>(
      'SUMMARY_ASSISTANT_ID'
    );

    this.conversationAgentId = this.configService.getOrThrow<string>(
      'INTAKE_CONVERSATION_AGENT_ID'
    );
    this.summaryAgentId = this.configService.getOrThrow<string>(
      'INTAKE_SUMMARIZATION_AGENT_ID'
    );
  }

  /**
   * This function is used to handle single conversation message from the frontend with endpoint `/conversations`
   *
   * Depending on AI response, it will continue the conversation in `IN_PROGRESS` state, or if the agent decide
   * to end the conversation, it will update the session status to `INTAKE` and `COMPLETED` and generate summary and ailment
   */
  public async handleIntakeTextMessage(
    payload: ConversationDto,
    session: healing_sessions,
    query: QueryParamsDto = {
      skip: 0,
      limit: 5,
      order: 'desc',
    },
    performance: MeasurePerformance,
    emitter: EventEmitter2 | ClientProxy = this.eventEmitter
  ): Promise<Prisma.$conversationsPayload['scalars']> {
    const sessionId = session.session_id;
    const profileId = payload.profile_id;

    let assistantResponse: AssistantResponse;
    let threadId: string;

    if (!session.thread_id) {
      performance?.info('start new intake thread', {
        session_id: sessionId,
        profile_id: profileId
      });
      const { assistantResp, sessionUpdate } = await this.newIntake(
        payload.content,
        this.conversationAgentId,
        session,
        performance,
        {
          wsEvent: WS_SEND.CONVERSATION_AI_DELTA,
          profileId: payload.profile_id,
          sessionId: session.session_id,
          emitter,
        }
      );
      assistantResponse = assistantResp;
      threadId = assistantResp.threadId;
      performance?.info('created new intake thread', {
        session_id: sessionId,
        profile_id: profileId,
        thread_id: threadId
      });
      if (!sessionUpdate) {
        throw new NotFoundException('Healing Session failed to update.');
      }
    } else {
      threadId = session.thread_id;
      performance?.info('continue intake thread', {
        session_id: sessionId,
        profile_id: profileId,
        thread_id: threadId
      });

      const assistantResp = await this.continueIntake(
        threadId,
        payload.content,
        this.conversationAgentId,
        performance,
        {
          wsEvent: WS_SEND.CONVERSATION_AI_DELTA,
          profileId: profileId,
          sessionId: sessionId,
          emitter,
        }
      );
      assistantResponse = assistantResp;
    }
    performance?.info('received assistant response', {
      session_id: sessionId,
      profile_id: profileId,
      thread_id: threadId
    });

    let conversation: any;
    if (!assistantResponse) {
      conversation = await this.conversationRepository.update({
        where: { conversation_id: payload.conversation_id },
        data: {
          responded_at: new Date(),
          is_null_response: true,
        },
      });

      performance?.warn('received a null response', {
        session_id: sessionId,
        profile_id: profileId,
        conversation_id: payload.conversation_id,
      });

      throw new InternalServerErrorException('Received a null response');
    }

    const aiRawResponse = assistantResponse?.output.trim() || '';
    const keywordToTrack =
      AI_MESSAGE_INCOMING_TRIGGER.CONVERSATION_COMPLETED.replace(/_/gi, '\\_');
    const isConversationCompleted = assistantResponse?.trigger.includes(
      AI_MESSAGE_INCOMING_TRIGGER.CONVERSATION_COMPLETED
    );

    const aiResponseToUser = isConversationCompleted
      ? aiRawResponse.replace(
          new RegExp(
            `[\\_\\:\\n\\s]*(${keywordToTrack})[\\w\\W\\_\\s\\n\\*\\:]*$`,
            'igm'
          ),
          ''
        )
      : aiRawResponse;

    // Store AI response to user
    const aiResponse: Prisma.conversationsCreateInput = {
      content: aiResponseToUser,
      sender_type: SenderType.AI,
      profiles: {
        connect: {
          profile_id: payload.profile_id,
        },
      },
      healing_sessions: {
        connect: {
          session_id: session.session_id,
        },
      },
      responded_at: new Date(),
      is_null_response: aiResponseToUser === '',
    };

    // Tech Debt: sort out conversation update scenario and logic
    conversation = await this.conversationRepository.update({
      where: { conversation_id: payload.conversation_id },
      data: {
        responded_at: new Date(),
        is_null_response: aiResponse.content === '',
      },
    });

    if (aiResponse.content !== '') {
      conversation = await this.conversationRepository.create(aiResponse);
      performance?.push('stored assistant response to db');
    }

    if (isConversationCompleted) {
      performance?.info('agent decided to end the intake conversation', {
        session_id: sessionId,
        profile_id: profileId,
      });
      // Update session status in database
      await this.libSessionService.update(
        session.session_id,
        {
          status: SessionStatus.INTAKE,
          sub_status: SessionSubStatusType.COMPLETED,
          sub_status_updated_at: new Date(),
          updated_at: new Date(),
        },
        payload.profile_id
      );
      performance?.push('updated session status to INTAKE.COMPLETED');

      const assistantResp =
        await this.libOpenAiService.continueConversationAssistant(
          threadId,
          "conversation_status:COMPLETED",
          this.summaryAgentId,
          'user',
          false,
          performance
        );

      performance?.info('summarized conversation', {
        session_id: sessionId,
        profile_id: profileId,
      });

      if (assistantResp?.output) {
        const regex = new RegExp(
          `${AI_MESSAGE_INCOMING_TRIGGER.HEALING_SESSION_START}\\s*`,
          'g'
        );

        const sanitizedResponse =
        assistantResp.output.replace(regex, '').trim() || '';

        await this.summaryAilmentService.createSummaryAndAilments(
          sanitizedResponse,
          session.session_id,
          session
        );

        await this.enqueueHealingSession(session, session.profile_id);
      } else {
        const errorMessage = 'failed to generate summary';
        performance?.warn(errorMessage, {
          session_id: sessionId,
          profile_id: profileId,
        });
        throw new InternalServerErrorException(errorMessage);
      }
    }
    performance?.info('conversation completed', {
      session_id: sessionId,
      profile_id: profileId,
      thread_id: threadId,
    });
    return conversation;
  }

  public async handleAIConfirmation(
    payload: ConversationDto,
    session: healing_sessions,
    query: QueryParamsDto = {
      skip: 0,
      limit: 5,
      order: 'desc',
    },
    performance: MeasurePerformance,
    emitter: EventEmitter2 | ClientProxy = this.eventEmitter
  ): Promise<Prisma.$conversationsPayload['scalars']> {
    const sessionId = session.session_id;
    const profileId = payload.profile_id;

    let assistantResponse: AssistantResponse;
    let threadId: string;

    if (!session.thread_id) {
      performance?.info('start new ai confirmation thread', {
        session_id: sessionId,
        profile_id: profileId
      });
      const { assistantResp, sessionUpdate } = await this.newIntake(
        payload.content,
        this.assistantId, // TODO: determine which agent to use for ai confirmation
        session,
        performance,
        {
          wsEvent: WS_SEND.CONVERSATION_AI_DELTA,
          profileId: payload.profile_id,
          sessionId: session.session_id,
          emitter,
        }
      );
      assistantResponse = assistantResp;
      threadId = assistantResp.threadId;
      performance?.info('created new ai confirmation thread', {
        session_id: sessionId,
        profile_id: profileId,
        thread_id: threadId
      });
      if (!sessionUpdate) {
        throw new NotFoundException('Healing Session failed to update.');
      }
    } else {
      threadId = session.thread_id;
      performance?.info('continue ai confirmation thread', {
        session_id: sessionId,
        profile_id: profileId,
        thread_id: threadId
      });

      const assistantResp = await this.continueIntake(
        session.thread_id,
        payload.content,
        this.assistantId, // TODO: determine which agent to use for ai confirmation
        performance,
        {
          wsEvent: WS_SEND.CONVERSATION_AI_DELTA,
          profileId: payload.profile_id,
          sessionId: session.session_id,
          emitter,
        }
      );
      assistantResponse = assistantResp;
    }
    performance?.info('received assistant response', {
      session_id: sessionId,
      profile_id: profileId,
      thread_id: threadId
    });

    let conversation: any;
    if (!assistantResponse) {
      conversation = await this.conversationRepository.update({
        where: { conversation_id: payload.conversation_id },
        data: {
          responded_at: new Date(),
          is_null_response: true,
        },
      });

      performance?.warn('received a null response', {
        session_id: sessionId,
        profile_id: profileId,
        conversation_id: payload.conversation_id,
      });

      throw new InternalServerErrorException('Received a null response');
    }

    const aiRawResponse = assistantResponse?.output.trim() || '';
    const keywordToTrack =
      AI_MESSAGE_INCOMING_TRIGGER.HEALING_SESSION_START.replace(/_/gi, '\\_');
    const isHealingStart = assistantResponse?.trigger.includes(
      AI_MESSAGE_INCOMING_TRIGGER.HEALING_SESSION_START
    );
    const aiResponseToUser = isHealingStart
      ? aiRawResponse.replace(
          new RegExp(
            `[\\_\\:\\n\\s]*(${keywordToTrack})[\\w\\W\\_\\s\\n\\*\\:]*$`,
            'igm'
          ),
          ''
        )
      : aiRawResponse;

    // Store AI response to user
    const aiResponse: Prisma.conversationsCreateInput = {
      content: aiResponseToUser,
      sender_type: SenderType.AI,
      profiles: {
        connect: {
          profile_id: payload.profile_id,
        },
      },
      healing_sessions: {
        connect: {
          session_id: session.session_id,
        },
      },
      responded_at: new Date(),
      is_null_response: aiResponseToUser === '',
    };

    // Tech Debt: sort out conversation update scenario and logic
    conversation = await this.conversationRepository.update({
      where: { conversation_id: payload.conversation_id },
      data: {
        responded_at: new Date(),
        is_null_response: aiResponse.content === '',
      },
    });

    if (aiResponse.content !== '') {
      conversation = await this.conversationRepository.create(aiResponse);
      performance?.push('stored assistant response to db');
    }

    if (isHealingStart) {
      performance?.info('agent decided to start healing', {
        session_id: sessionId,
        profile_id: profileId,
      });

      const extractedSummaryResponse = aiRawResponse.replace(
        new RegExp(
          `^[\\w\\W\\:\\_\\n\\s]*(${keywordToTrack})[\\_\\s\\n]*`,
          'igm'
        ),
        ''
      );

      this.startHealing(extractedSummaryResponse, session, performance);
    }

    return conversation;
  }

  async generateIntakeSummary(
    patientInfo: string[],
    conversationStatus: ConversationStatus,
    session: healing_sessions,
    performance?: MeasurePerformance
  ) {
    const { assistantResp } =
      await this.summarizeIntakeConversation(
        [
          ...patientInfo,
          `conversation_status:${conversationStatus}`,
        ].join('\n'),
        session,
        performance
      );
    const sanitizedResponse = assistantResp?.output;
    performance?.info('summarized intake conversation', {
      session_id: session.session_id
    });

    return sanitizedResponse;
  }

  async ingestIntakeConversationHistory(
    session: healing_sessions,
    conversation: { role: 'user' | 'assistant'; content: string }[],
    performance?: MeasurePerformance
  ) {
    await this.InsertIntakeConversation(
      conversation,
      session,
      performance
    );

    performance?.info('summarized intake conversation', {
      session_id: session.session_id
    });

    performance?.push('ingested intake conversation history');
  }

  async updateInQueueStatus(
    session: healing_sessions,
    emitter: EventEmitter2 | ClientProxy = this.eventEmitter
  ) {
    const queueNumber = await this.libSessionService.getNextQueueNumber();
    const data: Prisma.healing_sessionsUpdateInput = {
      status: SessionStatus.IN_QUEUE,
      sub_status: SessionSubStatusType.AVAILABLE,
      queue_number: queueNumber,
      sub_status_updated_at: new Date(),
      queue_start_time: new Date(),
      updated_at: new Date(),
    };
    const updatedSession = await this.sessionRepository.update({
      where: { session_id: session.session_id },
      data,
    });

    const responsePayload = await this.libSessionService.findOne(
      session.session_id
    );

    new WsSendEvent(
      emitter,
      SESSION_STATUS.STATUS,
      responsePayload,
      updatedSession?.profile_id
    ).emit();
  }

  async newIntake(
    content: string,
    assistantId: string | undefined,
    session: healing_sessions,
    performance?: MeasurePerformance,
    wsEventOptions?: ThreadRunOptions['wsEventOptions']
  ): Promise<{
    assistantResp: AssistantResponse;
    sessionUpdate: Prisma.healing_sessionsUpdateInput;
  }> {
    const assistantResp =
      await this.libOpenAiService.createConversationAssistant(
        content,
        assistantId,
        'user',
        false,
        performance,
        wsEventOptions
      );

    const sessionUpdate = await this.sessionRepository.update({
      where: { session_id: session.session_id },
      data: {
        thread_id: assistantResp.threadId,
      },
    });
    return { assistantResp, sessionUpdate };
  }

  async summarizeIntakeConversation(
    content: string,
    session: healing_sessions,
    performance?: MeasurePerformance,
    wsEventOptions?: ThreadRunOptions['wsEventOptions']
  ): Promise<{
    assistantResp: AssistantResponse;
    sessionUpdate: Prisma.healing_sessionsUpdateInput;
  }> {
    const assistantResp =
      await this.libOpenAiService.createConversationAssistant(
        content,
        this.summaryAgentId,
        'user',
        false,
        performance,
        wsEventOptions
      );

    if (assistantResp?.output) {
      const regex = new RegExp(
        `${AI_MESSAGE_INCOMING_TRIGGER.HEALING_SESSION_START}\\s*`,
        'g'
      );

      let sanitizedResponse =
        assistantResp.output.replace(regex, '').trim() || '';
      sanitizedResponse = sanitizedResponse.trim();

      assistantResp.output = sanitizedResponse;
    }

    const sessionUpdate = await this.sessionRepository.update({
      where: { session_id: session.session_id },
      data: {
        thread_id: assistantResp.threadId,
      },
    });
    return { assistantResp, sessionUpdate };
  }

  async InsertIntakeConversation(
    conversation: { role: 'user' | 'assistant'; content: string }[],
    session: healing_sessions,
    performance?: MeasurePerformance,
  ): Promise<{
    sessionUpdate: Prisma.healing_sessionsUpdateInput | null;
  }> {
    let threadId: string;0
    let sessionUpdate: Prisma.healing_sessionsUpdateInput | null = null;

    if (!session.thread_id) {
    // Current limitation: Bulk endpoint will always inject all messages to the thread, so we need to use new thread for each bulk conversation
      const thread = await this.libOpenAiService.startNewThread(performance);
      threadId = thread.id;
      sessionUpdate = await this.sessionRepository.update({
        where: { session_id: session.session_id },
        data: {
          thread_id: threadId,
        },
      });
    } else {
      threadId = session.thread_id;
    }

    for (const message of conversation) {
      await this.libOpenAiService.addMessageToThread(
        threadId,
        message.role,
        message.content,
        performance
      );
    }

    return { sessionUpdate: sessionUpdate };
  }

  private async continueIntake(
    thread_id: string,
    content: string,
    assistantId: string | undefined,
    performance?: MeasurePerformance,
    wsEventOptions?: ThreadRunOptions['wsEventOptions']
  ) {
    return this.libOpenAiService.continueConversationAssistant(
      thread_id,
      content,
      assistantId,
      'user',
      false,
      performance,
      wsEventOptions
    );
  }

  public async enqueueHealingSession(session: healing_sessions, profileId: number, performance?: MeasurePerformance) {
    const queueNumber = await this.libSessionService.getNextQueueNumber();

    await this.libSessionService.update(
      session.session_id,
      {
        status: SessionStatus.IN_QUEUE,
        sub_status: SessionSubStatusType.AVAILABLE,
        queue_number: queueNumber,
        sub_status_updated_at: new Date(),
        queue_start_time: new Date(),
        updated_at: new Date(),
      },
      profileId
    );

    performance?.info('enqueued healing session', {
      session_id: session.session_id,
      queue_number: queueNumber,
    });
  }

  /**
   * The legacy start healing function flow
   * Need to validate if this is still needed
   */
  private async startHealing(
    summaryResponse: string,
    session: healing_sessions,
    performance?: MeasurePerformance
  ) {
    performance?.info('start healing', {
      session_id: session.session_id,
    });

    await this.updateInQueueStatus(session);

    if (summaryResponse.trim() !== '') {
      const ailmentResponse =
        await this.summaryAilmentService.createSummaryAndAilments(
          summaryResponse,
          session.session_id,
          session,
          undefined,
          undefined,
          performance
        );

      performance?.push('summary & ailment created healing round');

      const userDetail = await this.userRepository.findFirst({
        where: { is_deleted: false, is_active: true },
        include: {
          profiles: {
            where: {
              profile_id: session.profile_id,
            },
          },
        },
      });

      performance?.push('obtained user detail for healing round');

      if (ailmentResponse.is_zero_pain) {
        performance?.info('user has no pain, completing session', {
          session_id: session.session_id,
        });

        await this.sessionRepository.update({
          where: { session_id: session.session_id },
          data: {
            status: SessionStatus.COMPLETED,
            sub_status: SessionStatus.COMPLETED,
            sub_status_updated_at: new Date(),
            updated_at: new Date(),
            session_end_at: new Date(),
            is_follow_up: null,
          },
        });

        if (userDetail?.user_id) {
          await this.sessionFollowUpRepository.create({
            session_id: session.session_id,
            user_id: userDetail.user_id,
            status: 'PENDING',
            follow_up_at: new Date(),
          });

          performance?.info('created follow up for user', {
            session_id: session.session_id,
            user_id: userDetail.user_id,
          });
        }
      } else {
        performance?.warn('user still has pain, but we are doing nothing, confirm is the flow ending correctly', {
          session_id: session.session_id,
        });
      }
    }
  }
}
