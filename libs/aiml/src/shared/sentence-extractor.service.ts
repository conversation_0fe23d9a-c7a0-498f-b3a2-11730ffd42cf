// SentenceExtractor.ts

import nlp from 'compromise';

export class SentenceExtractor {
  private buffer: string;

  constructor() {
    this.buffer = '';
  }

  /**
   * Processes incoming text chunks and extracts complete sentences.
   * @param newText - The new chunk of text received.
   * @returns An array of complete sentences extracted.
   */
  public processText(newText: string): string[] {
    // Append new text to the buffer
    this.buffer += newText.replace(/\\n[-]*/ig,'');

    // Use Compromise to parse sentences
    const doc = nlp(this.buffer);
    const sentences = doc.sentences().out('array');
    const completeSentences: string[] = [];

    if (sentences.length === 0) {
      // No complete sentences found yet
      return completeSentences;
    }

    // Determine the position where the last complete sentence ends
    let lastCompleteIndex = 0;

    // Iterate through sentences to determine completeness
    for (let i = 0; i < sentences.length; i++) {
      const sentence = sentences[i].trim();
      // Heuristics to determine if the sentence is complete
      // A simple approach: Check if the sentence ends with a sentence-ending punctuation
      if (/[.!?:]["')\]\\n-]*$/.test(sentence)) {
        completeSentences.push(sentence);
        // Update the last complete index to the end of this sentence in the buffer
        lastCompleteIndex = this.buffer.indexOf(sentence, lastCompleteIndex) + sentence.length;
      } else {
        // Incomplete sentence detected; stop processing further
        break;
      }
    }

    if (completeSentences.length > 0) {
      // Remove the extracted sentences from the buffer
      this.buffer = this.buffer.slice(lastCompleteIndex);
    }

    return completeSentences;
  }

  /**
   * Flushes any remaining text in the buffer as the final sentence.
   * Should be called when the stream ends.
   * @returns An array containing the last sentence, if any.
   */
  public flush(): string[] {
    const trimmed = this.buffer.trim();
    if (trimmed.length > 0) {
      return [trimmed];
    }
    return [];
  }
}
