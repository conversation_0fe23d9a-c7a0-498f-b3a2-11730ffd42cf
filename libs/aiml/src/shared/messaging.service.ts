import { Injectable } from '@nestjs/common';
import { AssistantResponse } from '../openai/openai.service';
import {
  AI_MESSAGE_INCOMING_TRIGGER,
  MessageType,
  ProfileType,
  SenderType,
} from '@core_be/global';
import { ConversationRepository } from '@core_be/data-access';
import { conversations } from '@core/prisma-client';

type IncomingAIMessage = {
  isResponseToPatient: boolean;
  isResponseToHealer: boolean;
  isHealingRoundCompleted: boolean;
};

@Injectable()
export class AIMessagingService {
  constructor(private conversationRepository: ConversationRepository) {}

  public processIncomingMessages(
    content: AssistantResponse
  ): IncomingAIMessage {
    const isResponseToPatient = content.trigger.includes(
      AI_MESSAGE_INCOMING_TRIGGER.HEALING_ROUND_MESSAGE_TO_PATIENT
    );
    const isResponseToHealer = content.trigger.includes(
      AI_MESSAGE_INCOMING_TRIGGER.HEALING_ROUND_MESSAGE_TO_ENERGY_HEALER
    );
    const isHealingRoundCompleted = content.trigger.includes(
      AI_MESSAGE_INCOMING_TRIGGER.HEALING_ROUND_COMPLETED
    );
    return {
      isResponseToPatient,
      isHealingRoundCompleted,
      isResponseToHealer,
    };
  }

  public async messageToEntity(opts: {
    message: string;
    round_id: number;
    profile_id: number;
    session_id: number;
    profile_type: ProfileType;
    entityConversation?: conversations;
  }) {
    const { message, round_id, profile_id, session_id, profile_type } = opts;
    const healerRegex = new RegExp(
      `[\\w\\W\\s]*${AI_MESSAGE_INCOMING_TRIGGER.HEALING_ROUND_MESSAGE_TO_ENERGY_HEALER}|${AI_MESSAGE_INCOMING_TRIGGER.HEALING_ROUND_MESSAGE_TO_PATIENT}[\\w\\W\\s]*$`,
      'ig'
    );
    const patientRegex = new RegExp(
      `[\\w\\W\\s]*${AI_MESSAGE_INCOMING_TRIGGER.HEALING_ROUND_MESSAGE_TO_PATIENT}|${AI_MESSAGE_INCOMING_TRIGGER.HEALING_ROUND_MESSAGE_TO_ENERGY_HEALER}[\\w\\W\\s]*$`,
      'ig'
    );
    let content = ``;
    if (profile_type == ProfileType.HEALER) {
      content = message.replace(healerRegex, '');
    } else if (profile_type == ProfileType.PATIENT) {
      content = message.replace(patientRegex, '');
    }

    if (content.trim() === '') {
      if (opts.entityConversation && profile_type == ProfileType.PATIENT) {
        return await this.conversationRepository.update({
          where: {
            conversation_id: opts.entityConversation.conversation_id,
          },
          data: {
            responded_at: new Date(),
            is_null_response: true,
          },
        });
      }
      return false;
    }

    return this.conversationRepository.create({
      sender_type: SenderType.AI,
      healing_rounds: {
        connect: {
          round_id,
        },
      },
      profiles: {
        connect: {
          profile_id,
        },
      },
      healing_sessions: {
        connect: {
          session_id,
        },
      },
      message_type: MessageType.Text,
      content: content.trim(),
      responded_at: new Date(),
      is_null_response: false,
    });
  }
}
