import { Injectable } from '@nestjs/common';
import { MetricsRepository } from '@core_be/data-access';
import { OnEvent } from '@nestjs/event-emitter';
import { ANALYTICS_LISTENER } from '@core/libs';

@Injectable()
export class LibAnalyticsService {
  constructor(private readonly metricsRepository: MetricsRepository) {}

  @OnEvent(ANALYTICS_LISTENER.CREATE)
  async analytics(data: any) {
    for (const metrics of data.data) {
      if (metrics.session_start) {
        metrics.session_start = new Date(metrics.session_start);
      }
      if (metrics.session_end) {
        metrics.session_end = new Date(metrics.session_end);
      }

      await this.metricsRepository.create(metrics);
    }
  }
}
