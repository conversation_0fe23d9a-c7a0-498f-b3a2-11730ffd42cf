import {
  BadRequestException,
  ForbiddenException,
  Inject,
  Injectable,
} from '@nestjs/common';
import {
  AssistantResponse,
  LibOpenaiService,
  ThreadRunOptions,
} from '../openai/openai.service';
import {
  AI_MESSAGE_INCOMING_TRIGGER,
  AI_MESSAGE_OUTGOING_TRIGGER,
  AiHealingRoundCheckInEvent,
  AiHealingRoundEvent,
  CONVERSATION_LISTEN,
  CreateHealingRoundDto,
  HEALING_ROUND_LISTEN,
  HealingRoundStatus,
  HealingRoundSubStatus,
  LibProfileService,
  LibSessionService,
  ProfileType,
  SenderType,
  SessionSubStatusType,
  SessionFollowUpStatus,
  SessionStatus,
  WS_LISTEN,
  ConversationStatus,
  ConversationType,
  SystemConfigService,
} from '@core_be/global';
import {
  ConversationRepository,
  HealingRoundRepository,
  SessionAilmentRepository,
  SessionFollowUpRepository,
  SessionRepository,
  SummaryRepository,
  UsersRepository,
} from '@core_be/data-access';
import { ConfigService } from '@nestjs/config';
import { AIMessagingService } from './messaging.service';
import { conversations, Prisma } from '@core/prisma-client';
import { OnEvent } from '@nestjs/event-emitter';
import { SummaryAilmentService } from './summary-ailment.service';
import { ClientProxy } from '@nestjs/microservices';
import { Logger } from 'nestjs-pino';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class AIHealingRoundService {
  private readonly HEALING_ROUND_HEALER_IN_PROGRESS_TIMEOUT: number;

  constructor(
    private readonly configService: ConfigService,
    private readonly profileService: LibProfileService,
    private readonly healingRoundRepository: HealingRoundRepository,
    private readonly sessionRepository: SessionRepository,
    private readonly summaryRepository: SummaryRepository,
    private readonly sessionAilmentRepository: SessionAilmentRepository,
    private readonly aiMessagingService: AIMessagingService,
    private readonly libOpenAiService: LibOpenaiService,
    private readonly summaryAilmentService: SummaryAilmentService,
    private readonly conversationRepository: ConversationRepository,
    private readonly sessionFollowUpRepository: SessionFollowUpRepository,
    private readonly userRepository: UsersRepository,
    private readonly libSessionService: LibSessionService,
    private readonly logger: Logger,
    private readonly systemConfigService: SystemConfigService,
    @Inject(WS_LISTEN.DELIMITER) private wsClient: ClientProxy
  ) {
    this.HEALING_ROUND_HEALER_IN_PROGRESS_TIMEOUT =
      parseInt(
        '' + this.configService.get('HEALING_ROUND_HEALER_IN_PROGRESS_TIMEOUT')
      ) || 1500000;
  }

  async executeHealingRoundThread(params: {
    sessionId: number;
    healing_round_id: number;
    patientInfo: string[];
    assistant_thread_id?: string;
    summaryContent?: string;
    conversationStatus?: ConversationStatus;
    conversationType?: ConversationType;
    wsEventOptions?: ThreadRunOptions['wsEventOptions'];
    region?: string;
  }): Promise<{
    assistantResp: AssistantResponse;
  }> {
    const {
      sessionId,
      healing_round_id,
      patientInfo,
      assistant_thread_id,
      summaryContent,
      conversationStatus,
      conversationType,
      wsEventOptions,
      region,
    } = params;

    const startTime = Date.now();
    const healingRoundThreadTraceId = uuidv4();

    // Get dynamic healing round assistant ID
    const healingRoundAssistantID =
      await this.systemConfigService.getHealingRoundAssistantId(region);
    if (!healingRoundAssistantID) {
      throw new Error(
        'Healing round assistant ID not found in system configuration'
      );
    }

    const userPromptArray: string[] = [];
    if (summaryContent) {
      userPromptArray.push(`__CASE_SUMMARY__: ${summaryContent}`);
    }
    if (patientInfo) {
      userPromptArray.push(`__CONVERSATION__:`);
      userPromptArray.push(...patientInfo);
    }
    if (conversationStatus) {
      userPromptArray.push(`__CONVERSATION_STATUS__: ${conversationStatus}`);
    }
    if (conversationType) {
      userPromptArray.push(`__CONVERSATION_TYPE__: ${conversationType}`);
    }
    const userPrompt = userPromptArray.join('\n');

    let assistantResp: AssistantResponse;
    if (assistant_thread_id) {
      assistantResp = await this.libOpenAiService.continueConversationAssistant(
        assistant_thread_id,
        userPrompt,
        healingRoundAssistantID,
        'user',
        false,
        wsEventOptions,
        region
      );

      this.logger.log({
        event: 'HEALING_ROUND_THREAD_CONTINUED',
        session_id: sessionId,
        round_id: healing_round_id,
        thread_id: assistant_thread_id,
        elapsed_time: Date.now() - startTime,
        trace_id: healingRoundThreadTraceId,
      });
    } else {
      assistantResp = await this.libOpenAiService.createConversationAssistant(
        userPrompt,
        healingRoundAssistantID,
        'user',
        false,
        wsEventOptions
      );
      const threadId = assistantResp.threadId;

      this.logger.log({
        event: 'HEALING_ROUND_THREAD_CREATED',
        session_id: sessionId,
        round_id: healing_round_id,
        thread_id: threadId,
        elapsed_time: Date.now() - startTime,
        trace_id: healingRoundThreadTraceId,
      });

      await this.healingRoundRepository.update({
        where: { round_id: healing_round_id },
        data: {
          assistant_thread_id: threadId,
          cloud_region: region,
        },
      });
    }
    await this._validateHealingRoundAgentResponse(
      assistantResp,
      conversationStatus,
      assistant_thread_id || assistantResp.threadId,
      userPrompt,
      healingRoundAssistantID,
      wsEventOptions,
      region
    );
    return { assistantResp };
  }

  private async _validateHealingRoundAgentResponse(
    assistantResp: AssistantResponse,
    conversationStatus?: ConversationStatus,
    assistant_thread_id?: string,
    userPrompt?: string,
    healingRoundAgentID?: string,
    wsEventOptions?: ThreadRunOptions['wsEventOptions'],
    region?: string,
    maxRetries: number = 3
  ): Promise<void> {
    // Only validate if conversation status is COMPLETED
    if (conversationStatus !== ConversationStatus.COMPLETED) {
      return;
    }

    // Check if response contains __TO_USER__: trigger
    const containsToUserTrigger = assistantResp.output?.includes(
      AI_MESSAGE_INCOMING_TRIGGER.HEALING_ROUND_MESSAGE_TO_PATIENT
    );

    if (!containsToUserTrigger) {
      return; // Response is valid, no action needed
    }

    // If we have the necessary parameters, try to get a new response
    if (assistant_thread_id && userPrompt && healingRoundAgentID) {
      this.logger.warn({
        event: 'HEALING_ROUND_INVALID_RESPONSE_DETECTED',
        message:
          'Response contains __TO_USER__: when status is COMPLETED, retrying...',
        original_output: assistantResp.output,
      });

      let retryCount = 0;
      while (retryCount < maxRetries) {
        try {
          // Add a clarification prompt to avoid the same mistake
          const clarificationPrompt = `${userPrompt}\n\nIMPORTANT: The __CONVERSATION_STATUS__ is COMPLETED. Do not send messages to the user with __TO_USER__: prefix. Generate a summary only.`;

          const newAssistantResp =
            await this.libOpenAiService.continueConversationAssistant(
              assistant_thread_id,
              clarificationPrompt,
              healingRoundAgentID,
              'user',
              false,
              wsEventOptions,
              region
            );

          // Check if the new response is valid
          const stillContainsToUserTrigger = newAssistantResp.output?.includes(
            AI_MESSAGE_INCOMING_TRIGGER.HEALING_ROUND_MESSAGE_TO_PATIENT
          );

          if (!stillContainsToUserTrigger) {
            // Update the original response with the corrected one
            assistantResp.output = newAssistantResp.output;
            assistantResp.trigger = newAssistantResp.trigger;

            this.logger.log({
              event: 'HEALING_ROUND_RESPONSE_CORRECTED',
              message: 'Successfully obtained corrected response',
              retry_count: retryCount + 1,
              corrected_output: newAssistantResp.output,
            });

            return; // Success, exit the method
          }

          retryCount++;
          this.logger.warn({
            event: 'HEALING_ROUND_RETRY_STILL_INVALID',
            message: 'Retry still contains __TO_USER__: trigger',
            retry_count: retryCount,
            max_retries: maxRetries,
          });
        } catch (error) {
          retryCount++;
          this.logger.error({
            event: 'HEALING_ROUND_RETRY_ERROR',
            message: 'Error during response retry',
            retry_count: retryCount,
            error_message:
              error instanceof Error ? error.message : 'unknown error',
          });

          if (retryCount >= maxRetries) {
            break;
          }
        }
      }

      // If we've exhausted retries, log a warning but don't throw an error
      this.logger.error({
        event: 'HEALING_ROUND_VALIDATION_FAILED',
        message:
          'Failed to get valid response after retries, proceeding with original',
        max_retries: maxRetries,
        final_output: assistantResp.output,
      });
    } else {
      // Log warning if we can't retry due to missing parameters
      this.logger.warn({
        event: 'HEALING_ROUND_VALIDATION_SKIPPED',
        message: 'Cannot retry response due to missing parameters',
        has_thread_id: !!assistant_thread_id,
        has_user_prompt: !!userPrompt,
        has_assistant_id: !!healingRoundAgentID,
      });
    }
  }

  @OnEvent(HEALING_ROUND_LISTEN.HEALING_ROUND_START, {
    async: true,
    promisify: true,
    nextTick: true,
  })
  public async processHealingRoundStart(
    createRoundInput: CreateHealingRoundDto,
    userId?: number,
    skipUserCheck = false
  ) {
    const startTime = Date.now();
    const healingRoundStartTraceId = uuidv4();
    try {
      // Log method entry
      this.logger.log({
        event: 'HEALING_ROUND_START_PROCESS_START',
        session_id: createRoundInput.session_id,
        profile_id: createRoundInput.profile_id,
        user_id: userId,
        skip_user_check: skipUserCheck,
        message: 'Starting healing round process',
        elapsed_time: Date.now() - startTime,
        trace_id: healingRoundStartTraceId,
      });

      const userProfile = await this.profileService.getProfileById(
        createRoundInput.profile_id
      );

      if (
        !userProfile ||
        userProfile.profile_type == ProfileType.PATIENT ||
        (!skipUserCheck && userProfile?.user_id !== userId)
      ) {
        this.logger.error({
          event: 'HEALING_ROUND_START_INVALID_PROFILE',
          session_id: createRoundInput.session_id,
          profile_id: createRoundInput.profile_id,
          user_id: userId,
          profile_type: userProfile?.profile_type,
          message: 'Invalid profile for healing round start',
          elapsed_time: Date.now() - startTime,
          trace_id: healingRoundStartTraceId,
        });
        throw new ForbiddenException('Invalid Profile');
      }
      let lastHealingRound =
        await this.healingRoundRepository.findOneWithInclude({
          where: {
            session_id: createRoundInput.session_id,
            is_deleted: false,
            healer_id: createRoundInput.profile_id,
          },
          orderBy: { round_id: 'desc' },
          include: {
            conversations: {
              where: {
                sender_type: SenderType.Patient,
                is_deleted: false,
              },
              take: 1,
            },
          },
        });
      const session = lastHealingRound?.healing_sessions
        ? lastHealingRound.healing_sessions
        : await this.sessionRepository.findFirst({
            where: {
              session_id: createRoundInput.session_id,
            },
          });

      if (!session?.healer_id) {
        this.logger.error({
          event: 'HEALING_ROUND_START_INVALID_SESSION',
          session_id: createRoundInput.session_id,
          profile_id: createRoundInput.profile_id,
          message: 'Session not found or missing healer',
          elapsed_time: Date.now() - startTime,
          trace_id: healingRoundStartTraceId,
        });
        throw new BadRequestException('Invalid Session');
      }

      if (session.healer_id !== lastHealingRound?.healer_id) {
        this.logger.log({
          event: 'HEALING_ROUND_START_HEALER_CHANGED',
          session_id: createRoundInput.session_id,
          profile_id: createRoundInput.profile_id,
          current_healer_id: session.healer_id,
          previous_healer_id: lastHealingRound?.healer_id,
          message: 'Healer changed, resetting last healing round',
          elapsed_time: Date.now() - startTime,
          trace_id: healingRoundStartTraceId,
        });
        lastHealingRound = null;
      }

      if (
        lastHealingRound &&
        lastHealingRound.status === HealingRoundStatus.IN_PROGRESS &&
        !lastHealingRound.round_end_at
      ) {
        this.logger.warn({
          event: 'HEALING_ROUND_ALREADY_IN_PROGRESS',
          session_id: createRoundInput.session_id,
          profile_id: createRoundInput.profile_id,
          last_round_id: lastHealingRound.round_id,
          last_round_status: lastHealingRound.status,
          message: 'Healing Round already in progress',
          elapsed_time: Date.now() - startTime,
          trace_id: healingRoundStartTraceId,
        });
        return lastHealingRound;
      }

      let maxTime =
        !lastHealingRound || lastHealingRound.is_positive_feedback
          ? this.HEALING_ROUND_HEALER_IN_PROGRESS_TIMEOUT
          : lastHealingRound.remaining_time;
      let checkInCount =
        !lastHealingRound || lastHealingRound.is_positive_feedback
          ? 3
          : --lastHealingRound.check_in_count;
      let roundNumber = !lastHealingRound ? 1 : ++lastHealingRound.round_number;

      const previousHealingRound =
        await this.healingRoundRepository.findOneWithSelect({
          where: {
            session_id: createRoundInput.session_id,
            is_deleted: false,
          },
          orderBy: { round_id: 'desc' },
        });
      if (previousHealingRound?.healer_id !== createRoundInput.profile_id) {
        maxTime = this.HEALING_ROUND_HEALER_IN_PROGRESS_TIMEOUT;
        checkInCount = 3;
        roundNumber = 1;
      }
      const patient_id = session.profile_id;
      const healer_id = session.healer_id;

      this.logger.debug({
        event: 'HEALING_ROUND_START_CHECKS_AND_VALIDATIONS',
        session_id: createRoundInput.session_id,
        profile_id: createRoundInput.profile_id,
        elapsed_time: Date.now() - startTime,
        trace_id: healingRoundStartTraceId,
      });
      const CreateData: Prisma.healing_roundsUncheckedCreateInput = {
        session_id: createRoundInput.session_id,
        healer_id: healer_id,
        assistant_thread_id: '',
        max_time: maxTime,
        check_in_count: checkInCount,
        round_number: roundNumber,
        is_healer_confirmed: true,
      };

      this.logger.log({
        event: 'HEALING_ROUND_START_CREATING_ROUND',
        session_id: createRoundInput.session_id,
        profile_id: createRoundInput.profile_id,
        healer_id: healer_id,
        patient_id: patient_id,
        round_number: roundNumber,
        max_time: maxTime,
        check_in_count: checkInCount,
        message: 'Creating new healing round',
        elapsed_time: Date.now() - startTime,
        trace_id: healingRoundStartTraceId,
      });

      const roundCreatedResponse = await this.createHealingRound(
        CreateData,
        healer_id,
        lastHealingRound?.cloud_region!
      );

      this.logger.debug({
        event: 'HEALING_ROUND_START_CREATED_PLACEHOLDER_HEALING_ROUND',
        session_id: createRoundInput.session_id,
        profile_id: createRoundInput.profile_id,
        elapsed_time: Date.now() - startTime,
        trace_id: healingRoundStartTraceId,
      });
      if (!roundCreatedResponse) {
        this.logger.error({
          event: 'HEALING_ROUND_START_CREATION_FAILED',
          session_id: createRoundInput.session_id,
          profile_id: createRoundInput.profile_id,
          healer_id: healer_id,
          message: 'Failed to create healing round',
          elapsed_time: Date.now() - startTime,
          trace_id: healingRoundStartTraceId,
        });
        throw new BadRequestException('Cannot create healing round');
      }

      let assistantResponse: AssistantResponse;
      if (!lastHealingRound || lastHealingRound.assistant_thread_id == null) {
        // Healing Round must get a new Healing Round Assistant thread id.
        assistantResponse = await this.createHealingThread(
          createRoundInput,
          undefined
        );

        this.logger.log({
          event: 'HEALING_ROUND_START_CREATED_HEALING_THREAD',
          session_id: createRoundInput.session_id,
          profile_id: createRoundInput.profile_id,
          elapsed_time: Date.now() - startTime,
          trace_id: healingRoundStartTraceId,
        });
      } else {
        // Get dynamic healing round assistant ID
        const healingRoundAssistantID =
          await this.systemConfigService.getHealingRoundAssistantId(
            lastHealingRound?.cloud_region!
          );
        if (!healingRoundAssistantID) {
          throw new Error(
            'Healing round assistant ID not found in system configuration'
          );
        }
        const healingRoundSystemInput = `${AI_MESSAGE_OUTGOING_TRIGGER.HEALING_ROUND_START}`;
        assistantResponse =
          await this.libOpenAiService.continueConversationAssistant(
            lastHealingRound.assistant_thread_id,
            healingRoundSystemInput,
            healingRoundAssistantID,
            'user',
            false,
            undefined,
            lastHealingRound.cloud_region!
          );

        this.logger.log({
          event: 'HEALING_ROUND_START_CONTINUED_HEALING_THREAD',
          session_id: createRoundInput.session_id,
          profile_id: createRoundInput.profile_id,
          round_id: roundCreatedResponse.round_id,
          elapsed_time: Date.now() - startTime,
          trace_id: healingRoundStartTraceId,
        });
      }
      if (!assistantResponse) {
        this.logger.error({
          event: 'HEALING_ROUND_START_ASSISTANT_RESPONSE_FAILED',
          session_id: createRoundInput.session_id,
          profile_id: createRoundInput.profile_id,
          round_id: roundCreatedResponse.round_id,
          message: 'Failed to get assistant response',
          elapsed_time: Date.now() - startTime,
          trace_id: healingRoundStartTraceId,
        });
        throw new BadRequestException('Cannot create healing round');
      }

      this.healingRoundRepository.update({
        where: { round_id: roundCreatedResponse.round_id },
        data: { assistant_thread_id: assistantResponse.threadId },
      });

      if (
        assistantResponse.trigger.includes(
          AI_MESSAGE_INCOMING_TRIGGER.HEALING_ROUND_MESSAGE_TO_PATIENT
        )
      ) {
        this.logger.log({
          event: 'HEALING_ROUND_START_MESSAGE_TO_PATIENT',
          session_id: createRoundInput.session_id,
          profile_id: createRoundInput.profile_id,
          round_id: roundCreatedResponse.round_id,
          patient_id: patient_id,
          message: 'Sending healing round message to patient',
          elapsed_time: Date.now() - startTime,
          trace_id: healingRoundStartTraceId,
        });

        this.aiMessagingService.messageToEntity({
          message: assistantResponse.output,
          round_id: roundCreatedResponse.round_id,
          profile_id: patient_id,
          session_id: roundCreatedResponse.session_id,
          profile_type: ProfileType.PATIENT,
          entityConversation: lastHealingRound?.conversations[0],
        });
      } else if (lastHealingRound?.conversations[0]) {
        await this.updateAINullResponse(
          lastHealingRound.conversations[0].conversation_id
        );
      }

      if (
        assistantResponse.trigger.includes(
          AI_MESSAGE_INCOMING_TRIGGER.HEALING_ROUND_MESSAGE_TO_ENERGY_HEALER
        )
      ) {
        this.logger.log(
          {
            event: 'HEALING_ROUND_START_MESSAGE_TO_HEALER',
            session_id: createRoundInput.session_id,
            profile_id: createRoundInput.profile_id,
            round_id: roundCreatedResponse.round_id,
            healer_id: healer_id,
            message: 'Sending healing round message to healer',
            elapsed_time: Date.now() - startTime,
            trace_id: healingRoundStartTraceId,
          },
          'Healing Round Start Healer Message'
        );

        this.aiMessagingService.messageToEntity({
          message: assistantResponse.output,
          round_id: roundCreatedResponse.round_id,
          profile_id: healer_id,
          session_id: roundCreatedResponse.session_id,
          profile_type: ProfileType.HEALER,
          entityConversation: lastHealingRound?.conversations[0],
        });
      }

      this.logger.log({
        event: 'HEALING_ROUND_START_SUCCESS',
        session_id: createRoundInput.session_id,
        profile_id: createRoundInput.profile_id,
        round_id: roundCreatedResponse.round_id,
        healer_id: healer_id,
        patient_id: patient_id,
        round_number: roundNumber,
        assistant_thread_id: assistantResponse.threadId,
        message: 'Healing round started successfully',
        elapsed_time: Date.now() - startTime,
        trace_id: healingRoundStartTraceId,
      });

      return roundCreatedResponse;
    } catch (error) {
      this.logger.error({
        event: 'HEALING_ROUND_START_ERROR',
        session_id: createRoundInput.session_id,
        profile_id: createRoundInput.profile_id,
        user_id: userId,
        error_message: error instanceof Error ? error.message : 'unknown error',
        message: 'Healing round start process failed',
        elapsed_time: Date.now() - startTime,
        trace_id: healingRoundStartTraceId,
      });
      throw error;
    }
  }

  private async createHealingThread(
    createRoundInput: CreateHealingRoundDto,
    wsEventOptions?: ThreadRunOptions['wsEventOptions']
  ) {
    const patientSummaryDocument =
      await this.summaryRepository.getLatestSessionSummary({
        where: { session_id: createRoundInput.session_id },
        orderBy: {
          created_at: 'desc',
        },
      });

    // Healing Round must get a new Healing Round Assistant thread id.
    const uniqueAilmentsKey: {
      [ailment: string]: number;
    } = {};
    (
      await this.sessionAilmentRepository.findAll({
        where: {
          session_id: createRoundInput.session_id,
          round_id: null,
          is_deleted: false,
        },
        orderBy: { session_ailment_id: 'asc' },
      })
    )?.forEach((ailment) => {
      const ailmentName = ailment.name.toLowerCase();
      uniqueAilmentsKey[ailmentName] = ailment.level;
    });

    const healingRoundSystemInput = `${
      AI_MESSAGE_OUTGOING_TRIGGER.HEALING_ROUND_SYSTEM_INPUT
    } ${patientSummaryDocument?.content}\n
      Intake Ailments noted for Patient:
      ${Object.entries(uniqueAilmentsKey)
        .map(([name, level]) => `name: ${name} | discomfort level: ${level}`)
        .join('\n')}
      `;

    // Get dynamic healing round assistant ID
    const healingRoundAssistantID =
      await this.systemConfigService.getHealingRoundAssistantId();
    if (!healingRoundAssistantID) {
      throw new Error(
        'Healing round assistant ID not found in system configuration'
      );
    }

    return await this.libOpenAiService.createConversationAssistant(
      healingRoundSystemInput,
      healingRoundAssistantID,
      'user',
      false,
      wsEventOptions
    );
  }

  async createHealingRound(
    body: Prisma.healing_roundsUncheckedCreateInput,
    healerProfileId: number,
    region?: string
  ) {
    if (!region) {
      region = await this.systemConfigService.getActiveRegion();
    }
    const roundData = {
      round_number: body.round_number,
      created_by: healerProfileId,
      assistant_thread_id: body.assistant_thread_id,
      max_time: body.max_time,
      check_in_count: body.check_in_count,
      cloud_region: region,
      healing_sessions: {
        connect: {
          session_id: body.session_id,
        },
      },
      profiles: {
        connect: {
          profile_id: healerProfileId,
        },
      },
      status: HealingRoundStatus.IN_PROGRESS,
      is_healer_confirmed: true,
    };
    const rounds = await this.healingRoundRepository.create(roundData);

    await this.sessionRepository.update({
      where: { session_id: body.session_id },
      data: {
        status: SessionStatus?.HEALING_ROUND,
        sub_status: HealingRoundSubStatus.IN_PROGRESS,
        sub_status_updated_at: new Date(),
      },
    });

    return rounds;
  }

  @OnEvent(HEALING_ROUND_LISTEN.HEALING_ROUND_CHECK_IN, {
    async: true,
    promisify: true,
    nextTick: true,
  })
  async processHealingRoundCheckIn(
    event: AiHealingRoundCheckInEvent,
    region?: string
  ) {
    const startTime = Date.now();
    const healingRoundCheckInTraceId = uuidv4();
    try {
      const { updateHealingRoundDto } = event;
      const sessionId =
        updateHealingRoundDto.healing_sessions?.connect?.session_id;

      // Log method entry
      this.logger.log({
        event: 'HEALING_ROUND_CHECK_IN_START',
        session_id: sessionId,
        profile_id: updateHealingRoundDto.profiles?.connect?.profile_id,
        message: 'Starting healing round check-in process',
        elapsed_time: Date.now() - startTime,
        trace_id: healingRoundCheckInTraceId,
      });

      if (!sessionId) {
        this.logger.error({
          event: 'HEALING_ROUND_CHECK_IN_INVALID_SESSION_ID',
          session_id: sessionId,
          profile_id: updateHealingRoundDto.profiles?.connect?.profile_id,
          message: 'Invalid session ID provided for check-in',
          elapsed_time: Date.now() - startTime,
          trace_id: healingRoundCheckInTraceId,
        });
        throw new BadRequestException('Invalid Session Id');
      }

      const lastHealingRound =
        await this.healingRoundRepository.findOneWithInclude({
          where: {
            session_id: sessionId,
            is_deleted: false,
          },
          orderBy: { round_id: 'desc' },
          include: {
            conversations: {
              where: {
                sender_type: SenderType.Patient,
                is_deleted: false,
              },
              take: 1,
            },
          },
        });
      // Get dynamic healing round assistant ID
      const healingRoundAssistantID =
        await this.systemConfigService.getHealingRoundAssistantId(
          lastHealingRound?.cloud_region || region
        );
      if (!healingRoundAssistantID) {
        throw new Error(
          'Healing round assistant ID not found in system configuration'
        );
      }

      if (!lastHealingRound) {
        this.logger.error({
          event: 'HEALING_ROUND_CHECK_IN_NO_ROUND_FOUND',
          session_id: sessionId,
          profile_id: updateHealingRoundDto.profiles?.connect?.profile_id,
          message: 'No healing round found for check-in',
          elapsed_time: Date.now() - startTime,
          trace_id: healingRoundCheckInTraceId,
        });
        throw new BadRequestException('Invalid Session');
      }

      const maxRemaining =
        lastHealingRound.max_time ||
        this.HEALING_ROUND_HEALER_IN_PROGRESS_TIMEOUT;
      const inProgressTimeSpent =
        new Date().getTime() - lastHealingRound.round_start_at.getTime();
      let remainingTime = maxRemaining - inProgressTimeSpent;
      if (remainingTime < 0) {
        remainingTime = 0;
      }

      this.logger.log({
        event: 'HEALING_ROUND_CHECK_IN_TIME_CALCULATED',
        session_id: sessionId,
        profile_id: updateHealingRoundDto.profiles?.connect?.profile_id,
        round_id: lastHealingRound.round_id,
        max_time: maxRemaining,
        time_spent: inProgressTimeSpent,
        remaining_time: remainingTime,
        message: 'Calculated remaining time for healing round',
        elapsed_time: Date.now() - startTime,
        trace_id: healingRoundCheckInTraceId,
      });

      const healingRoundData: Prisma.healing_roundsUpdateInput = {
        ...updateHealingRoundDto,
        status: HealingRoundStatus.FEEDBACK_REQUIRED,
        updated_by: updateHealingRoundDto.profiles?.connect?.profile_id,
        updated_at: new Date(),
        feedback_start_at: new Date(),
        remaining_time: remainingTime,
      };

      const healingRoundRecord = await this.updateHealingRound(
        lastHealingRound.round_id,
        healingRoundData
      );

      const healingSessionRecord = await this.libSessionService.update(
        sessionId,
        {
          status: SessionStatus.HEALING_ROUND,
          sub_status: HealingRoundStatus.FEEDBACK_REQUIRED || null,
          sub_status_updated_at: new Date(),
          updated_at: new Date(),
        },
        Number(healingRoundData.profiles?.connect?.profile_id),
        this.wsClient
      );

      this.logger.log({
        event: 'HEALING_ROUND_CHECK_IN_STATUS_UPDATED',
        session_id: sessionId,
        profile_id: updateHealingRoundDto.profiles?.connect?.profile_id,
        round_id: healingRoundRecord.round_id,
        round_status: HealingRoundStatus.FEEDBACK_REQUIRED,
        session_status: SessionStatus.HEALING_ROUND,
        message: 'Updated healing round and session status',
        elapsed_time: Date.now() - startTime,
        trace_id: healingRoundCheckInTraceId,
      });

      // Healing Round must get a new Healing Round Assistant thread id.
      const assistantResponse =
        await this.libOpenAiService.continueConversationAssistant(
          healingRoundRecord.assistant_thread_id,
          AI_MESSAGE_OUTGOING_TRIGGER.HEALING_ROUND_START_FEEDBACK,
          healingRoundAssistantID,
          'user',
          false,
          undefined,
          lastHealingRound?.cloud_region || region
        );

      this.logger.log({
        event: 'HEALING_ROUND_CHECK_IN_RECEIVED_ASSISTANT_RESPONSE',
        session_id: sessionId,
        profile_id: updateHealingRoundDto.profiles?.connect?.profile_id,
        round_id: healingRoundRecord.round_id,
        elapsed_time: Date.now() - startTime,
        trace_id: healingRoundCheckInTraceId,
      });

      const { isResponseToPatient, isResponseToHealer } =
        this.aiMessagingService.processIncomingMessages(assistantResponse);

      if (isResponseToPatient) {
        const targetProfileId = healingSessionRecord.profile_id;
        this.logger.log({
          event: 'HEALING_ROUND_CHECK_IN_MESSAGE_TO_PATIENT',
          session_id: sessionId,
          round_id: healingRoundRecord.round_id,
          patient_id: targetProfileId,
          message: 'Sending check-in message to patient',
          elapsed_time: Date.now() - startTime,
          trace_id: healingRoundCheckInTraceId,
        });

        await this.aiMessagingService.messageToEntity({
          message: assistantResponse.output,
          round_id: healingRoundRecord.round_id,
          profile_id: targetProfileId,
          profile_type: ProfileType.PATIENT,
          session_id: healingSessionRecord.session_id,
        });
      } else if (lastHealingRound?.conversations[0]) {
        await this.updateAINullResponse(
          lastHealingRound.conversations[0].conversation_id
        );
      }

      if (isResponseToHealer) {
        const targetProfileId = healingRoundRecord.healer_id!;
        this.logger.log({
          event: 'HEALING_ROUND_CHECK_IN_MESSAGE_TO_HEALER',
          session_id: sessionId,
          round_id: healingRoundRecord.round_id,
          healer_id: targetProfileId,
          message: 'Sending check-in message to healer',
          elapsed_time: Date.now() - startTime,
          trace_id: healingRoundCheckInTraceId,
        });

        await this.aiMessagingService.messageToEntity({
          message: assistantResponse.output,
          round_id: healingRoundRecord.round_id,
          profile_id: targetProfileId,
          profile_type: ProfileType.HEALER,
          session_id: healingSessionRecord.session_id,
        });
      }

      this.logger.log({
        event: 'HEALING_ROUND_CHECK_IN_SUCCESS',
        session_id: sessionId,
        round_id: healingRoundRecord.round_id,
        healer_id: healingRoundRecord.healer_id,
        patient_id: healingSessionRecord.profile_id,
        remaining_time: remainingTime,
        message: 'Healing round check-in completed successfully',
        elapsed_time: Date.now() - startTime,
        trace_id: healingRoundCheckInTraceId,
      });

      return healingRoundRecord;
    } catch (error) {
      this.logger.error({
        event: 'HEALING_ROUND_CHECK_IN_ERROR',
        session_id:
          event.updateHealingRoundDto.healing_sessions?.connect?.session_id,
        profile_id: event.updateHealingRoundDto.profiles?.connect?.profile_id,
        error_message: error instanceof Error ? error.message : 'unknown error',
        message: 'Healing round check-in process failed',
        elapsed_time: Date.now() - startTime,
        trace_id: healingRoundCheckInTraceId,
      });
      throw error;
    }
  }

  async updateAINullResponse(conversationId: number) {
    return this.conversationRepository.update({
      where: {
        conversation_id: conversationId,
      },
      data: {
        responded_at: new Date(),
        is_null_response: true,
      },
    });
  }

  async updateHealingRound(
    round_id: number,
    body: Prisma.healing_roundsUpdateInput
  ) {
    const rounds = await this.healingRoundRepository.update({
      where: { round_id: round_id, is_deleted: false },
      data: body,
    });

    const updateSession = {
      session_end_at: new Date(),
      status: body.status,
    };

    await this.libSessionService.update(
      Number(body.healing_sessions?.connect?.session_id),
      {
        ...updateSession,
      },
      Number(body.profiles?.connect?.profile_id),
      this.wsClient
    );
    return rounds;
  }

  @OnEvent(CONVERSATION_LISTEN.HEALING_ROUND_CONVERSATION, { async: true })
  public async healingRoundConversations(
    input: AiHealingRoundEvent,
    region?: string
  ): Promise<conversations | true> {
    const startTime = Date.now();
    const healingRoundConversationsTraceId = uuidv4();
    try {
      // Get dynamic healing round assistant ID
      const healingRoundAssistantID =
        await this.systemConfigService.getHealingRoundAssistantId(region);
      if (!healingRoundAssistantID) {
        throw new Error(
          'Healing round assistant ID not found in system configuration'
        );
      }

      let conversation: conversations | true = true;
      let targetProfileId: number;
      let updateRoundData:
        | {
            status?: string;
            is_positive_feedback?: boolean;
            updated_at: Date;
            round_end_at?: Date;
          }
        | undefined;
      const currDate = new Date();
      const {
        session,
        conversation: entityConversation,
        round_id,
        sender_type,
      } = input;

      this.logger.log({
        event: 'HEALING_ROUND_CONVERSATION_PREPARE_HEALING_ROUND_QUERY',
        session_id: session.session_id,
        round_id: round_id,
        elapsed_time: Date.now() - startTime,
        trace_id: healingRoundConversationsTraceId,
      });
      await this.healingRoundRepository.update({
        where: { round_id, is_deleted: false },
        data: {
          ...updateRoundData,
          updated_at: new Date(),
        },
      });
      const healingRoundRecord = await this.healingRoundRepository.findUnique({
        where: { round_id, is_deleted: false },
      });

      if (!healingRoundRecord) {
        this.logger.error({
          event: 'HEALING_ROUND_CONVERSATION_INVALID_ROUND_ID',
          session_id: session.session_id,
          round_id: round_id,
          elapsed_time: Date.now() - startTime,
          trace_id: healingRoundConversationsTraceId,
        });
        throw new BadRequestException('Invalid Round Id');
      }

      if (
        healingRoundRecord.status == HealingRoundStatus.COMPLETED &&
        sender_type == SenderType.Healer &&
        !healingRoundRecord.is_positive_feedback
      ) {
        this.logger.error({
          event: 'HEALING_ROUND_CONVERSATION_LAST_ROUND_NOT_SUCCESSFUL',
          session_id: session.session_id,
          round_id: round_id,
          elapsed_time: Date.now() - startTime,
          trace_id: healingRoundConversationsTraceId,
        });
        throw new BadRequestException(
          "Healer can't message when last round is not successful."
        );
      }

      this.logger.debug({
        event: 'HEALING_ROUND_CONVERSATION_START_ASSISTANT_RESPONSE',
        session_id: session.session_id,
        round_id: round_id,
        elapsed_time: Date.now() - startTime,
        trace_id: healingRoundConversationsTraceId,
      });
      const senderTrigger =
        sender_type == SenderType.Patient
          ? AI_MESSAGE_OUTGOING_TRIGGER.HEALING_ROUND_MESSAGE_FROM_PATIENT
          : AI_MESSAGE_OUTGOING_TRIGGER.HEALING_ROUND_MESSAGE_FROM_ENERGY_HEALER;
      const healingRoundSystemInput = `${senderTrigger} ${entityConversation.content}`;
      const assistantResponse =
        await this.libOpenAiService.continueConversationAssistant(
          healingRoundRecord.assistant_thread_id,
          healingRoundSystemInput,
          healingRoundAssistantID,
          'user',
          false,
          undefined,
          region
        );

      this.logger.debug({
        event: 'HEALING_ROUND_CONVERSATION_COMPLETED_ASSISTANT_RESPONSE',
        session_id: session.session_id,
        round_id: round_id,
        elapsed_time: Date.now() - startTime,
        trace_id: healingRoundConversationsTraceId,
      });

      const {
        isHealingRoundCompleted,
        isResponseToHealer,
        isResponseToPatient,
      } = this.aiMessagingService.processIncomingMessages(assistantResponse);

      if (isHealingRoundCompleted) {
        this.closeOpenHealingRounds(session.session_id);
        const summaryContent = assistantResponse.output
          .replace(
            new RegExp(
              `^[\\s\\w\\W]*${AI_MESSAGE_INCOMING_TRIGGER.HEALING_ROUND_COMPLETED}[\\:\\s]*`,
              'ig'
            ),
            ''
          )
          .trim();

        const ailmentResponse =
          await this.summaryAilmentService.createSummaryAndAilments(
            summaryContent,
            session.session_id,
            session,
            round_id
          );

        this.logger.log({
          event: 'HEALING_ROUND_CONVERSATION_CREATED_SUMMARY_AND_AILMENTS',
          session_id: session.session_id,
          round_id: round_id,
          elapsed_time: Date.now() - startTime,
          trace_id: healingRoundConversationsTraceId,
        });

        const roundSuccess =
          ailmentResponse.is_zero_pain ||
          (await this.sessionAilmentRepository.compareRoundFeedback(
            session.session_id,
            +round_id
          ));

        const inProgressCompleted =
          healingRoundRecord.feedback_start_at?.getTime() || currDate.getTime();

        const inProgressElapsedTime =
          inProgressCompleted - healingRoundRecord.round_start_at.getTime();

        const roundTimeUp = inProgressElapsedTime > healingRoundRecord.max_time;
        const canHealerContinue =
          roundSuccess ||
          (!roundTimeUp && healingRoundRecord.check_in_count > 1);

        let sessionStatus = SessionStatus.HEALING_ROUND;
        let sessionSubStatus = SessionSubStatusType.COMPLETED;

        if (ailmentResponse.is_zero_pain || !canHealerContinue) {
          sessionStatus = SessionStatus.COMPLETED;
          sessionSubStatus = SessionSubStatusType.COMPLETED;
        }

        await this.sessionRepository.update({
          where: { session_id: session.session_id },
          data: {
            status: sessionStatus,
            sub_status: sessionSubStatus,
            sub_status_updated_at: new Date(),
            updated_at: new Date(),
            is_follow_up: null,
          },
        });

        const userDetail = await this.userRepository.findFirst({
          where: { is_deleted: false, is_active: true },
          include: {
            profiles: {
              where: {
                profile_id: session.profile_id,
              },
            },
          },
        });

        if (sessionStatus === SessionStatus.COMPLETED && userDetail?.user_id) {
          await this.sessionFollowUpRepository.create({
            session_id: session.session_id,
            user_id: userDetail.user_id,
            status: SessionFollowUpStatus.PENDING,
            follow_up_at: new Date(),
          });
        }

        updateRoundData = {
          status: HealingRoundStatus.COMPLETED,
          is_positive_feedback: roundSuccess,
          round_end_at: currDate,
          updated_at: new Date(),
        };
      }

      if (isResponseToPatient) {
        targetProfileId = session.profile_id;
        conversation =
          (await this.aiMessagingService.messageToEntity({
            message: isHealingRoundCompleted
              ? assistantResponse.output.replace(
                  new RegExp(
                    `${AI_MESSAGE_INCOMING_TRIGGER.HEALING_ROUND_COMPLETED}[\\s\\w\\W]*$`,
                    'i'
                  ),
                  ''
                )
              : assistantResponse.output,
            round_id,
            profile_id: targetProfileId,
            session_id: session.session_id,
            profile_type: ProfileType.PATIENT,
            entityConversation,
          })) || true;
      } else if (entityConversation) {
        await this.updateAINullResponse(entityConversation.conversation_id);
      }
      if (isResponseToHealer) {
        targetProfileId = healingRoundRecord.healer_id!;
        conversation =
          (await this.aiMessagingService.messageToEntity({
            message: isHealingRoundCompleted
              ? assistantResponse.output.replace(
                  new RegExp(
                    `${AI_MESSAGE_INCOMING_TRIGGER.HEALING_ROUND_COMPLETED}[\\s\\w\\W]*$`,
                    'i'
                  ),
                  ''
                )
              : assistantResponse.output,
            round_id,
            profile_id: targetProfileId,
            session_id: session.session_id,
            profile_type: ProfileType.HEALER,
            entityConversation,
          })) || true;
      }

      await this.healingRoundRepository.update({
        where: { round_id, is_deleted: false },
        data: {
          ...updateRoundData,
          updated_at: new Date(),
        },
      });

      this.logger.log({
        event: 'HEALING_ROUND_CONVERSATION_SUCCESS',
        session_id: session.session_id,
        round_id: round_id,
        elapsed_time: Date.now() - startTime,
        trace_id: healingRoundConversationsTraceId,
      });

      return conversation;
    } catch (error) {
      this.logger.error({
        event: 'HEALING_ROUND_CONVERSATION_ERROR',
        elapsed_time: Date.now() - startTime,
        trace_id: healingRoundConversationsTraceId,
      });
      throw error;
    }
  }

  async closeOpenHealingRounds(sessionId: number) {
    const currDate = new Date();
    return this.healingRoundRepository.updateMany({
      where: {
        session_id: sessionId,
        status: {
          notIn: [HealingRoundStatus.CANCELLED, HealingRoundStatus.COMPLETED],
        },
      },
      data: {
        status: HealingRoundStatus.COMPLETED,
        updated_at: currDate,
        round_end_at: currDate,
      },
    });
  }
}
