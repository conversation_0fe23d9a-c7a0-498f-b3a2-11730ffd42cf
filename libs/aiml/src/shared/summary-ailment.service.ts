import { Injectable } from '@nestjs/common';
import {
  AilmentAssistantResponse,
  LibOpenaiService,
} from '../openai/openai.service';
import { SummaryRepository } from '@core_be/data-access';
import { Prisma, PrismaService, healing_sessions } from '@core/prisma-client';
import { SessionSubStatusType } from '@core_be/global';
import { MeasurePerformance } from '@core/libs';

@Injectable()
export class SummaryAilmentService {
  constructor(
    private readonly summaryRepository: SummaryRepository,
    private readonly libOpenAiService: LibOpenaiService,
    private readonly prisma: PrismaService
  ) {}

  public async createSummaryAndAilments(
    extractedSummaryResponse: string,
    session_id: number,
    session: healing_sessions,
    round_id?: number,
    initialSummary?: string,
    performance?: MeasurePerformance
  ): Promise<AilmentAssistantResponse['output']> {
    let existingAilments: Prisma.session_ailmentsCreateManyInput[] = [];
    let summaryId: number;
    let summaryInput = `__SUMMARY__:${extractedSummaryResponse}`;
    if (initialSummary) {
      summaryInput = [
        `__SUMMARY_HISTORY__: ${initialSummary}`,
        `__SUMMARY__:${extractedSummaryResponse}`,
      ].join('\n');
    }

    const isAIConfirmationRequired =
      session.sub_status === SessionSubStatusType.AI_CONFIRMATION_REQUIRED;

    if (isAIConfirmationRequired) {
      /*
       * If the session is in AI_CONFIRMATION_REQUIRED state,
       * update the summary with the new summary
       *
       * Also obtain existing ailments for the session
       */
      summaryId = await this.summaryRepository.update({
        session_id,
        data: { content: extractedSummaryResponse, updated_at: new Date() },
      });

      performance?.push('updated session summary');

      existingAilments = await this.prisma.session_ailments.findMany({
        where: { session_id },
      });

      performance?.push('obtained existing ailments');
    } else {
      summaryId = await this.summaryRepository.create({
        content: extractedSummaryResponse,
        healing_sessions: {
          connect: {
            session_id,
          },
        },
        round_id,
      });
      performance?.push('created session summary');
    }

    const extractAilments = await this.libOpenAiService.extractAilments(
      summaryInput
    );

    performance?.info('extracted ailments to db', {
      session_id: session_id,
      round_id,
    });

    const ailments = extractAilments?.output?.ailments;
    for (let i = 0, iL = ailments.length; i < iL; i++) {
      const ailment = ailments[i];
      const data: Prisma.session_ailmentsCreateInput = {
        name: ailment.name,
        level: +ailment.level,
        description: ailment.description,
        healing_sessions: {
          connect: {
            session_id,
          },
        },
        session_summaries: {
          connect: {
            summary_id: summaryId,
          },
        },
      };
      if (round_id) {
        data.healing_rounds = {
          connect: {
            round_id,
          },
        };
      }
      if (isAIConfirmationRequired) {
        // Potential bug: out of bounds error if length of existingAilments is less than the length of extractedAilments output
        const session_ailment = existingAilments[i];
        await this.prisma.session_ailments.update({
          where: { session_ailment_id: session_ailment?.session_ailment_id },
          data: { level: +ailment.level },
        });
      } else {
        await this.prisma.session_ailments.create({
          data,
        });
      }
    }

    performance?.push('upserted session ailments to db');
    return extractAilments.output;
  }
}
