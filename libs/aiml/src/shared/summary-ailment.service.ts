import { Injectable } from '@nestjs/common';
import {
  AilmentAssistantResponse,
  LibOpenaiService,
} from '../openai/openai.service';
import {
  SessionAilmentRepository,
  SummaryRepository,
} from '@core_be/data-access';
import { Prisma, PrismaService, healing_sessions } from '@core/prisma-client';
import { SessionSubStatusType } from '@core_be/global';
import { Logger } from 'nestjs-pino';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class SummaryAilmentService {
  constructor(
    private readonly summaryRepository: SummaryRepository,
    private readonly libOpenAiService: LibOpenaiService,
    private readonly prisma: PrismaService,
    private readonly sessionAilmentRepository: SessionAilmentRepository,
    private readonly logger: Logger
  ) {}

  public async createSummaryAndAilments(
    extractedSummaryResponse: string,
    session_id: number,
    session: healing_sessions,
    round_id?: number,
    initialSummary?: string
  ): Promise<AilmentAssistantResponse['output']> {
    const startTime = Date.now();
    const summaryAilmentTraceId = uuidv4();

    let existingAilments: Prisma.session_ailmentsCreateManyInput[] = [];
    let summaryId: number;
    let summaryInput = `__SUMMARY__:${extractedSummaryResponse}`;
    if (initialSummary) {
      summaryInput = [
        `__SUMMARY_HISTORY__: ${initialSummary}`,
        `__SUMMARY__:${extractedSummaryResponse}`,
      ].join('\n');
    }
    const previousRoundAilments =
      await this.sessionAilmentRepository.getPreviousRoundAilments(
        session_id,
        round_id
      );
    if (previousRoundAilments.length > 0) {
      summaryInput = [
        `__PREVIOUS_ROUND_AILMENTS__: ${JSON.stringify(
          previousRoundAilments,
          null,
          2
        )}`,
        summaryInput,
      ].join('\n');
    }

    const isAIConfirmationRequired =
      session.sub_status === SessionSubStatusType.AI_CONFIRMATION_REQUIRED;

    if (isAIConfirmationRequired) {
      /*
       * If the session is in AI_CONFIRMATION_REQUIRED state,
       * update the summary with the new summary
       *
       * Also obtain existing ailments for the session
       */
      summaryId = await this.summaryRepository.update({
        session_id,
        data: { content: extractedSummaryResponse, updated_at: new Date() },
      });

      this.logger.debug({
        event: 'SUMMARY_AILMENT_SERVICE_UPDATED_SESSION_SUMMARY',
        session_id,
        elapsed_time: Date.now() - startTime,
        trace_id: summaryAilmentTraceId,
      });

      existingAilments = await this.prisma.session_ailments.findMany({
        where: { session_id },
      });

      this.logger.debug({
        event: 'SUMMARY_AILMENT_SERVICE_OBTAINED_EXISTING_AILMENTS',
        session_id,
        elapsed_time: Date.now() - startTime,
        trace_id: summaryAilmentTraceId,
      });
    } else {
      summaryId = await this.summaryRepository.create({
        content: extractedSummaryResponse,
        healing_sessions: {
          connect: {
            session_id,
          },
        },
        round_id,
      });
      this.logger.debug({
        event: 'SUMMARY_AILMENT_SERVICE_CREATED_SESSION_SUMMARY',
        session_id,
        elapsed_time: Date.now() - startTime,
        trace_id: summaryAilmentTraceId,
      });
    }

    const extractAilments = await this.libOpenAiService.extractAilments(
      summaryInput,
      session.cloud_region!
    );

    this.logger.debug({
      event: 'SUMMARY_AILMENT_SERVICE_EXTRACTED_AILMENTS',
      session_id,
      round_id,
      elapsed_time: Date.now() - startTime,
      trace_id: summaryAilmentTraceId,
    });

    const ailments = extractAilments?.output?.ailments;
    for (let i = 0, iL = ailments.length; i < iL; i++) {
      const ailment = ailments[i];
      // Drop ailment if it is the initial intake and ailment level is 0
      if (!round_id && +ailment.level === 0) {
        this.logger.warn({
          event: 'SUMMARY_AILMENT_SERVICE_IGNORED_ZERO_LEVEL_AILMENT',
          session_id,
          round_id,
          ailment_name: ailment.name,
          ailment_level: ailment.level,
          trace_id: summaryAilmentTraceId,
        });
        continue;
      }
      const data: Prisma.session_ailmentsCreateInput = {
        name: ailment.name,
        level: ailment.level === null ? -1 : +ailment.level,
        description: ailment.description,
        healing_sessions: {
          connect: {
            session_id,
          },
        },
        session_summaries: {
          connect: {
            summary_id: summaryId,
          },
        },
      };
      if (round_id) {
        data.healing_rounds = {
          connect: {
            round_id,
          },
        };
      }
      if (isAIConfirmationRequired) {
        // Potential bug: out of bounds error if length of existingAilments is less than the length of extractedAilments output
        const session_ailment = existingAilments[i];
        await this.prisma.session_ailments.update({
          where: { session_ailment_id: session_ailment?.session_ailment_id },
          data: { level: +ailment.level },
        });
      } else {
        await this.prisma.session_ailments.create({
          data,
        });
      }
    }

    this.logger.debug({
      event: 'SUMMARY_AILMENT_SERVICE_UPSERTED_SESSION_AILMENTS',
      session_id,
      round_id,
      elapsed_time: Date.now() - startTime,
      trace_id: summaryAilmentTraceId,
    });
    return extractAilments.output;
  }
}
