import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  InternalServerErrorException,
} from '@nestjs/common';
import { PrismaService, Prisma } from '@core/prisma-client';
import {
  HealingRoundStatus,
  MessageType,
  SessionStatus,
  SessionSubStatusType,
  AiHealingRoundEvent,
  CONVERSATION_LISTEN,
  ConversationDto,
  MeasurePerformance,
  CreateConversationEvent,
  SessionRepository,
} from '@core/libs';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { LibSessionService } from '@core_be/global';
import { LibAIIntakeService, ContentFilterException } from '@core_be/aiml';

@Injectable()
export class ConversationListenerService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly libSessionService: LibSessionService,
    private readonly eventEmitter: EventEmitter2,
    private readonly aiIntakeService: LibAIIntakeService,
    private readonly sessionRepository: SessionRepository,
    private readonly performance: MeasurePerformance
  ) {}

  /*
   * This service is to handle conversation triggered from the single conversation API,
   * which is primarily used for handling text input from the frontend.
   *
   * This function is also used for summary generation when the patient is in queue.
   *
   * Voice-based conversation transcript from ElevenLab is handled by the bulk conversation API and
   * the ConversationService in apps/ai/src/app/api/conversations/controllers/conversation.controller.ts
   */
  @OnEvent(CONVERSATION_LISTEN.CREATE)
  async createConversation(data: CreateConversationEvent) {
    const perf = this.performance;
    try {
      const sessionId = data.conversation.session_id;
      const profileId = data.conversation.profile_id;

      // Find the session from the session_id
      const session = await this.libSessionService.findFirstSession({
        session_id: sessionId,
      });
      perf.push(`obtained session`);

      if (!session) {
        const errorMessage = 'session not found';
        perf.warn(errorMessage, {
          session_id: sessionId,
        });
        throw new NotFoundException(errorMessage);
      } else if (session.profile_id !== profileId) {
        const errorMessage = 'profile id mismatch';
        perf.warn(errorMessage, {
          session_id: sessionId,
          session_profile_id: session.profile_id,
          profile_id: profileId,
        });
        throw new ForbiddenException(errorMessage);
      }

      const sessionStatus = session.status;
      const sessionSubStatus = session.sub_status;

      perf.debug('session details', {
        session_id: sessionId,
        session_status: sessionStatus,
        session_sub_status: sessionSubStatus,
      });
      // Starting from this point, the conversation is stored in the database
      // TODO: Improve Session State Validation
      // Update the conversation table with the user's message
      const userResponse: Prisma.conversationsCreateInput = {
        content: data.conversation.content,
        sender_type: data.conversation.sender_type,
        profiles: {
          connect: {
            profile_id: profileId,
          },
        },
        healing_sessions: {
          connect: {
            session_id: sessionId,
          },
        },
      };

      // Inject the round_id if it exists
      if (data.conversation.round_id) {
        userResponse['healing_rounds'] = {
          connect: {
            round_id: data.conversation.round_id,
          },
        };
      }

      const aiConversation = await this.prisma.conversations.create({
        data: userResponse,
      });

      if (!aiConversation) {
        const errorMessage = 'failed to save conversation records';
        perf.warn(errorMessage, {
          session_id: sessionId,
          profile_id: profileId,
        });
        throw new InternalServerErrorException(errorMessage);
      }

      // Only measure the performance if the conversation is created successfully
      perf.push(`saved conversation records`);

      // Handle the user message and generate the AI response
      const response = {
        session_id: sessionId,
        ai_response_to_user: '',
      };

      const conversation = new ConversationDto();
      conversation.profile_id = profileId;
      conversation.content = '' + aiConversation.content;
      conversation.conversation_id = aiConversation.conversation_id;

      if (
        session.status === SessionStatus.INTAKE &&
        session.sub_status === SessionSubStatusType.IN_PROGRESS
      ) {
        /*
         * Handling single conversation or summary conversation
         * Case: FLOW-INTAKE-TEXT-MESSAGE
         */

        try {
          await this.aiIntakeService.handleIntakeTextMessage(
            conversation,
            session,
            data.query,
            perf
          );
        } catch (error) {
          if (error instanceof ContentFilterException) {
            perf.warn('content filter exception', {
              session_id: sessionId,
              profile_id: profileId,
            });
          } else {
            perf.warn('failed to handle intake text message', {
              session_id: sessionId,
              profile_id: profileId,
              error: error instanceof Error ? error.message : 'unknown error',
            });
          }
          throw error;
        }
      } else if (
        session.status === SessionStatus.IN_QUEUE &&
        session.sub_status === SessionSubStatusType.AI_CONFIRMATION_REQUIRED
      ) {
        /*
         * Handling single conversation or summary conversation
         * Case: FLOW-INTAKE-TEXT-MESSAGE
         * Case: FLOW-INQUEUE-AI-CONFIRMATION-REQUIRED
         */
        try {
          await this.aiIntakeService.handleAIConfirmation(
            conversation,
            session,
            data.query,
            perf
          );
        } catch (error) {
          if (error instanceof ContentFilterException) {
            perf.warn('content filter exception', {
              session_id: sessionId,
              profile_id: profileId,
            });
          } else {
            perf.warn('failed to handle AI confirmation', {
              session_id: sessionId,
              profile_id: profileId,
              error: error instanceof Error ? error.message : 'unknown error',
            });
          }
          throw error;
        }
      } else if (
        data.conversation?.round_id &&
        session.status === SessionStatus.HEALING_ROUND &&
        (session.sub_status === HealingRoundStatus.IN_PROGRESS ||
          session.sub_status === HealingRoundStatus.FEEDBACK_REQUIRED ||
          session.sub_status === HealingRoundStatus.COMPLETED)
      ) {
        /*
         * Handling healing round conversation
         * Case: FLOW-HEALING-ROUND-IN-PROGRESS
         * Case: FLOW-HEALING-ROUND-FEEDBACK-REQUIRED
         * Case: FLOW-HEALING-ROUND-COMPLETED
         */
        this.eventEmitter.emitAsync(
          CONVERSATION_LISTEN.HEALING_ROUND_CONVERSATION,
          new AiHealingRoundEvent(
            aiConversation,
            session,
            data.conversation.round_id,
            data.conversation.sender_type,
            MessageType.Text,
            perf
          )
        );
      }

      return response;
    } finally {
      perf.logPerformance();
    }
  }
}
