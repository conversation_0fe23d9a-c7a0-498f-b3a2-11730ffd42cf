import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  InternalServerErrorException,
  HttpException,
} from '@nestjs/common';
import { PrismaService, Prisma } from '@core/prisma-client';
import {
  HealingRoundStatus,
  MessageType,
  SessionStatus,
  SessionSubStatusType,
  AiHealingRoundEvent,
  CONVERSATION_LISTEN,
  ConversationDto,
  CreateConversationEvent,
  SessionRepository,
} from '@core/libs';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { LibSessionService } from '@core_be/global';
import { LibAIIntakeService, ContentFilterException } from '@core_be/aiml';
import { Logger } from 'nestjs-pino';

@Injectable()
export class ConversationListenerService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly libSessionService: LibSessionService,
    private readonly eventEmitter: EventEmitter2,
    private readonly aiIntakeService: LibAIIntakeService,
    private readonly sessionRepository: SessionRepository,
    private readonly logger: Logger
  ) {}

  /*
   * This service is to handle conversation triggered from the single conversation API,
   * which is primarily used for handling text input from the frontend.
   *
   * This function is also used for summary generation when the patient is in queue.
   *
   * Voice-based conversation transcript from ElevenLab is handled by the bulk conversation API and
   * the ConversationService in apps/ai/src/app/api/conversations/controllers/conversation.controller.ts
   */
  @OnEvent(CONVERSATION_LISTEN.CREATE)
  async createConversation(data: CreateConversationEvent) {
    const startTime = Date.now();
    const sessionId = data.conversation.session_id;
    const profileId = data.conversation.profile_id;

    try {
      // Find the session from the session_id
      const session = await this.libSessionService.findFirstSession({
        session_id: sessionId,
      });

      this.logger.log({
        timestamp: new Date().toISOString(),
        event: 'INDIVDUAL_CONSERVATION_START',
        session_id: sessionId,
        profile_id: profileId,
        message: 'Starting individual conversation processing',
        elapsed_time: Date.now() - startTime,
      });

      if (!session) {
        const errorMessage = 'session not found';
        this.logger.warn({
          timestamp: new Date().toISOString(),
          event: 'INDIVDUAL_CONSERVATION_SESSION_NOT_FOUND',
          session_id: sessionId,
          profile_id: profileId,
          message: errorMessage,
          elapsed_time: Date.now() - startTime,
        });
        throw new NotFoundException(errorMessage);
      } else if (session.profile_id !== profileId) {
        const errorMessage = 'profile id mismatch';
        this.logger.warn({
          timestamp: new Date().toISOString(),
          event: 'INDIVDUAL_CONSERVATION_PROFILE_ID_MISMATCH',
          session_id: sessionId,
          profile_id: profileId,
          message: errorMessage,
          elapsed_time: Date.now() - startTime,
        });
        throw new ForbiddenException(errorMessage);
      }

      const sessionStatus = session.status;
      const sessionSubStatus = session.sub_status;

      this.logger.debug({
        timestamp: new Date().toISOString(),
        event: 'INDIVDUAL_CONSERVATION_SESSION_DETAILS',
        session_id: sessionId,
        profile_id: profileId,
        session_status: sessionStatus,
        session_sub_status: sessionSubStatus,
        message: 'Obtained session details',
        elapsed_time: Date.now() - startTime,
      });
      // Starting from this point, the conversation is stored in the database
      // TODO: Improve Session State Validation
      // Update the conversation table with the user's message
      const userResponse: Prisma.conversationsCreateInput = {
        content: data.conversation.content,
        sender_type: data.conversation.sender_type,
        profiles: {
          connect: {
            profile_id: profileId,
          },
        },
        healing_sessions: {
          connect: {
            session_id: sessionId,
          },
        },
      };

      // Inject the round_id if it exists
      if (data.conversation.round_id) {
        userResponse['healing_rounds'] = {
          connect: {
            round_id: data.conversation.round_id,
          },
        };
      }

      const aiConversation = await this.prisma.conversations.create({
        data: userResponse,
      });

      if (!aiConversation) {
        const errorMessage = 'failed to save conversation records';
        this.logger.warn({
          timestamp: new Date().toISOString(),
          event: 'INDIVDUAL_CONSERVATION_FAILED_TO_SAVE_CONVERSATION_RECORDS',
          session_id: sessionId,
          profile_id: profileId,
          message: errorMessage,
          elapsed_time: Date.now() - startTime,
        });
        throw new InternalServerErrorException(errorMessage);
      }

      // Only measure the performance if the conversation is created successfully
      this.logger.debug({
        timestamp: new Date().toISOString(),
        event: 'INDIVDUAL_CONSERVATION_SAVED_CONVERSATION_RECORDS',
        session_id: sessionId,
        profile_id: profileId,
        message: 'saved conversation records',
        elapsed_time: Date.now() - startTime,
      });

      // Handle the user message and generate the AI response
      const response = {
        session_id: sessionId,
        ai_response_to_user: '',
      };

      const conversation = new ConversationDto();
      conversation.profile_id = profileId;
      conversation.content = '' + aiConversation.content;
      conversation.conversation_id = aiConversation.conversation_id;

      if (
        session.status === SessionStatus.INTAKE &&
        session.sub_status === SessionSubStatusType.IN_PROGRESS
      ) {
        /*
         * Handling single conversation or summary conversation
         * Case: FLOW-INTAKE-TEXT-MESSAGE
         */

        try {
          this.logger.log({
            timestamp: new Date().toISOString(),
            event: 'INDIVDUAL_CONSERVATION_INTAKE_TEXT_MESSAGE',
            session_id: sessionId,
            profile_id: profileId,
            message: 'handling intake text message',
            elapsed_time: Date.now() - startTime,
          });
          await this.aiIntakeService.handleIntakeTextMessage(
            conversation,
            session
          );
        } catch (error) {
          if (error instanceof ContentFilterException) {
            this.logger.warn({
              timestamp: new Date().toISOString(),
              event: 'INDIVDUAL_CONSERVATION_CONTENT_FILTER_EXCEPTION',
              session_id: sessionId,
              profile_id: profileId,
              message: 'content filter exception',
              elapsed_time: Date.now() - startTime,
            });
          } else {
            this.logger.warn({
              timestamp: new Date().toISOString(),
              event:
                'INDIVDUAL_CONSERVATION_FAILED_TO_HANDLE_INTAKE_TEXT_MESSAGE',
              session_id: sessionId,
              profile_id: profileId,
              error: error instanceof Error ? error.message : 'unknown error',
              message: 'failed to handle intake text message',
              elapsed_time: Date.now() - startTime,
            });
          }
          throw error;
        }
        // Currently this flow is not used, but keeping it for future reference
      } else if (
        session.status === SessionStatus.IN_QUEUE &&
        session.sub_status === SessionSubStatusType.AI_CONFIRMATION_REQUIRED
      ) {
        /*
         * Handling single conversation or summary conversation
         * Case: FLOW-INTAKE-TEXT-MESSAGE
         * Case: FLOW-INQUEUE-AI-CONFIRMATION-REQUIRED
         */
        try {
          this.logger.log({
            timestamp: new Date().toISOString(),
            event: 'INDIVDUAL_CONSERVATION_AI_CONFIRMATION_REQUIRED',
            session_id: sessionId,
            profile_id: profileId,
            message: 'handling AI confirmation',
            elapsed_time: Date.now() - startTime,
          });

          await this.aiIntakeService.handleAIConfirmation(
            conversation,
            session,
            data.query
          );
        } catch (error) {
          if (error instanceof ContentFilterException) {
            this.logger.warn({
              timestamp: new Date().toISOString(),
              event: 'INDIVDUAL_CONSERVATION_CONTENT_FILTER_EXCEPTION',
              session_id: sessionId,
              profile_id: profileId,
              message: 'content filter exception',
              elapsed_time: Date.now() - startTime,
            });
          } else {
            this.logger.warn({
              timestamp: new Date().toISOString(),
              event: 'INDIVDUAL_CONSERVATION_FAILED_TO_HANDLE_AI_CONFIRMATION',
              session_id: sessionId,
              profile_id: profileId,
              error: error instanceof Error ? error.message : 'unknown error',
              message: 'failed to handle AI confirmation',
              elapsed_time: Date.now() - startTime,
            });
          }
          throw error;
        }
      } else if (
        data.conversation?.round_id &&
        session.status === SessionStatus.HEALING_ROUND &&
        (session.sub_status === HealingRoundStatus.IN_PROGRESS ||
          session.sub_status === HealingRoundStatus.FEEDBACK_REQUIRED ||
          session.sub_status === HealingRoundStatus.COMPLETED)
      ) {
        /*
         * Handling healing round conversation
         * Case: FLOW-HEALING-ROUND-IN-PROGRESS
         * Case: FLOW-HEALING-ROUND-FEEDBACK-REQUIRED
         * Case: FLOW-HEALING-ROUND-COMPLETED
         */

        this.logger.log({
          timestamp: new Date().toISOString(),
          event: 'INDIVDUAL_CONSERVATION_HEALING_ROUND_CONVERSATION',
          session_id: sessionId,
          profile_id: profileId,
          round_id: data.conversation.round_id,
          sender_type: data.conversation.sender_type,
          message: 'emitting healing round conversation event',
          elapsed_time: Date.now() - startTime,
        });

        this.eventEmitter.emitAsync(
          CONVERSATION_LISTEN.HEALING_ROUND_CONVERSATION,
          new AiHealingRoundEvent(
            aiConversation,
            session,
            data.conversation.round_id,
            data.conversation.sender_type,
            MessageType.Text
          )
        );
      }
      this.logger.log({
        timestamp: new Date().toISOString(),
        event: 'INDIVDUAL_CONSERVATION_SUCCESS',
        session_id: sessionId,
        profile_id: profileId,
        message: 'individual conversation processing completed',
        elapsed_time: Date.now() - startTime,
      });
      return response;
    } catch (error) {
      this.logger.error({
        timestamp: new Date().toISOString(),
        event: 'INDIVDUAL_CONSERVATION_ERROR',
        session_id: sessionId,
        profile_id: profileId,
        error_message:
          error instanceof HttpException ? error.message : 'unknown error',
        message: 'individual conversation processing failed',
        elapsed_time: Date.now() - startTime,
      });

      throw error;
    }
  }
}
