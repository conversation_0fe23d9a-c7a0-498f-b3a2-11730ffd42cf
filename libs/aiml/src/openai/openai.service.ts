import { BadRequestException, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AIProjectClient } from '@azure/ai-projects';
import {
  RunStreamEvent,
  MessageStreamEvent,
  ErrorEvent,
  DoneEvent,
  ThreadsCreateThreadOptionalParams,
  MessageDeltaChunk,
  MessageDeltaTextContent,
  AgentThread,
} from '@azure/ai-agents';
import { DefaultAzureCredential } from '@azure/identity';
import { AI_MESSAGE_INCOMING_TRIGGER } from '@core/libs';
import { SentenceExtractor } from '../shared/sentence-extractor.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { ClientProxy } from '@nestjs/microservices';
import { Logger } from 'nestjs-pino';
import { v4 as uuidv4 } from 'uuid';
import { SystemConfigService } from '@core_be/global';

type OpenAIResponse = {
  threadId: string;
};

type AilmentObject = {
  name: string;
  level: number;
  location: string;
  situation: string;
  is_situational: boolean;
  is_pain: boolean;
  description: string;
};

export type ThreadRunOptions = {
  threadId: string;
  assistant_id: string;
  isJsonOutput: true | false;
  region?: string;
  wsEventOptions?: {
    emitter: EventEmitter2 | ClientProxy;
    profileId: number;
    sessionId: number;
    wsEvent: string;
  };
};

type AssistantThreadRunOptions = ThreadRunOptions & {
  isJsonOutput: false;
};

type AssistantJsonThreadRunOptions = ThreadRunOptions & {
  isJsonOutput: true;
};

export type IncrementalText = {
  latest: string;
  accumulator: string[];
  sent: string[];
};

export interface AssistantResponse extends OpenAIResponse {
  output: string;
  trigger: string[];
}

export interface AssistantJsonResponse extends OpenAIResponse {
  output: Record<string, unknown>;
}

export interface AilmentAssistantResponse extends AssistantJsonResponse {
  output: {
    ailments: AilmentObject[];
    is_zero_pain: boolean;
  };
}

export class ContentFilterException extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'ContentFilterException';
  }
}

@Injectable()
export class LibOpenaiService {
  private aiFoundryProjectClient?: AIProjectClient;
  private readonly triggerValues = Object.values(AI_MESSAGE_INCOMING_TRIGGER);
  // Regex-based forbidden prompt injection patterns
  private readonly forbiddenPatterns: RegExp[] = [
    /ignore\s+previous\s+instructions/i,
    /act\s+as/i,
    /you\s+are\s+now/i,
    /disregard\s+(the\s+)?above/i,
    /forget\s+(the\s+)?previous/i,
    /jailbreak/i,
    /system\s+prompt/i,
    /pretend\s+to\s+be/i,
    /override\s+.*instructions/i,
  ];

  constructor(
    private readonly configService: ConfigService,
    private readonly logger: Logger,
    private readonly systemConfigService: SystemConfigService
  ) {
    // Initialize with default configuration - will be updated dynamically as needed
    this.initializeAIFoundryClient().catch((error) => {
      this.logger.error(
        'Failed to initialize AI Foundry client in constructor',
        error
      );
    });
  }

  private async initializeAIFoundryClient(region?: string) {
    try {
      const endpoint =
        await this.systemConfigService.getAIFoundryProjectEndpoint(region);
      if (endpoint) {
        this.aiFoundryProjectClient = new AIProjectClient(
          endpoint,
          new DefaultAzureCredential()
        );
      } else {
        // Fallback to config service with env variables if database system config is not available
        const fallbackEndpoint = this.configService.getOrThrow<string>(
          'AI_FOUNDRY_PROJECT_ENDPOINT'
        );
        this.aiFoundryProjectClient = new AIProjectClient(
          fallbackEndpoint,
          new DefaultAzureCredential()
        );
      }
    } catch (error) {
      this.logger.error('Failed to initialize AI Foundry client', error);
      // Fallback to config service
      const fallbackEndpoint = this.configService.getOrThrow<string>(
        'AI_FOUNDRY_PROJECT_ENDPOINT'
      );
      this.aiFoundryProjectClient = new AIProjectClient(
        fallbackEndpoint,
        new DefaultAzureCredential()
      );
    }
  }

  private async getAIFoundryClient(region?: string): Promise<AIProjectClient> {
    await this.initializeAIFoundryClient(region);
    if (!this.aiFoundryProjectClient) {
      throw new Error('Failed to initialize AI Foundry client');
    }
    return this.aiFoundryProjectClient;
  }

  private async openaiThreadRun(
    options: AssistantThreadRunOptions
  ): Promise<AssistantResponse>;

  private async openaiThreadRun<T extends AssistantJsonResponse>(
    options: AssistantJsonThreadRunOptions
  ): Promise<T>;

  private async openaiThreadRun(
    options: ThreadRunOptions
  ): Promise<AssistantResponse | AssistantJsonResponse> {
    const threadRunStartTime = Date.now();
    const threadRunTraceId = uuidv4();
    let isIncomplete = false;
    let incompleteReason = '';
    let output = '';
    const incremental: IncrementalText = {
      latest: '',
      accumulator: [],
      sent: [],
    };
    const extractor = new SentenceExtractor();

    const { assistant_id, isJsonOutput = false, threadId, region } = options;

    this.logger.log('Starting thread run', {
      threadId,
      assistant_id,
      elapsed_time: Date.now() - threadRunStartTime,
      trace_id: threadRunTraceId,
    });

    const client = await this.getAIFoundryClient(region);
    const streamEventMessages = await client.agents.runs
      .create(threadId, assistant_id)
      .stream();

    for await (const eventMessage of streamEventMessages) {
      switch (eventMessage.event) {
        case RunStreamEvent.ThreadRunQueued: {
          this.logger.debug('Thread run queued', {
            threadId,
            assistant_id,
            event: eventMessage.event,
            elapsed_time: Date.now() - threadRunStartTime,
            trace_id: threadRunTraceId,
          });
          break;
        }
        case RunStreamEvent.ThreadRunCreated: {
          this.logger.debug('Thread run created', {
            threadId,
            assistant_id,
            event: eventMessage.event,
            elapsed_time: Date.now() - threadRunStartTime,
            trace_id: threadRunTraceId,
          });
          break;
        }
        case RunStreamEvent.ThreadRunCompleted: {
          this.logger.log('Thread run completed', {
            threadId,
            assistant_id,
            event: eventMessage.event,
            elapsed_time: Date.now() - threadRunStartTime,
            trace_id: threadRunTraceId,
          });
          break;
        }
        case RunStreamEvent.ThreadRunIncomplete: {
          // Evaluate Thread Run Incomplete detail
          isIncomplete = true;
          const threadRunIncomplete = eventMessage.data as any; // The type for ThreadRun has incorrect casing.
          incompleteReason =
            threadRunIncomplete?.incomplete_details?.reason || 'Unknown';

          this.logger.warn('Thread run incomplete', {
            threadId,
            assistant_id,
            event: eventMessage.event,
            elapsed_time: Date.now() - threadRunStartTime,
            incompleteReason,
            trace_id: threadRunTraceId,
          });
          break;
        }
        case MessageStreamEvent.ThreadMessageDelta: {
          const textDelta = (eventMessage.data as MessageDeltaChunk).delta;
          const textDeltaValue = textDelta.content
            .filter(
              (content): content is MessageDeltaTextContent =>
                content.type === 'text'
            )
            .map((content) => content.text?.value)
            .join('');

          output += textDeltaValue;

          incremental.accumulator = extractor.processText('' + textDeltaValue);
          if (incremental.accumulator.length > 0) {
            incremental.latest = incremental.accumulator.join(' ').trim();
          }
          break;
        }
        case ErrorEvent.Error: {
          this.logger.error('Error Event', {
            threadId,
            assistant_id,
            event: eventMessage.event,
            elapsed_time: Date.now() - threadRunStartTime,
            trace_id: threadRunTraceId,
          });
          throw new Error('Error Event: ' + eventMessage.data);
        }
        case DoneEvent.Done: {
          if (isIncomplete) {
            throw new ContentFilterException(
              `Run Incomplete: ${incompleteReason}`
            );
          } else {
            if (!isJsonOutput) {
              const filteredOutput = this.removeRagReferences(output);
              return {
                output: filteredOutput,
                threadId,
                trigger: this.triggerValues.filter((trigger) =>
                  output.includes(trigger)
                ),
              };
            } else {
              let jsonResponse: AssistantJsonResponse['output'] = {};
              try {
                jsonResponse = JSON.parse(output);
                return {
                  output: jsonResponse,
                  threadId,
                };
              } catch (e: any) {
                throw new Error(
                  'JSON Parse Error: OpenAI Response: ' + e.message
                );
              }
            }
          }
        }
        default: {
          break;
        }
      }
    }

    throw new Error('Event stream did not complete properly');
  }

  private removeRagReferences(output: string): string {
    return output.replace(/【[\\/w\\/W\\/s]*.*】[\\/s]*/gi, '');
  }

  private async openaiThreadRunRetry<
    T extends AssistantResponse | AssistantJsonResponse
  >(options: ThreadRunOptions, retry = 5): Promise<T> {
    const threadRunRetryTraceId = uuidv4();
    for (let attempt = 1; attempt <= retry; attempt++) {
      try {
        this.logger.log('Attempting thread run', {
          threadId: options.threadId,
          assistant_id: options.assistant_id,
          attempt,
          trace_id: threadRunRetryTraceId,
        });
        if (options.isJsonOutput) {
          return (await this.openaiThreadRun(
            options as AssistantJsonThreadRunOptions
          )) as T;
        } else {
          return (await this.openaiThreadRun(
            options as AssistantThreadRunOptions
          )) as T;
        }
      } catch (error: any) {
        if (error instanceof ContentFilterException) {
          this.logger.log('Content filter exception', {
            threadId: options.threadId,
            assistant_id: options.assistant_id,
            attempt,
            error_message: error.message,
            trace_id: threadRunRetryTraceId,
          });
          throw new BadRequestException(
            'Input contains potentially malicious instructions.'
          );
        }

        if (attempt === retry) {
          this.logger.error('All retry attempts failed', {
            threadId: options.threadId,
            assistant_id: options.assistant_id,
            attempt,
            error_message: error.message,
            trace_id: threadRunRetryTraceId,
          });
          throw error;
        }
        // add a delay before retrying
        const delay = Math.min(10000 * attempt, 30000); // max 30 seconds delay

        this.logger.warn('Thread run failed, retrying...', {
          threadId: options.threadId,
          assistant_id: options.assistant_id,
          attempt,
          delay,
          error_message: error.message,
          trace_id: threadRunRetryTraceId,
        });
        await this.awaitThreadRunOrCancel(options.threadId, options.region);
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }
    throw new Error('All retry attempts failed');
  }

  sanitizeInput(input: string): void {
    if (input) {
      const normalized = input.toLowerCase().trim();
      for (const pattern of this.forbiddenPatterns) {
        if (pattern.test(normalized)) {
          throw new BadRequestException(
            'Input contains potentially malicious instructions.'
          );
        }
      }
    }
  }

  async startNewThread(): Promise<AgentThread> {
    const client = await this.getAIFoundryClient();
    const threadOptions: ThreadsCreateThreadOptionalParams = {};
    const thread = await client.agents.threads.create(threadOptions);
    this.logger.debug('Created agent thread', {
      threadId: thread.id,
    });
    return thread;
  }

  async addMessageToThread(
    threadId: string,
    role: 'user' | 'assistant' = 'user',
    content: string,
    region?: string
  ) {
    this.sanitizeInput(content);
    const client = await this.getAIFoundryClient(region);
    await client.agents.messages.create(threadId, role, content);
    this.logger.debug('Pushed thread message', {
      threadId,
      role,
    });
  }

  async createConversationAssistant(
    content: string,
    assistant_id: string,
    role: 'user' | 'assistant' = 'user',
    isJsonOutput = false,
    wsEventOptions?: ThreadRunOptions['wsEventOptions']
  ): Promise<AssistantResponse> {
    const thread = await this.startNewThread();
    const threadId = thread.id;

    return this.continueConversationAssistant(
      threadId,
      content,
      assistant_id,
      role,
      isJsonOutput,
      wsEventOptions
    );
  }

  async continueConversationAssistant(
    threadId: string,
    content: string,
    assistant_id: string,
    role: 'user' | 'assistant' = 'user',
    isJsonOutput = false,
    wsEventOptions?: ThreadRunOptions['wsEventOptions'],
    region?: string
  ): Promise<AssistantResponse> {
    if (content) {
      await this.addMessageToThread(threadId, role, content, region);
    }

    return this.openaiThreadRunRetry(
      {
        threadId,
        assistant_id,
        isJsonOutput,
        region,
        wsEventOptions,
      },
      10
    );
  }

  async canRunThread(
    threadId: string,
    cancel = false,
    region?: string
  ): Promise<boolean> {
    const client = await this.getAIFoundryClient(region);
    const threadRuns = await client.agents.runs.list(threadId);

    const latestRun = await threadRuns.next();
    if (!latestRun) return true;
    if (latestRun.value.status === 'completed') return true;

    const isActiveRun = ['queued', 'in_progress'].includes(
      latestRun.value.status
    );
    if (cancel && isActiveRun) {
      await client.agents.runs.cancel(threadId, latestRun.value.id);
    }
    return cancel ? true : !isActiveRun;
  }

  async awaitThreadRunOrCancel(threadId: string, region?: string) {
    let canRunThread = false;
    for (let attempt = 1; attempt <= 6 && !canRunThread; attempt++) {
      // if the thread can run, break the loop and exit immediately
      canRunThread = await this.canRunThread(threadId, false, region);
      if (canRunThread) break;

      // add a delay of 10 seconds before checking again
      await new Promise((resolve) => setTimeout(resolve, 10000));
    }
    if (!canRunThread) {
      await this.cancelThreadRun(threadId, region);
    }
  }

  private async cancelThreadRun(threadId: string, region?: string) {
    const client = await this.getAIFoundryClient(region);
    const threadRuns = await client.agents.runs.list(threadId);
    const latestRun = await threadRuns.next();
    if (!latestRun) return;
    await client.agents.runs.cancel(threadId, latestRun.value.id);
  }

  async extractAilments(
    content: string,
    region?: string
  ): Promise<AilmentAssistantResponse> {
    const ailmentStartTime = Date.now();

    this.sanitizeInput(content);

    // Ensure AI Foundry client is initialized with the correct region
    const client = await this.getAIFoundryClient(region);

    const thread = await client.agents.threads.create({
      messages: [
        {
          role: 'user',
          content: content,
        },
      ],
    });
    this.logger.debug('Created agent thread for extracting ailments', {
      threadId: thread.id,
      elapsed_time: Date.now() - ailmentStartTime,
    });

    // Get dynamic ailment assistant ID
    const ailmentAssistantID =
      await this.systemConfigService.getAilmentExtractionAssistantId(region);
    if (!ailmentAssistantID) {
      throw new Error(
        'Ailment extraction assistant ID not found in system configuration'
      );
    }

    const response = this.openaiThreadRunRetry<AilmentAssistantResponse>(
      {
        threadId: thread.id,
        assistant_id: ailmentAssistantID,
        isJsonOutput: true,
        region,
      },
      10
    );

    return response;
  }
}
