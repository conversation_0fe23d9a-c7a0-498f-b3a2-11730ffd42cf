import { BadRequestException, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AIProjectClient } from '@azure/ai-projects';
import {
  RunStreamEvent,
  MessageStreamEvent,
  ErrorEvent,
  DoneEvent,
  ThreadsCreateThreadOptionalParams,
  MessageDeltaChunk,
  MessageDeltaTextContent,
  AgentThread,
} from '@azure/ai-agents';
import { DefaultAzureCredential } from '@azure/identity';
import {
  AI_MESSAGE_INCOMING_TRIGGER,
  MeasurePerformance,
  PerformanceObject,
} from '@core/libs';
import { SentenceExtractor } from '../shared/sentence-extractor.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { ClientProxy } from '@nestjs/microservices';
import { Logger } from 'nestjs-pino';

type OpenAIResponse = {
  threadId: string;
  performance: PerformanceObject[];
};

type AilmentObject = {
  name: string;
  level: number;
  location: string;
  situation: string;
  is_situational: boolean;
  is_pain: boolean;
  description: string;
};

export type ThreadRunOptions = {
  threadId: string;
  assistant_id: string;
  isJsonOutput: true | false;
  wsEventOptions?: {
    emitter: EventEmitter2 | ClientProxy;
    profileId: number;
    sessionId: number;
    wsEvent: string;
  };
};

type AssistantThreadRunOptions = ThreadRunOptions & {
  isJsonOutput: false;
};

type AssistantJsonThreadRunOptions = ThreadRunOptions & {
  isJsonOutput: true;
};

export type IncrementalText = {
  latest: string;
  accumulator: string[];
  sent: string[];
};

export interface AssistantResponse extends OpenAIResponse {
  output: string;
  trigger: string[];
}

export interface AssistantJsonResponse extends OpenAIResponse {
  output: Record<string, unknown>;
}

export interface AilmentAssistantResponse extends AssistantJsonResponse {
  output: {
    ailments: AilmentObject[];
    is_zero_pain: boolean;
  };
}

export class ContentFilterException extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'ContentFilterException';
  }
}

@Injectable()
export class LibOpenaiService {
  private readonly aiFoundryProjectClient: AIProjectClient;

  private readonly ailmentAssistantID: string;
  private readonly summaryAssistantID: string;
  private readonly triggerValues = Object.values(AI_MESSAGE_INCOMING_TRIGGER);
  // Regex-based forbidden prompt injection patterns
  private readonly forbiddenPatterns: RegExp[] = [
    /ignore\s+previous\s+instructions/i,
    /act\s+as/i,
    /you\s+are\s+now/i,
    /disregard\s+(the\s+)?above/i,
    /forget\s+(the\s+)?previous/i,
    /jailbreak/i,
    /system\s+prompt/i,
    /pretend\s+to\s+be/i,
    /override\s+.*instructions/i,
  ];

  constructor(
    private readonly configService: ConfigService,
    private readonly performance: MeasurePerformance,
    private readonly logger: Logger
  ) {
    // const apiKey = this.configService.getOrThrow<string>('OPENAI_TOKEN');
    // const azureOpenAIVersion = this.configService.get<string>('OPENAI_API_VERSION');
    this.ailmentAssistantID = this.configService.getOrThrow<string>(
      'AILMENT_EXTRACTION_ASSISTANT_ID'
    );
    this.summaryAssistantID = this.configService.getOrThrow<string>(
      'SUMMARY_ASSISTANT_ID'
    );

    const endpoint = this.configService.getOrThrow<string>(
      'AI_FOUNDRY_PROJECT_ENDPOINT'
    );

    this.aiFoundryProjectClient = new AIProjectClient(
      endpoint,
      new DefaultAzureCredential()
    );
  }

  private async openaiThreadRun(
    options: AssistantThreadRunOptions,
    performance?: MeasurePerformance
  ): Promise<AssistantResponse>;

  private async openaiThreadRun<T extends AssistantJsonResponse>(
    options: AssistantJsonThreadRunOptions,
    performance?: MeasurePerformance
  ): Promise<T>;

  private async openaiThreadRun(
    options: ThreadRunOptions,
    performance?: MeasurePerformance
  ): Promise<AssistantResponse | AssistantJsonResponse> {
    let isIncomplete = false;
    let incompleteReason = '';
    let output = '';
    const incremental: IncrementalText = {
      latest: '',
      accumulator: [],
      sent: [],
    };
    const extractor = new SentenceExtractor();

    performance = performance || this.performance;

    const { assistant_id, isJsonOutput = false, threadId } = options;

    performance.push(`${threadId}, ${assistant_id} | Start `);

    const streamEventMessages = await this.aiFoundryProjectClient.agents.runs
      .create(threadId, assistant_id)
      .stream();

    for await (const eventMessage of streamEventMessages) {
      switch (eventMessage.event) {
        case RunStreamEvent.ThreadRunCreated: {
          performance.push(
            `${threadId}, ${assistant_id} | (${eventMessage.event}) Run Created `
          );
          break;
        }
        case RunStreamEvent.ThreadRunCompleted: {
          performance.push(
            `${threadId}, ${assistant_id} | (${eventMessage.event}) Run Completed `
          );
          break;
        }
        case RunStreamEvent.ThreadRunIncomplete: {
          // Evaluate Thread Run Incomplete detail
          isIncomplete = true;
          const threadRunIncomplete = eventMessage.data as any; // The type for ThreadRun has incorrect casing.
          incompleteReason =
            threadRunIncomplete?.incomplete_details?.reason || 'Unknown';

          performance.push(
            `${threadId}, ${assistant_id} | (${eventMessage.event}) Run Incomplete: ${incompleteReason}`
          );
          break;
        }
        case MessageStreamEvent.ThreadMessageDelta: {
          const textDelta = (eventMessage.data as MessageDeltaChunk).delta;
          const textDeltaValue = textDelta.content
            .filter(
              (content): content is MessageDeltaTextContent =>
                content.type === 'text'
            )
            .map((content) => content.text?.value)
            .join('');

          output += textDeltaValue;

          incremental.accumulator = extractor.processText('' + textDeltaValue);
          if (incremental.accumulator.length > 0) {
            incremental.latest = incremental.accumulator.join(' ').trim();
          }
          break;
        }
        case ErrorEvent.Error: {
          throw new Error('Error Event: ' + eventMessage.data);
        }
        case DoneEvent.Done: {
          if (isIncomplete) {
            throw new ContentFilterException(
              `Run Incomplete: ${incompleteReason}`
            );
          } else {
            if (!isJsonOutput) {
              const filteredOutput = this.removeRagReferences(output);
              return {
                output: filteredOutput,
                threadId,
                performance: performance.get(),
                trigger: this.triggerValues.filter((trigger) =>
                  output.includes(trigger)
                ),
              };
            } else {
              let jsonResponse: AssistantJsonResponse['output'] = {};
              try {
                jsonResponse = JSON.parse(output);
                return {
                  output: jsonResponse,
                  threadId,
                  performance: performance.get(),
                };
              } catch (e: any) {
                throw new Error(
                  'JSON Parse Error: OpenAI Response: ' + e.message
                );
              }
            }
          }
        }
        default: {
          performance?.debug('unhandled event', { event: eventMessage.event });
          break;
        }
      }
    }

    throw new Error('Event stream did not complete properly');
  }

  private removeRagReferences(output: string): string {
    return output.replace(/【[\\/w\\/W\\/s]*.*】[\\/s]*/gi, '');
  }

  private async openaiThreadRunRetry<
    T extends AssistantResponse | AssistantJsonResponse
  >(
    options: ThreadRunOptions,
    retry = 5,
    performance?: MeasurePerformance
  ): Promise<T> {
    for (let attempt = 1; attempt <= retry; attempt++) {
      try {
        performance?.push(`Attempt ${attempt} => Thread Run`);
        if (options.isJsonOutput) {
          return (await this.openaiThreadRun(
            options as AssistantJsonThreadRunOptions,
            performance
          )) as T;
        } else {
          return (await this.openaiThreadRun(
            options as AssistantThreadRunOptions,
            performance
          )) as T;
        }
      } catch (error: any) {
        if (error instanceof ContentFilterException) {
          performance?.warn('content filter exception', {
            error_message: error.message,
          });
          throw new BadRequestException(
            'Input contains potentially malicious instructions.'
          );
        }

        console.error(
          `Attempt ${attempt} failed for thread run with ID: ${options.threadId}.`,
          error.message || error
        );
        performance?.push(`Attempt ${attempt} failed`);
        if (attempt === retry) {
          throw error;
        }
        console.warn(`Attempt ${attempt} failed. Retrying...`);
        await this.awaitThreadRunOrCancel(options.threadId);
        // add a delay before retrying
        const delay = Math.min(10000 * attempt, 30000); // max 30 seconds delay
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }
    throw new Error('All retry attempts failed');
  }

  sanitizeInput(input: string): void {
    if (input) {
      const normalized = input.toLowerCase().trim();
      for (const pattern of this.forbiddenPatterns) {
        if (pattern.test(normalized)) {
          throw new BadRequestException(
            'Input contains potentially malicious instructions.'
          );
        }
      }
    }
  }

  async startNewThread(performance?: MeasurePerformance): Promise<AgentThread> {
    const threadOptions: ThreadsCreateThreadOptionalParams = {};
    const thread = await this.aiFoundryProjectClient.agents.threads.create(
      threadOptions
    );
    performance?.push('created agent thread');
    return thread;
  }

  async addMessageToThread(
    threadId: string,
    role: 'user' | 'assistant' = 'user',
    content: string,
    performance?: MeasurePerformance
  ) {
    this.sanitizeInput(content);
    await this.aiFoundryProjectClient.agents.messages.create(
      threadId,
      role,
      content
    );
    performance?.push('pushed thread message');
  }

  async createConversationAssistant(
    content?: string,
    assistant_id?: string,
    role: 'user' | 'assistant' = 'user',
    isJsonOutput = false,
    performance?: MeasurePerformance,
    wsEventOptions?: ThreadRunOptions['wsEventOptions']
  ): Promise<AssistantResponse> {
    const thread = await this.startNewThread(performance);
    const threadId = thread.id;

    return this.continueConversationAssistant(
      threadId,
      content,
      assistant_id,
      role,
      isJsonOutput,
      performance,
      wsEventOptions
    );
  }

  async continueConversationAssistant(
    threadId: string,
    content?: string,
    assistant_id?: string,
    role: 'user' | 'assistant' = 'user',
    isJsonOutput = false,
    performance?: MeasurePerformance,
    wsEventOptions?: ThreadRunOptions['wsEventOptions']
  ): Promise<AssistantResponse> {
    if (content) {
      await this.addMessageToThread(threadId, role, content, performance);
    }

    return this.openaiThreadRunRetry(
      {
        threadId,
        assistant_id: assistant_id || this.summaryAssistantID,
        isJsonOutput: isJsonOutput,
        wsEventOptions,
      },
      10,
      performance
    );
  }

  async canRunThread(threadId: string, cancel = false): Promise<boolean> {
    const threadRuns = await this.aiFoundryProjectClient.agents.runs.list(
      threadId
    );

    const latestRun = await threadRuns.next();
    if (!latestRun) return true;
    if (latestRun.value.status === 'completed') return true;

    const isActiveRun = ['queued', 'in_progress'].includes(
      latestRun.value.status
    );
    if (cancel && isActiveRun) {
      await this.aiFoundryProjectClient.agents.runs.cancel(
        threadId,
        latestRun.value.id
      );
    }
    return cancel ? true : !isActiveRun;
  }

  async awaitThreadRunOrCancel(threadId: string) {
    let canRunThread = false;
    for (let attempt = 1; attempt <= 6 && !canRunThread; attempt++) {
      // if the thread can run, break the loop and exit immediately
      canRunThread = await this.canRunThread(threadId);
      if (canRunThread) break;

      // add a delay of 10 seconds before checking again
      await new Promise((resolve) => setTimeout(resolve, 10000));
    }
    if (!canRunThread) {
      await this.cancelThreadRun(threadId);
    }
  }

  private async cancelThreadRun(threadId: string) {
    const threadRuns = await this.aiFoundryProjectClient.agents.runs.list(
      threadId
    );
    const latestRun = await threadRuns.next();
    if (!latestRun) return;
    await this.aiFoundryProjectClient.agents.runs.cancel(
      threadId,
      latestRun.value.id
    );
  }

  async extractAilments(content: string): Promise<AilmentAssistantResponse> {
    this.sanitizeInput(content);
    const thread = await this.aiFoundryProjectClient.agents.threads.create({
      messages: [
        {
          role: 'user',
          content: content,
        },
      ],
    });

    return this.openaiThreadRunRetry<AilmentAssistantResponse>(
      {
        threadId: thread.id,
        assistant_id: this.ailmentAssistantID,
        isJsonOutput: true,
      },
      10
    );
  }
}
