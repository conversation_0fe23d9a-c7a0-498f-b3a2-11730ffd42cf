import { Test, TestingModule } from '@nestjs/testing';
import { LibOpenaiService } from './openai.service';

describe('LibOpenaiService', () => {
  let service: LibOpenaiService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [LibOpenaiService],
    }).compile();

    service = module.get<LibOpenaiService>(LibOpenaiService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
