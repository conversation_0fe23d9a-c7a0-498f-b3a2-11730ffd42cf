import { ApiProperty } from '@nestjs/swagger';

export class MessageDto {
  @ApiProperty({
    example: 'user',
    description: 'Role of the message sender',
    required: true,
  })
  role: string;

  @ApiProperty({
    example: 'What is OpenAPI?',
    description: 'Content of the message',
    required: true,
  })
  content: string;

  @ApiProperty({
    example: 1,
    description: 'Profile id',
    required: true,
  })
  profile_id: string;

  @ApiProperty({
    example: 1,
    description: 'Session id',
    required: true,
  })
  session_id: string;
}

export class ChatOpenaiDto {
  @ApiProperty({
    example: 'gpt-3.5-turbo',
    description: 'Model Name',
    required: true,
  })
  model: string;

  @ApiProperty({
    type: [MessageDto],
    description: 'Array of messages',
    required: true,
  })
  messages: Array<{ role: string; content: string }>; // Array of objects with specific properties

  @ApiProperty({
    example: 1,
    description: 'Profile id',
    required: true,
  })
  profile_id: number;

  @ApiProperty({
    example: 1,
    description: 'Session id',
    required: true,
  })
  session_id: number;
}

export class SessionSummariesDto {
  @ApiProperty({
    example: 1,
    description: 'session id',
    required: true,
  })
  session_id: number;

  @ApiProperty({
    example: 'Say this is a test',
    description: 'conversation',
    required: true,
  })
  prompt: string;
}
