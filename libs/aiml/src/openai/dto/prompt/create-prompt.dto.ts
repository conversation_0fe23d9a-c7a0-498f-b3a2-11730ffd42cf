import { ApiProperty } from '@nestjs/swagger';

export class MessageDto {
  @ApiProperty({
    example: 'user',
    description: 'Role of the message sender',
    required: true,
  })
  role: string;

  @ApiProperty({
    example: 'What is OpenAPI?',
    description: 'Content of the message',
    required: true,
  })
  content: string;
}

export class PromptOpenaiDto {
  @ApiProperty({
    example: 'gpt-3.5-turbo-instruct',
    description: 'Model Name',
    required: false,
  })
  model?: string;

  @ApiProperty({
    example: 'Write a limmerick about APIs',
    description: 'Prompt',
    required: false,
  })
  prompt?: string; // Array of objects with specific properties

  @ApiProperty({
    example: 'Write a limmerick about APIs',
    description: 'Content',
    required: false,
  })
  content?: string;

  @ApiProperty({
    type: [MessageDto],
    description: 'Array of messages',
    required: false,
  })
  message: Array<{ role: string; content: string }>; // Array of objects with specific properties
}
