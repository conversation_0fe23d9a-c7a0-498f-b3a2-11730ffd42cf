import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import WebSocket from 'ws';
import { map } from 'rxjs/operators';
import { firstValueFrom, BehaviorSubject, Observable } from 'rxjs';
import { Logger } from 'nestjs-pino';

@Injectable()
export class LibHumeService {
  private readonly apiKey: string | undefined;
  private readonly clientSecret: string | undefined;
  private readonly clientCredentials: string;
  private socket!: WebSocket;
  private receivedMessageSubject = new BehaviorSubject<any[]>([]);

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
    private readonly logger: Logger
  ) {
    this.apiKey = this.configService.get('HUME_API_KEY');
    this.clientSecret = this.configService.get('HUME_CLIENT_SECRET');
    this.clientCredentials = Buffer.from(
      `${this.apiKey}:${this.clientSecret}`
    ).toString('base64');
  }

  async connect(message: string): Promise<string> {
    try {
      const accessToken = await this.getAccessToken();
      this.socket = new WebSocket(
        `wss://api.hume.ai/v0/evi/chat?access_token=${accessToken}`
      );

      // Handle WebSocket events
      this.socket.on('open', async () => {
        await this.sendMessage(message);
      });

      return new Promise((resolve, reject) => {
        this.socket.on('message', (data) => {
          this.handleWebSocketMessageEvent(data);
          resolve('Message sent successfully');
        });

        this.socket.on('error', (error) => {
          this.logger.error('WebSocket error:', error);
          reject(error);
        });

        this.socket.on('close', () => {
          this.logger.log('WebSocket connection closed');
        });
      });
    } catch (error) {
      this.logger.error('Failed to send user input message:', error);
      return 'Failed to send user input message';
    }
  }

  async sendMessage(message: string): Promise<void> {
    try {
      if (this.socket.readyState === WebSocket.OPEN) {
        const userInputMessage = JSON.stringify(message);

        this.socket.send(userInputMessage);
      } else {
        throw new Error('WebSocket connection is not open');
      }
    } catch (error) {
      this.logger.error('Failed to send user input message:', error);
    }
  }

  private handleWebSocketMessageEvent(data: WebSocket.Data): void {
    try {
      const messageString = Buffer.isBuffer(data) ? data.toString('utf-8') : '';
      const receivedMessage = JSON.parse(messageString);
      this.receivedMessageSubject.next(receivedMessage);
    } catch (error) {
      this.logger.error('Error handling WebSocket message:', error);
    }
  }

  getReceivedMessage(): Observable<any> {
    return this.receivedMessageSubject.asObservable();
  }

  async getAccessToken(): Promise<string> {
    const data = 'grant_type=client_credentials';
    const config = {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        Authorization: `Basic ${this.clientCredentials}`,
      },
    };

    try {
      const response = await firstValueFrom(
        this.httpService
          .post('https://api.hume.ai/oauth2-cc/token', data, config)
          .pipe(map((res) => res.data))
      );
      return response.access_token;
    } catch (error) {
      this.logger.error('Failed to retrieve access token', error);
      throw new Error('Failed to retrieve access token');
    }
  }
}
