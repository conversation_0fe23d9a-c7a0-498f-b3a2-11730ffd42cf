import { Test, TestingModule } from '@nestjs/testing';
import { LibHumeService } from './hume.service';

describe('LibHumeService', () => {
  let service: LibHumeService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [LibHumeService],
    }).compile();

    service = module.get<LibHumeService>(LibHumeService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
