import { Global, Module } from '@nestjs/common';
import {
  LibNotificationService,
  LibNotificationsModule,
} from '@core_be/notifications';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AIMessagingService } from './shared/messaging.service';
import { AIHealingRoundService } from './shared/healing-round.service';
import { LibAIIntakeService } from './shared/ai-intake.service';
import { SummaryAilmentService } from './shared/summary-ailment.service';
import { LibDataAccessModule } from '@core_be/data-access';
import { LibOpenaiService } from './openai/openai.service';
import { EventEmitterModule } from '@nestjs/event-emitter';
import {
  CONVERSATION_LISTEN,
  HEALING_ROUND_LISTEN,
  LibSessionService,
  MeasurePerformance,
  SESSION_STATUS,
  WS_LISTEN,
} from '@core_be/global';
import { ConversationListenerService } from './shared/conversation-listener.service';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { PublisherService } from 'libs/global/src/lib/queue/publisher.service';
import { HealerStatusRepository } from 'libs/data-access/src/lib/healer_status/healer_status.repository';

@Global()
@Module({
  imports: [
    LibDataAccessModule,
    LibNotificationsModule,
    ClientsModule.registerAsync([
      {
        imports: [ConfigModule],
        name: WS_LISTEN.DELIMITER,
        useFactory: async (configService: ConfigService) => ({
          transport: Transport.REDIS,
          options: {
            host: configService.getOrThrow<string>('REDIS_HOST'),
            port: parseInt(configService.getOrThrow<string>('REDIS_PORT')),
            wildcards: true,
          },
        }),
        inject: [ConfigService],
      },
    ]),
    EventEmitterModule.forRoot({
      delimiter: CONVERSATION_LISTEN.DELIMITER,
      ignoreErrors: false,
      maxListeners: 1,
      wildcard: true,
      verboseMemoryLeak: true,
    }),
    EventEmitterModule.forRoot({
      delimiter: HEALING_ROUND_LISTEN.DELIMITER,
      ignoreErrors: false,
      maxListeners: 1,
      wildcard: true,
      verboseMemoryLeak: true,
    }),
    EventEmitterModule.forRoot({
      delimiter: SESSION_STATUS.DELIMITER,
      ignoreErrors: false,
      maxListeners: 1,
      wildcard: true,
      verboseMemoryLeak: true,
    }),
  ],
  controllers: [],
  providers: [
    // with event listeners
    LibAIIntakeService,
    AIHealingRoundService,
    AIMessagingService,
    SummaryAilmentService,
    ConversationListenerService,
    // ---------
    ConfigService,
    LibOpenaiService,
    LibSessionService,
    LibNotificationService,
    PublisherService,
    HealerStatusRepository,
    MeasurePerformance,
  ],
  exports: [
    // with event listeners
    LibAIIntakeService,
    AIHealingRoundService,
    AIMessagingService,
    SummaryAilmentService,
    ConversationListenerService,
    // ---------
    LibOpenaiService,
  ],
})
export class LibAimlModule {}
