import {
  WebSocketGateway,
  SubscribeMessage,
  WebSocketServer,
  OnGatewayInit,
  OnGatewayConnection,
  OnGatewayDisconnect,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';

@WebSocketGateway()
export class SocketGateway
  implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect
{
  @WebSocketServer()
  server!: Server;

  // Store connected clients by ID
  private clients: Map<string, Socket> = new Map();

  afterInit(server: Server) {
    console.log('Socket.IO Gateway initialized');
  }

  handleConnection(client: Socket) {
    console.log(`Client connected: ${client.id}`);
  }

  handleDisconnect(client: Socket) {
    console.log(`Client disconnected: ${client.id}`);
  }

  @SubscribeMessage('message')
  handleMessage(client: Socket, payload: string): string {
    console.log(`Received message: ${payload}`);
    this.server.emit('message', `Server: ${payload}`);
    return `Server: ${payload}`;
  }

  sendMessageToClient(clientId: string, message: string) {
    const client = this.clients.get(clientId);
    if (client) {
      client.emit('message', { senderId: 'server', message });
      console.log(`Message sent to client ${clientId}: ${message}`);
    } else {
      console.log(`Client with ID ${clientId} not found`);
    }
  }
}
