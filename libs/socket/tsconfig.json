{"extends": "../../tsconfig.base.json", "compilerOptions": {"forceConsistentCasingInFileNames": true, "strict": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noImplicitReturns": true, "composite": true, "declaration": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "noFallthroughCasesInSwitch": true, "esModuleInterop": true}, "files": [], "include": [], "references": [{"path": "./tsconfig.lib.json"}, {"path": "./tsconfig.spec.json"}]}