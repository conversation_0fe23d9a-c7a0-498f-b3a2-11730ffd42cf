-- CreateEnum
CREATE TYPE "SessionFollowUpInterval" AS ENUM ('TWENTY_FOUR_HRS', 'SEVEN_DAYS', 'NONE');

-- CreateEnum
CREATE TYPE "SessionFollowUpStatus" AS ENUM ('PENDING', 'COMPLETED', 'FAILED');

-- CreateTable
CREATE TABLE "session_follow_up" (
    "session_follow_up_id" SERIAL NOT NULL,
    "session_id" INTEGER NOT NULL,
    "user_id" INTEGER NOT NULL,
    "follow_up_interval" "SessionFollowUpInterval" NOT NULL DEFAULT 'NONE',
    "follow_up_at" TIMESTAMPTZ(6),
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(6),
    "is_deleted" BOOLEAN DEFAULT false,
    "status" "SessionFollowUpStatus" NOT NULL DEFAULT 'PENDING',

    CONSTRAINT "session_follow_up_pkey" PRIMARY KEY ("session_follow_up_id")
);
