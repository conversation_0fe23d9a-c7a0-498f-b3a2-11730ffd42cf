-- CreateTable
CREATE TABLE "metrics" (
    "metric_id" SERIAL NOT NULL,
    "app_state" TEXT,
    "app_version" TEXT,
    "click_item" TEXT,
    "source_page_name" TEXT,
    "destination_page_name" TEXT,
    "device_country" TEXT,
    "device_id" TEXT,
    "device_name" TEXT,
    "device_os" TEXT,
    "latitude" INTEGER,
    "longitude" INTEGER,
    "event" TEXT,
    "event_id" TEXT,
    "event_details_schema_version" TEXT,
    "session_info" JSONB,
    "notification_body" TEXT,
    "notification_title" TEXT,
    "notification_topic" TEXT,
    "platform" TEXT,
    "profile_id" INTEGER,
    "profile_type" TEXT,
    "session_duration" INTEGER,
    "session_end" TIMESTAMP(3),
    "session_start" TIMESTAMP(3),
    "timestamp" BIGINT,
    "user_id" INTEGER,
    "version" TEXT,
    "widgets" TEXT,
    "age" TEXT,
    "gender" TEXT,
    "no_of_profile" TEXT,
    "subscription_plan" TEXT,

    CONSTRAINT "metrics_pkey" PRIMARY KEY ("metric_id")
);
