-- CreateTable
CREATE TABLE "ailments" (
    "ailment_id" SERIAL NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "description" TEXT,
    "category" VARCHAR(255),
    "created_by" INTEGER,
    "created_at" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "updated_by" INTEGER,
    "updated_at" TIMESTAMPTZ(6),
    "deleted_by" INTEGER,
    "deleted_at" TIMESTAMPTZ(6),
    "is_deleted" BOOLEAN DEFAULT false,

    CONSTRAINT "ailments_pkey" PRIMARY KEY ("ailment_id")
);

-- CreateTable
CREATE TABLE "attributes" (
    "attribute_id" SERIAL NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "description" VARCHAR(255),
    "created_by" INTEGER,
    "created_at" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "updated_by" INTEGER,
    "updated_at" TIMESTAMPTZ(6),
    "deleted_by" INTEGER,
    "deleted_at" TIMESTAMPTZ(6),
    "is_deleted" BOOLEAN DEFAULT false,

    CONSTRAINT "attributes_pkey" PRIMARY KEY ("attribute_id")
);

-- CreateTable
CREATE TABLE "conversations" (
    "conversation_id" SERIAL NOT NULL,
    "profile_id" INTEGER NOT NULL,
    "model_id" INTEGER,
    "session_id" INTEGER NOT NULL,
    "round_id" INTEGER,
    "summary_id" INTEGER,
    "follow_up_id" INTEGER,
    "sender_type" TEXT,
    "message_type" VARCHAR(100),
    "content" TEXT,
    "file_name" VARCHAR(255),
    "metadata" JSONB,
    "created_at" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(6),
    "deleted_at" TIMESTAMPTZ(6),
    "is_deleted" BOOLEAN DEFAULT false,

    CONSTRAINT "conversations_pkey" PRIMARY KEY ("conversation_id")
);

-- CreateTable
CREATE TABLE "coupons" (
    "coupon_id" SERIAL NOT NULL,
    "stripe_coupon_id" VARCHAR(100) NOT NULL,
    "discount_id" INTEGER NOT NULL,
    "currency" INTEGER NOT NULL,
    "amount_off" INTEGER,
    "percent_off" INTEGER,
    "duration" VARCHAR(50),
    "duration_in_months" INTEGER,
    "max_redemptions" INTEGER,
    "created_by" INTEGER,
    "created_at" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "updated_by" INTEGER,
    "updated_at" TIMESTAMPTZ(6),
    "deleted_by" INTEGER,
    "deleted_at" TIMESTAMPTZ(6),
    "is_deleted" BOOLEAN DEFAULT false,

    CONSTRAINT "coupons_pkey" PRIMARY KEY ("coupon_id")
);

-- CreateTable
CREATE TABLE "discounts" (
    "discount_id" SERIAL NOT NULL,
    "stripe_discount_id" VARCHAR(100) NOT NULL,
    "subscription_id" INTEGER NOT NULL,
    "user_id" INTEGER NOT NULL,
    "start_date" TIMESTAMPTZ(6),
    "end_date" TIMESTAMPTZ(6),
    "promotion_code" VARCHAR(255),
    "created_by" INTEGER,
    "created_at" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "updated_by" INTEGER,
    "updated_at" TIMESTAMPTZ(6),
    "deleted_by" INTEGER,
    "deleted_at" TIMESTAMPTZ(6),
    "is_deleted" BOOLEAN DEFAULT false,

    CONSTRAINT "discounts_pkey" PRIMARY KEY ("discount_id")
);

-- CreateTable
CREATE TABLE "follow_ups" (
    "follow_up_id" SERIAL NOT NULL,
    "session_id" INTEGER NOT NULL,
    "profile_id" INTEGER NOT NULL,
    "follow_up_action" TEXT,
    "scheduled_at" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "completed_at" TIMESTAMPTZ(6),
    "created_at" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(6),
    "deleted_at" TIMESTAMPTZ(6),
    "is_deleted" BOOLEAN DEFAULT false,

    CONSTRAINT "follow_ups_pkey" PRIMARY KEY ("follow_up_id")
);

-- CreateTable
CREATE TABLE "feedback" (
    "feedback_id" SERIAL NOT NULL,
    "rating" INTEGER,
    "comment" VARCHAR(500) NOT NULL,
    "healer_id" INTEGER NOT NULL,
    "patient_id" INTEGER NOT NULL,
    "created_by" INTEGER,
    "created_at" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "updated_by" INTEGER,
    "updated_at" TIMESTAMPTZ(6),
    "deleted_by" INTEGER,
    "deleted_at" TIMESTAMPTZ(6),
    "is_deleted" BOOLEAN DEFAULT false,

    CONSTRAINT "feedback_pkey" PRIMARY KEY ("feedback_id")
);

-- CreateTable
CREATE TABLE "healing_rounds" (
    "round_id" SERIAL NOT NULL,
    "session_id" INTEGER,
    "healer_id" INTEGER,
    "summary_id" INTEGER,
    "round_number" INTEGER,
    "round_start_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "round_end_at" TIMESTAMPTZ(6),
    "max_time" INTEGER NOT NULL DEFAULT 300000,
    "remaining_time" INTEGER NOT NULL DEFAULT 0,
    "feedback_start_at" TIMESTAMPTZ(6),
    "status" TEXT,
    "assistant_thread_id" TEXT,
    "satisfaction_score" INTEGER,
    "is_satisfied" BOOLEAN,
    "is_patient_confirmed" BOOLEAN DEFAULT false,
    "is_healer_confirmed" BOOLEAN DEFAULT false,
    "check_in_count" INTEGER NOT NULL DEFAULT 3,
    "is_positive_feedback" BOOLEAN,
    "created_by" INTEGER,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_by" INTEGER,
    "updated_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted_by" INTEGER,
    "deleted_at" TIMESTAMPTZ(6),
    "is_deleted" BOOLEAN DEFAULT false,

    CONSTRAINT "healing_rounds_pkey" PRIMARY KEY ("round_id")
);

-- CreateTable
CREATE TABLE "healing_sessions" (
    "session_id" SERIAL NOT NULL,
    "profile_id" INTEGER NOT NULL,
    "healer_id" INTEGER,
    "subscription_id" INTEGER,
    "thread_id" TEXT,
    "session_start_at" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "session_end_at" TIMESTAMPTZ(6),
    "status" TEXT,
    "sub_status" TEXT,
    "sub_status_updated_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "queue_number" INTEGER,
    "queue_start_time" TIMESTAMP(3),
    "satisfaction_score" INTEGER,
    "is_satisfied" BOOLEAN,
    "is_follow_up" BOOLEAN,
    "created_by" INTEGER,
    "created_at" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "updated_by" INTEGER,
    "updated_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted_by" INTEGER,
    "deleted_at" TIMESTAMPTZ(6),
    "is_deleted" BOOLEAN DEFAULT false,

    CONSTRAINT "healing_sessions_pkey" PRIMARY KEY ("session_id")
);

-- CreateTable
CREATE TABLE "openai_models" (
    "model_id" SERIAL NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "description" TEXT,
    "author" VARCHAR(255),
    "created_by" INTEGER,
    "created_at" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "updated_by" INTEGER,
    "updated_at" TIMESTAMPTZ(6),
    "deleted_by" INTEGER,
    "deleted_at" TIMESTAMPTZ(6),
    "is_deleted" BOOLEAN DEFAULT false,

    CONSTRAINT "openai_models_pkey" PRIMARY KEY ("model_id")
);

-- CreateTable
CREATE TABLE "onboarding_status" (
    "status_id" SERIAL NOT NULL,
    "user_id" INTEGER NOT NULL,
    "device_type" VARCHAR(100),
    "device_id" TEXT,
    "device_model" TEXT,
    "os_name" TEXT,
    "os_version" TEXT,
    "app_version" TEXT,
    "status" TEXT NOT NULL DEFAULT 'PENDING',
    "notes" TEXT,
    "created_by" INTEGER,
    "created_at" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "updated_by" INTEGER,
    "updated_at" TIMESTAMPTZ(6),
    "deleted_by" INTEGER,
    "deleted_at" TIMESTAMPTZ(6),
    "is_deleted" BOOLEAN DEFAULT false,

    CONSTRAINT "onboarding_status_pkey" PRIMARY KEY ("status_id")
);

-- CreateTable
CREATE TABLE "products" (
    "product_id" SERIAL NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "description" VARCHAR(500),
    "amount" DECIMAL NOT NULL,
    "currency" VARCHAR(50),
    "is_recurring" BOOLEAN DEFAULT false,
    "recurring_interval" VARCHAR(50) NOT NULL,
    "stripe_product_id" VARCHAR(100) NOT NULL,
    "stripe_price_id" VARCHAR(100) NOT NULL,
    "is_active" BOOLEAN DEFAULT true,
    "created_by" INTEGER,
    "created_at" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "updated_by" INTEGER,
    "updated_at" TIMESTAMPTZ(6),
    "deleted_by" INTEGER,
    "deleted_at" TIMESTAMPTZ(6),
    "is_deleted" BOOLEAN DEFAULT false,

    CONSTRAINT "products_pkey" PRIMARY KEY ("product_id")
);

-- CreateTable
CREATE TABLE "profiles" (
    "profile_id" SERIAL NOT NULL,
    "profile_type" VARCHAR(50) NOT NULL,
    "user_id" INTEGER NOT NULL,
    "first_name" VARCHAR(255) NOT NULL,
    "last_name" VARCHAR(255) NOT NULL,
    "relation_to_user" VARCHAR(255),
    "gender" VARCHAR(255) NOT NULL,
    "date_of_birth" TIMESTAMPTZ(6),
    "profile_picture_file_name" VARCHAR(255),
    "phone_number" VARCHAR(20),
    "country" VARCHAR(255),
    "state" VARCHAR(255),
    "city" VARCHAR(255),
    "address" VARCHAR(255),
    "zip_code" VARCHAR(20),
    "is_default" BOOLEAN DEFAULT false,
    "created_by" INTEGER,
    "created_at" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "updated_by" INTEGER,
    "updated_at" TIMESTAMPTZ(6),
    "deleted_by" INTEGER,
    "deleted_at" TIMESTAMPTZ(6),
    "is_active" BOOLEAN DEFAULT true,
    "is_deleted" BOOLEAN DEFAULT false,

    CONSTRAINT "profiles_pkey" PRIMARY KEY ("profile_id")
);

-- CreateTable
CREATE TABLE "role_attributes" (
    "role_attribute_id" SERIAL NOT NULL,
    "role_id" INTEGER NOT NULL,
    "attribute_id" INTEGER NOT NULL,

    CONSTRAINT "role_attributes_pkey" PRIMARY KEY ("role_attribute_id")
);

-- CreateTable
CREATE TABLE "roles" (
    "role_id" SERIAL NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "description" VARCHAR(255),
    "created_by" INTEGER,
    "created_at" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "updated_by" INTEGER,
    "updated_at" TIMESTAMPTZ(6),
    "deleted_by" INTEGER,
    "deleted_at" TIMESTAMPTZ(6),
    "is_deleted" BOOLEAN DEFAULT false,

    CONSTRAINT "roles_pkey" PRIMARY KEY ("role_id")
);

-- CreateTable
CREATE TABLE "session_ailments" (
    "session_ailment_id" SERIAL NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "description" VARCHAR(500),
    "round_id" INTEGER,
    "session_id" INTEGER NOT NULL,
    "summary_id" INTEGER NOT NULL,
    "level" INTEGER NOT NULL,
    "created_by" INTEGER,
    "created_at" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "updated_by" INTEGER,
    "updated_at" TIMESTAMPTZ(6),
    "deleted_by" INTEGER,
    "deleted_at" TIMESTAMPTZ(6),
    "is_deleted" BOOLEAN DEFAULT false,

    CONSTRAINT "session_ailments_pkey" PRIMARY KEY ("session_ailment_id")
);

-- CreateTable
CREATE TABLE "session_summaries" (
    "summary_id" SERIAL NOT NULL,
    "session_id" INTEGER,
    "round_id" INTEGER,
    "content" TEXT,
    "created_by" INTEGER,
    "created_at" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "updated_by" INTEGER,
    "updated_at" TIMESTAMPTZ(6),
    "deleted_by" INTEGER,
    "deleted_at" TIMESTAMPTZ(6),
    "is_deleted" BOOLEAN DEFAULT false,

    CONSTRAINT "session_summaries_pkey" PRIMARY KEY ("summary_id")
);

-- CreateTable
CREATE TABLE "subscriptions" (
    "subscription_id" SERIAL NOT NULL,
    "user_id" INTEGER,
    "stripe_subscription_id" TEXT,
    "product_id" INTEGER,
    "current_period_start" TIMESTAMPTZ(6),
    "current_period_end" TIMESTAMPTZ(6),
    "status" TEXT NOT NULL,
    "created_by" INTEGER,
    "created_at" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "updated_by" INTEGER,
    "updated_at" TIMESTAMPTZ(6),
    "deleted_by" INTEGER,
    "deleted_at" TIMESTAMPTZ(6),
    "is_deleted" BOOLEAN DEFAULT false,

    CONSTRAINT "subscriptions_pkey" PRIMARY KEY ("subscription_id")
);

-- CreateTable
CREATE TABLE "privacy_policy" (
    "privacy_policy_id" SERIAL NOT NULL,
    "version" TEXT,
    "title" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "language" TEXT NOT NULL,
    "is_active" BOOLEAN NOT NULL DEFAULT false,
    "effective_date" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" INTEGER,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_by" INTEGER,
    "updated_at" TIMESTAMPTZ(6),
    "deleted_by" INTEGER,
    "deleted_at" TIMESTAMPTZ(6),
    "is_deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "privacy_policy_pkey" PRIMARY KEY ("privacy_policy_id")
);

-- CreateTable
CREATE TABLE "user_privacy_policy" (
    "user_privacy_policy_id" SERIAL NOT NULL,
    "user_id" INTEGER NOT NULL,
    "privacy_policy_id" INTEGER NOT NULL,
    "is_privacy_policy_accepted" BOOLEAN NOT NULL DEFAULT false,
    "privacy_policy_accepted_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "user_privacy_policy_pkey" PRIMARY KEY ("user_privacy_policy_id")
);

-- CreateTable
CREATE TABLE "terms_and_conditions" (
    "terms_id" SERIAL NOT NULL,
    "title" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "is_active" BOOLEAN DEFAULT false,
    "effective_date" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "created_by" INTEGER,
    "created_at" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "updated_by" INTEGER,
    "updated_at" TIMESTAMPTZ(6),
    "deleted_by" INTEGER,
    "deleted_at" TIMESTAMPTZ(6),
    "is_deleted" BOOLEAN DEFAULT false,

    CONSTRAINT "terms_and_conditions_pkey" PRIMARY KEY ("terms_id")
);

-- CreateTable
CREATE TABLE "user_devices" (
    "user_device_id" SERIAL NOT NULL,
    "user_id" INTEGER NOT NULL,
    "device_type" VARCHAR(100),
    "device_id" TEXT,
    "device_model" TEXT,
    "os_name" TEXT,
    "os_version" TEXT,
    "app_version" TEXT,
    "fcm_token" TEXT,
    "created_by" INTEGER,
    "created_at" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "updated_by" INTEGER,
    "updated_at" TIMESTAMPTZ(6),
    "deleted_by" INTEGER,
    "deleted_at" TIMESTAMPTZ(6),
    "is_deleted" BOOLEAN DEFAULT false,

    CONSTRAINT "user_devices_pkey" PRIMARY KEY ("user_device_id")
);

-- CreateTable
CREATE TABLE "user_roles" (
    "user_role_id" SERIAL NOT NULL,
    "user_id" INTEGER NOT NULL,
    "role_id" INTEGER NOT NULL,

    CONSTRAINT "user_roles_pkey" PRIMARY KEY ("user_role_id")
);

-- CreateTable
CREATE TABLE "user_terms_agreement" (
    "user_terms_agreement_id" SERIAL NOT NULL,
    "user_id" INTEGER NOT NULL,
    "terms_id" INTEGER NOT NULL,
    "is_terms_accepted" BOOLEAN DEFAULT false,
    "terms_accepted_at" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "user_terms_agreement_pkey" PRIMARY KEY ("user_terms_agreement_id")
);

-- CreateTable
CREATE TABLE "users" (
    "user_id" SERIAL NOT NULL,
    "stripe_customer_id" VARCHAR(255),
    "username" VARCHAR(255) NOT NULL,
    "email" VARCHAR(255) NOT NULL,
    "password_hash" VARCHAR(255) NOT NULL,
    "last_login_date" TIMESTAMPTZ(6),
    "password_reset_token" TEXT,
    "password_reset_token_expiry" TIMESTAMPTZ(6),
    "created_by" INTEGER,
    "created_at" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "updated_by" INTEGER,
    "updated_at" TIMESTAMPTZ(6),
    "deleted_by" INTEGER,
    "deleted_at" TIMESTAMPTZ(6),
    "is_deleted" BOOLEAN DEFAULT false,

    CONSTRAINT "users_pkey" PRIMARY KEY ("user_id")
);

-- CreateTable
CREATE TABLE "whitelist" (
    "whitelist_id" SERIAL NOT NULL,
    "entity_type" TEXT NOT NULL,
    "entity_value" VARCHAR(255),
    "status" TEXT,
    "created_by" INTEGER,
    "created_at" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP,
    "updated_by" INTEGER,
    "updated_at" TIMESTAMPTZ(6),
    "deleted_by" INTEGER,
    "deleted_at" TIMESTAMPTZ(6),
    "is_deleted" BOOLEAN DEFAULT false,

    CONSTRAINT "whitelist_pkey" PRIMARY KEY ("whitelist_id")
);

-- CreateIndex
CREATE INDEX "privacy_policy_is_active_is_deleted_version_language_idx" ON "privacy_policy"("is_active", "is_deleted", "version", "language");

-- CreateIndex
CREATE UNIQUE INDEX "users_username_key" ON "users"("username");

-- CreateIndex
CREATE UNIQUE INDEX "users_email_key" ON "users"("email");

-- AddForeignKey
ALTER TABLE "conversations" ADD CONSTRAINT "conversations_follow_up_id_fkey" FOREIGN KEY ("follow_up_id") REFERENCES "follow_ups"("follow_up_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "conversations" ADD CONSTRAINT "conversations_model_id_fkey" FOREIGN KEY ("model_id") REFERENCES "openai_models"("model_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "conversations" ADD CONSTRAINT "conversations_profile_id_fkey" FOREIGN KEY ("profile_id") REFERENCES "profiles"("profile_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "conversations" ADD CONSTRAINT "conversations_round_id_fkey" FOREIGN KEY ("round_id") REFERENCES "healing_rounds"("round_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "conversations" ADD CONSTRAINT "conversations_session_id_fkey" FOREIGN KEY ("session_id") REFERENCES "healing_sessions"("session_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "coupons" ADD CONSTRAINT "coupons_discount_id_fkey" FOREIGN KEY ("discount_id") REFERENCES "discounts"("discount_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "discounts" ADD CONSTRAINT "discounts_subscription_id_fkey" FOREIGN KEY ("subscription_id") REFERENCES "subscriptions"("subscription_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "discounts" ADD CONSTRAINT "discounts_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("user_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "follow_ups" ADD CONSTRAINT "follow_ups_profile_id_fkey" FOREIGN KEY ("profile_id") REFERENCES "profiles"("profile_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "feedback" ADD CONSTRAINT "feedback_healer_id_fkey" FOREIGN KEY ("healer_id") REFERENCES "profiles"("profile_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "feedback" ADD CONSTRAINT "feedback_patient_id_fkey" FOREIGN KEY ("patient_id") REFERENCES "profiles"("profile_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "healing_rounds" ADD CONSTRAINT "healing_rounds_healer_id_fkey" FOREIGN KEY ("healer_id") REFERENCES "profiles"("profile_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "healing_rounds" ADD CONSTRAINT "healing_rounds_summary_id_fkey" FOREIGN KEY ("summary_id") REFERENCES "session_summaries"("summary_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "healing_rounds" ADD CONSTRAINT "healing_rounds_session_id_fkey" FOREIGN KEY ("session_id") REFERENCES "healing_sessions"("session_id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "healing_sessions" ADD CONSTRAINT "healing_sessions_profile_id_fkey" FOREIGN KEY ("profile_id") REFERENCES "profiles"("profile_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "healing_sessions" ADD CONSTRAINT "healing_sessions_subscription_id_fkey" FOREIGN KEY ("subscription_id") REFERENCES "subscriptions"("subscription_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "onboarding_status" ADD CONSTRAINT "onboarding_status_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("user_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "profiles" ADD CONSTRAINT "profiles_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("user_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "role_attributes" ADD CONSTRAINT "role_attributes_attribute_id_fkey" FOREIGN KEY ("attribute_id") REFERENCES "attributes"("attribute_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "role_attributes" ADD CONSTRAINT "role_attributes_role_id_fkey" FOREIGN KEY ("role_id") REFERENCES "roles"("role_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "session_ailments" ADD CONSTRAINT "session_ailments_round_id_fkey" FOREIGN KEY ("round_id") REFERENCES "healing_rounds"("round_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "session_ailments" ADD CONSTRAINT "session_ailments_session_id_fkey" FOREIGN KEY ("session_id") REFERENCES "healing_sessions"("session_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "session_ailments" ADD CONSTRAINT "session_ailments_summary_id_fkey" FOREIGN KEY ("summary_id") REFERENCES "session_summaries"("summary_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "session_summaries" ADD CONSTRAINT "session_summaries_session_id_fkey" FOREIGN KEY ("session_id") REFERENCES "healing_sessions"("session_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "subscriptions" ADD CONSTRAINT "subscriptions_product_id_fkey" FOREIGN KEY ("product_id") REFERENCES "products"("product_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "subscriptions" ADD CONSTRAINT "subscriptions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("user_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "user_privacy_policy" ADD CONSTRAINT "user_privacy_policy_privacy_policy_id_fkey" FOREIGN KEY ("privacy_policy_id") REFERENCES "privacy_policy"("privacy_policy_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "user_privacy_policy" ADD CONSTRAINT "user_privacy_policy_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("user_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "user_devices" ADD CONSTRAINT "user_devices_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("user_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "user_roles" ADD CONSTRAINT "user_roles_role_id_fkey" FOREIGN KEY ("role_id") REFERENCES "roles"("role_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "user_roles" ADD CONSTRAINT "user_roles_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("user_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "user_terms_agreement" ADD CONSTRAINT "user_terms_agreement_terms_id_fkey" FOREIGN KEY ("terms_id") REFERENCES "terms_and_conditions"("terms_id") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "user_terms_agreement" ADD CONSTRAINT "user_terms_agreement_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("user_id") ON DELETE NO ACTION ON UPDATE NO ACTION;
