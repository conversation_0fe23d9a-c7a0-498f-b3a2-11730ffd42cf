/*
  Warnings:

  - Made the column `is_default` on table `profiles` required. This step will fail if there are existing NULL values in that column.
  - Made the column `created_at` on table `profiles` required. This step will fail if there are existing NULL values in that column.
  - Made the column `updated_at` on table `profiles` required. This step will fail if there are existing NULL values in that column.
  - Made the column `is_active` on table `profiles` required. This step will fail if there are existing NULL values in that column.
  - Made the column `is_deleted` on table `profiles` required. This step will fail if there are existing NULL values in that column.
  - Made the column `is_intro_complete` on table `profiles` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterTable
ALTER TABLE "conversations" ADD COLUMN     "ai_responded_at" TIMESTAMPTZ(6);


update profiles set is_default = true where is_default is NULL;
update profiles set created_at = CURRENT_DATE where created_at is NULL;
update profiles set is_active = true where is_active is NULL;
update profiles set is_deleted = false where is_deleted is NULL;
update profiles set is_intro_complete = false where is_intro_complete is NULL;

-- AlterTable
ALTER TABLE "profiles" ALTER COLUMN "is_default" SET NOT NULL,
ALTER COLUMN "created_at" SET NOT NULL,
ALTER COLUMN "is_active" SET NOT NULL,
ALTER COLUMN "is_deleted" SET NOT NULL,
ALTER COLUMN "is_intro_complete" SET NOT NULL;

-- CreateIndex
CREATE INDEX "conversations_sender_type_is_deleted_profile_id_session_id__idx" ON "conversations"("sender_type", "is_deleted", "profile_id", "session_id", "round_id", "message_type", "ai_responded_at");

-- CreateIndex
CREATE INDEX "healing_rounds_status_is_deleted_healer_id_session_id_round_idx" ON "healing_rounds"("status", "is_deleted", "healer_id", "session_id", "round_start_at");

-- CreateIndex
CREATE INDEX "healing_sessions_status_is_deleted_session_start_at_profile_idx" ON "healing_sessions"("status", "is_deleted", "session_start_at", "profile_id", "sub_status", "healer_id");

-- CreateIndex
CREATE INDEX "profiles_is_active_is_deleted_is_default_profile_type_user__idx" ON "profiles"("is_active", "is_deleted", "is_default", "profile_type", "user_id");
