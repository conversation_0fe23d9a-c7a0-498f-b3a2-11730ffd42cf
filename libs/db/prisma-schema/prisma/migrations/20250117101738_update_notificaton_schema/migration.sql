/*
  Warnings:

  - You are about to drop the column `read_at` on the `notification` table. All the data in the column will be lost.
  - You are about to drop the column `user_id` on the `notification` table. All the data in the column will be lost.
  - The `status` column on the `notification` table would be dropped and recreated. This will lead to data loss if there is data in the column.

*/
-- CreateEnum
CREATE TYPE "NotificationStatus" AS ENUM ('DRAFT', 'READY_TO_SEND', 'SCHEDULED', 'SENT', 'FAILED');

-- CreateEnum
CREATE TYPE "NotificationTriggerType" AS ENUM ('MANUAL', 'AUTOMATED');

-- DropForeignKey
ALTER TABLE "notification" DROP CONSTRAINT "notification_user_id_fkey";

-- AlterTable
ALTER TABLE "notification" DROP COLUMN "read_at",
DROP COLUMN "user_id",
ADD COLUMN     "image_url" TEXT,
ADD COLUMN     "notification_topic_id" INTEGER,
ADD COLUMN     "scheduled_at" TIMESTAMP(3),
ADD COLUMN     "sent_at" TIMESTAMP(3),
ADD COLUMN     "trigger_type" "NotificationTriggerType" NOT NULL DEFAULT 'MANUAL',
DROP COLUMN "status",
ADD COLUMN     "status" "NotificationStatus" NOT NULL DEFAULT 'DRAFT';

-- DropEnum
DROP TYPE "Status";

-- CreateTable
CREATE TABLE "notification_topics" (
    "notification_topic_id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "created_by" INTEGER,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_by" INTEGER,
    "updated_at" TIMESTAMPTZ(6),
    "deleted_by" INTEGER,
    "deleted_at" TIMESTAMPTZ(6),
    "is_deleted" BOOLEAN DEFAULT false,

    CONSTRAINT "notification_topics_pkey" PRIMARY KEY ("notification_topic_id")
);

-- CreateTable
CREATE TABLE "user_notification_preferences" (
    "user_notification_preference_id" SERIAL NOT NULL,
    "user_id" INTEGER NOT NULL,
    "notification_topic_id" INTEGER NOT NULL,
    "is_subscribed" BOOLEAN NOT NULL DEFAULT true,
    "created_by" INTEGER,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_by" INTEGER,
    "updated_at" TIMESTAMPTZ(6),

    CONSTRAINT "user_notification_preferences_pkey" PRIMARY KEY ("user_notification_preference_id")
);

-- CreateTable
CREATE TABLE "notification_recipients" (
    "notification_recipient_id" SERIAL NOT NULL,
    "notification_id" INTEGER NOT NULL,
    "user_id" INTEGER NOT NULL,
    "status" "NotificationStatus" NOT NULL DEFAULT 'DRAFT',
    "sent_at" TIMESTAMP(3),
    "created_by" INTEGER,
    "created_at" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_by" INTEGER,
    "updated_at" TIMESTAMPTZ(6),
    "deleted_by" INTEGER,
    "deleted_at" TIMESTAMPTZ(6),
    "is_deleted" BOOLEAN DEFAULT false,

    CONSTRAINT "notification_recipients_pkey" PRIMARY KEY ("notification_recipient_id")
);

-- CreateIndex
CREATE UNIQUE INDEX "notification_topics_name_key" ON "notification_topics"("name");

-- CreateIndex
CREATE UNIQUE INDEX "user_notification_preferences_user_id_notification_topic_id_key" ON "user_notification_preferences"("user_id", "notification_topic_id");

-- AddForeignKey
ALTER TABLE "user_notification_preferences" ADD CONSTRAINT "user_notification_preferences_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("user_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_notification_preferences" ADD CONSTRAINT "user_notification_preferences_notification_topic_id_fkey" FOREIGN KEY ("notification_topic_id") REFERENCES "notification_topics"("notification_topic_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "notification" ADD CONSTRAINT "notification_notification_topic_id_fkey" FOREIGN KEY ("notification_topic_id") REFERENCES "notification_topics"("notification_topic_id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "notification_recipients" ADD CONSTRAINT "notification_recipients_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("user_id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "notification_recipients" ADD CONSTRAINT "notification_recipients_notification_id_fkey" FOREIGN KEY ("notification_id") REFERENCES "notification"("notification_id") ON DELETE RESTRICT ON UPDATE CASCADE;
