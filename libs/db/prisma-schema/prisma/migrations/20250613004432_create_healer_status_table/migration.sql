-- CreateTable
CREATE TABLE "healer_status" (
    "healer_status_id" SERIAL NOT NULL,
    "profile_id" INTEGER NOT NULL,
    "status" TEXT,
    "created_at" TIMESTAMPTZ(6) DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updated_at" TIMESTAMPTZ(6),
    "is_deleted" BOOLEAN DEFAULT false NOT NULL,

    CONSTRAINT "healer_status_pkey" PRIMARY KEY ("healer_status_id"),
    CONSTRAINT "healer_status_profile_id_fkey" FOREIGN KEY ("profile_id") REFERENCES "profiles"("profile_id") ON DELETE NO ACTION ON UPDATE NO ACTION
);

-- CreateIndex
CREATE UNIQUE INDEX "healer_status_profile_id_key" ON "healer_status"("profile_id");