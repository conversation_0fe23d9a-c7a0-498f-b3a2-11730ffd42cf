/*
  Warnings:

  - Made the column `session_id` on table `healing_rounds` required. This step will fail if there are existing NULL values in that column.
  - Made the column `healer_id` on table `healing_rounds` required. This step will fail if there are existing NULL values in that column.
  - Made the column `round_number` on table `healing_rounds` required. This step will fail if there are existing NULL values in that column.
  - Made the column `assistant_thread_id` on table `healing_rounds` required. This step will fail if there are existing NULL values in that column.
  - Made the column `timestamp` on table `metrics` required. This step will fail if there are existing NULL values in that column.

*/
-- DropForeignKey
ALTER TABLE "healing_rounds" DROP CONSTRAINT "healing_rounds_session_id_fkey";


update healing_rounds set session_id =1 where session_id is NULL;
update healing_rounds set healer_id =1 where healer_id is NULL;
update healing_rounds set round_number =1 where round_number is NULL;
update healing_rounds set assistant_thread_id ='t' where assistant_thread_id is NULL;

-- AlterTable
ALTER TABLE "healing_rounds" ALTER COLUMN "session_id" SET NOT NULL,
ALTER COLUMN "healer_id" SET NOT NULL,
ALTER COLUMN "round_number" SET NOT NULL,
ALTER COLUMN "assistant_thread_id" SET NOT NULL;

-- AlterTable
ALTER TABLE "metrics" ALTER COLUMN "timestamp" SET NOT NULL;

-- AddForeignKey
ALTER TABLE "healing_rounds" ADD CONSTRAINT "healing_rounds_session_id_fkey" FOREIGN KEY ("session_id") REFERENCES "healing_sessions"("session_id") ON DELETE RESTRICT ON UPDATE CASCADE;
