generator client {
  provider      = "prisma-client-js"
  output        = "../../../../node_modules/@prisma/core"
  binaryTargets = ["native", "linux-musl", "linux-musl-openssl-3.0.x", "linux-musl-arm64-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model ailments {
  ailment_id  Int       @id @default(autoincrement())
  name        String    @db.VarChar(255)
  description String?
  category    String?   @db.VarChar(255)
  created_by  Int?
  created_at  DateTime? @default(now()) @db.Timestamptz(6)
  updated_by  Int?
  updated_at  DateTime? @db.Timestamptz(6)
  deleted_by  Int?
  deleted_at  DateTime? @db.Timestamptz(6)
  is_deleted  Boolean?  @default(false)
}

model attributes {
  attribute_id    Int               @id @default(autoincrement())
  name            String            @db.VarChar(100)
  description     String?           @db.<PERSON>(255)
  created_by      Int?
  created_at      DateTime?         @default(now()) @db.Timestamptz(6)
  updated_by      Int?
  updated_at      DateTime?         @db.Timestamptz(6)
  deleted_by      Int?
  deleted_at      DateTime?         @db.Timestamptz(6)
  is_deleted      Boolean?          @default(false)
  role_attributes role_attributes[]
}

model conversations {
  conversation_id  Int               @id @default(autoincrement())
  profile_id       Int
  model_id         Int?
  session_id       Int
  round_id         Int?
  summary_id       Int?
  follow_up_id     Int?
  responded_at  DateTime?         @db.Timestamptz(6)
  is_null_response Boolean           @default(false)
  sender_type      String?
  message_type     String?           @db.VarChar(100)
  content          String?
  file_name        String?           @db.VarChar(255)
  metadata         Json?
  created_at       DateTime?         @default(now()) @db.Timestamptz(6)
  updated_at       DateTime?         @db.Timestamptz(6)
  deleted_at       DateTime?         @db.Timestamptz(6)
  is_deleted       Boolean?          @default(false)
  follow_ups       follow_ups?       @relation(fields: [follow_up_id], references: [follow_up_id], onDelete: NoAction, onUpdate: NoAction)
  openai_models    openai_models?    @relation(fields: [model_id], references: [model_id], onDelete: NoAction, onUpdate: NoAction)
  profiles         profiles          @relation(fields: [profile_id], references: [profile_id], onDelete: NoAction, onUpdate: NoAction)
  healing_rounds   healing_rounds?   @relation(fields: [round_id], references: [round_id], onDelete: NoAction, onUpdate: NoAction)
  healing_sessions healing_sessions? @relation(fields: [session_id], references: [session_id], onDelete: NoAction, onUpdate: NoAction)

  @@index([sender_type, is_deleted, profile_id, session_id, round_id, message_type, responded_at])
}

model coupons {
  coupon_id          Int       @id @default(autoincrement())
  stripe_coupon_id   String    @db.VarChar(100)
  discount_id        Int
  currency           Int
  amount_off         Int?
  percent_off        Int?
  duration           String?   @db.VarChar(50)
  duration_in_months Int?
  max_redemptions    Int?
  created_by         Int?
  created_at         DateTime? @default(now()) @db.Timestamptz(6)
  updated_by         Int?
  updated_at         DateTime? @db.Timestamptz(6)
  deleted_by         Int?
  deleted_at         DateTime? @db.Timestamptz(6)
  is_deleted         Boolean?  @default(false)
  discounts          discounts @relation(fields: [discount_id], references: [discount_id], onDelete: NoAction, onUpdate: NoAction)
}

model discounts {
  discount_id        Int           @id @default(autoincrement())
  stripe_discount_id String        @db.VarChar(100)
  subscription_id    Int
  user_id            Int
  start_date         DateTime?     @db.Timestamptz(6)
  end_date           DateTime?     @db.Timestamptz(6)
  promotion_code     String?       @db.VarChar(255)
  created_by         Int?
  created_at         DateTime?     @default(now()) @db.Timestamptz(6)
  updated_by         Int?
  updated_at         DateTime?     @db.Timestamptz(6)
  deleted_by         Int?
  deleted_at         DateTime?     @db.Timestamptz(6)
  is_deleted         Boolean?      @default(false)
  coupons            coupons[]
  subscriptions      subscriptions @relation(fields: [subscription_id], references: [subscription_id], onDelete: NoAction, onUpdate: NoAction)
  users              users         @relation(fields: [user_id], references: [user_id], onDelete: NoAction, onUpdate: NoAction)
}

model follow_ups {
  follow_up_id     Int             @id @default(autoincrement())
  session_id       Int
  profile_id       Int
  follow_up_action String?
  scheduled_at     DateTime?       @default(now()) @db.Timestamptz(6)
  completed_at     DateTime?       @db.Timestamptz(6)
  created_at       DateTime?       @default(now()) @db.Timestamptz(6)
  updated_at       DateTime?       @db.Timestamptz(6)
  deleted_at       DateTime?       @db.Timestamptz(6)
  is_deleted       Boolean?        @default(false)
  conversations    conversations[]
  profiles         profiles        @relation(fields: [profile_id], references: [profile_id], onDelete: NoAction, onUpdate: NoAction)
}

model feedback {
  feedback_id     Int       @id @default(autoincrement())
  rating          Int?
  comment         String    @db.VarChar(500)
  healer_id       Int
  patient_id      Int
  created_by      Int?
  created_at      DateTime? @default(now()) @db.Timestamptz(6)
  updated_by      Int?
  updated_at      DateTime? @db.Timestamptz(6)
  deleted_by      Int?
  deleted_at      DateTime? @db.Timestamptz(6)
  is_deleted      Boolean?  @default(false)
  healer_profile  profiles  @relation("HealerFeedback", fields: [healer_id], references: [profile_id], onDelete: NoAction, onUpdate: NoAction)
  patient_profile profiles  @relation("PatientFeedback", fields: [patient_id], references: [profile_id], onDelete: NoAction, onUpdate: NoAction)
}

model healing_rounds {
  round_id             Int                @id @default(autoincrement())
  session_id           Int
  healer_id            Int?
  summary_id           Int?
  round_number         Int
  round_start_at       DateTime           @default(now()) @db.Timestamptz(6)
  round_end_at         DateTime?          @db.Timestamptz(6)
  max_time             Int                @default(300000)
  remaining_time       Int                @default(0)
  feedback_start_at    DateTime?          @db.Timestamptz(6)
  status               String?
  assistant_thread_id  String
  satisfaction_score   Int?
  is_satisfied         Boolean?
  is_patient_confirmed Boolean?           @default(true)
  is_healer_confirmed  Boolean?           @default(false)
  check_in_count       Int                @default(3)
  is_positive_feedback Boolean?
  cloud_region         String?            @db.VarChar(50)
  created_by           Int?
  created_at           DateTime           @default(now()) @db.Timestamptz(6)
  updated_by           Int?
  updated_at           DateTime           @default(now()) @db.Timestamptz(6)
  deleted_by           Int?
  deleted_at           DateTime?          @db.Timestamptz(6)
  is_deleted           Boolean?           @default(false)
  conversations        conversations[]
  profiles             profiles?           @relation(fields: [healer_id], references: [profile_id], onDelete: NoAction, onUpdate: NoAction)
  session_summaries    session_summaries? @relation(fields: [summary_id], references: [summary_id], onDelete: NoAction, onUpdate: NoAction)
  healing_sessions     healing_sessions   @relation(name: "healing_roundsTohealing_sessions", fields: [session_id], references: [session_id])
  session_ailments     session_ailments[]

  @@index([status, is_deleted, healer_id, session_id, round_start_at])
}

model healing_sessions {
  session_id            Int                 @id @default(autoincrement())
  profile_id            Int
  healer_id             Int?
  subscription_id       Int?
  thread_id             String?
  session_start_at      DateTime?           @default(now()) @db.Timestamptz(6)
  session_end_at        DateTime?           @db.Timestamptz(6)
  status                String?
  sub_status            String?
  session_type          String?
  sub_status_updated_at DateTime            @default(now()) @db.Timestamptz(6)
  queue_number          BigInt?
  queue_start_time      DateTime?
  satisfaction_score    Int?
  is_satisfied          Boolean?
  is_follow_up          Boolean?
  cloud_region          String?
  created_by            Int?
  created_at            DateTime?           @default(now()) @db.Timestamptz(6)
  updated_by            Int?
  updated_at            DateTime            @default(now()) @db.Timestamptz(6)
  deleted_by            Int?
  deleted_at            DateTime?           @db.Timestamptz(6)
  is_deleted            Boolean?            @default(false)
  profiles              profiles            @relation(fields: [profile_id], references: [profile_id], onDelete: NoAction, onUpdate: NoAction)
  subscriptions         subscriptions?      @relation(fields: [subscription_id], references: [subscription_id], onDelete: NoAction, onUpdate: NoAction)
  session_ailments      session_ailments[]
  session_summaries     session_summaries[]
  conversations         conversations[]
  healing_rounds        healing_rounds[]    @relation(name: "healing_roundsTohealing_sessions")
  session_follow_up     session_follow_up[]
  last_session_offer_at DateTime?           @db.Timestamptz(6)

  @@index([status, is_deleted, session_start_at, profile_id, sub_status, healer_id])
}

model metrics {
  metric_id                    Int       @id @default(autoincrement())
  app_state                    String?
  app_version                  String?
  click_item                   String?
  source_page_name             String?
  destination_page_name        String?
  device_country               String?
  device_id                    String?
  device_name                  String?
  device_os                    String?
  latitude                     Float?
  longitude                    Float?
  event                        String?
  event_id                     String?
  event_details_schema_version String?
  session_info                 Json?
  notification_body            String?
  notification_title           String?
  notification_topic           String?
  platform                     String?
  profile_id                   Int?
  profile_type                 String?
  session_duration             Int?
  session_end                  DateTime?
  session_start                DateTime?
  timestamp                    DateTime  @default(now()) @db.Timestamptz(6)
  user_id                      Int?
  version                      String?
  widgets                      String?
  age                          String?
  gender                       String?
  no_of_profile                String?
  subscription_plan            String?
}

model openai_models {
  model_id      Int             @id @default(autoincrement())
  name          String          @db.VarChar(255)
  description   String?
  author        String?         @db.VarChar(255)
  created_by    Int?
  created_at    DateTime?       @default(now()) @db.Timestamptz(6)
  updated_by    Int?
  updated_at    DateTime?       @db.Timestamptz(6)
  deleted_by    Int?
  deleted_at    DateTime?       @db.Timestamptz(6)
  is_deleted    Boolean?        @default(false)
  conversations conversations[]
}

model onboarding_status {
  status_id    Int       @id @default(autoincrement())
  user_id      Int
  device_type  String?   @db.VarChar(100)
  device_id    String?
  device_model String?
  os_name      String?
  os_version   String?
  app_version  String?
  status       String    @default("PENDING")
  notes        String?
  created_by   Int?
  created_at   DateTime? @default(now()) @db.Timestamptz(6)
  updated_by   Int?
  updated_at   DateTime? @db.Timestamptz(6)
  deleted_by   Int?
  deleted_at   DateTime? @db.Timestamptz(6)
  is_deleted   Boolean?  @default(false)
  users        users     @relation(fields: [user_id], references: [user_id], onDelete: NoAction, onUpdate: NoAction)
}

model products {
  product_id         Int             @id @default(autoincrement())
  name               String          @db.VarChar(255)
  description        String?         @db.VarChar(500)
  amount             Decimal         @db.Decimal
  currency           String?         @db.VarChar(50)
  is_recurring       Boolean?        @default(false)
  recurring_interval String          @db.VarChar(50)
  stripe_product_id  String          @db.VarChar(100)
  stripe_price_id    String          @db.VarChar(100)
  is_active          Boolean?        @default(true)
  created_by         Int?
  created_at         DateTime?       @default(now()) @db.Timestamptz(6)
  updated_by         Int?
  updated_at         DateTime?       @db.Timestamptz(6)
  deleted_by         Int?
  deleted_at         DateTime?       @db.Timestamptz(6)
  is_deleted         Boolean?        @default(false)
  subscriptions      subscriptions[]
}

model profiles {
  profile_id                Int                @id @default(autoincrement())
  profile_type              String             @db.VarChar(50)
  user_id                   Int
  first_name                String             @db.VarChar(255)
  last_name                 String             @db.VarChar(255)
  relation_to_user          String?            @db.VarChar(255)
  gender                    String             @db.VarChar(255)
  date_of_birth             DateTime?          @db.Timestamptz(6)
  profile_picture_file_name String?            @db.VarChar(255)
  phone_number              String?            @db.VarChar(20)
  country                   String?            @db.VarChar(255)
  state                     String?            @db.VarChar(255)
  city                      String?            @db.VarChar(255)
  address                   String?            @db.VarChar(255)
  zip_code                  String?            @db.VarChar(20)
  is_default                Boolean            @default(false)
  created_by                Int?
  created_at                DateTime           @default(now()) @db.Timestamptz(6)
  updated_by                Int?
  updated_at                DateTime?          @updatedAt @db.Timestamptz(6)
  deleted_by                Int?
  deleted_at                DateTime?          @db.Timestamptz(6)
  is_active                 Boolean            @default(true)
  is_deleted                Boolean            @default(false)
  is_intro_complete         Boolean            @default(false)
  conversations             conversations[]
  follow_ups                follow_ups[]
  healing_rounds            healing_rounds[]
  healing_sessions          healing_sessions[]
  healer_status             healer_status[]
  healer_feedback           feedback[]         @relation("HealerFeedback")
  patient_feedback          feedback[]         @relation("PatientFeedback")
  users                     users              @relation(fields: [user_id], references: [user_id], onDelete: NoAction, onUpdate: NoAction)

  @@index([is_active, is_deleted, is_default, profile_type, user_id])
}

model role_attributes {
  role_attribute_id Int        @id @default(autoincrement())
  role_id           Int
  attribute_id      Int
  attributes        attributes @relation(fields: [attribute_id], references: [attribute_id], onDelete: NoAction, onUpdate: NoAction)
  roles             roles      @relation(fields: [role_id], references: [role_id], onDelete: NoAction, onUpdate: NoAction)
}

model roles {
  role_id         Int               @id @default(autoincrement())
  name            String            @db.VarChar(100)
  description     String?           @db.VarChar(255)
  created_by      Int?
  created_at      DateTime?         @default(now()) @db.Timestamptz(6)
  updated_by      Int?
  updated_at      DateTime?         @db.Timestamptz(6)
  deleted_by      Int?
  deleted_at      DateTime?         @db.Timestamptz(6)
  is_deleted      Boolean?          @default(false)
  role_attributes role_attributes[]
  user_roles      user_roles[]
}

model session_ailments {
  session_ailment_id Int               @id @default(autoincrement())
  name               String            @db.VarChar(100)
  description        String?           @db.VarChar(500)
  round_id           Int?
  session_id         Int
  summary_id         Int
  level              Int
  created_by         Int?
  created_at         DateTime?         @default(now()) @db.Timestamptz(6)
  updated_by         Int?
  updated_at         DateTime?         @db.Timestamptz(6)
  deleted_by         Int?
  deleted_at         DateTime?         @db.Timestamptz(6)
  is_deleted         Boolean?          @default(false)
  healing_rounds     healing_rounds?   @relation(fields: [round_id], references: [round_id], onDelete: NoAction, onUpdate: NoAction)
  healing_sessions   healing_sessions  @relation(fields: [session_id], references: [session_id], onDelete: NoAction, onUpdate: NoAction)
  session_summaries  session_summaries @relation(fields: [summary_id], references: [summary_id], onDelete: NoAction, onUpdate: NoAction)
}

model session_summaries {
  summary_id       Int                @id @default(autoincrement())
  session_id       Int?
  round_id         Int?
  content          String?
  created_by       Int?
  created_at       DateTime?          @default(now()) @db.Timestamptz(6)
  updated_by       Int?
  updated_at       DateTime?          @db.Timestamptz(6)
  deleted_by       Int?
  deleted_at       DateTime?          @db.Timestamptz(6)
  is_deleted       Boolean?           @default(false)
  session_ailments session_ailments[]
  healing_sessions healing_sessions?  @relation(fields: [session_id], references: [session_id], onDelete: NoAction, onUpdate: NoAction)
  healing_rounds   healing_rounds[]
}

model subscriptions {
  subscription_id        Int                @id @default(autoincrement())
  user_id                Int?
  stripe_subscription_id String?
  product_id             Int?
  current_period_start   DateTime?          @db.Timestamptz(6)
  current_period_end     DateTime?          @db.Timestamptz(6)
  status                 String
  created_by             Int?
  created_at             DateTime?          @default(now()) @db.Timestamptz(6)
  updated_by             Int?
  updated_at             DateTime?          @db.Timestamptz(6)
  deleted_by             Int?
  deleted_at             DateTime?          @db.Timestamptz(6)
  is_deleted             Boolean?           @default(false)
  discounts              discounts[]
  healing_sessions       healing_sessions[]
  products               products?          @relation(fields: [product_id], references: [product_id], onDelete: NoAction, onUpdate: NoAction)
  users                  users?             @relation(fields: [user_id], references: [user_id], onDelete: NoAction, onUpdate: NoAction)
}

model privacy_policy {
  privacy_policy_id   Int                   @id @default(autoincrement())
  version             String?
  title               String
  content             String
  language            String
  is_active           Boolean               @default(false)
  effective_date      DateTime              @default(now()) @db.Timestamptz(6)
  created_by          Int?
  created_at          DateTime              @default(now()) @db.Timestamptz(6)
  updated_by          Int?
  updated_at          DateTime?             @db.Timestamptz(6)
  deleted_by          Int?
  deleted_at          DateTime?             @db.Timestamptz(6)
  is_deleted          Boolean               @default(false)
  user_privacy_policy user_privacy_policy[]

  @@index([is_active, is_deleted, version, language])
}

model user_privacy_policy {
  user_privacy_policy_id     Int            @id @default(autoincrement())
  user_id                    Int
  privacy_policy_id          Int?
  is_privacy_policy_accepted Boolean        @default(false)
  privacy_policy_accepted_at DateTime       @default(now()) @db.Timestamptz(6)
  privacy_policy             privacy_policy? @relation(fields: [privacy_policy_id], references: [privacy_policy_id], onDelete: NoAction, onUpdate: NoAction)
  users                      users          @relation(fields: [user_id], references: [user_id], onDelete: NoAction, onUpdate: NoAction)
}

model terms_and_conditions {
  terms_id             Int                    @id @default(autoincrement())
  title                String
  content              String
  is_active            Boolean?               @default(false)
  effective_date       DateTime?              @default(now()) @db.Timestamptz(6)
  created_by           Int?
  created_at           DateTime?              @default(now()) @db.Timestamptz(6)
  updated_by           Int?
  updated_at           DateTime?              @db.Timestamptz(6)
  deleted_by           Int?
  deleted_at           DateTime?              @db.Timestamptz(6)
  is_deleted           Boolean?               @default(false)
  user_terms_agreement user_terms_agreement[]
}

model user_devices {
  user_device_id Int       @id @default(autoincrement())
  user_id        Int
  device_type    String?   @db.VarChar(100)
  device_id      String?
  device_model   String?
  os_name        String?
  os_version     String?
  app_version    String?
  fcm_token      String?
  created_by     Int?
  created_at     DateTime? @default(now()) @db.Timestamptz(6)
  updated_by     Int?
  updated_at     DateTime? @db.Timestamptz(6)
  deleted_by     Int?
  deleted_at     DateTime? @db.Timestamptz(6)
  is_deleted     Boolean?  @default(false)
  users          users     @relation(fields: [user_id], references: [user_id], onDelete: NoAction, onUpdate: NoAction)
  last_seen_at   DateTime? @db.Timestamptz(6)
}

model user_roles {
  user_role_id Int   @id @default(autoincrement())
  user_id      Int
  role_id      Int
  roles        roles @relation(fields: [role_id], references: [role_id], onDelete: NoAction, onUpdate: NoAction)
  users        users @relation(fields: [user_id], references: [user_id], onDelete: NoAction, onUpdate: NoAction)
}

model user_terms_agreement {
  user_terms_agreement_id Int                  @id @default(autoincrement())
  user_id                 Int
  terms_id                Int?
  is_terms_accepted       Boolean?             @default(false)
  terms_accepted_at       DateTime?            @default(now()) @db.Timestamptz(6)
  terms_and_conditions    terms_and_conditions? @relation(fields: [terms_id], references: [terms_id], onDelete: NoAction, onUpdate: NoAction)
  users                   users                @relation(fields: [user_id], references: [user_id], onDelete: NoAction, onUpdate: NoAction)
}

model users {
  user_id                       Int                             @id @default(autoincrement())
  stripe_customer_id            String?                         @db.VarChar(255)
  username                      String                          @unique @db.VarChar(255)
  email                         String                          @unique @db.VarChar(255)
  password_hash                 String                          @db.VarChar(255)
  last_login_date               DateTime?                       @db.Timestamptz(6)
  password_reset_token          String?
  password_reset_token_expiry   DateTime?                       @db.Timestamptz(6)
  created_by                    Int?
  created_at                    DateTime?                       @default(now()) @db.Timestamptz(6)
  updated_by                    Int?
  updated_at                    DateTime?                       @db.Timestamptz(6)
  deleted_by                    Int?
  deleted_at                    DateTime?                       @db.Timestamptz(6)
  is_deleted                    Boolean?                        @default(false)
  is_active                     Boolean?                        @default(true)
  year_of_birth                 Int?
  profiles                      profiles[]
  subscriptions                 subscriptions[]
  user_devices                  user_devices[]
  user_roles                    user_roles[]
  onboarding_status             onboarding_status[]
  discounts                     discounts[]
  user_terms_agreement          user_terms_agreement[]
  user_privacy_policy           user_privacy_policy[]
  user_notification_preferences user_notification_preferences[]
  notification_recipients       notification_recipients[]
}

model whitelist {
  whitelist_id Int       @id @default(autoincrement())
  entity_type  String
  entity_value String?   @db.VarChar(255)
  status       String?
  created_by   Int?
  created_at   DateTime? @default(now()) @db.Timestamptz(6)
  updated_by   Int?
  updated_at   DateTime? @db.Timestamptz(6)
  deleted_by   Int?
  deleted_at   DateTime? @db.Timestamptz(6)
  is_deleted   Boolean?  @default(false)
}

model notification_topics {
  notification_topic_id         Int                             @id @default(autoincrement())
  name                          String                          @unique()
  description                   String?
  created_by                    Int?
  created_at                    DateTime                        @default(now()) @db.Timestamptz(6)
  updated_by                    Int?
  updated_at                    DateTime?                       @db.Timestamptz(6)
  deleted_by                    Int?
  deleted_at                    DateTime?                       @db.Timestamptz(6)
  is_deleted                    Boolean?                        @default(false)
  user_notification_preferences user_notification_preferences[]
  notification                  notification[]
}

model user_notification_preferences {
  user_notification_preference_id Int                 @id @default(autoincrement())
  user_id                         Int
  notification_topic_id           Int
  is_subscribed                   Boolean             @default(true)
  created_by                      Int?
  created_at                      DateTime            @default(now()) @db.Timestamptz(6)
  updated_by                      Int?
  updated_at                      DateTime?           @db.Timestamptz(6)
  user                            users               @relation(fields: [user_id], references: [user_id])
  notification_topic              notification_topics @relation(fields: [notification_topic_id], references: [notification_topic_id])

  @@unique([user_id, notification_topic_id])
}

model notification {
  notification_id         Int                       @id @default(autoincrement())
  title                   String
  body                    String
  image_url               String?
  notification_topic_id   Int?
  status                  NotificationStatus        @default(DRAFT)
  type                    NotificationType          @default(PUSH)
  trigger_type            NotificationTriggerType   @default(MANUAL)
  priority                NotificationPriority      @default(MEDIUM)
  scheduled_at            DateTime?
  sent_at                 DateTime?
  created_by              Int?
  created_at              DateTime                  @default(now()) @db.Timestamptz(6)
  updated_by              Int?
  updated_at              DateTime?                 @db.Timestamptz(6)
  deleted_by              Int?
  deleted_at              DateTime?                 @db.Timestamptz(6)
  is_deleted              Boolean?                  @default(false)
  notification_topic      notification_topics?      @relation(fields: [notification_topic_id], references: [notification_topic_id])
  notification_recipients notification_recipients[]
}

model notification_recipients {
  notification_recipient_id Int                @id @default(autoincrement())
  notification_id           Int
  user_id                   Int
  status                    NotificationStatus @default(DRAFT)
  sent_at                   DateTime?
  created_by                Int?
  created_at                DateTime           @default(now()) @db.Timestamptz(6)
  updated_by                Int?
  updated_at                DateTime?          @db.Timestamptz(6)
  deleted_by                Int?
  deleted_at                DateTime?          @db.Timestamptz(6)
  is_deleted                Boolean?           @default(false)
  user                      users              @relation(fields: [user_id], references: [user_id])
  notification              notification       @relation(fields: [notification_id], references: [notification_id])
}

enum NotificationStatus {
  DRAFT
  READY_TO_SEND
  SCHEDULED
  SENT
  FAILED
  ERROR
  READ
  UNREAD
}

enum NotificationType {
  PUSH
  EMAIL
  SMS
}

enum NotificationPriority {
  LOW
  MEDIUM
  HIGH
}

model session_follow_up {
  session_follow_up_id Int                     @id @default(autoincrement())
  session_id           Int
  user_id              Int
  follow_up_interval   SessionFollowUpInterval @default(NONE)
  follow_up_at         DateTime?               @db.Timestamptz(6)
  created_at           DateTime                @default(now()) @db.Timestamptz(6)
  updated_at           DateTime?               @db.Timestamptz(6)
  is_deleted           Boolean?                @default(false)
  status               SessionFollowUpStatus   @default(PENDING)
  healing_sessions     healing_sessions?       @relation(fields: [session_id], references: [session_id], onDelete: NoAction, onUpdate: NoAction)
}

enum SessionFollowUpInterval {
  TWENTY_FOUR_HRS
  SEVEN_DAYS
  NONE
}

enum SessionFollowUpStatus {
  PENDING
  COMPLETED
  FAILED
}

enum NotificationTriggerType {
  MANUAL
  AUTOMATED
}

model waitlist {
  waitlist_id   Int       @id @default(autoincrement())
  email         String?   @db.VarChar(255)
  user_type     String?
  status        String?
  created_by    Int?
  created_at    DateTime  @default(now()) @db.Timestamptz(6)
  is_deleted    Boolean   @default(false)
}

model healer_status {
  healer_status_id  Int         @id @default(autoincrement())
  profile_id        Int         @unique
  status            String?
  created_at        DateTime    @default(now()) @db.Timestamptz(6)
  updated_at        DateTime?   @db.Timestamptz(6)
  is_deleted        Boolean     @default(false)
  
  profiles          profiles    @relation(fields: [profile_id], references: [profile_id], onDelete: NoAction, onUpdate: NoAction)
}

model survey_submissions {
  id                Int         @id @default(autoincrement())
  email             String?
  user_type         String?
  type              String?
  data              Json?
  created_at        DateTime    @default(now()) @db.Timestamptz(6)
  updated_at        DateTime?   @db.Timestamptz(6)
  is_deleted        Boolean     @default(false)
}

model app_documents {
  document_id          Int                    @id @default(autoincrement())
  name                 String
  content              String
  type                 String
  is_active            Boolean?               @default(false)
  effective_date       DateTime?              @default(now()) @db.Timestamptz(6)
  created_by           Int?
  created_at           DateTime?              @default(now()) @db.Timestamptz(6)
  updated_by           Int?
  updated_at           DateTime?              @db.Timestamptz(6)
  deleted_by           Int?
  deleted_at           DateTime?              @db.Timestamptz(6)
  is_deleted           Boolean?               @default(false)
}

model system_configs {
  id                   Int                    @id @default(autoincrement())
  name                 String @unique
  value                String
  description          String?
  is_active            Boolean?               @default(false)
  created_at           DateTime?              @default(now()) @db.Timestamptz(6)
  updated_at           DateTime?              @db.Timestamptz(6)
  deleted_at           DateTime?              @db.Timestamptz(6)
  is_deleted           Boolean?               @default(false)

  @@index([name])
}