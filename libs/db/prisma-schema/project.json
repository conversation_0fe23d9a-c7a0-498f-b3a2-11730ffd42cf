{"name": "prisma-schema", "$schema": "../../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/db/prisma-schema/prisma", "root": "libs/db/prisma-schema", "projectType": "library", "tags": [], "targets": {"prisma": {"command": "prisma generate && prisma migrate dev --name=core", "options": {"cwd": "libs/db/prisma-schema"}}, "migrate": {"command": "prisma migrate dev --name=core", "options": {"cwd": "libs/db/prisma-schema"}}, "generate-types": {"command": "prisma generate", "options": {"cwd": "libs/db/prisma-schema"}}}}