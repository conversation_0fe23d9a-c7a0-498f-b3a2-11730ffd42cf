{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "../../../dist/out-tsc", "types": ["node"], "target": "es2021", "strictNullChecks": true, "noImplicitAny": true, "strictBindCallApply": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "declaration": true, "declarationMap": true, "sourceMap": true}, "include": ["src/**/*.ts"], "exclude": ["jest.config.ts", "src/**/*.spec.ts", "src/**/*.test.ts"]}