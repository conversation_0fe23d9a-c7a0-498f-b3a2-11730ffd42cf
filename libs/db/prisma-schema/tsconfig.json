{"extends": "../../../tsconfig.base.json", "compilerOptions": {"moduleResolution": "NodeNext", "target": "es2015", "module": "NodeNext", "forceConsistentCasingInFileNames": true, "strict": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "declaration": true, "declarationMap": true, "sourceMap": true}, "files": [], "include": [], "exclude": ["../../../**/*.spec.ts", "../../../**/*.test.ts"], "references": [{"path": "./tsconfig.lib.json"}, {"path": "./tsconfig.spec.json"}]}