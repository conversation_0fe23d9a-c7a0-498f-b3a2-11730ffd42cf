import { BadRequestException, Injectable } from '@nestjs/common';
import { Prisma, PrismaService } from '@core/prisma-client';
import {
  ConversationRepository,
  HealingRoundRepository,
  SessionAilmentRepository,
  SessionRepository,
  SummaryRepository,
} from 'libs/data-access/src';
import {
  ProfileType,
  SessionSubStatusType,
  SessionStatus,
  SessionType,
  PlaceboActionType,
  SenderType,
  HealingRoundStatus,
  HealingRoundSubStatus,
  AI_MESSAGE_INCOMING_TRIGGER,
  AI_MESSAGE_OUTGOING_TRIGGER,
} from '../../enum';
import { SystemConfigService } from './system_config.service';
import { PublisherService } from '../queue/publisher.service';
import { PLACEBO_REQUESTS_DELAY_QUEUE } from '../config/rabbitmq.config';
import {
  AIMessagingService,
  AssistantResponse,
  LibOpenaiService,
} from '@core_be/aiml';
import { ConfigService } from '@nestjs/config';
import { Logger } from 'nestjs-pino';

@Injectable()
export class LibPlaceboService {
  private readonly HEALING_ROUND_HEALER_IN_PROGRESS_TIMEOUT: number;

  constructor(
    private readonly prisma: PrismaService,
    private readonly sessionRepository: SessionRepository,
    private readonly conversationRepository: ConversationRepository,
    private readonly healingRoundRepository: HealingRoundRepository,
    private readonly summaryRepository: SummaryRepository,
    private readonly sessionAilmentRepository: SessionAilmentRepository,
    private readonly libOpenAiService: LibOpenaiService,
    private readonly configService: ConfigService,
    private readonly aiMessagingService: AIMessagingService,
    private readonly publisherService: PublisherService,
    private readonly logger: Logger,
    private readonly systemConfigService: SystemConfigService
  ) {
    this.HEALING_ROUND_HEALER_IN_PROGRESS_TIMEOUT =
      parseInt(
        '' + this.configService.get('HEALING_ROUND_HEALER_IN_PROGRESS_TIMEOUT')
      ) || 1500000;
  }

  async processPlaceboRequest(data: {
    session_id: number;
    action: PlaceboActionType;
    round_id?: number;
  }): Promise<void> {
    try {
      const { session_id, action, round_id } = data;

      // Log method entry
      this.logger.log(
        {
          event: 'PROCESS_PLACEBO_REQUEST_START',
          session_id: session_id,
          action: action,
          round_id: round_id,
          message: 'Starting placebo request processing',
        },
        'Process Placebo Request'
      );

      if (session_id) {
        // Processing asynchronously to avoid blocking the queue
        if (action === PlaceboActionType.START_HEALING) {
          this.logger.log(
            {
              event: 'PROCESS_PLACEBO_REQUEST_START_HEALING',
              session_id: session_id,
              action: action,
              message: 'Processing placebo healing round start',
            },
            'Process Placebo Request Start Healing'
          );
          this.processPlaceboHealingRoundStart(session_id);
        } else if (action === PlaceboActionType.TRIGGER_CHECK_IN && round_id) {
          this.logger.log(
            {
              event: 'PROCESS_PLACEBO_REQUEST_CHECK_IN',
              session_id: session_id,
              round_id: round_id,
              action: action,
              message: 'Processing placebo healing round check-in',
            },
            'Process Placebo Request Check-In'
          );
          this.processPlaceboHealingRoundCheckIn({ session_id, round_id });
        } else {
          this.logger.log(
            {
              event: 'PROCESS_PLACEBO_REQUEST_UNKNOWN_ACTION',
              session_id: session_id,
              action: action,
              round_id: round_id,
              message: 'Unknown placebo action or missing round_id',
            },
            'Process Placebo Request Unknown Action'
          );
        }
      } else {
        this.logger.error(
          {
            event: 'PROCESS_PLACEBO_REQUEST_INVALID_SESSION',
            session_id: session_id,
            action: action,
            message: 'Invalid session_id provided',
          },
          'Process Placebo Request Validation Failed'
        );
      }
    } catch (error) {
      this.logger.error(
        {
          event: 'PROCESS_PLACEBO_REQUEST_ERROR',
          session_id: data.session_id,
          action: data.action,
          round_id: data.round_id,
          error_message:
            error instanceof Error ? error.message : 'unknown error',
          message: 'Placebo request processing failed',
        },
        'Process Placebo Request Error'
      );
      throw error;
    }
  }

  public async processPlaceboHealingRoundStart(
    session_id: number
  ): Promise<void> {
    try {
      // Get dynamic healing round assistant ID
      const healingRoundAssistantID =
        await this.systemConfigService.getHealingRoundAssistantId();
      if (!healingRoundAssistantID) {
        throw new Error(
          'Healing round assistant ID not found in system configuration'
        );
      }

      // Log method entry
      this.logger.log(
        {
          event: 'PLACEBO_HEALING_ROUND_START_PROCESS_START',
          session_id: session_id,
          message: 'Starting placebo healing round process',
        },
        'Placebo Healing Round Start Process'
      );

      const session = await this.sessionRepository.findFirst({
        where: {
          session_id,
        },
      });

      if (session) {
        this.logger.log(
          {
            event: 'PLACEBO_HEALING_ROUND_START_SESSION_FOUND',
            session_id: session_id,
            session_type: session.session_type,
            session_status: session.status,
            session_sub_status: session.sub_status,
            message: 'Session found for placebo healing round',
          },
          'Placebo Healing Round Start Session Found'
        );

        let lastHealingRound =
          await this.healingRoundRepository.findOneWithInclude({
            where: {
              session_id: session_id,
              is_deleted: false,
            },
            orderBy: { round_id: 'desc' },
            include: {
              conversations: {
                where: {
                  sender_type: SenderType.Patient,
                  is_deleted: false,
                },
                take: 1,
              },
            },
          });

        if (
          session.session_type == SessionType.PLACEBO &&
          session.status === SessionStatus.IN_QUEUE &&
          session.sub_status === SessionSubStatusType.SESSION_CONFIRMED
        ) {
          this.logger.log(
            {
              event: 'PLACEBO_HEALING_ROUND_START_RESETTING_ROUND',
              session_id: session_id,
              previous_round_id: lastHealingRound?.round_id,
              message: 'Resetting healing round for confirmed placebo session',
            },
            'Placebo Healing Round Start Reset'
          );
          lastHealingRound = null;
        }
        const maxTime =
          !lastHealingRound || lastHealingRound.is_positive_feedback
            ? this.HEALING_ROUND_HEALER_IN_PROGRESS_TIMEOUT
            : lastHealingRound.remaining_time;
        const checkInCount =
          !lastHealingRound || lastHealingRound.is_positive_feedback
            ? 3
            : --lastHealingRound.check_in_count;
        const roundNumber = !lastHealingRound
          ? 1
          : ++lastHealingRound.round_number;

        const patient_id = session.profile_id;
        const healer_id = session.healer_id;

        this.logger.log(
          {
            event: 'PLACEBO_HEALING_ROUND_START_ROUND_CONFIG',
            session_id: session_id,
            patient_id: patient_id,
            healer_id: healer_id,
            max_time: maxTime,
            check_in_count: checkInCount,
            round_number: roundNumber,
            has_previous_round: !!lastHealingRound,
            message: 'Configured placebo healing round parameters',
          },
          'Placebo Healing Round Start Configuration'
        );

        const CreateData: Prisma.healing_roundsUncheckedCreateInput = {
          session_id,
          assistant_thread_id: '',
          max_time: maxTime,
          check_in_count: checkInCount,
          round_number: roundNumber,
          is_healer_confirmed: true,
        };

        const roundCreatedResponse = await this.createHealingRound(CreateData);

        if (!roundCreatedResponse) {
          this.logger.error(
            {
              event: 'PLACEBO_HEALING_ROUND_START_CREATION_FAILED',
              session_id: session_id,
              patient_id: patient_id,
              message: 'Failed to create placebo healing round',
            },
            'Placebo Healing Round Start Creation Failed'
          );
          throw new BadRequestException('Cannot create healing round');
        }

        this.logger.log(
          {
            event: 'PLACEBO_HEALING_ROUND_START_ROUND_CREATED',
            session_id: session_id,
            round_id: roundCreatedResponse.round_id,
            patient_id: patient_id,
            message: 'Placebo healing round created successfully',
          },
          'Placebo Healing Round Start Round Created'
        );

        let assistantResponse: AssistantResponse;
        if (!lastHealingRound || lastHealingRound.assistant_thread_id == null) {
          this.logger.log(
            {
              event: 'PLACEBO_HEALING_ROUND_START_CREATING_THREAD',
              session_id: session_id,
              round_id: roundCreatedResponse.round_id,
              message: 'Creating new healing thread for placebo session',
            },
            'Placebo Healing Round Start Thread Creation'
          );
          // Healing Round must get a new Healing Round Assistant thread id.
          assistantResponse = await this.createHealingThread(session_id);
        } else {
          this.logger.log(
            {
              event: 'PLACEBO_HEALING_ROUND_START_CONTINUING_THREAD',
              session_id: session_id,
              round_id: roundCreatedResponse.round_id,
              thread_id: lastHealingRound.assistant_thread_id,
              message: 'Continuing existing healing thread for placebo session',
            },
            'Placebo Healing Round Start Thread Continuation'
          );
          const healingRoundSystemInput = `${AI_MESSAGE_OUTGOING_TRIGGER.HEALING_ROUND_START}`;
          assistantResponse =
            await this.libOpenAiService.continueConversationAssistant(
              lastHealingRound.assistant_thread_id,
              healingRoundSystemInput,
              healingRoundAssistantID,
              'user',
              false,
              undefined,
              lastHealingRound.cloud_region!
            );
        }

        if (!assistantResponse) {
          this.logger.error(
            {
              event: 'PLACEBO_HEALING_ROUND_START_ASSISTANT_FAILED',
              session_id: session_id,
              round_id: roundCreatedResponse.round_id,
              message:
                'Failed to get assistant response for placebo healing round',
            },
            'Placebo Healing Round Start Assistant Failed'
          );
          throw new BadRequestException('Cannot create healing round');
        }

        this.healingRoundRepository.update({
          where: { round_id: roundCreatedResponse.round_id },
          data: { assistant_thread_id: assistantResponse.threadId },
        });

        if (
          assistantResponse.trigger.includes(
            AI_MESSAGE_INCOMING_TRIGGER.HEALING_ROUND_MESSAGE_TO_PATIENT
          )
        ) {
          this.logger.log(
            {
              event: 'PLACEBO_HEALING_ROUND_START_MESSAGE_TO_PATIENT',
              session_id: session_id,
              round_id: roundCreatedResponse.round_id,
              patient_id: patient_id,
              message: 'Sending placebo healing round message to patient',
            },
            'Placebo Healing Round Start Patient Message'
          );
          this.aiMessagingService.messageToEntity({
            message: assistantResponse.output,
            round_id: roundCreatedResponse.round_id,
            profile_id: patient_id,
            session_id: roundCreatedResponse.session_id,
            profile_type: ProfileType.PATIENT,
            entityConversation: lastHealingRound?.conversations[0],
          });
        } else if (lastHealingRound?.conversations[0]) {
          await this.updateAINullResponse(
            lastHealingRound.conversations[0].conversation_id
          );
        }

        if (
          assistantResponse.trigger.includes(
            AI_MESSAGE_INCOMING_TRIGGER.HEALING_ROUND_MESSAGE_TO_ENERGY_HEALER
          )
        ) {
          this.logger.log(
            {
              event: 'PLACEBO_HEALING_ROUND_START_MESSAGE_TO_HEALER',
              session_id: session_id,
              round_id: roundCreatedResponse.round_id,
              healer_id: healer_id,
              message: 'Sending placebo healing round message to healer',
            },
            'Placebo Healing Round Start Healer Message'
          );
          this.aiMessagingService.messageToEntity({
            message: assistantResponse.output,
            round_id: roundCreatedResponse.round_id,
            profile_id: healer_id!,
            session_id: roundCreatedResponse.session_id,
            profile_type: ProfileType.HEALER,
            entityConversation: lastHealingRound?.conversations[0],
          });
        }

        // Healer only can trigger check-in after 30s, emulating the same behavior for placebo.
        if (maxTime > 30000) {
          const delayTime = this.publisherService.getRandomDelayInMs(
            30000,
            maxTime
          );
          this.logger.log(
            {
              event: 'PLACEBO_HEALING_ROUND_START_SCHEDULING_CHECK_IN',
              session_id: session_id,
              round_id: roundCreatedResponse.round_id,
              delay_time: delayTime,
              max_time: maxTime,
              message: 'Scheduling placebo check-in with random delay',
            },
            'Placebo Healing Round Start Check-In Schedule'
          );

          await this.publisherService.sendMessage({
            message: {
              session_id: session.session_id,
              round_id: roundCreatedResponse.round_id,
              action: PlaceboActionType.TRIGGER_CHECK_IN,
            },
            queueRoutingKey: PLACEBO_REQUESTS_DELAY_QUEUE.routingKey,
            expiration: delayTime,
          });
        }

        this.logger.log(
          {
            event: 'PLACEBO_HEALING_ROUND_START_SUCCESS',
            session_id: session_id,
            round_id: roundCreatedResponse.round_id,
            patient_id: patient_id,
            healer_id: healer_id,
            round_number: roundNumber,
            assistant_thread_id: assistantResponse.threadId,
            message: 'Placebo healing round started successfully',
          },
          'Placebo Healing Round Start Success'
        );
      } else {
        this.logger.error(
          {
            event: 'PLACEBO_HEALING_ROUND_START_NO_SESSION',
            session_id: session_id,
            message: 'No session found for placebo healing round start',
          },
          'Placebo Healing Round Start Session Not Found'
        );
      }
    } catch (error) {
      this.logger.error(
        {
          event: 'PLACEBO_HEALING_ROUND_START_ERROR',
          session_id: session_id,
          error_message:
            error instanceof Error ? error.message : 'unknown error',
          message: 'Placebo healing round start failed',
        },
        'Placebo Healing Round Start Error'
      );
      throw error;
    }
  }

  private async createHealingThread(session_id: number) {
    // Get dynamic healing round assistant ID
    const healingRoundAssistantID =
      await this.systemConfigService.getHealingRoundAssistantId();
    if (!healingRoundAssistantID) {
      throw new Error(
        'Healing round assistant ID not found in system configuration'
      );
    }

    const patientSummaryDocument =
      await this.summaryRepository.getLatestSessionSummary({
        where: { session_id },
        orderBy: {
          created_at: 'desc',
        },
      });

    // Healing Round must get a new Healing Round Assistant thread id.
    const uniqueAilmentsKey: {
      [ailment: string]: number;
    } = {};
    (
      await this.sessionAilmentRepository.findAll({
        where: {
          session_id,
          round_id: null,
          is_deleted: false,
        },
        orderBy: { session_ailment_id: 'asc' },
      })
    )?.forEach((ailment) => {
      const ailmentName = ailment.name.toLowerCase();
      uniqueAilmentsKey[ailmentName] = ailment.level;
    });

    const healingRoundSystemInput = `${
      AI_MESSAGE_OUTGOING_TRIGGER.HEALING_ROUND_SYSTEM_INPUT
    } ${patientSummaryDocument?.content}\n
        Intake Ailments noted for Patient:
        ${Object.entries(uniqueAilmentsKey)
          .map(([name, level]) => `name: ${name} | discomfort level: ${level}`)
          .join('\n')}
        `;

    return await this.libOpenAiService.createConversationAssistant(
      healingRoundSystemInput,
      healingRoundAssistantID,
      'user',
      false,
      undefined // wsEventOptions
    );
  }

  async createHealingRound(body: Prisma.healing_roundsUncheckedCreateInput) {
    const roundData = {
      round_number: body.round_number,
      assistant_thread_id: body.assistant_thread_id,
      max_time: body.max_time,
      check_in_count: body.check_in_count,
      healer_id: -1, // Placebo healing round healer id as -1
      healing_sessions: {
        connect: {
          session_id: body.session_id,
        },
      },
      status: HealingRoundStatus.IN_PROGRESS,
      is_healer_confirmed: true,
    };
    const rounds = await this.healingRoundRepository.create(roundData);

    await this.sessionRepository.update({
      where: { session_id: body.session_id },
      data: {
        status: SessionStatus?.HEALING_ROUND,
        sub_status: HealingRoundSubStatus.IN_PROGRESS,
        sub_status_updated_at: new Date(),
      },
    });
    return rounds;
  }

  async updateAINullResponse(conversationId: number) {
    return this.conversationRepository.update({
      where: {
        conversation_id: conversationId,
      },
      data: {
        responded_at: new Date(),
        is_null_response: true,
      },
    });
  }

  async processPlaceboHealingRoundCheckIn(data: {
    session_id: number;
    round_id: number;
    region?: string;
  }) {
    try {
      const { session_id, round_id, region } = data;

      // Get dynamic healing round assistant ID
      const healingRoundAssistantID =
        await this.systemConfigService.getHealingRoundAssistantId(region);
      if (!healingRoundAssistantID) {
        throw new Error(
          'Healing round assistant ID not found in system configuration'
        );
      }

      // Log method entry
      this.logger.log(
        {
          event: 'PLACEBO_HEALING_ROUND_CHECK_IN_START',
          session_id: session_id,
          round_id: round_id,
          message: 'Starting placebo healing round check-in',
        },
        'Placebo Healing Round Check-In Process'
      );

      const session = await this.sessionRepository.findFirst({
        where: {
          session_id,
        },
      });

      if (session) {
        this.logger.log(
          {
            event: 'PLACEBO_HEALING_ROUND_CHECK_IN_SESSION_FOUND',
            session_id: session_id,
            round_id: round_id,
            session_type: session.session_type,
            message: 'Session found for placebo check-in',
          },
          'Placebo Healing Round Check-In Session Found'
        );

        const lastHealingRound =
          await this.healingRoundRepository.findOneWithInclude({
            where: {
              round_id,
              session_id,
              is_deleted: false,
            },
            orderBy: { round_id: 'desc' },
            include: {
              conversations: {
                where: {
                  sender_type: SenderType.Patient,
                  is_deleted: false,
                },
                take: 1,
              },
            },
          });

        if (!lastHealingRound) {
          this.logger.error(
            {
              event: 'PLACEBO_HEALING_ROUND_CHECK_IN_NO_ROUND',
              session_id: session_id,
              round_id: round_id,
              message: 'No healing round found for placebo check-in',
            },
            'Placebo Healing Round Check-In Round Not Found'
          );
          throw new BadRequestException('Invalid Session');
        }

        const maxRemaining =
          lastHealingRound.max_time ||
          this.HEALING_ROUND_HEALER_IN_PROGRESS_TIMEOUT;
        const inProgressTimeSpent =
          new Date().getTime() - lastHealingRound.round_start_at.getTime();
        let remainingTime = maxRemaining - inProgressTimeSpent;
        if (remainingTime < 0) {
          remainingTime = 0;
        }

        this.logger.log(
          {
            event: 'PLACEBO_HEALING_ROUND_CHECK_IN_TIME_CALCULATED',
            session_id: session_id,
            round_id: round_id,
            max_time: maxRemaining,
            time_spent: inProgressTimeSpent,
            remaining_time: remainingTime,
            message: 'Calculated remaining time for placebo check-in',
          },
          'Placebo Healing Round Check-In Time Calculation'
        );

        const updateHealingRoundDto: Prisma.healing_roundsUpdateInput = {
          healing_sessions: {
            connect: {
              session_id: session.session_id,
            },
          },
        };
        const healingRoundData: Prisma.healing_roundsUpdateInput = {
          ...updateHealingRoundDto,
          status: HealingRoundStatus.FEEDBACK_REQUIRED,
          updated_by: -1,
          updated_at: new Date(),
          feedback_start_at: new Date(),
          remaining_time: remainingTime,
        };

        const healingRoundRecord = await this.updateHealingRound(
          lastHealingRound.round_id,
          healingRoundData
        );

        const healingSessionRecord = await this.prisma.healing_sessions.update({
          where: { session_id },
          data: {
            status: SessionStatus.HEALING_ROUND,
            sub_status: HealingRoundStatus.FEEDBACK_REQUIRED || null,
            sub_status_updated_at: new Date(),
            updated_at: new Date(),
          },
        });

        this.logger.log(
          {
            event: 'PLACEBO_HEALING_ROUND_CHECK_IN_STATUS_UPDATED',
            session_id: session_id,
            round_id: round_id,
            round_status: HealingRoundStatus.FEEDBACK_REQUIRED,
            session_status: SessionStatus.HEALING_ROUND,
            message: 'Updated placebo healing round and session status',
          },
          'Placebo Healing Round Check-In Status Update'
        );

        // Healing Round must get a new Healing Round Assistant thread id.
        const assistantResponse =
          await this.libOpenAiService.continueConversationAssistant(
            healingRoundRecord.assistant_thread_id,
            AI_MESSAGE_OUTGOING_TRIGGER.HEALING_ROUND_START_FEEDBACK,
            healingRoundAssistantID,
            'user',
            false,
            undefined,
            healingRoundRecord.cloud_region!
          );

        const { isResponseToPatient } =
          this.aiMessagingService.processIncomingMessages(assistantResponse);

        if (isResponseToPatient) {
          const targetProfileId = healingSessionRecord.profile_id;
          this.logger.log(
            {
              event: 'PLACEBO_HEALING_ROUND_CHECK_IN_MESSAGE_TO_PATIENT',
              session_id: session_id,
              round_id: round_id,
              patient_id: targetProfileId,
              message: 'Sending placebo check-in message to patient',
            },
            'Placebo Healing Round Check-In Patient Message'
          );

          await this.aiMessagingService.messageToEntity({
            message: assistantResponse.output,
            round_id: healingRoundRecord.round_id,
            profile_id: targetProfileId,
            profile_type: ProfileType.PATIENT,
            session_id: healingSessionRecord.session_id,
          });
        } else if (lastHealingRound?.conversations[0]) {
          await this.updateAINullResponse(
            lastHealingRound.conversations[0].conversation_id
          );
        }

        this.logger.log(
          {
            event: 'PLACEBO_HEALING_ROUND_CHECK_IN_SUCCESS',
            session_id: session_id,
            round_id: round_id,
            patient_id: healingSessionRecord.profile_id,
            remaining_time: remainingTime,
            message: 'Placebo healing round check-in completed successfully',
          },
          'Placebo Healing Round Check-In Success'
        );
      } else {
        this.logger.error(
          {
            event: 'PLACEBO_HEALING_ROUND_CHECK_IN_NO_SESSION',
            session_id: session_id,
            round_id: round_id,
            message: 'No session found for placebo check-in',
          },
          'Placebo Healing Round Check-In Session Not Found'
        );
      }
    } catch (error) {
      this.logger.error(
        {
          event: 'PLACEBO_HEALING_ROUND_CHECK_IN_ERROR',
          session_id: data.session_id,
          round_id: data.round_id,
          error_message:
            error instanceof Error ? error.message : 'unknown error',
          message: 'Placebo healing round check-in failed',
        },
        'Placebo Healing Round Check-In Error'
      );
      throw error;
    }
  }

  async updateHealingRound(
    round_id: number,
    body: Prisma.healing_roundsUpdateInput
  ) {
    const rounds = await this.healingRoundRepository.update({
      where: { round_id: round_id, is_deleted: false },
      data: body,
    });

    const updateSession = {
      session_end_at: new Date(),
      status: body.status,
    };

    await this.sessionRepository.update({
      where: { session_id: rounds.session_id },
      data: updateSession,
    });
    return rounds;
  }
}
