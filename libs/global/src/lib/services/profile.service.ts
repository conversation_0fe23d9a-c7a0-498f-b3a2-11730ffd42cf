import { BadRequestException, Injectable } from '@nestjs/common';
import { UsersRepository } from 'libs/data-access/src';
import { Prisma, PrismaService } from '@core/prisma-client';
import { ProfileType, RelationToUser } from '../../enum';
import { ProfileDto } from '../dtos';

@Injectable()
export class LibProfileService {
  constructor(
    private prisma: PrismaService,
    private usersRepository: UsersRepository
  ) {}

  async getProfileById(profile_id: number) {
    try {
      const profile = await this.prisma.profiles.findFirst({
        where: { profile_id, is_deleted: false },
      });
      return profile;
    } catch (error) {
      throw new BadRequestException(error || 'Please try again');
    }
  }

  async getProfileByUserId(user_id: number) {
    try {
      const profile = await this.prisma.profiles.findFirst({
        where: { user_id, is_deleted: false },
      });
      return profile;
    } catch (error) {
      throw new BadRequestException(error || 'Please try again');
    }
  }

  async getProfileByUserIdAndProfileId(user_id: number, profile_id: number) {
    try {
      const profile = await this.prisma.profiles.findFirst({
        where: { user_id, profile_id, is_deleted: false },
      });
      return profile;
    } catch (error) {
      throw new BadRequestException(error || 'Please try again');
    }
  }

  async checkUserProfile(createProfileDto: any): Promise<any | null> {
    const userProfiles = await this.usersRepository.findOne(
      {
        user_id: createProfileDto.user_id,
        is_deleted: false,
      },
      {
        profiles: {
          where: {
            is_deleted: false,
            profile_type: createProfileDto.profile_type,
            relation_to_user: RelationToUser.Self,
          },
        },
      }
    );
    return userProfiles;
  }

  async createProfile(
    data: Prisma.profilesCreateInput,
    prisma?: Prisma.TransactionClient
  ) {
    const client = prisma || this.prisma;
    return client.profiles.create({ data });
  }

  async profiles(params: {
    skip?: number;
    take?: number;
    cursor?: Prisma.profilesWhereUniqueInput;
    where?: Prisma.profilesWhereInput;
    orderBy?: Prisma.profilesOrderByWithAggregationInput;
  }) {
    const { skip, take, cursor, where, orderBy } = params;
    return this.prisma.profiles.findMany({
      skip,
      take,
      cursor,
      where,
      orderBy,
    });
  }

  async profile(where: Prisma.profilesWhereUniqueInput) {
    return this.prisma.profiles.findUnique({ where });
  }

  async profileByUserId(where: Prisma.profilesWhereInput) {
    return this.prisma.profiles.findFirst({ where });
  }

  async getDefaultProfile(where: Prisma.profilesWhereInput) {
    return this.prisma.profiles.findFirst({ where });
  }

  async updateProfile(params: {
    where: Prisma.profilesWhereUniqueInput;
    data: Prisma.profilesUpdateInput;
  }) {
    const { where, data } = params;
    return this.prisma.profiles.update({ data, where });
  }

  async deleteProfile(where: Prisma.profilesWhereUniqueInput, data: any) {
    const profile = await this.profile(where);
    if (profile?.is_default) {
      throw new BadRequestException(
        'You cannot delete the default user profile.'
      );
    }
    return this.prisma.profiles.update({ data, where });
  }

  async profileCount(params: { where?: Prisma.profilesWhereInput }) {
    const { where } = params;
    return this.prisma.profiles.count({ where });
  }
  async validateUserProfile(
    profiles: ProfileDto[] = [],
    user_id: number,
    profile_id: number,
    profile_type?: ProfileType
  ): Promise<boolean> {
    if (profiles.some((profile) => profile?.profile_id === profile_id)) {
      return true;
    }

    const profileFilter: Prisma.profilesWhereInput = {
      user_id,
      profile_id,
    };
    if (profile_type) {
      profileFilter.profile_type = profile_type;
    }
    const profileExists = await this.prisma.profiles.findFirst({
      where: profileFilter,
      select: { profile_id: true },
    });

    return !!profileExists;
  }
}
