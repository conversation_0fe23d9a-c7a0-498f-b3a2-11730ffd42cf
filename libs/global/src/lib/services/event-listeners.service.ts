import {
  AI_LISTENER,
  CONVERSATION_LISTEN,
  ConversationDto,
  QueryParamsDto,
  WS_LISTEN,
  WS_SEND,
} from '@core/libs';
import { Prisma } from '@core/prisma-client';
import { HttpException } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { ClientProxy } from '@nestjs/microservices';

export function createEventError(
  eventEmitter: EventEmitter2 | ClientProxy,
  error: HttpException,
  target: number,
  skipThrow = false
) {
  new WsSendEvent(
    eventEmitter,
    WS_SEND.ERROR,
    JSON.parse(JSON.stringify(error)),
    target
  ).emit();
  if (!skipThrow) {
    throw error;
  }
}

export class EmitEvent {
  constructor(protected emitter: EventEmitter2 | ClientProxy, public event: string) {}

  public async emit() {
    if(this.emitter instanceof ClientProxy) {
      return this.emitter.emit(this.event, this);
    }
    return this.emitter.emitAsync(this.event, this);
  }
}

export class CreateConversationEvent extends EmitEvent {
  constructor(
    override readonly emitter: EventEmitter2 | ClientProxy,
    public conversation: ConversationDto,
    public query?: QueryParamsDto
  ) {
    super(emitter, CONVERSATION_LISTEN.CREATE);
  }
}

export class CreateAIThreadEvent extends EmitEvent {
  constructor(
    override readonly emitter: EventEmitter2 | ClientProxy,
    public override readonly event: string,
    public readonly aiModelId: string,
    public readonly skipRun: boolean,
    public readonly content?: string
  ) {
    super(emitter, AI_LISTENER.CREATE_NEW_THREAD);
  }
}

export type wsPayload = {
  [key: string]: unknown;
  session_id?: number |undefined;
  profile_id?: number |undefined;
  skip?: number;
  limit?: number;
  order?: string;
};

export class WsSendEvent extends EmitEvent {
  constructor(
    override readonly emitter: EventEmitter2 | ClientProxy,
    public readonly wsEvent: string,
    public readonly payload: wsPayload,
    public readonly profileId: number,
    public readonly query?: QueryParamsDto
  ) {
    super(emitter, WS_LISTEN.SEND);
  }
}

export class ProcessConversationEvent extends EmitEvent {
  constructor(
    override readonly emitter: EventEmitter2 | ClientProxy,
    public conversation: Prisma.$conversationsPayload['scalars'],
    public aiModelId: string,
    public user: number,
    public skipRun?: boolean,
    public raw?: Record<string, unknown>
  ) {
    super(emitter, CONVERSATION_LISTEN.PROCESS_NEW_AI_THREAD);
  }
}
