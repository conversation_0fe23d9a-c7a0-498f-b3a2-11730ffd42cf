import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import {
  conversations,
  healing_rounds,
  healing_sessions,
  Prisma,
  PrismaService,
} from '@core/prisma-client';
import {
  ConversationRepository,
  HealingRoundRepository,
  ProfileRepository,
  SessionAilmentRepository,
  SessionRepository,
  SummaryRepository,
} from 'libs/data-access/src';
import {
  // Import Services
  LibProfileService,
} from './index';
import { WsSendEvent } from './event-listeners.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import {
  ProfileType,
  SessionSubStatusType,
  SESSION_STATUS,
  SessionStatus,
  SessionType,
  PlaceboActionType,
  SessionDeclineTriggerSourceType,
  PushNotificationTitleType,
  PushNotificationBodyType,
  HealerStatusType,
} from '../../enum';
import { AilmentsDto } from '@core/libs';
import { ClientProxy } from '@nestjs/microservices';
import { LibNotificationService } from '@core_be/notifications';
import { PublisherService } from '../queue/publisher.service';
import { PLACEBO_REQUESTS_DELAY_QUEUE } from '../config/rabbitmq.config';
import { ConfigService } from '@nestjs/config';
import { HealerStatusRepository } from 'libs/data-access/src/lib/healer_status/healer_status.repository';
import { Logger } from 'nestjs-pino';

@Injectable()
export class LibSessionService {
  private readonly HEALING_SESSION_PLACEBO_PERCENTAGE: number;
  private readonly MIN_SESSION_COUNT_FOR_PLACEBO: number;

  constructor(
    private readonly prisma: PrismaService,
    private readonly profileService: LibProfileService,
    private readonly sessionRepository: SessionRepository,
    private readonly conversationRepository: ConversationRepository,
    private readonly healingRoundRepository: HealingRoundRepository,
    private readonly summaryRepository: SummaryRepository,
    private readonly sessionAilmentRepository: SessionAilmentRepository,
    private readonly eventEmitter: EventEmitter2,
    private readonly profileRepository: ProfileRepository,
    private readonly notificationService: LibNotificationService,
    private readonly configService: ConfigService,
    private readonly publisherService: PublisherService,
    private readonly healerStatusRepository: HealerStatusRepository,
    private readonly logger: Logger
  ) {
    this.HEALING_SESSION_PLACEBO_PERCENTAGE =
      parseInt(
        '' + this.configService.get('HEALING_SESSION_PLACEBO_PERCENTAGE')
      ) || 30;
    this.MIN_SESSION_COUNT_FOR_PLACEBO =
      parseInt('' + this.configService.get('MIN_SESSION_COUNT_FOR_PLACEBO')) ||
      3;
  }

  async findOne(
    session_id: number,
    profile_id?: number,
    query: { limit: number } = { limit: 10 }
  ) {
    try {
      const session = await this.sessionRepository.findOneWithSelectUnique({
        where: {
          session_id,
          is_deleted: false,
        },
        select: {
          session_id: true,
          profile_id: true,
          subscription_id: true,
          thread_id: true,
          session_start_at: true,
          session_end_at: true,
          status: true,
          sub_status: true,
          queue_number: true,
          queue_start_time: true,
          created_by: true,
          sub_status_updated_at: true,
        },
      });

      if (!session) {
        throw new BadRequestException('Invalid Session ID');
      }

      const [conversations, healingRound, lastSummary] = await Promise.all([
        this.conversationRepository.findAll({
          where: {
            session_id,
            profile_id,
            is_deleted: false,
          },
          orderBy: {
            created_at: 'desc',
          },
          take: query?.limit || 10,
          select: {
            conversation_id: true,
            profile_id: true,
            model_id: true,
            session_id: true,
            round_id: true,
            summary_id: true,
            follow_up_id: true,
            sender_type: true,
            message_type: true,
            content: true,
            created_at: true,
            is_null_response: true,
            responded_at: true,
          },
        }),
        this.healingRoundRepository.findOneWithSelect({
          where: {
            session_id,
            is_deleted: false,
          },
          orderBy: {
            round_end_at: 'desc',
          },
          select: {
            round_id: true,
            session_id: true,
            healer_id: true,
            summary_id: true,
            round_number: true,
            round_start_at: true,
            round_end_at: true,
            status: true,
            check_in_count: true,
            max_time: true,
            is_positive_feedback: true,
            is_patient_confirmed: true,
            is_healer_confirmed: true,
          },
        }),
        this.summaryRepository.findFirstSelect({
          where: {
            session_id,
            is_deleted: false,
          },
          orderBy: {
            created_at: 'desc',
          },
          select: {
            summary_id: true,
            content: true,
          },
        }),
      ]);

      const ailments = await this.sessionAilmentRepository.findAilments({
        session_id,
      });

      const responsePayload: Partial<healing_sessions> & {
        summary_info: {
          summary_id: number;
          content: string;
        } | null;
        session_queue_info?: {
          queue_number: number | bigint;
          queue_start_time: Date;
          queue_status: string;
          queue_sub_status: string;
          queue_waiting_time: string | number | null;
        } | null;
        healing_round:
          | (healing_rounds & { last_round_feedback: string })
          | null;
        conversations: conversations[] | null;
        ailments: AilmentsDto[] | null;
      } = {
        ...session,
        summary_info: lastSummary
          ? {
              summary_id: lastSummary.summary_id,
              content: '' + lastSummary.content,
            }
          : null,
        healing_round: healingRound
          ? {
              ...healingRound,
              last_round_feedback: '',
            }
          : null,
        conversations: conversations.length ? conversations.reverse() : null,
        ailments: ailments ?? null,
      };
      if (
        [SessionStatus.IN_QUEUE].includes(session?.status as SessionStatus) &&
        [
          // SessionSubStatusType.ENTER_IN_QUEUE,
          // SessionSubStatusType.IN_FRONT_OF_QUEUE,
          SessionSubStatusType.AVAILABLE,
          // SessionSubStatusType.CONFIRMATION_REQUIRED,
        ].includes(session?.sub_status as SessionSubStatusType)
      ) {
        responsePayload.session_queue_info = {
          queue_number: session.queue_number ?? 0,
          queue_status: session.status ?? '',
          queue_sub_status: session.sub_status ?? '',
          queue_start_time: session.queue_start_time ?? new Date(),
          queue_waiting_time: null,
        };
      }

      if (
        responsePayload.healing_round &&
        typeof lastSummary?.content !== 'undefined'
      ) {
        responsePayload.healing_round.last_round_feedback =
          lastSummary?.content ?? '';
      }

      delete responsePayload['queue_number'];
      delete responsePayload['queue_start_time'];
      return responsePayload;
    } catch (error) {
      throw new BadRequestException('Please try again');
    }
  }

  async getNextQueueNumber(): Promise<bigint> {
    const result: { nextval: bigint }[] = await this.prisma.$queryRawUnsafe(
      `SELECT nextval('global_queue_number_seq')`
    );
    return result[0]?.nextval;
  }

  async update(
    session_id: number,
    body: Prisma.healing_sessionsUpdateInput,
    profile_id: number,
    emitter?: EventEmitter2 | ClientProxy
  ) {
    const [profile, checkSession] = await Promise.all([
      this.profileService.profile({
        profile_id: profile_id,
      }),
      this.findOne(session_id),
    ]);

    if (!checkSession) {
      throw new NotFoundException('Healing Session not found');
    }
    if (!profile) {
      throw new NotFoundException('Profile not found');
    }

    if (profile?.profile_type.toLocaleLowerCase() == ProfileType.HEALER) {
      throw new BadRequestException('Healer can not update a session');
    }

    const updatedSession = await this.sessionRepository.update({
      where: { session_id, is_deleted: false },
      data: { ...body },
    });

    if (checkSession && checkSession.sub_status_updated_at) {
      const oldDate = new Date(checkSession.sub_status_updated_at);
      const newDate = new Date(body.sub_status_updated_at as Date);
      const responsePayload = await this.findOne(session_id);
      if (newDate > oldDate) {
        new WsSendEvent(
          emitter ?? this.eventEmitter,
          SESSION_STATUS.STATUS,
          responsePayload,
          profile?.profile_id
        ).emit();
      }
    }
    return updatedSession;
  }

  async acceptSessionOffer(profile_id: number, session_id: number) {
    try {
      // Log method entry
      this.logger.log(
        {
          event: 'ACCEPT_SESSION_OFFER_START',
          profile_id: profile_id,
          session_id: session_id,
          message: 'Starting session offer acceptance process',
        },
        'Accept Session Offer'
      );

      const session = await this.findFirstSession({
        profile_id,
        session_id,
      });

      if (!session) {
        this.logger.error(
          {
            event: 'ACCEPT_SESSION_OFFER_SESSION_NOT_FOUND',
            profile_id: profile_id,
            session_id: session_id,
            message: 'Session not found for offer acceptance',
          },
          'Accept Session Offer Validation Failed'
        );
        throw new BadRequestException('Session not found.');
      }

      if (
        session?.status !== SessionStatus.IN_QUEUE ||
        session?.sub_status !== SessionSubStatusType.CONFIRMATION_REQUIRED
      ) {
        this.logger.error(
          {
            event: 'ACCEPT_SESSION_OFFER_INVALID_STATUS',
            profile_id: profile_id,
            session_id: session_id,
            session_status: session.status,
            session_sub_status: session.sub_status,
            message: 'Invalid session status for offer acceptance',
          },
          'Accept Session Offer Status Validation Failed'
        );
        throw new BadRequestException('Invalid session status.');
      }
      // Trigger Placebo healing if session type is PLACEBO
      if (session.session_type === SessionType.PLACEBO) {
        this.logger.log(
          {
            event: 'ACCEPT_SESSION_OFFER_PLACEBO_HEALING_START',
            profile_id: profile_id,
            session_id: session_id,
            session_type: session.session_type,
            message: 'Starting placebo healing process',
          },
          'Accept Session Offer Placebo'
        );

        await this.publisherService.sendMessage({
          message: {
            session_id,
            action: PlaceboActionType.START_HEALING,
          },
          queueRoutingKey: PLACEBO_REQUESTS_DELAY_QUEUE.routingKey,
          expiration: this.publisherService.getRandomDelayInMs(3000, 10000), // Sending request to delay with with a rando delay between 3 to 10 seconds,
        });
      }

      if (session.session_type === SessionType.HEALER && session.healer_id) {
        this.logger.log(
          {
            event: 'ACCEPT_SESSION_OFFER_HEALER_STATUS_UPDATE',
            profile_id: profile_id,
            session_id: session_id,
            healer_id: session.healer_id,
            session_type: session.session_type,
            message: 'Updating healer status to patient confirmed',
          },
          'Accept Session Offer Healer Update'
        );

        // Update healer status to waiting for patient confirmation
        await this.healerStatusRepository.upsert({
          where: { profile_id: session.healer_id },
          create: {
            profile_id,
            status: HealerStatusType.PATIENT_CONFIRMED,
            updated_at: new Date(),
          },
          update: {
            status: HealerStatusType.PATIENT_CONFIRMED,
            updated_at: new Date(),
          },
        });
      }

      const updatedSession = await this.sessionRepository.update({
        where: { session_id },
        data: {
          sub_status: SessionSubStatusType.SESSION_CONFIRMED,
          sub_status_updated_at: new Date(),
          updated_at: new Date(),
        },
      });

      this.logger.log(
        {
          event: 'ACCEPT_SESSION_OFFER_SUCCESS',
          profile_id: profile_id,
          session_id: session_id,
          session_type: session.session_type,
          healer_id: session.healer_id || null,
          message: 'Session offer accepted successfully',
        },
        'Accept Session Offer Success'
      );

      return updatedSession;
    } catch (error) {
      this.logger.error(
        {
          event: 'ACCEPT_SESSION_OFFER_ERROR',
          profile_id: profile_id,
          session_id: session_id,
          error_message:
            error instanceof Error ? error.message : 'unknown error',
          message: 'Session offer acceptance failed',
        },
        'Accept Session Offer Error'
      );
      throw error;
    }
  }

  async declineSessionOffer(
    user_id: number,
    profile_id: number,
    session_id: number,
    trigger_source: SessionDeclineTriggerSourceType = SessionDeclineTriggerSourceType.USER
  ) {
    try {
      // Log method entry
      this.logger.log(
        {
          event: 'DECLINE_SESSION_OFFER_START',
          user_id: user_id,
          profile_id: profile_id,
          session_id: session_id,
          trigger_source: trigger_source,
          message: 'Starting session offer decline process',
        },
        'Decline Session Offer'
      );

      const session = await this.findFirstSession({
        profile_id,
        session_id,
      });

      if (!session) {
        this.logger.error(
          {
            event: 'DECLINE_SESSION_OFFER_SESSION_NOT_FOUND',
            user_id: user_id,
            profile_id: profile_id,
            session_id: session_id,
            message: 'Session not found for offer decline',
          },
          'Decline Session Offer Validation Failed'
        );
        throw new BadRequestException('Session not found.');
      }

      if (
        session?.status !== SessionStatus.IN_QUEUE ||
        session?.sub_status !== SessionSubStatusType.CONFIRMATION_REQUIRED
      ) {
        this.logger.error(
          {
            event: 'DECLINE_SESSION_OFFER_INVALID_STATUS',
            user_id: user_id,
            profile_id: profile_id,
            session_id: session_id,
            session_status: session.status,
            session_sub_status: session.sub_status,
            message: 'Invalid session status for offer decline',
          },
          'Decline Session Offer Status Validation Failed'
        );
        throw new BadRequestException('Invalid session status.');
      }
      const sub_status =
        trigger_source === SessionDeclineTriggerSourceType.USER
          ? SessionSubStatusType.NOT_AVAILABLE
          : SessionSubStatusType.SESSION_MISSED;

      this.logger.log(
        {
          event: 'DECLINE_SESSION_OFFER_STATUS_DETERMINED',
          user_id: user_id,
          profile_id: profile_id,
          session_id: session_id,
          trigger_source: trigger_source,
          determined_sub_status: sub_status,
          message: 'Determined session sub-status based on trigger source',
        },
        'Decline Session Offer Status'
      );

      const sessionUpdate = await this.sessionRepository.update({
        where: { session_id },
        data: {
          healer_id: null,
          sub_status,
          sub_status_updated_at: new Date(),
          updated_at: new Date(),
        },
      });

      if (sub_status === SessionSubStatusType.SESSION_MISSED) {
        this.logger.log(
          {
            event: 'DECLINE_SESSION_OFFER_MISSED_NOTIFICATION',
            user_id: user_id,
            profile_id: profile_id,
            session_id: session_id,
            message: 'Sending session missed notification',
          },
          'Decline Session Offer Notification'
        );

        await this.notificationService.sendPushNotification(user_id, {
          title: PushNotificationTitleType.SESSION_MISSED,
          body: PushNotificationBodyType.SESSION_MISSED,
        });
      }

      if (session.session_type === SessionType.HEALER && session.healer_id) {
        this.logger.log(
          {
            event: 'DECLINE_SESSION_OFFER_HEALER_STATUS_UPDATE',
            user_id: user_id,
            profile_id: profile_id,
            session_id: session_id,
            healer_id: session.healer_id,
            message: 'Updating healer status and processing next request',
          },
          'Decline Session Offer Healer Update'
        );

        await this.healerStatusRepository.upsert({
          where: { profile_id: session.healer_id },
          create: {
            profile_id,
            status: HealerStatusType.PATIENT_REQUESTED,
            updated_at: new Date(),
          },
          update: {
            status: HealerStatusType.PATIENT_REQUESTED,
            updated_at: new Date(),
          },
        });

        // Assign next available patient to healer asynchronously
        this.processHealerRequest({
          profile_id: session.healer_id,
        } as { profile_id: number }).catch((err) => {
          this.logger.error(
            {
              event: 'DECLINE_SESSION_OFFER_HEALER_REQUEST_FAILED',
              user_id: user_id,
              profile_id: profile_id,
              session_id: session_id,
              healer_id: session.healer_id,
              error_message:
                err instanceof Error ? err.message : 'unknown error',
              message: 'Failed to process healer request after decline',
            },
            'Decline Session Offer Healer Request Error'
          );
        });
      }

      this.logger.log(
        {
          event: 'DECLINE_SESSION_OFFER_SUCCESS',
          user_id: user_id,
          profile_id: profile_id,
          session_id: session_id,
          trigger_source: trigger_source,
          final_sub_status: sub_status,
          session_type: session.session_type,
          healer_id: session.healer_id || null,
          message: 'Session offer declined successfully',
        },
        'Decline Session Offer Success'
      );

      return sessionUpdate;
    } catch (error) {
      this.logger.error(
        {
          event: 'DECLINE_SESSION_OFFER_ERROR',
          user_id: user_id,
          profile_id: profile_id,
          session_id: session_id,
          trigger_source: trigger_source,
          error_message:
            error instanceof Error ? error.message : 'unknown error',
          message: 'Session offer decline failed',
        },
        'Decline Session Offer Error'
      );
      throw error;
    }
  }

  async processHealerRequest(data: { profile_id: number }): Promise<boolean> {
    try {
      const { profile_id } = data;

      // Log method entry
      this.logger.log(
        {
          event: 'PROCESS_HEALER_REQUEST_START',
          profile_id: profile_id,
          message: 'Starting healer request processing',
        },
        'Process Healer Request'
      );

      const availablePatientCount = await this.availablePatientCount();
      const availableSession = await this.getAvailablePatientOlder(profile_id);

      this.logger.log(
        {
          event: 'PROCESS_HEALER_REQUEST_PATIENT_COUNT_CHECKED',
          profile_id: profile_id,
          available_patient_count: availablePatientCount,
          min_count_for_placebo: this.MIN_SESSION_COUNT_FOR_PLACEBO,
          placebo_percentage: this.HEALING_SESSION_PLACEBO_PERCENTAGE,
          message: 'Checked available patient count and placebo configuration',
        },
        'Process Healer Request Patient Count'
      );

      // Always assign if few patients are left
      if (
        availablePatientCount <= this.MIN_SESSION_COUNT_FOR_PLACEBO ||
        Math.random() > this.HEALING_SESSION_PLACEBO_PERCENTAGE / 100
      ) {
        this.logger.log(
          {
            event: 'PROCESS_HEALER_REQUEST_ASSIGNING_TO_HEALER',
            profile_id: profile_id,
            session_id: availableSession?.session_info?.session_id,
            reason:
              availablePatientCount <= this.MIN_SESSION_COUNT_FOR_PLACEBO
                ? 'low_patient_count'
                : 'random_selection',
            message: 'Assigning session to healer',
          },
          'Process Healer Request Healer Assignment'
        );

        await this.sessionAssignedToHealer(profile_id, availableSession);

        this.logger.log(
          {
            event: 'PROCESS_HEALER_REQUEST_SUCCESS',
            profile_id: profile_id,
            session_id: availableSession?.session_info?.session_id,
            assignment_type: 'healer',
            message: 'Healer request processed successfully',
          },
          'Process Healer Request Success'
        );

        return true;
      }

      // Offload this session to placebo
      this.logger.log(
        {
          event: 'PROCESS_HEALER_REQUEST_OFFLOADING_TO_PLACEBO',
          profile_id: profile_id,
          session_id: availableSession?.session_info?.session_id,
          message: 'Offloading session to placebo and retrying',
        },
        'Process Healer Request Placebo Offload'
      );

      await this.initiatePlaceboSession(availableSession);
      // Try again until healer gets a session
      return this.processHealerRequest(data);
    } catch (error) {
      this.logger.error(
        {
          event: 'PROCESS_HEALER_REQUEST_ERROR',
          profile_id: data.profile_id,
          error_message:
            error instanceof Error ? error.message : 'unknown error',
          message: 'Healer request processing failed',
        },
        'Process Healer Request Error'
      );
      throw error;
    }
  }

  async initiatePlaceboSession(availableSession: any): Promise<void> {
    try {
      // Log method entry
      this.logger.log(
        {
          event: 'INITIATE_PLACEBO_SESSION_START',
          session_id: availableSession?.session_info?.session_id,
          profile_id: availableSession?.session_info?.profile_id,
          message: 'Starting placebo session initiation',
        },
        'Initiate Placebo Session'
      );

      if (!availableSession || !availableSession.session_info) {
        this.logger.error(
          {
            event: 'INITIATE_PLACEBO_SESSION_NO_PATIENT',
            message: 'No patient session found for placebo initiation',
          },
          'Initiate Placebo Session Validation Failed'
        );
        throw new NotFoundException('No patient found');
      }

      const sessionId = availableSession.session_info.session_id;
      const currentTimeStamp = new Date().toISOString();

      this.logger.log(
        {
          event: 'INITIATE_PLACEBO_SESSION_UPDATING_STATUS',
          session_id: sessionId,
          profile_id: availableSession.session_info.profile_id,
          new_status: SessionStatus.IN_QUEUE,
          new_sub_status: SessionSubStatusType.CONFIRMATION_REQUIRED,
          session_type: SessionType.PLACEBO,
          message: 'Updating session status for placebo',
        },
        'Initiate Placebo Session Status Update'
      );

      await this.sessionRepository.update({
        where: { session_id: sessionId },
        data: {
          status: SessionStatus.IN_QUEUE,
          sub_status: SessionSubStatusType.CONFIRMATION_REQUIRED,
          sub_status_updated_at: currentTimeStamp,
          healer_id: -1, // Placeholder for placebo healer
          updated_at: currentTimeStamp,
          last_session_offer_at: currentTimeStamp,
          session_type: SessionType.PLACEBO,
        },
      });

      this.logger.log(
        {
          event: 'INITIATE_PLACEBO_SESSION_SENDING_NOTIFICATION',
          session_id: sessionId,
          profile_id: availableSession.session_info.profile_id,
          user_id: availableSession?.session_info?.profiles?.users?.user_id,
          message: 'Sending session offer notification to patient',
        },
        'Initiate Placebo Session Notification'
      );

      await this.notificationService.sendPushNotification(
        availableSession?.session_info?.profiles?.users?.user_id,
        {
          title: PushNotificationTitleType.SESSION_OFFER,
          body: PushNotificationBodyType.SESSION_OFFER,
        }
      );

      this.logger.log(
        {
          event: 'INITIATE_PLACEBO_SESSION_SUCCESS',
          session_id: sessionId,
          profile_id: availableSession.session_info.profile_id,
          session_type: SessionType.PLACEBO,
          message: 'Placebo session initiated successfully',
        },
        'Initiate Placebo Session Success'
      );
    } catch (error) {
      this.logger.error(
        {
          event: 'INITIATE_PLACEBO_SESSION_ERROR',
          session_id: availableSession?.session_info?.session_id,
          profile_id: availableSession?.session_info?.profile_id,
          error_message:
            error instanceof Error ? error.message : 'unknown error',
          message: 'Placebo session initiation failed',
        },
        'Initiate Placebo Session Error'
      );
      throw error;
    }
  }

  async sessionAssignedToHealer(profile_id: number, availableSession: any) {
    try {
      // Log method entry
      this.logger.log(
        {
          event: 'SESSION_ASSIGNED_TO_HEALER_START',
          profile_id: profile_id,
          session_id: availableSession?.session_info?.session_id,
          patient_id: availableSession?.session_info?.profile_id,
          message: 'Starting session assignment to healer',
        },
        'Session Assigned To Healer'
      );

      const healer = await this.getAvailableHealer(profile_id);
      if (!healer) {
        this.logger.error(
          {
            event: 'SESSION_ASSIGNED_TO_HEALER_NO_HEALER',
            profile_id: profile_id,
            message: 'No available healer found',
          },
          'Session Assigned To Healer Validation Failed'
        );
        throw new NotFoundException('No available Healer!');
      }

      if (!availableSession || !availableSession.session_info) {
        this.logger.log(
          {
            event: 'SESSION_ASSIGNED_TO_HEALER_NO_PATIENT_UPDATING_STATUS',
            profile_id: profile_id,
            new_status: HealerStatusType.NO_AVAILABLE_PATIENTS,
            message: 'No patient found, updating healer status',
          },
          'Session Assigned To Healer No Patient'
        );

        await this.healerStatusRepository.upsert({
          where: { profile_id },
          create: {
            profile_id,
            status: HealerStatusType.NO_AVAILABLE_PATIENTS,
            updated_at: new Date(),
          },
          update: {
            status: HealerStatusType.NO_AVAILABLE_PATIENTS,
            updated_at: new Date(),
          },
        });

        this.logger.error(
          {
            event: 'SESSION_ASSIGNED_TO_HEALER_NO_PATIENT',
            profile_id: profile_id,
            message: 'No patient session found for healer assignment',
          },
          'Session Assigned To Healer Patient Validation Failed'
        );
        throw new NotFoundException('No patient found');
      }
      const currentTimeStamp = new Date().toISOString();

      this.logger.log(
        {
          event: 'SESSION_ASSIGNED_TO_HEALER_UPDATING_SESSION',
          profile_id: profile_id,
          healer_id: healer.profile_id,
          session_id: availableSession.session_info.session_id,
          patient_id: availableSession.session_info.profile_id,
          new_status: SessionStatus.IN_QUEUE,
          new_sub_status: SessionSubStatusType.CONFIRMATION_REQUIRED,
          session_type: SessionType.HEALER,
          message: 'Updating session with healer assignment',
        },
        'Session Assigned To Healer Session Update'
      );

      const session_update = await this.sessionRepository.update({
        where: { session_id: availableSession.session_info.session_id },
        data: {
          status: SessionStatus.IN_QUEUE,
          sub_status: SessionSubStatusType.CONFIRMATION_REQUIRED,
          sub_status_updated_at: currentTimeStamp,
          updated_at: currentTimeStamp,
          last_session_offer_at: currentTimeStamp,
          healer_id: healer.profile_id,
          session_type: SessionType.HEALER,
        },
      });

      this.logger.log(
        {
          event: 'SESSION_ASSIGNED_TO_HEALER_UPDATING_HEALER_STATUS',
          profile_id: profile_id,
          healer_id: healer.profile_id,
          session_id: availableSession.session_info.session_id,
          new_healer_status: HealerStatusType.WAITING_PATIENT_CONFIRMATION,
          message: 'Updating healer status to waiting for patient confirmation',
        },
        'Session Assigned To Healer Status Update'
      );

      await this.healerStatusRepository.upsert({
        where: { profile_id },
        create: {
          profile_id,
          status: HealerStatusType.WAITING_PATIENT_CONFIRMATION,
          updated_at: new Date(),
        },
        update: {
          status: HealerStatusType.WAITING_PATIENT_CONFIRMATION,
          updated_at: new Date(),
        },
      });

      this.logger.log(
        {
          event: 'SESSION_ASSIGNED_TO_HEALER_SENDING_NOTIFICATION',
          profile_id: profile_id,
          healer_id: healer.profile_id,
          session_id: availableSession.session_info.session_id,
          patient_id: availableSession.session_info.profile_id,
          user_id: availableSession?.session_info?.profiles?.users?.user_id,
          message: 'Sending session offer notification to patient',
        },
        'Session Assigned To Healer Notification'
      );

      await this.notificationService.sendPushNotification(
        availableSession?.session_info?.profiles?.users?.user_id,
        {
          title: PushNotificationTitleType.SESSION_OFFER,
          body: PushNotificationBodyType.SESSION_OFFER,
        }
      );

      this.logger.log(
        {
          event: 'SESSION_ASSIGNED_TO_HEALER_SUCCESS',
          profile_id: profile_id,
          healer_id: healer.profile_id,
          session_id: availableSession.session_info.session_id,
          patient_id: availableSession.session_info.profile_id,
          session_type: SessionType.HEALER,
          message: 'Session assigned to healer successfully',
        },
        'Session Assigned To Healer Success'
      );

      return { session_info: session_update };
    } catch (error) {
      this.logger.error(
        {
          event: 'SESSION_ASSIGNED_TO_HEALER_ERROR',
          profile_id: profile_id,
          session_id: availableSession?.session_info?.session_id,
          patient_id: availableSession?.session_info?.profile_id,
          error_message:
            error instanceof Error ? error.message : 'unknown error',
          message: 'Session assignment to healer failed',
        },
        'Session Assigned To Healer Error'
      );
      throw error;
    }
  }

  async getAvailableHealer(profile_id: number) {
    return this.profileRepository.getHealerProfileInfo({
      where: {
        profile_type: ProfileType?.HEALER,
        is_active: true,
        profile_id,
      },
    });
  }

  async availablePatientCount(): Promise<number> {
    return this.prisma.healing_sessions.count({
      where: {
        status: SessionStatus.IN_QUEUE,
        sub_status: SessionSubStatusType.AVAILABLE,
      },
    });
  }

  async getAvailablePatientOlder(healerId: number) {
    try {
      // const occupiedPositions = await this.sessionRepository.findAll({
      //   where: {
      //     status: SessionStatus.IN_QUEUE,
      //     sub_status: SessionSubStatusType.AVAILABLE,
      //     OR: [
      //       {
      //         healer_id: {
      //           not: healerId,
      //         },
      //       },
      //       {
      //         healer_id: null,
      //       },
      //     ],
      //   },
      //   orderBy: {
      //     queue_number: 'asc',
      //   },
      // });

      // if (!occupiedPositions.length) {
      //   return null;
      // }

      // let availableQueueNumber = 1;

      // for (const patient of occupiedPositions) {
      //   if (availableQueueNumber < patient.queue_number!) {
      //     break;
      //   }
      //   availableQueueNumber++;
      // }

      const sessionData = await this.prisma.healing_sessions.findFirst({
        // where: {
        //   session_id: occupiedPositions[0]?.session_id,
        //   status: SessionStatus.IN_QUEUE,
        //   sub_status: SessionSubStatusType.AVAILABLE,
        //   queue_number: occupiedPositions[0]?.queue_number,
        // },
        where: {
          status: SessionStatus.IN_QUEUE,
          sub_status: SessionSubStatusType.AVAILABLE,
          queue_number: {
            not: null,
          },
          OR: [
            {
              healer_id: {
                not: healerId,
              },
            },
            {
              healer_id: null,
            },
          ],
        },
        orderBy: {
          queue_number: 'asc',
        },
        include: {
          profiles: {
            select: {
              users: true,
            },
          },
        },
      });

      const patientData = await this.profileRepository.getHealerProfileInfo({
        where: { profile_id: sessionData?.profile_id },
      });

      return {
        patient_info: patientData,
        session_info: sessionData,
      };
    } catch (error) {
      throw new BadRequestException('There are no available patients');
    }
  }

  async updateSessionStatus(
    session_id: number,
    status: string,
    subStatus?: string
  ) {
    return this.sessionRepository.update({
      where: { session_id },
      data: {
        status,
        sub_status: subStatus || null,
        sub_status_updated_at: new Date(),
        updated_at: new Date(),
      },
    });
  }

  async findFirstSession(
    where: Prisma.healing_sessionsWhereInput,
    include?: Prisma.healing_sessionsInclude
  ) {
    const includes = include || {
      conversations: true,
    };
    return await this.sessionRepository.findOneWithInclude({
      where,
      include: includes,
    });
  }

  // format minutes to hours and days
  formatMinutes(minutes: number): string {
    if (minutes < 60) {
      return `${minutes} minute${minutes !== 1 ? 's' : ''}`;
    } else if (minutes < 1440) {
      const hours = Math.floor(minutes / 60);
      return `${hours} hour${hours !== 1 ? 's' : ''}`;
    } else {
      const days = Math.floor(minutes / 1440);
      return `${days} day${days !== 1 ? 's' : ''}`;
    }
  }
}
