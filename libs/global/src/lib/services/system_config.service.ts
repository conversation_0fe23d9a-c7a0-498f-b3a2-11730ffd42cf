import { Injectable } from '@nestjs/common';
import { PrismaService } from '@core/prisma-client';
import { SystemConfigType } from '../../enum';
import { Logger } from 'nestjs-pino';

@Injectable()
export class SystemConfigService {
  async getNotificationTitle() {
    try {
      const region = await this.prisma.system_configs.findFirst({
        where: {
          name: SystemConfigType.NotificationTitle,
          is_deleted: false,
          is_active: true,
        },
      });
      return region?.value || '';
    } catch (error) {
      this.logger.error(
        {
          event: 'GET_NOTIFICATION_TITLE_ERROR',
          error_message:
            error instanceof Error ? error.message : 'unknown error',
          message: 'Failed to retrieve notification title',
        },
        'Get Notification Title Error'
      );
      throw error;
    }
  }
  constructor(private prisma: PrismaService, private readonly logger: Logger) {}

  async getNotificationMessage() {
    try {
      const region = await this.prisma.system_configs.findFirst({
        where: {
          name: SystemConfigType.NotificationMessage,
          is_deleted: false,
          is_active: true,
        },
      });
      return region?.value || '';
    } catch (error) {
      this.logger.error(
        {
          event: 'GET_NOTIFICATION_MESSAGE_ERROR',
          error_message:
            error instanceof Error ? error.message : 'unknown error',
          message: 'Failed to retrieve notification message',
        },
        'Get Notification Message Error'
      );
      throw error;
    }
  }

  async getActiveRegion() {
    try {
      const region = await this.prisma.system_configs.findFirst({
        where: {
          name: SystemConfigType.ActiveRegion,
          is_deleted: false,
          is_active: true,
        },
      });
      return region?.value || '';
    } catch (error) {
      this.logger.error(
        {
          event: 'GET_ACTIVE_REGION_ERROR',
          error_message:
            error instanceof Error ? error.message : 'unknown error',
          message: 'Failed to retrieve active region',
        },
        'Get Active Region Error'
      );
      throw error;
    }
  }

  async getAIFoundryProjectEndpoint(region?: string) {
    try {
      if (!region) {
        region = await this.getActiveRegion();
      }
      const endpointUrl = await this.prisma.system_configs.findFirst({
        where: {
          name: `${region}${SystemConfigType.AIFoundryProjectEndpoint}`,
          is_deleted: false,
          is_active: true,
        },
      });
      return endpointUrl?.value || '';
    } catch (error) {
      this.logger.error(
        {
          event: 'GET_AI_FOUNDRY_PROJECT_ENDPOINT_ERROR',
          error_message:
            error instanceof Error ? error.message : 'unknown error',
          message: 'Failed to retrieve AI Foundry project endpoint',
        },
        'Get AI Foundry Project Endpoint Error'
      );
      throw error;
    }
  }

  async getIntakeConversationAgentId(region?: string) {
    try {
      if (!region) {
        region = await this.getActiveRegion();
      }
      const config = await this.prisma.system_configs.findFirst({
        where: {
          name: `${region}${SystemConfigType.IntakeConversationAgentId}`,
          is_deleted: false,
        },
      });
      return config?.value || '';
    } catch (error) {
      this.logger.error({
        event: 'GET_INTAKE_CONVERSATION_AGENT_ID_ERROR',
        error_message: error instanceof Error ? error.message : 'unknown error',
        message: 'Failed to retrieve intake conversation agent id',
      });
      throw error;
    }
  }

  async getIntakeSummarizationAgentId(region?: string) {
    try {
      if (!region) {
        region = await this.getActiveRegion();
      }
      const config = await this.prisma.system_configs.findFirst({
        where: {
          name: `${region}${SystemConfigType.IntakeSummarizationAgentId}`,
          is_deleted: false,
          is_active: true,
        },
      });
      return config?.value || '';
    } catch (error) {
      this.logger.error({
        event: 'GET_INTAKE_SUMMARIZATION_AGENT_ID_ERROR',
        error_message: error instanceof Error ? error.message : 'unknown error',
        message: 'Failed to retrieve intake summarization agent id',
      });
      throw error;
    }
  }

  async getAilmentExtractionAssistantId(region?: string) {
    try {
      if (!region) {
        region = await this.getActiveRegion();
      }
      const config = await this.prisma.system_configs.findFirst({
        where: {
          name: `${region}${SystemConfigType.AilmentExtractionAssistantId}`,
          is_deleted: false,
          is_active: true,
        },
      });
      return config?.value || '';
    } catch (error) {
      this.logger.error({
        event: 'GET_AILMENT_EXTRACTION_ASSISTANT_ID_ERROR',
        error_message: error instanceof Error ? error.message : 'unknown error',
        message: 'Failed to retrieve ailment extraction assistant id',
      });
      throw error;
    }
  }

  async getHealingRoundAssistantId(region?: string) {
    try {
      if (!region) {
        region = await this.getActiveRegion();
      }
      const config = await this.prisma.system_configs.findFirst({
        where: {
          name: `${region}${SystemConfigType.HealingRoundAssistantId}`,
          is_deleted: false,
          is_active: true,
        },
      });
      return config?.value || '';
    } catch (error) {
      this.logger.error({
        event: 'GET_HEALING_ROUND_ASSISTANT_ID_ERROR',
        error_message: error instanceof Error ? error.message : 'unknown error',
        message: 'Failed to retrieve healing round assistant id',
      });
      throw error;
    }
  }
}
