import { ConversationDto } from "@core/libs";
import { ClassConstructor, plainToClass } from "@nestjs/class-transformer";
import { validate, ValidationError } from "@nestjs/class-validator";
import { BadRequestException } from "@nestjs/common";

export class WsParser {
    constructor(
        public payload: Record<string, unknown>
    ) {}

    public async parser<T extends object, V>(cls: ClassConstructor<T>, plain: V): Promise<T> {
        const dto = plainToClass(cls, this.payload)
        const errors = await validate(dto);
        if (errors.length > 0) {
            throw new BadRequestException('Validation failed: ' + errors.map((err: ValidationError) => err.constraints?Object.entries(err.constraints):'').join('\n'));
        }
        return dto;
    }
}
  
export class WsConversationParser extends WsParser{
    public async getPayload(){
        return this.parser(ConversationDto, this.payload);
    }
}