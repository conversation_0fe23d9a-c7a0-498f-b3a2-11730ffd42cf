import { Injectable } from '@nestjs/common';
import { SessionFollowUpInterval, SessionFollowUpStatus } from '../../enum';
import { SessionFollowUpRepository } from 'libs/data-access/src';
import { SessionFollowUp } from '../dtos/global.dto';
import { LibFcmService } from '@core_be/notifications';
import { PrismaService } from '@core/prisma-client';

@Injectable()
export class FollowUpService {
  constructor(
    private sessionFollowUpRepo: SessionFollowUpRepository,
    private fcmService: LibFcmService,
    private prisma: PrismaService
  ) {}

  async scheduleFollowUpMessage(
    interval: SessionFollowUpInterval,
    timeAgo: Date
  ) {
    const { deviceIds, sessionIds } =
      await this.handleFollowUpMessageAfterInterval(interval, timeAgo);

    if (deviceIds?.length) {
      await this.sendFollowUpNotificationInBatches(deviceIds);
    }

    if (sessionIds?.length) {
      await this.updateSessionsFollowUpStatus(sessionIds, interval);
    }
  }

  async sendFollowUpMessageEveryDay() {
    const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
    await this.scheduleFollowUpMessage(
      SessionFollowUpInterval.TWENTY_FOUR_HRS,
      twentyFourHoursAgo
    );
  }

  async sendFollowUpMessageEveryWeek() {
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setHours(0, 0, 0, 0);
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    await this.scheduleFollowUpMessage(
      SessionFollowUpInterval.SEVEN_DAYS,
      sevenDaysAgo
    );
  }

  private async sendFollowUpNotificationInBatches(deviceIds: string[]) {
    if (!deviceIds || deviceIds.length === 0) return;

    const batchSize = 100;
    const deviceIdChunks = this.chunkArray(deviceIds, batchSize);

    for (const chunk of deviceIdChunks) {
      await this.fcmService.sendMessageByTokens(
        chunk,
        {
          notification: {
            title: 'Follow up notification',
            body: 'Are you feeling better after your healing sessions?',
          },
        },
        false
      );
    }
  }

  private async updateSessionsFollowUpStatus(
    sessionIds: number[],
    interval: SessionFollowUpInterval
  ) {
    if (!sessionIds || sessionIds.length === 0) return;

    const batchSize = 500;
    const sessionIdChunks = this.chunkArray(sessionIds, batchSize);
    const dateNow = new Date().toISOString();

    for (const chunk of sessionIdChunks) {
      await this.prisma.session_follow_up.updateMany({
        where: { session_id: { in: chunk } },
        data: {
          follow_up_interval: interval,
          status: SessionFollowUpStatus.COMPLETED,
          updated_at: dateNow,
          follow_up_at: dateNow,
        },
      });
    }
  }

  async handleFollowUpMessageAfterInterval(
    interval: SessionFollowUpInterval,
    timeAgo: Date
  ): Promise<{ deviceIds: string[]; sessionIds: number[] }> {
    const sessionFollowUps = await this.fetchSessionFollowUps(
      interval,
      timeAgo
    );
    return await this.getDeviceIds(sessionFollowUps);
  }

  private async fetchSessionFollowUps(
    interval: SessionFollowUpInterval,
    timeAgo: Date
  ) {
    const whereCondition =
      interval === SessionFollowUpInterval.TWENTY_FOUR_HRS
        ? {
            status: SessionFollowUpStatus.PENDING,
            follow_up_interval: {
              notIn: [
                SessionFollowUpInterval.TWENTY_FOUR_HRS,
                SessionFollowUpInterval.SEVEN_DAYS,
              ],
            },
            follow_up_at: { gte: timeAgo },
          }
        : {
            status: SessionFollowUpStatus.COMPLETED,
            follow_up_interval: SessionFollowUpInterval.TWENTY_FOUR_HRS,
            follow_up_at: { gte: timeAgo },
          };

    const pageSize = 500;
    const sessions = await this.sessionFollowUpRepo.findAllWithInclude({
      where: whereCondition,
      take: pageSize,
    });

    return sessions;
  }

  async getDeviceIds(
    followUpSessions: SessionFollowUp[] | null
  ): Promise<{ deviceIds: string[]; sessionIds: number[] }> {
    if (!followUpSessions || followUpSessions.length === 0) {
      return { deviceIds: [], sessionIds: [] };
    }

    const userIds = followUpSessions.map((session) => session.user_id);
    const sessionIds = followUpSessions.map((session) => session.session_id);

    const batchSize = 500;
    let deviceIds: string[] = [];

    if (userIds.length > 0) {
      for (let i = 0; i < userIds.length; i += batchSize) {
        const userIdBatch = userIds.slice(i, i + batchSize);
        const deviceIdDetail = await this.prisma.user_devices.findMany({
          where: { is_deleted: false, user_id: { in: userIdBatch } },
        });

        // Only add devices if the batch returned valid results
        if (deviceIdDetail?.length > 0) {
          deviceIds = deviceIdDetail.map((device) => device.device_id);
        }
      }
    }

    return { deviceIds, sessionIds };
  }

  chunkArray(array: any[], size: number): any[][] {
    const result = [];
    if (!array || array.length === 0) return result;

    for (let i = 0; i < array.length; i += size) {
      result.push(array.slice(i, i + size));
    }
    return result;
  }
}
