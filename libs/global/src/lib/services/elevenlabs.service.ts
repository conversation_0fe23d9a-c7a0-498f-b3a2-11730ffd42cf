import { Injectable } from '@nestjs/common';
import { ElevenLabsClient } from '@elevenlabs/elevenlabs-js';
import { ConversationalAgentType } from '../../enum';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class ElevenLabsService {
  private readonly ELEVENLABS_INTAKE_AGENT_ID: string;
  private readonly ELEVENLABS_REPORT_AGENT_ID: string;
  private readonly ELEVENLABS_CHECK_IN_AGENT_ID: string;
  private readonly elevenlabs: ElevenLabsClient;

  constructor(private readonly configService: ConfigService) {
    this.elevenlabs = new ElevenLabsClient({
      apiKey: this.configService.getOrThrow('ELEVENLABS_API_KEY'),
    });

    this.ELEVENLABS_INTAKE_AGENT_ID = this.configService.getOrThrow(
      'ELEVENLABS_INTAKE_AGENT_ID'
    );
    this.ELEVENLABS_CHECK_IN_AGENT_ID = this.configService.getOrThrow(
      'ELEVENLABS_CHECK_IN_AGENT_ID'
    );
    this.ELEVENLABS_REPORT_AGENT_ID = this.configService.getOrThrow(
      'ELEVENLABS_REPORT_AGENT_ID'
    );
  }

  async getConversationalAgentDetails(agentType: ConversationalAgentType) {
    try {
      const agentId = this._getElevenlabsAgentId(agentType);
      const [signedUrlResponse, agentDetailsResponse] = await Promise.all([
        this.elevenlabs.conversationalAi.conversations.getSignedUrl({
          agentId,
        }),
        this.elevenlabs.conversationalAi.agents.get(agentId),
      ]);
      const signedUrl = signedUrlResponse?.signedUrl;
      if (!signedUrl || !agentDetailsResponse) {
        throw new Error(
          'Failed to fetch elevenlabs agent details or signed URL'
        );
      }
      return {
        signedUrl,
        agentDetails: {
          agent_id: agentDetailsResponse.agentId,
          name: agentDetailsResponse.name,
          conversation_config: {
            agent: {
              first_message: (
                agentDetailsResponse?.conversationConfig?.agent as any
              )?.first_message,
              prompt: (agentDetailsResponse?.conversationConfig?.agent as any)
                ?.prompt,
            },
          },
        },
      };
    } catch (error) {
      console.error('Error fetching elevenlabs data: ', error);
      throw error;
    }
  }

  private _getElevenlabsAgentId(agentType: ConversationalAgentType): string {
    let agentId: string;
    switch (agentType) {
      case ConversationalAgentType.Intake:
        agentId = this.ELEVENLABS_INTAKE_AGENT_ID;
        break;
      case ConversationalAgentType.Report:
        agentId = this.ELEVENLABS_REPORT_AGENT_ID;
        break;
      case ConversationalAgentType.CheckIn:
        agentId = this.ELEVENLABS_CHECK_IN_AGENT_ID;
        break;
      default:
        throw new Error('Unknown agent type');
    }
    return agentId;
  }
}
