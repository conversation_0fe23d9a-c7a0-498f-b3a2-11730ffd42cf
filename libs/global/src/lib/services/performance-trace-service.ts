import { Injectable, Inject } from '@nestjs/common';
import { INQUIRER } from '@nestjs/core';
import { Logger } from 'nestjs-pino';

export type PerformanceObject = {
  ts: DOMHighResTimeStamp;
  label: string;
  delay: number;
};

@Injectable()
export class MeasurePerformance {
  private perfArr: PerformanceObject[] = [];
  private readonly label: string;

  constructor(
    private readonly logger: Logger, // Inject the INQUIRER token, which holds the parent class
    @Inject(INQUIRER) private readonly parent: object
  ) {
    // Get the name of the parent class constructor
    this.label = this.parent?.constructor?.name || 'performance';
  }

  public fatal(message: string, data: any = {}) {
    this.logger.fatal({ _message: message, ...data });
    this.push(message);
  }

  public error(message: string, data: any = {}) {
    this.logger.error({ _message: message, ...data });
    this.push(message);
  }

  public warn(message: string, data: any = {}) {
    this.logger.warn({ _message: message, ...data });
    this.push(message);
  }

  public info(message: string, data: any = {}) {
    this.logger.log({ _message: message, ...data });
    this.push(message);
  }

  public debug(message: string, data: any = {}) {
    this.logger.debug({ _message: message, ...data });
  }

  public trace(message: string, data: any = {}) {
    this.logger.verbose({ _message: message, ...data });
  }

  public push(eventName: string) {
    const ts = ~~performance.now();
    this.perfArr.push({
      label: `${this.label} ${eventName}`,
      ts,
      delay:
        this.perfArr.length == 0
          ? 0
          : ts - this.perfArr[this.perfArr.length - 1].ts,
    });
  }

  public get() {
    return this.perfArr;
  }

  public logPerformance() {
    let sum = 0;
    const fullLog = this.perfArr.map((perfObj) => ({
      ...perfObj,
      sum: (sum += perfObj.delay),
    }));
    this.info('performance log', { performance: fullLog, total_time: sum });
  }

  public print() {
    let sum = 0;
    this.logger.log(
      `\n----------------------------- Performance Log for ${this.label}----------------------------\n      label      |        timestamp       |      delay (ms)      |        sum (ms)      \n` +
        this.perfArr
          .map(
            (perfObj) =>
              `${perfObj.label}   |   ${perfObj.ts}   |   ${
                perfObj.delay
              }   |   ${(sum += perfObj.delay)}`
          )
          .join('\n') +
        `\n------------------------------------------------------------------\n${this.label} Total Time: ${sum}ms\n------------------------------------------------------------------`
    );
  }
}
