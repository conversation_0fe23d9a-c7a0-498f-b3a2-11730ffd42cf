import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  UnauthorizedException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Role } from '../../enum';
import { Logger } from 'nestjs-pino';

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private readonly logger: Logger
  ) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.getAllAndOverride<Role[]>('roles', [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!requiredRoles || requiredRoles.length === 0) {
      throw new ForbiddenException(
        'Access denied: No roles specified for this endpoint'
      );
    }

    const request = context.switchToHttp().getRequest();
    const { user } = request;
    if (!user) {
      this.logger.error({
        timestamp: new Date().toISOString(),
        event: 'ROLE_CHECK_NO_USER',
        endpoint: request.url,
        requiredRoles: requiredRoles,
        message: 'Authentication required for role-protected endpoint'
      }, 'Role Authorization Failed');

      throw new UnauthorizedException('Authentication required');
    }

    if (!Array.isArray(user.roles)) {
      this.logger.error({
        timestamp: new Date().toISOString(),
        event: 'ROLE_CHECK_INVALID_DATA',
        userId: user.user_id,
        endpoint: request.url,
        requiredRoles: requiredRoles,
        message: 'Invalid user role data structure'
      }, 'Role Authorization Failed');

      throw new UnauthorizedException('Invalid user role data');
    }

    const userRoles = user.roles.filter(
      (role: string) => typeof role === 'string'
    );
    const hasRole = requiredRoles.some((role) => userRoles.includes(role));

    if (!hasRole) {
      this.logger.error({
        timestamp: new Date().toISOString(),
        event: 'ROLE_CHECK_INSUFFICIENT_PERMISSIONS',
        userId: user.user_id,
        userRoles: userRoles,
        requiredRoles: requiredRoles,
        endpoint: request.url,
        message: 'User lacks required roles for endpoint access'
      }, 'Role Authorization Failed');

      throw new ForbiddenException(
        `Access denied: You need one of the following roles: ${requiredRoles.join(
          ', '
        )}`
      );
    }

    this.logger.log({
      timestamp: new Date().toISOString(),
      event: 'ROLE_CHECK_SUCCESS',
      userId: user.user_id,
      userRoles: userRoles,
      requiredRoles: requiredRoles,
      endpoint: request.url,
      message: 'Role authorization successful'
    }, 'Role Authorization Success');

    return true;
  }
}
