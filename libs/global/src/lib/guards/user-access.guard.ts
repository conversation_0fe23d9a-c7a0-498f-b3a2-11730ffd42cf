import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  UnauthorizedException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { RequestWithUser } from './types';
import { Logger } from 'nestjs-pino';

@Injectable()
export class UserAccessGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private readonly logger: Logger
  ) {}

  canActivate(context: ExecutionContext): boolean {
    // Check if route is marked public
    const isPublic = this.reflector.getAllAndOverride('isPublic', [
      context.getHandler(),
      context.getClass(),
    ]);
    if (isPublic) return true;

    const request = context.switchToHttp().getRequest<RequestWithUser>();

    const user = request.user;
    const userIdSources = [
      request.params['user_id'],
      request.body?.user_id,
      request.query['user_id'],
    ];

    const targetUserId = parseInt(userIdSources.find(Boolean) || '', 10);
    if (!user?.user_id || isNaN(targetUserId)) {
      this.logger.error({
        timestamp: new Date().toISOString(),
        event: 'USER_ACCESS_INVALID_DATA',
        userId: user?.user_id,
        targetUserId: targetUserId,
        endpoint: request.url,
        message: 'Invalid user data for access control'
      }, 'User Access Control Failed');

      throw new UnauthorizedException('Invalid user data access');
    }

    const userId = user?.user_id;
    if (userId !== targetUserId) {
      this.logger.error({
        timestamp: new Date().toISOString(),
        event: 'USER_ACCESS_DENIED',
        userId: userId,
        targetUserId: targetUserId,
        endpoint: request.url,
        message: 'User attempted to access another user\'s data'
      }, 'User Access Control Failed');

      throw new ForbiddenException('User data access denied');
    }

    this.logger.log({
      timestamp: new Date().toISOString(),
      event: 'USER_ACCESS_GRANTED',
      userId: userId,
      targetUserId: targetUserId,
      endpoint: request.url,
      message: 'User access control passed'
    }, 'User Access Control Success');

    return true;
  }
}
