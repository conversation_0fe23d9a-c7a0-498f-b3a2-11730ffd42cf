import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  UnauthorizedException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { RequestWithUser } from './types';
import { ProfileType } from '../../enum';

@Injectable()
export class ProfileAccessGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    // Check if route is marked public
    const isPublic = this.reflector.getAllAndOverride('isPublic', [
      context.getHandler(),
      context.getClass(),
    ]);
    if (isPublic) return true;

    const requiredProfileTypes = this.reflector.getAllAndOverride<
      ProfileType[]
    >('profileTypes', [context.getHandler(), context.getClass()]);

    const request = context.switchToHttp().getRequest<RequestWithUser>() as any;
    const rawUserData = request?.raw?.user;
    const user = request.user;
    const profileIdSources = [
      request.params['profile_id'],
      request.body?.profile_id,
      request.query['profile_id'],
    ];
    const targetProfileId = parseInt(profileIdSources.find(Boolean) || '', 10);
    if (
      !Array.isArray(user?.profile_ids) ||
      !user?.profile_ids?.length ||
      isNaN(targetProfileId)
    ) {
      throw new UnauthorizedException('Invalid profile access');
    }
    const userProfileIds = user?.profile_ids;
    if (!userProfileIds.includes(targetProfileId)) {
      throw new ForbiddenException('Profile access denied');
    }
    // Validation for required profile types
    if (requiredProfileTypes?.length) {
      if (!rawUserData?.profiles || !rawUserData?.profiles.length) {
        throw new UnauthorizedException('User profiles not found');
      }
      const targetProfileType = rawUserData.profiles.find(
        (profile: { profile_id: number; profile_type: ProfileType }) =>
          profile.profile_id === targetProfileId &&
          requiredProfileTypes.includes(profile.profile_type)
      )?.profile_type;
      if (!targetProfileType) {
        throw new ForbiddenException(
          `Access denied: You need one of the following profile types: ${requiredProfileTypes.join(
            ', '
          )}`
        );
      }
      const hasProfileType = requiredProfileTypes.some(
        (type) => type === targetProfileType
      );
      if (!hasProfileType) {
        throw new ForbiddenException(
          `Access denied: You need one of the following profile types: ${requiredProfileTypes.join(
            ', '
          )}`
        );
      }
    }
    return true;
  }
}
