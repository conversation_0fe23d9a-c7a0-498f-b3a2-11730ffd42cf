import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { ConfigService } from '@nestjs/config';
import * as bcrypt from 'bcrypt';
import { Logger } from 'nestjs-pino';

@Injectable()
export class ApiKeyGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private configService: ConfigService,
    private readonly logger: Logger
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const apiKey =
      request.headers['x-api-key'] || request.headers['authorization'];

    if (!apiKey) {
      this.logger.error({
        timestamp: new Date().toISOString(),
        event: 'API_KEY_MISSING',
        endpoint: request.url,
        method: request.method,
        ip: request.ip || request.connection?.remoteAddress,
        message: 'API key missing in request headers'
      }, 'API Key Authentication Failed');

      throw new UnauthorizedException('Missing API key');
    }

    const expectedAuthApiKey =
      this.configService.getOrThrow<string>('AUTH_API_KEY');
    const isMatch: boolean = await bcrypt.compare(apiKey, expectedAuthApiKey);

    if (!isMatch) {
      this.logger.error({
        timestamp: new Date().toISOString(),
        event: 'API_KEY_INVALID',
        endpoint: request.url,
        method: request.method,
        ip: request.ip || request.connection?.remoteAddress,
        message: 'Invalid API key provided'
      }, 'API Key Authentication Failed');

      throw new UnauthorizedException('Invalid API key');
    }

    this.logger.log({
      timestamp: new Date().toISOString(),
      event: 'API_KEY_VALID',
      endpoint: request.url,
      method: request.method,
      ip: request.ip || request.connection?.remoteAddress,
      message: 'API key authentication successful'
    }, 'API Key Authentication Success');

    return true;
  }
}
