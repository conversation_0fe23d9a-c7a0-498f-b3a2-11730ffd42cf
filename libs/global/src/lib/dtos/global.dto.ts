import { SenderType } from '@core/libs';
import {
  conversations,
  healing_rounds,
  healing_sessions,
  Prisma,
} from '@core/prisma-client';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';
import { JsonValue } from 'type-fest';
import { NotificationStatus } from '../../enum';
export class BaseApiResponseDto<T> {
  @ApiProperty()
  status: string;

  @ApiProperty()
  message: string;

  @ApiPropertyOptional({ type: Boolean })
  error?: boolean;

  @ApiProperty()
  data: T;

  constructor(data: T, message = 'Success', status = 'Success', error = false) {
    this.data = data;
    this.message = message;
    this.status = status;
    this.error = error;
  }
}

export type QueryParamsDto = {
  order?: string;
  skip?: number;
  limit?: number;
};

export class BaseApiPaginatedResponseDto<T> {
  @ApiProperty()
  success?: boolean;

  @ApiProperty()
  status?: string;

  @ApiProperty()
  message: string;

  @ApiPropertyOptional()
  error?: boolean;

  @ApiProperty()
  data: T[];

  @ApiProperty()
  totalCount: number;

  @ApiProperty()
  offset: number;

  @ApiProperty()
  limit: number;

  constructor(
    data: T[],
    totalCount: number,
    offset: number,
    limit: number,
    message = 'Success',
    success = true
  ) {
    this.data = data;
    this.totalCount = totalCount;
    this.offset = offset;
    this.limit = limit;
    this.message = message;
    this.success = success;
  }
}

export class BaseApiNullResponseDto {
  @ApiProperty()
  success: boolean;

  @ApiProperty()
  message: string;

  constructor(success = false, message = 'An error occurred') {
    this.success = success;
    this.message = message;
  }
}

export class NullResponseDto {
  @ApiProperty()
  success: boolean;

  @ApiProperty()
  message: string;

  constructor(success = false, message = 'An error occurred') {
    this.success = success;
    this.message = message;
  }
}

export class ApiResponseDto<T> extends NullResponseDto {
  @ApiProperty()
  public data: T;

  constructor(data: T) {
    super();
    this.data = data;
  }
}

export type SessionDto = {
  session_id: number;
  profile_id?: number;
  subscription_id?: number;
  thread_id?: string;
  session_start_at?: Date;
  session_end_at?: Date;
  status?: string;
  sub_status?: string;
  queue_number?: number;
  queue_start_time?: Date;
  satisfaction_score?: number;
  is_satisfied?: boolean;
  is_follow_up?: boolean;
  created_by?: number;
  created_at?: Date;
  updated_by?: number;
  updated_at?: Date;
  deleted_by?: number;
  deleted_at?: Date;
  is_deleted?: boolean;
  profiles?: ProfileDto;
  subscriptions?: unknown;
  session_ailments?: unknown;
  session_summaries?: unknown;
  conversations?: unknown;
  healing_rounds?: unknown;
  fcm_token?: string;
};

export type SessionResponseDto = {
  session_id: number;
  profile_id: number;
  subscription_id?: number;
  thread_id?: string;
  session_start_at?: Date;
  session_end_at?: Date;
  status?: string;
  sub_status?: string;
  queue_number?: number;
  queue_start_time?: Date;
  satisfaction_score?: number;
  is_satisfied?: boolean;
  is_follow_up?: boolean;
  created_by?: number;
  created_at?: Date;
  updated_by?: number;
  updated_at?: Date;
  deleted_by?: number;
  deleted_at?: Date;
  is_deleted?: boolean;
  profiles?: ProfileDto;
  subscriptions?: unknown;
  session_ailments?: unknown;
  session_summaries?: unknown;
  conversations?: unknown;
  healing_rounds?: healing_rounds[] | number;
  fcm_token?: string;
  device_id?: string;
};

export class UpdateSessionDto {
  profile_id: number | undefined;
  is_follow_up: boolean | undefined;
  status: string | undefined;
  sub_status?: string | undefined;
}

export type HealingRoundRequestDto = {
  profile_id: number;
  session_id: number;
  healer_id: number;
  summary_id?: number;
  round_number: number;
  created_by?: number;
  status?: string;
  max_time?: number;
  check_in_count?: number;
  feedback_start_at?: Date;
  remaining_time?: number;
  assistant_thread_id: string;
};

export class QueueConfirmationDto {
  session_id: number;
  is_confirmed: boolean;

  constructor(session_id: number, is_confirmed: boolean) {
    this.session_id = session_id;
    this.is_confirmed = is_confirmed;
  }
}

export class StartHealingRoundDto {
  constructor(public healer_id: number, public status: string) {}
}

export class FeedbackSummaryDto {
  constructor(
    public session_id: number,
    public content: string,
    public satisfaction_score: number,
    public is_satisfied: boolean,
    public status: string,
    public created_by?: number
  ) {}
}
export class CreateSessionDto {
  constructor(public profile_id: number) {}
}

export class QueryDto {
  constructor(
    public session_id: string,
    public profile_id: string,
    public sortBy: string,
    public order: string,
    public page: number,
    public limit: number,
    public name?: string
  ) {}
}

export class AilmentsDto {
  constructor(
    public session_ailment_id: number,
    public name: string,
    public description?: string,
    public pain_level_before?: number,
    public pain_level_after?: number
  ) {}
}

export class CreateConversationDto {
  constructor(
    public sender_type: string,
    public healing_rounds: healing_rounds,
    public profiles: Record<string, unknown> | Record<string, unknown>[],
    public healing_sessions: SessionDto,
    public message_type: string,
    public content: string
  ) {}
}

export class ProfileDto {
  profile_id?: string | number | null | undefined;
  profile_type?: string | null | undefined;
  user_id?: number | null | undefined;
  first_name?: string | null | undefined;
  last_name?: string | null | undefined;
  relation_to_user?: string | null | undefined;
  gender?: string | null | undefined;
  date_of_birth?: Date | null | undefined;
  profile_picture_file_name?: string | null | undefined;
  phone_number?: string | null | undefined;
  country?: string | null | undefined;
  state?: string | null | undefined;
  city?: string | null | undefined;
  address?: string | null | undefined;
  zip_code?: string | null | undefined;
  is_default?: boolean | null | undefined;
  created_by?: number | null | undefined;
  created_at?: Date | null | undefined;
  updated_by?: number | null | undefined;
  updated_at?: Date | null | undefined;
  deleted_by?: number | null | undefined;
  deleted_at?: Date | null | undefined;
  is_active?: boolean | null | undefined;
  is_deleted?: boolean | null | undefined;
  users?: User[] | any;
}

export class User {
  user_devices?: UserDevice[];
  created_by?: number;
  created_at?: string;
  updated_by?: number;
  updated_at?: string;
  deleted_by?: number;
  deleted_at?: string;
  is_deleted?: boolean;
  profiles?: unknown;
  subscriptions?: boolean;
  user_roles?: string;
  onboarding_status?: boolean;
  discounts?: boolean;
  user_terms_agreement?: boolean;
  user_id: number;
  stripe_customer_id: string;
  username: string;
  email: string;
  password_hash: string;
  last_login_date: Date;
  password_reset_token: string;
  password_reset_token_expiry: Date;
}

export class UserDevice {
  device_id: string;
  is_deleted: boolean;
}

export type IncomingAIMessage = {
  isResponseToPatient: boolean;
  isResponseToHealer: boolean;
  isHealingRoundCompleted: boolean;
};

export class CreateHealingRoundDto {
  constructor(public profile_id: number, public session_id: number) {}
}

export type BaseMessageType = {
  token: string;
  notificationStatus: NotificationStatus;
  sent_at: Date;
  payload: {
    notification: {
      title: string;
      body: string;
    };
    data: {
      status: string;
      data: string;
    };
  };
};

export class Conversation {
  conversation_id!: number; // Primary Key
  profile_id!: number;
  model_id?: number | null;
  session_id!: number;
  round_id?: number | null;
  summary_id?: number | null;
  follow_up_id?: number | null;
  sender_type?: string | null;
  message_type?: string | null;
  content?: string | null;
  file_name?: string | null;
  metadata?: JsonValue; // JSONB type
  created_at?: Date; // Defaults to CURRENT_TIMESTAMP in the DB
  updated_at?: Date | null;
  deleted_at?: Date | null;
  is_deleted?: boolean = false; // Defaults to false in the DB
}

export type InitialSessionDto = {
  session_id: number;
  profile_id: number;
  healer_id?: number | null;
  subscription_id?: number | null;
  thread_id?: string | null;
  session_start_at?: Date;
  session_end_at?: Date;
  status?: string;
  sub_status?: string;
  sub_status_updated_at?: Date | string;
  queue_number?: number;
  queue_start_time?: Date;
  satisfaction_score?: number;
  is_satisfied?: boolean;
  is_follow_up?: boolean;
  created_by?: number;
  created_at?: Date;
  updated_by?: number;
  updated_at?: Date;
  deleted_by?: number;
  deleted_at?: Date;
  is_deleted?: boolean;
  conversations?: Conversation[];
};

export class ConversationProcessEvent {
  constructor(
    public content: string,
    public session: InitialSessionDto,
    public profileId: number,
    public patientConversationId: number
  ) {}
}

export class AiHealingRoundEvent {
  constructor(
    public conversation: conversations,
    public session: healing_sessions,
    public round_id: number,
    public sender_type: string,
    public message_type: string
  ) {}
}

export class AiHealingRoundStartEvent {
  /**
   * Constructs a new AiHealingRoundStartEvent.
   * @param createRoundInput - Input data to create a new healing round.
   * @param user_id - The ID of the user initiating the round.
   * @param skipUserCheck - Whether to skip user validation. Defaults to false.
   */
  constructor(
    public createRoundInput: CreateRoundInput,
    public user_id: number,
    public skipUserCheck = false
  ) {}
}

export class AiHealingRoundCheckInEvent {
  /**
   * Constructs a new AiHealingRoundCheckInEvent.
   * @param updateHealingRoundDto - Data to update the healing round.
   * @param user_id - The ID of the user checking in.
   */
  constructor(public updateHealingRoundDto: Prisma.healing_roundsUpdateInput) {}
}

export class CreateRoundInput {
  constructor(public profile_id: number, public session_id: number) {}
}

export class UpdateHealingRoundDto {
  constructor(
    public profile_id: number,
    public session_id: number,
    public round_id: number
  ) {}
}

export type JWTPayload = {
  sub: number;
  email: string;
  iss: string;
  roles: string[];
  profile_ids: number[];
};

export class CreateSessionEvent {
  session_id: number;
}

export class ConversationDto {
  @ApiProperty({
    example: 1,
    description: 'Profile Id',
    required: true,
  })
  @IsNotEmpty()
  @IsNumber()
  profile_id: number;

  @ApiProperty({
    example: 1,
    description: 'Conversation Id',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  conversation_id?: number;

  @ApiProperty({
    example: 'CONVERSATION.CREATE',
    description: 'CONVERSATION.CREATE',
    required: false,
  })
  @IsOptional()
  @IsString()
  event?: string;

  @ApiProperty({
    example: SenderType?.Patient,
    description: 'sender_type',
    required: false,
  })
  @IsNotEmpty()
  @IsString()
  sender_type: SenderType;

  @ApiProperty({
    example: 'Hi, How are you?',
    description: 'content',
    required: false,
  })
  @IsOptional()
  @IsString()
  content: string;

  @ApiProperty({
    example: 1,
    description: 'Session Id',
    required: true,
  })
  @IsNotEmpty()
  @IsNumber()
  session_id: number;

  @ApiProperty({
    example: 1,
    description: 'Round Id',
    required: true,
  })
  @IsOptional()
  @IsNumber()
  round_id?: number;
}

export interface SessionFollowUp {
  session_follow_up_id: number;
  session_id: number;
  user_id: number;
  follow_up_interval: unknown;
  follow_up_at?: Date;
  created_at: Date;
  updated_at?: Date;
  is_deleted?: boolean;
  status: unknown;
}
