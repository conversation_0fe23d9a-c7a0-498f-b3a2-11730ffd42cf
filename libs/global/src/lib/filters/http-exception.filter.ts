import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Injectable,
} from '@nestjs/common';
import { FastifyReply, FastifyRequest } from 'fastify';
import { ResponseDto } from '../dtos/response.dto';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { Logger } from 'nestjs-pino';

export interface ExceptionLogData {
  timestamp: string;
  method: string;
  endpoint: string;
  statusCode: number;
  success: boolean;
  userContext: {
    userId: number | null;
    username: string | null;
    email: string | null;
    sessionId: string | null;
  };
  requestInfo: {
    userAgent: string | null;
    ip: string | null;
  };
  errorDetails: {
    name: string;
    message: string;
    statusCode: number;
  };
}

@Catch(HttpException)
@Injectable()
export class HttpExceptionFilter implements ExceptionFilter {
  constructor(private readonly logger: Logger) {}

  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<FastifyReply>();
    const request = ctx.getRequest<FastifyRequest>();

    const status = exception.getStatus
      ? exception.getStatus()
      : HttpStatus.INTERNAL_SERVER_ERROR;
    const type = exception.name || 'InternalServerErrorException';
    let message =
      typeof exception.getResponse() === 'object'
        ? (exception.getResponse() as any).message || exception.message
        : exception.message;

    const description = message;

    if (exception instanceof PrismaClientKnownRequestError) {
      message = 'Database operation failed. Please check your input.';
    }

    const error = {
      type,
      description,
      timestamp: new Date().toISOString(),
      path: request.url,
    };
    // Log the exception with structured format
    this.logException(request, status, type, message);

    response
      .status(status)
      .send(new ResponseDto(status, false, message, null, error));
  }

  private logException(
    request: any,
    status: number,
    type: string,
    message: string
  ): void {
    // Extract user information from request
    const user = request.user || request.raw?.user;

    // Extract session ID from headers
    const sessionId = this.extractSessionId(request);

    // For unauthenticated endpoints, try to extract email from request body
    const emailFromBody = this.extractEmailFromBody(request);

    const logData: ExceptionLogData = {
      timestamp: new Date().toISOString(),
      method: request.method,
      endpoint: this.sanitizeEndpoint(request.url),
      statusCode: status,
      success: false,
      userContext: {
        userId: user?.user_id || null,
        username: user?.username || user?.email || emailFromBody || null,
        email: user?.email || emailFromBody || null,
        sessionId: sessionId,
      },
      requestInfo: {
        userAgent: request.headers['user-agent'] || null,
        ip: this.extractClientIp(request),
      },
      errorDetails: {
        name: type,
        message: this.sanitizeErrorMessage(message),
        statusCode: status,
      },
    };
    this.logger.error(logData, 'API_REQUEST_FAILED');
  }

  private extractSessionId(request: any): string | null {
    return (
      request.headers['x-session-id'] ||
      request.headers['x-request-id'] ||
      request.id ||
      null
    );
  }

  private extractEmailFromBody(request: any): string | null {
    // For login and registration endpoints, extract email/username from body
    const isAuthEndpoint = request.url?.includes('/auth/');
    if (isAuthEndpoint && request.body) {
      return request.body.username || request.body.email || null;
    }
    return null;
  }

  private extractClientIp(request: any): string | null {
    return (
      request.ip ||
      request.connection?.remoteAddress ||
      request.socket?.remoteAddress ||
      request.headers['x-forwarded-for']?.split(',')[0]?.trim() ||
      request.headers['x-real-ip'] ||
      null
    );
  }

  private sanitizeEndpoint(url: string): string {
    // Remove query parameters that might contain sensitive data
    if (!url) return '';

    const urlParts = url.split('?');
    return urlParts[0];
  }

  private sanitizeErrorMessage(message: string): string {
    if (!message) return 'Unknown error';

    // Remove potential sensitive information from error messages
    const sensitivePatterns = [
      /password/gi,
      /token/gi,
      /secret/gi,
      /key/gi,
      /authorization/gi,
    ];

    let sanitized = message;
    sensitivePatterns.forEach((pattern) => {
      sanitized = sanitized.replace(pattern, '[REDACTED]');
    });
    return sanitized;
  }
}
