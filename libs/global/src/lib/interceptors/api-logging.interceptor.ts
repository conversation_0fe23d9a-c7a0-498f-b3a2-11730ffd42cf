import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { Logger } from 'nestjs-pino';
import { throwError } from 'rxjs';

export interface ApiLogData {
  timestamp: string;
  method: string;
  endpoint: string;
  statusCode: number;
  success: boolean;
  duration: string;
  userContext: {
    userId: number | null;
    username: string | null;
    email: string | null;
    sessionId: string | null;
  };
  requestInfo: {
    userAgent: string | null;
    ip: string | null;
  };
  error?: {
    name: string;
    message: string;
    statusCode: number;
  };
}

@Injectable()
export class ApiLoggingInterceptor implements NestInterceptor {
  constructor(private readonly logger: Logger) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();
    const startTime = Date.now();

    return next.handle().pipe(
      tap((data) => {
        // Log successful requests
        this.logApiRequest(request, response, startTime, true);
      }),
      catchError((error) => {
        // Log failed requests - this should catch all errors
        console.log(
          'ApiLoggingInterceptor caught error:',
          error?.message || 'Unknown error'
        ); // Debug log

        // Ensure we have error details for logging
        const errorToLog = {
          name: error?.name || error?.constructor?.name || 'UnknownError',
          message: error?.message || 'Unknown error occurred',
          status: error?.status || error?.statusCode || 500,
          statusCode: error?.status || error?.statusCode || 500,
          stack: error?.stack,
        };

        this.logApiRequest(request, response, startTime, false, errorToLog);
        return throwError(() => error);
      })
    );
  }

  private logApiRequest(
    request: any,
    response: any,
    startTime: number,
    success: boolean,
    error?: any
  ): void {
    const endTime = Date.now();
    const duration = endTime - startTime;

    // Extract user information from request
    const user = request.user || request.raw?.user;

    // Extract session ID from headers or generate request ID
    const sessionId = this.extractSessionId(request);

    // For unauthenticated endpoints, try to extract email from request body
    const emailFromBody = this.extractEmailFromBody(request);

    const logData: ApiLogData = {
      timestamp: new Date().toISOString(),
      method: request.method,
      endpoint: this.sanitizeEndpoint(request.url),
      statusCode:
        response.statusCode || error?.status || error?.statusCode || 500,
      success,
      duration: `${duration}ms`,
      userContext: {
        userId: user?.user_id || null,
        username: user?.username || user?.email || emailFromBody || null,
        email: user?.email || emailFromBody || null,
        sessionId: sessionId,
      },
      requestInfo: {
        userAgent: request.headers['user-agent'] || null,
        ip: this.extractClientIp(request),
      },
    };

    // Add error details for failed requests
    if (!success && error) {
      logData.error = {
        name: error.name || 'UnknownError',
        message: this.sanitizeErrorMessage(error.message),
        statusCode: error.status || error.statusCode || 500,
      };
    }

    // Log as single line JSON string for Azure
    const logMessage = JSON.stringify(logData);

    // Log at appropriate level
    if (success) {
      this.logger.log(logMessage, 'API_REQUEST_SUCCESS');
    } else {
      this.logger.error(logMessage, 'API_REQUEST_FAILED');
    }
  }

  private extractSessionId(request: any): string | null {
    return (
      request.headers['x-session-id'] ||
      request.headers['x-request-id'] ||
      request.id ||
      null
    );
  }

  private extractEmailFromBody(request: any): string | null {
    // For login and registration endpoints, extract email/username from body
    const isAuthEndpoint = request.url?.includes('/auth/');
    if (isAuthEndpoint && request.body) {
      return request.body.username || request.body.email || null;
    }
    return null;
  }

  private extractClientIp(request: any): string | null {
    return (
      request.ip ||
      request.connection?.remoteAddress ||
      request.socket?.remoteAddress ||
      request.headers['x-forwarded-for']?.split(',')[0]?.trim() ||
      request.headers['x-real-ip'] ||
      null
    );
  }

  private sanitizeEndpoint(url: string): string {
    // Remove query parameters that might contain sensitive data
    if (!url) return '';

    const urlParts = url.split('?');
    return urlParts[0];
  }

  private sanitizeErrorMessage(message: string): string {
    if (!message) return 'Unknown error';

    // Remove potential sensitive information from error messages
    const sensitivePatterns = [
      /password/gi,
      /token/gi,
      /secret/gi,
      /key/gi,
      /authorization/gi,
    ];

    let sanitized = message;
    sensitivePatterns.forEach((pattern) => {
      sanitized = sanitized.replace(pattern, '[REDACTED]');
    });

    return sanitized;
  }
}
