// src/rabbitmq/rabbitmq.service.ts

import { Injectable, OnModuleInit } from '@nestjs/common';
import { Channel, connect, ChannelModel } from 'amqplib';
import { RABBITMQ_EXCHANGE, RABBITMQ_QUEUES } from '../config/rabbitmq.config';
import { ConfigService } from '@nestjs/config';
import { Logger } from 'nestjs-pino';

@Injectable()
export class QueueService implements OnModuleInit {
  private connection!: ChannelModel;
  private channel!: Channel;
  private rabbitmqUrl: string;
  constructor(
    private readonly configService: ConfigService,
    private readonly logger: Logger
  ) {
    this.rabbitmqUrl = this.configService.getOrThrow<string>('RABBITMQ_URL');
  }

  async onModuleInit() {
    await this.connect();
    await this.setupExchangeAndQueues();
  }

  private async connect() {
    this.connection = await connect(this.rabbitmqUrl);
    this.channel = await this.connection.createChannel();
  }

  private async setupExchangeAndQueues() {
    await this.channel.assertExchange(RABBITMQ_EXCHANGE, 'direct', {
      durable: true,
    });

    for (const queueConfig of RABBITMQ_QUEUES) {
      await this.channel.assertQueue(
        queueConfig.name,
        queueConfig.options ?? { durable: true }
      );
      await this.channel.bindQueue(
        queueConfig.name,
        RABBITMQ_EXCHANGE,
        queueConfig.routingKey
      );
    }
    this.logger.log('RabbitMQ setup completed');
  }

  async close() {
    await this.channel.close();
    await this.connection.close();
  }
}
