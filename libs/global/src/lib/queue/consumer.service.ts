import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { Channel, connect, ChannelModel, ConsumeMessage } from 'amqplib';
import {
  HEALER_REQUESTS_QUEUE,
  PLACEBO_REQUESTS_QUEUE,
} from '../config/rabbitmq.config';
import { ConfigService } from '@nestjs/config';
import { LibSessionService, PlaceboActionType } from '@core/libs';
import { LibPlaceboService } from '../services/placebo.service';
import { Logger } from 'nestjs-pino';

@Injectable()
export class ConsumerService implements OnModuleInit, OnModuleDestroy {
  private connection!: ChannelModel;
  private channel!: Channel;
  private rabbitmqUrl: string;
  private sessionOfferTimeout: number;

  constructor(
    private readonly configService: ConfigService,
    private readonly libSessionService: LibSessionService,
    private readonly libPlaceboService: LibPlaceboService,
    private readonly logger: Logger
  ) {
    this.rabbitmqUrl = this.configService.getOrThrow<string>('RABBITMQ_URL');
    this.sessionOfferTimeout =
      parseInt('' + this.configService.get('SESSION_OFFER_TIMEOUT')) || 120000;
  }

  async onModuleInit() {
    await this.connectToRabbitMQ();
  }

  private async connectToRabbitMQ() {
    while (true) {
      try {
        this.connection = await connect(this.rabbitmqUrl);
        // Bind listener for future connection loss
        this.connection.on('close', this.handleConnectionClose.bind(this));
        this.channel = await this.connection.createChannel();
        await this.channel.prefetch(1);
        this.channel.consume(HEALER_REQUESTS_QUEUE.name, async (msg) => {
          if (msg) {
            await this.processHealerMessage(msg, this.channel);
          }
        });
        this.channel.consume(PLACEBO_REQUESTS_QUEUE.name, async (msg) => {
          if (msg) {
            await this.processPlaceboMessage(msg, this.channel);
          }
        });
        this.logger.log('Consumers are connected to RabbitMQ');
        break;
      } catch (err) {
        this.logger.error('Connect attempt failed: ', err);
        await new Promise((resolve) => setTimeout(resolve, 3000));
      }
    }
  }

  private async handleConnectionClose() {
    this.logger.warn('RabbitMQ connection closed, attempting to reconnect...');
    await this.connectToRabbitMQ();
  }

  async processHealerMessage(msg: ConsumeMessage, channel: Channel) {
    const message = msg.content?.toString();
    const parsedMessage = JSON.parse(message);
    const startTime = new Date();
    let currentTime = new Date();
    let isFirstTry = true;
    let succeeded = false;
    let retryCount = 0;

    // Log method entry
    this.logger.log(
      {
        event: 'HEALER_MESSAGE_PROCESS_START',
        profile_id: (parsedMessage as { profile_id: number })?.profile_id,
        message: 'Starting healer message processing',
      },
      'Healer Message Processing'
    );

    // Retry logic for 120 seconds
    while (
      currentTime.getTime() - startTime.getTime() <
      this.sessionOfferTimeout
    ) {
      try {
        if (!isFirstTry) {
          retryCount++;
          this.logger.log(
            {
              event: 'HEALER_MESSAGE_PROCESS_RETRY',
              profile_id: (parsedMessage as { profile_id: number })?.profile_id,
              retry_count: retryCount,
              elapsed_time: currentTime.getTime() - startTime.getTime(),
              message: 'Retrying healer message processing',
            },
            'Healer Message Retry'
          );
          await this.sleep(5000); // Add 5 second delay only for retries
        }
        isFirstTry = false;
        await this.libSessionService.processHealerRequest(
          parsedMessage as { profile_id: number }
        );
        succeeded = true;
        channel.ack(msg);

        this.logger.log(
          {
            event: 'HEALER_MESSAGE_PROCESS_SUCCESS',
            profile_id: (parsedMessage as { profile_id: number })?.profile_id,
            retry_count: retryCount,
            total_time: currentTime.getTime() - startTime.getTime(),
            message: 'Healer message processed successfully',
          },
          'Healer Message Success'
        );
        break; // Exit the loop if successful
      } catch (err) {
        this.logger.error(
          {
            event: 'HEALER_MESSAGE_PROCESS_ERROR',
            profile_id: (parsedMessage as { profile_id: number })?.profile_id,
            retry_count: retryCount,
            error_message: err instanceof Error ? err.message : 'unknown error',
            elapsed_time: currentTime.getTime() - startTime.getTime(),
            message: 'Failed to process healer message',
          },
          'Healer Message Error'
        );
        currentTime = new Date();
      }
    }
    if (!succeeded) {
      this.logger.error(
        {
          event: 'HEALER_MESSAGE_PROCESS_TIMEOUT',
          profile_id: (parsedMessage as { profile_id: number })?.profile_id,
          retry_count: retryCount,
          total_time: currentTime.getTime() - startTime.getTime(),
          timeout_limit: this.sessionOfferTimeout,
          message: 'Healer message processing timed out after retries',
        },
        'Healer Message Timeout'
      );
      channel.ack(msg);
    }
  }

  async processPlaceboMessage(msg: ConsumeMessage, channel: Channel) {
    const message = msg.content?.toString();
    const parsedMessage = JSON.parse(message);
    const startTime = new Date();
    let currentTime = new Date();
    let isFirstTry = true;
    let succeeded = false;
    let retryCount = 0;

    // Log method entry
    this.logger.log(
      {
        event: 'PLACEBO_MESSAGE_PROCESS_START',
        session_id: (parsedMessage as { session_id: number })?.session_id,
        round_id: (parsedMessage as { round_id: number })?.round_id,
        action: (parsedMessage as { action: PlaceboActionType })?.action,
        message: 'Starting placebo message processing',
      },
      'Placebo Message Processing'
    );

    // Retry logic for 120 seconds
    while (
      currentTime.getTime() - startTime.getTime() <
      this.sessionOfferTimeout
    ) {
      try {
        if (!isFirstTry) {
          retryCount++;
          this.logger.log(
            {
              event: 'PLACEBO_MESSAGE_PROCESS_RETRY',
              session_id: (parsedMessage as { session_id: number })?.session_id,
              round_id: (parsedMessage as { round_id: number })?.round_id,
              action: (parsedMessage as { action: PlaceboActionType })?.action,
              retry_count: retryCount,
              elapsed_time: currentTime.getTime() - startTime.getTime(),
              message: 'Retrying placebo message processing',
            },
            'Placebo Message Retry'
          );
          await this.sleep(5000); // Add 5 second delay only for retries
        }
        isFirstTry = false;
        await this.libPlaceboService.processPlaceboRequest(
          parsedMessage as {
            session_id: number;
            round_id: number;
            action: PlaceboActionType;
          }
        );
        succeeded = true;
        channel.ack(msg);

        this.logger.log(
          {
            event: 'PLACEBO_MESSAGE_PROCESS_SUCCESS',
            session_id: (parsedMessage as { session_id: number })?.session_id,
            round_id: (parsedMessage as { round_id: number })?.round_id,
            action: (parsedMessage as { action: PlaceboActionType })?.action,
            retry_count: retryCount,
            total_time: currentTime.getTime() - startTime.getTime(),
            message: 'Placebo message processed successfully',
          },
          'Placebo Message Success'
        );
        break; // Exit the loop if successful
      } catch (err) {
        this.logger.error(
          {
            event: 'PLACEBO_MESSAGE_PROCESS_ERROR',
            session_id: (parsedMessage as { session_id: number })?.session_id,
            round_id: (parsedMessage as { round_id: number })?.round_id,
            action: (parsedMessage as { action: PlaceboActionType })?.action,
            retry_count: retryCount,
            error_message: err instanceof Error ? err.message : 'unknown error',
            elapsed_time: currentTime.getTime() - startTime.getTime(),
            message: 'Failed to process placebo message',
          },
          'Placebo Message Error'
        );
        currentTime = new Date();
      }
    }
    if (!succeeded) {
      this.logger.error(
        {
          event: 'PLACEBO_MESSAGE_PROCESS_TIMEOUT',
          session_id: (parsedMessage as { session_id: number })?.session_id,
          round_id: (parsedMessage as { round_id: number })?.round_id,
          action: (parsedMessage as { action: PlaceboActionType })?.action,
          retry_count: retryCount,
          total_time: currentTime.getTime() - startTime.getTime(),
          timeout_limit: this.sessionOfferTimeout,
          message: 'Placebo message processing timed out after retries',
        },
        'Placebo Message Timeout'
      );
      channel.ack(msg);
    }
  }

  async sleep(ms: number) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  async onModuleDestroy() {
    await this.channel?.close();
    await this.connection?.close();
    this.logger.log('Consumer connection closed');
  }
}
