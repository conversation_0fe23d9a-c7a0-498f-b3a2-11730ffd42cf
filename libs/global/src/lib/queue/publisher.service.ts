import { Injectable, OnModuleInit } from '@nestjs/common';
import { Channel, connect, ChannelModel, Options } from 'amqplib';
import { RABBITMQ_EXCHANGE } from '../config/rabbitmq.config';
import { ConfigService } from '@nestjs/config';
import { Logger } from 'nestjs-pino';

@Injectable()
export class PublisherService implements OnModuleInit {
  private connection!: ChannelModel;
  private channel!: Channel;
  private rabbitmqUrl: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly logger: Logger
  ) {
    this.rabbitmqUrl = this.configService.getOrThrow<string>('RABBITMQ_URL');
  }
  async onModuleInit() {
    const connection = await connect(this.rabbitmqUrl);
    this.channel = await connection.createChannel();
    this.logger.log('Publisher connected to RabbitMQ');
  }

  async onModuleDestroy() {
    await this.channel?.close();
    await this.connection?.close();
    this.logger.log('Publisher connection closed');
  }

  getRandomDelayInMs(minMs: number, maxMs: number): number {
    return Math.floor(Math.random() * (maxMs - minMs) + minMs);
  }

  async sendMessage({
    message,
    queueRoutingKey,
    maxRetrCount = 5,
    expiration,
  }: {
    message: any;
    queueRoutingKey: string;
    maxRetrCount?: number;
    expiration?: number;
  }): Promise<boolean> {
    for (let attempt = 1; attempt <= maxRetrCount; attempt++) {
      try {
        if (!this.channel) {
          throw new Error('Channel not initialized');
        }
        const options: Options.Publish = {
          persistent: true, // Message will survive broker restart
          contentType: 'application/json',
          timestamp: Date.now(),
        };
        if (expiration) {
          options.expiration = `${expiration}`;
        }
        const messageBuffer = Buffer.from(JSON.stringify(message));
        return this.channel.publish(
          RABBITMQ_EXCHANGE,
          queueRoutingKey,
          messageBuffer,
          options
        );
      } catch (error: any) {
        this.logger.warn(`Attempt ${attempt} failed. Retrying...`);
        if (error?.message === 'Channel closed') {
          this.logger.log(
            'Channel closed detected. Attempting to reconnect...'
          );
        }
        if (attempt === maxRetrCount) {
          this.logger.warn(`Maximum retry count exceeded!`);
        } else {
          try {
            // add 3 seconds delay before retrying
            await new Promise((resolve) => setTimeout(resolve, 3000));
            const connection = await connect(this.rabbitmqUrl);
            this.channel = await connection.createChannel();
            this.logger.log('Publisher reconnected to RabbitMQ!');
          } catch (err: any) {
            this.logger.error(
              `Failed to reconnect publisher to RabbitMQ: ${err.message}`
            );
          }
        }
      }
    }
    throw new Error('All retry attempts failed');
  }
}
