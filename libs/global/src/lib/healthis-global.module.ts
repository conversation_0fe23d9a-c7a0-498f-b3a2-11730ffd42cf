import { Global, Module } from '@nestjs/common';
import { PrismaClientModule } from '@core/prisma-client';
import { LibNotificationsModule } from '@core_be/notifications';
import { LibDataAccessModule } from '@core_be/data-access';
import { ConfigService } from '@nestjs/config';
import { profile } from 'console';
import { LibProfileService } from './services';
import { LibSessionService } from './services/session.service';

@Global()
@Module({
  imports: [PrismaClientModule, LibNotificationsModule, LibDataAccessModule],
  controllers: [],
  providers: [ConfigService, LibProfileService],
  exports: [LibProfileService],
})
export class LibGlobalModule {}
