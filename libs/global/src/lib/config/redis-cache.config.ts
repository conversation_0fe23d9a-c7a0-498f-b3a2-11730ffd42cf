

import { CacheModuleAsyncOptions } from '@nestjs/cache-manager';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { redisStore } from 'cache-manager-redis-store';


export const cacheConfigurationOptions:CacheModuleAsyncOptions = {
    imports: [ConfigModule],
    inject: [ConfigService],
    useFactory: async (configService: ConfigService) => {
        const store = await redisStore({
            socket:{
                host: configService.getOrThrow('REDIS_HOST'),
                port: configService.getOrThrow('REDIS_PORT'),
            },
        });

        return {
            store,
            ttl: configService.getOrThrow('REDIS_TTL'),
        };
    },
};

