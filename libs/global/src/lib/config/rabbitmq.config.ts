export const RABBITMQ_EXCHANGE = 'healthis.exchange';

export const HEALER_REQUESTS_QUEUE = {
  name: 'healer.requests.queue',
  routingKey: 'healer.requests',
  options: {
    durable: true,
  },
};

export const PLACEBO_REQUESTS_QUEUE = {
  name: 'placebo.requests.queue',
  routingKey: 'placebo.requests',
  options: {
    durable: true,
  },
};

export const PLACEBO_REQUESTS_DELAY_QUEUE = {
  name: 'placebo.requests.delay.queue',
  routingKey: 'placebo.requests.delay',
  options: {
    durable: true,
    arguments: {
      // When a message expires in this queue, it is dead-lettered to the main queue
      'x-dead-letter-exchange': RABBITMQ_EXCHANGE,
      'x-dead-letter-routing-key': PLACEBO_REQUESTS_QUEUE.routingKey,
    },
  },
};

export const RABBITMQ_QUEUES = [
  HEALER_REQUESTS_QUEUE,
  PLACEBO_REQUESTS_QUEUE,
  PLACEBO_REQUESTS_DELAY_QUEUE,
];
