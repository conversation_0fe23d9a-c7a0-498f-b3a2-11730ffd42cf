import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import fastifyBasicAuth from '@fastify/basic-auth';
import { INestApplication } from '@nestjs/common';
import { FastifyAdapter } from '@nestjs/platform-fastify';
import { ConfigService } from '@nestjs/config';

export async function setupSwagger(
  app: INestApplication,
  fastifyAdapter: FastifyAdapter,
  title: string,
  description: string,
  docPath: string
) {
  const options = new DocumentBuilder()
    .setTitle(title)
    .setDescription(description)
    .setVersion('1.0')
    .addBearerAuth()
    .build();

  const swaggerUsername = app.get(ConfigService).getOrThrow('SWAGGER_USER');
  const swaggerPassword = app.get(ConfigService).getOrThrow('SWAGGER_PASSWORD');
  const fastifyInstance = fastifyAdapter.getInstance();

  await fastifyInstance.register(fastifyBasicAuth as any, {
    validate: (username:string, password:string) => {
      if (username === swaggerUsername && password === swaggerPassword) {
        return;
      }
      throw new Error('Unauthorized');
    },
    authenticate: true,
  });

  fastifyInstance.addHook('onRequest', async (req, reply) => {
    if (req.url.startsWith(`/${docPath}`) || req.url.startsWith(`/${docPath}-json`)) {
      try {
        await (fastifyInstance as any).basicAuth(req, reply);
      } catch (err) {
        reply.code(401).send({ error: 'Unauthorized' });
      }
    }
  });

  const document = SwaggerModule.createDocument(app, options);
  SwaggerModule.setup(docPath, app, document);
}
