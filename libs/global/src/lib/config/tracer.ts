import tracer, { Tracer } from 'dd-trace';

interface Plugins {
  "aerospike": tracer.plugins.aerospike;
  "amqp10": tracer.plugins.amqp10;
  "amqplib": tracer.plugins.amqplib;
  "apollo": tracer.plugins.apollo;
  "avsc": tracer.plugins.avsc;
  "aws-sdk": tracer.plugins.aws_sdk;
  "azure-functions": tracer.plugins.azure_functions;
  "bunyan": tracer.plugins.bunyan;
  "cassandra-driver": tracer.plugins.cassandra_driver;
  "child_process": tracer.plugins.child_process;
  "connect": tracer.plugins.connect;
  "couchbase": tracer.plugins.couchbase;
  "cucumber": tracer.plugins.cucumber;
  "cypress": tracer.plugins.cypress;
  "dns": tracer.plugins.dns;
  "elasticsearch": tracer.plugins.elasticsearch;
  "express": tracer.plugins.express;
  "fastify": tracer.plugins.fastify;
  "fetch": tracer.plugins.fetch;
  "generic-pool": tracer.plugins.generic_pool;
  "google-cloud-pubsub": tracer.plugins.google_cloud_pubsub;
  "graphql": tracer.plugins.graphql;
  "grpc": tracer.plugins.grpc;
  "hapi": tracer.plugins.hapi;
  "http": tracer.plugins.http;
  "http2": tracer.plugins.http2;
  "ioredis": tracer.plugins.ioredis;
  "jest": tracer.plugins.jest;
  "kafkajs": tracer.plugins.kafkajs
  "knex": tracer.plugins.knex;
  "koa": tracer.plugins.koa;
  "langchain": tracer.plugins.langchain;
  "mariadb": tracer.plugins.mariadb;
  "memcached": tracer.plugins.memcached;
  "microgateway-core": tracer.plugins.microgateway_core;
  "mocha": tracer.plugins.mocha;
  "moleculer": tracer.plugins.moleculer;
  "mongodb-core": tracer.plugins.mongodb_core;
  "mongoose": tracer.plugins.mongoose;
  "mysql": tracer.plugins.mysql;
  "mysql2": tracer.plugins.mysql2;
  "net": tracer.plugins.net;
  "next": tracer.plugins.next;
  "openai": tracer.plugins.openai;
  "opensearch": tracer.plugins.opensearch;
  "oracledb": tracer.plugins.oracledb;
  "paperplane": tracer.plugins.paperplane;
  "playwright": tracer.plugins.playwright;
  "pg": tracer.plugins.pg;
  "pino": tracer.plugins.pino;
  "protobufjs": tracer.plugins.protobufjs;
  "redis": tracer.plugins.redis;
  "restify": tracer.plugins.restify;
  "rhea": tracer.plugins.rhea;
  "router": tracer.plugins.router;
  "selenium": tracer.plugins.selenium;
  "sharedb": tracer.plugins.sharedb;
  "tedious": tracer.plugins.tedious;
  "undici": tracer.plugins.undici;
  "vitest": tracer.plugins.vitest;
  "winston": tracer.plugins.winston;
}

class DatadogTracer {
  private instance: Tracer;
  constructor(config: tracer.TracerOptions) {
    this.instance = tracer.init(config);
  }
  use<P extends keyof Plugins>(plugin: keyof Plugins, config?: Plugins[P] | boolean){
    return this.instance.use(plugin, config);
  }
  get() {
    return this.instance;
  }
}

export { 
  DatadogTracer
};