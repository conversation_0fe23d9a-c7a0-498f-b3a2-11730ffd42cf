export enum ProfileType {
  HEALER = 'Healer',
  PATIENT = 'Patient',
}

export enum WaitListUserType {
  HEALER = 'Healer',
  PATIENT = 'Patient',
  SUBSCRIBER = 'Subscriber',
}

export enum SessionDeclineTriggerSourceType {
  USER = 'USER',
  TIMER = 'TIMER',
}

export enum HealerStatusType {
  PATIENT_REQUESTED = 'PATIENT_REQUESTED',
  WAITING_PATIENT_CONFIRMATION = 'WAITING_PATIENT_CONFIRMATION',
  PATIENT_CONFIRMED = 'PATIENT_CONFIRMED',
  NO_AVAILABLE_PATIENTS = 'NO_AVAILABLE_PATIENTS',
}

export enum PushNotificationBodyType {
  SESSION_OFFER = 'A test energy healer is ready for your session. Tap to start. If you’re not ready, let us know so we can reserve your spot at the front of the queue.',
  SESSION_MISSED = 'You missed the session window. You didn’t respond in time, so we’ve paused matching. Your place in the queue is saved.',
  MARK_UNAVAILABLE = 'You’ve been marked as unavailable to prevent missed sessions. You can set yourself as ready at any time in the app.',
}

export enum PushNotificationTitleType {
  SESSION_OFFER = 'Session starting now!',
  SESSION_MISSED = 'You missed the session offer!',
  MARK_UNAVAILABLE = 'Session availability update',
}

export enum SessionType {
  PLACEBO = 'Placebo',
  HEALER = 'Healer',
}

export enum PlaceboActionType {
  START_HEALING = 'START_HEALING',
  TRIGGER_CHECK_IN = 'TRIGGER_CHECK_IN',
}

export enum AssistantType {
  SYSTEM = 'System',
  ASSISTANT = 'Assistant',
}

export enum RelationToUser {
  Self = 'Self',
  Friend = 'Friend',
  Family = 'Family',
  Colleague = 'Colleague',
  Partner = 'Partner',
  Guest = 'Guest',
  Other = 'Other',
}

export enum Role {
  Admin = 'Admin',
  Healer = 'Healer',
  Patient = 'Patient',
}

export enum SurveryType {
  Onboarding = 'ONBOARDING',
}

export enum AI_MESSAGE_INCOMING_TRIGGER {
  CONVERSATION_COMPLETED = '__CONVERSATION_COMPLETED__',
  HEALING_SESSION_START = '__HEALING_SESSION_START__',
  HEALING_ROUND_MESSAGE_TO_HEALER = '__TO_HEALER__:',
  HEALING_ROUND_MESSAGE_TO_PATIENT = '__TO_PATIENT__:',
  HEALING_ROUND_COMPLETED = '__HEALING_ROUND_COMPLETED__',
}

export enum AI_MESSAGE_OUTGOING_TRIGGER {
  HEALING_ROUND_SYSTEM_INPUT = '__SYSTEM_INPUT__:',
  HEALING_ROUND_START = '__START_NEW_HEALING_ROUND__',
  HEALING_ROUND_START_FEEDBACK = '__GATHER_ROUND_FEEDBACK__',
  HEALING_ROUND_MESSAGE_FROM_PATIENT = '__PATIENT_RESPONSE__:',
  HEALING_ROUND_MESSAGE_FROM_HEALER = '__HEALER_RESPONSE__:',
  REVIEW_CASE_SUMMARY = '__REVIEW_CASE_SUMMARY__',
}

export enum SenderType {
  Patient = 'Patient',
  Healer = 'Healer',
  AI = 'AI',
}

export enum MessageType {
  Text = 'Text',
  Audio = 'Audio',
}

export enum OnboardingStatus {
  PENDING = 'PENDING',
  COMPLETED = 'COMPLETED',
}

export enum SessionStatus {
  INTAKE = 'INTAKE',
  IN_QUEUE = 'IN_QUEUE',
  HEALING_ROUND = 'HEALING_ROUND',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  ABANDONED = 'ABANDONED',
  ERROR = 'ERROR',
}

export enum HealingRoundStatus {
  IN_PROGRESS = 'IN_PROGRESS',
  FEEDBACK_REQUIRED = 'FEEDBACK_REQUIRED',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
}

// export enum QueueSubStatus {
//   ENTER_IN_QUEUE = 'ENTER_IN_QUEUE',
//   IN_FRONT_OF_QUEUE = 'IN_FRONT_OF_QUEUE',
//   CONFIRMATION_REQUIRED = 'CONFIRMATION_REQUIRED',
//   AI_CONFIRMATION_REQUIRED = 'AI_CONFIRMATION_REQUIRED',
//   AVAILABLE = 'AVAILABLE',
// }

export enum HealingRoundSubStatus {
  HEALER_ASSIGNED = 'HEALER_ASSIGNED',
  IN_PROGRESS = 'IN_PROGRESS',
  FEEDBACK_REQUIRED = 'FEEDBACK_REQUIRED',
  PROCEED_CONFIRMATION = 'PROCEED_CONFIRMATION',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
}

export enum AbandonmentSubStatus {
  HEALER_ABANDONED = 'HEALER_ABANDONED',
  PATIENT_ABANDONED = 'PATIENT_ABANDONED',
  TIMEOUT = 'TIMEOUT',
}

export enum ConversationType {
  INTAKE = 'INTAKE',
  REPORT_SYMPTOMS = 'REPORT_SYMPTOMS',
  HEALING_ROUND_CHECK_IN = 'HEALING_ROUND_CHECK_IN',
}

export enum ConversationStatus {
  COMPLETED = 'COMPLETED',
  IN_PROGRESS = 'IN_PROGRESS',
}

export enum SessionSubStatusType {
  // ENTER_IN_QUEUE = 'ENTER_IN_QUEUE',
  // IN_FRONT_OF_QUEUE = 'IN_FRONT_OF_QUEUE',
  SESSION_CONFIRMED = 'SESSION_CONFIRMED',
  CONFIRMATION_REQUIRED = 'CONFIRMATION_REQUIRED',
  SESSION_MISSED = 'SESSION_MISSED',
  AI_CONFIRMATION_REQUIRED = 'AI_CONFIRMATION_REQUIRED',
  AVAILABLE = 'AVAILABLE',
  NOT_AVAILABLE = 'NOT_AVAILABLE',

  HEALER_ASSIGNED = 'HEALER_ASSIGNED',
  IN_PROGRESS = 'IN_PROGRESS',
  FEEDBACK_REQUIRED = 'FEEDBACK_REQUIRED',
  PROCEED_CONFIRMATION = 'PROCEED_CONFIRMATION',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  ERROR_OCCURRED = 'ERROR_OCCURRED',

  HEALER_ABANDONED = 'HEALER_ABANDONED',
  PATIENT_ABANDONED = 'PATIENT_ABANDONED',
  TIMEOUT = 'TIMEOUT',
}

export enum NotificationStatus {
  DRAFT = 'DRAFT',
  SENT = 'SENT',
  ERROR = 'ERROR',
  READY_TO_SEND = 'READY_TO_SEND',
  SCHEDULED = 'SCHEDULED',
  FAILED = 'FAILED',
  READ = 'READ',
  UNREAD = 'UNREAD',
}

export enum ProfileStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

export enum NotificationType {
  PUSH = 'PUSH',
  EMAIL = 'EMAIL',
  SMS = 'SMS',
}

export enum DeviceType {
  ANDROID = 'Android',
  IOS = 'iOS',
}

export enum NotificationPriority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
}

export enum HEALING_ROUND_LISTEN {
  HEALING_ROUND_START = 'HEALING_ROUND_START',
  HEALING_ROUND_CHECK_IN = 'HEALING_ROUND_CHECK_IN',
  DELIMITER = 'HEALING_ROUND.',
}

export enum CONVERSATION_LISTEN {
  HEALING_ROUND_CONVERSATION = 'HEALING_ROUND_CONVERSATION',
  CREATE_SUMMARY = 'CONVERSATION.CREATE_SUMMARY',
  CREATE = 'CONVERSATION.CREATE',
  DELIMITER = 'CONVERSATION.',
  PROCESS_NEW_AI_THREAD = 'CONVERSATION.PROCESS_NEW_AI_THREAD',
}

export enum AI_LISTENER {
  DELIMITER = 'AI.',
  CREATE_NEW_THREAD = 'AI.CREATE_NEW_THREAD',
  CREATE_NEW_THREAD_W_RUN = 'AI.CREATE_NEW_THREAD_W_RUN',
  CONTINUE_THREAD = 'AI.CONTINUE_THREAD',
}

export enum WS_LISTEN {
  DELIMITER = 'WS.',
  SEND = 'WS.SEND',
  BROADCAST = 'WS.BROADCAST',
}

export enum ANALYTICS_LISTENER {
  DELIMITER = 'ANALYTICS.',
  CREATE = 'ANALYTICS.CREATE',
}

export enum SessionFollowUpInterval {
  TWENTY_FOUR_HRS = 'TWENTY_FOUR_HRS',
  SEVEN_DAYS = 'SEVEN_DAYS',
  NONE = 'NONE',
}

export enum SessionFollowUpStatus {
  PENDING = 'PENDING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
}
export enum WS_RECEIVE {
  LISTENER = 'REQUEST',
  CREATE_CONVERSATION = 'CONVERSATION.CREATE',
  CREATE_MESSAGE = 'MESSAGE.CREATE',
  MESSAGE = 'WELCOME.MESSAGE',
}

export enum WS_SEND {
  ERROR = 'ERROR',
  CONVERSATION_CREATED = 'CONVERSATION.CREATED',
  CONVERSATION_UPDATED = 'CONVERSATION.UPDATED',
  CONVERSATION_AI_DELTA = 'CONVERSATION.AI_DELTA',
  MESSAGE_CREATED = 'MESSAGE.CREATED',
  MESSAGE_AI_RESPONSE = 'MESSAGE.AI_RESPONSE',
}

export enum SortOrderDto {
  ASC = 'asc',
  DESC = 'desc',
}
export enum NotificationTriggerType {
  MANUAL = 'MANUAL',
  AUTOMATED = 'AUTOMATED',
}

export enum WELCOME_MESSAGE {
  MESSAGE = 'CONVERSATION.HISTORY',
}

export enum SESSION_STATUS {
  DELIMITER = 'STATUS.',
  STATUS = 'STATUS.SESSION_STATUS',
}

export enum AppDocumentName {
  Disclosure = 'DISCLOSURE',
}

export enum ConversationalAgentType {
  Intake = 'INTAKE_AGENT',
  CheckIn = 'CHECK_IN_AGENT',
  Report = 'REPORT_AGENT',
}
