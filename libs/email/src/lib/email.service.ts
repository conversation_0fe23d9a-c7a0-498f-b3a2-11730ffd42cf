import { Injectable } from '@nestjs/common';
import sgMail from '@sendgrid/mail';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class LibEmailService {
  private readonly sendgridFromEmail: string;
  constructor(private configService: ConfigService) {
    this.sendgridFromEmail = this.configService.getOrThrow<string>(
      'SENDGRID_FROM_EMAIL'
    );
    const sendgridApiKey =
      this.configService.getOrThrow<string>('SENDGRID_API_KEY');
    sgMail.setApiKey(sendgridApiKey);
  }

  async sendEmail(createEmailDto: any): Promise<void> {
    try {
      const msg = {
        to: createEmailDto.to,
        from: {
          email: this.sendgridFromEmail,
          name: '<PERSON><PERSON>',
        },
        templateId: createEmailDto.templateId,
        dynamic_template_data: createEmailDto.dynamic_template_data,
        tracking_settings: createEmailDto.tracking_settings,
      };
      await sgMail.send(msg);
    } catch (error: any) {
      if (error.response) {
        console.error('Error sending email:', error.response.body.errors);
      } else {
        console.error('Error sending email:', error.message);
      }
    }
  }
}
