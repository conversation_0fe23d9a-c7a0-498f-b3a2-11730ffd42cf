import { ApiProperty } from '@nestjs/swagger';
import {
  IsEmail,
  IsNotEmpty,
  IsObject,
  IsString,
} from '@nestjs/class-validator';

export class CreateEmailDto {
  @ApiProperty({
    example: '<EMAIL>',
    description: 'User email address',
    required: true,
  })
  @IsEmail()
  @IsNotEmpty()
  to: string;

  @ApiProperty({
    example: 'd-e0962af18fc14bf4a97e40862e9272d6',
    description: 'Sample Template Id',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  templateId: string;

  @ApiProperty({
    example: { Name: 'sample' },
    description: 'Permissions object with specific attributes',
    required: true,
  })
  @IsNotEmpty()
  @IsObject()
  dynamic_template_data: Record<string, any>;
}
