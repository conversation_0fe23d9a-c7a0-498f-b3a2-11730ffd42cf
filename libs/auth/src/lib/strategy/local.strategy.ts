import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-local';
import * as bcrypt from 'bcrypt';
import { users } from '@core/prisma-client';
import { UsersRepository } from '@core_be/data-access';
import { Logger } from 'nestjs-pino';

@Injectable()
export class LocalStrategy extends PassportStrategy(Strategy) {
  constructor(
    private usersRepository: UsersRepository,
    private readonly logger: Logger
  ) {
    super({
      usernameField: 'username',
    });
  }
  async validate(username: string, password: string): Promise<users> {
    try {
      const user = await this.usersRepository.findOneByUsername(username);

      if (!user) {
        // Log failed login attempt with email
        this.logger.error(
          {
            timestamp: new Date().toISOString(),
            event: 'LOGIN_FAILED',
            reason: 'USER_NOT_FOUND',
            email: username,
            message: 'Login attempt with non-existent user',
          },
          'Authentication Failed'
        );

        throw new UnauthorizedException('Username or password does not match.');
      }

      const isMatch: boolean = await bcrypt.compare(
        password,
        user.password_hash
      );
      if (!isMatch) {
        // Log failed login attempt with email
        this.logger.error(
          {
            timestamp: new Date().toISOString(),
            event: 'LOGIN_FAILED',
            reason: 'INVALID_PASSWORD',
            email: username,
            userId: user.user_id,
            message: 'Login attempt with incorrect password',
          },
          'Authentication Failed'
        );

        throw new UnauthorizedException('Username or password does not match.');
      }

      // Log successful authentication
      this.logger.log(
        {
          timestamp: new Date().toISOString(),
          event: 'LOGIN_SUCCESS',
          email: username,
          userId: user.user_id,
          message: 'User successfully authenticated',
        },
        'Authentication Success'
      );

      return user;
    } catch (error: any) {
      // Re-throw UnauthorizedException as-is, log other errors
      if (error instanceof UnauthorizedException) {
        throw error;
      }

      this.logger.error(
        {
          timestamp: new Date().toISOString(),
          event: 'LOGIN_ERROR',
          email: username,
          error: error.message,
          message: 'Unexpected error during authentication',
        },
        'Authentication Error'
      );

      throw new UnauthorizedException('Username or password does not match.');
    }
  }
}
