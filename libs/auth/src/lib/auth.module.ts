import { Global, Module } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { PrismaClientModule } from '@core/prisma-client';
import { TokenBlacklistService } from './token-blacklist.service';
import { LibDataAccessModule } from 'libs/data-access/src/lib/data-access.module';
import { LibProfileService } from '@core_be/global';

@Global()
@Module({
  imports: [LibDataAccessModule, PrismaClientModule],
  controllers: [],
  providers: [LibProfileService, JwtService, TokenBlacklistService],
  exports: [TokenBlacklistService],
})
export class LibAuthModule {}
