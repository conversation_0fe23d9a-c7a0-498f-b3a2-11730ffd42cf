import { TokenBlacklistService } from '@core_be/auth';
import {
  ExecutionContext,
  forwardRef,
  Inject,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { AuthGuard } from '@nestjs/passport';
@Injectable()
export class JwtGuard extends AuthGuard('jwt') {
  constructor(
    private reflector: Reflector,
    @Inject(forwardRef(() => TokenBlacklistService))
    private tokenblacklistService: TokenBlacklistService
  ) {
    super();
  }
  override async canActivate(context: ExecutionContext): Promise<boolean> {
    const isPublic = this.reflector.getAllAndOverride('isPublic', [
      context.getHandler(),
      context.getClass(),
    ]);
    if (isPublic) return true;
    const isAuthenticated = (await super.canActivate(context)) as boolean;

    if (!isAuthenticated) {
      return false;
    }

    const request = context.switchToHttp().getRequest();
    const authHeader = request.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new UnauthorizedException('Invalid authorization header format');
    }
    const token = authHeader.substring(7); // Remove 'Bearer ' prefix
    if (token && (await this.tokenblacklistService.isTokenBlacklisted(token))) {
      throw new UnauthorizedException('Token has been invalidated.');
      //return false
    }
    return true;
  }
}
