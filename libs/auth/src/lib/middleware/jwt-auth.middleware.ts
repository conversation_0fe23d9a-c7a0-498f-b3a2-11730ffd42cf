import {
  Injectable,
  NestMiddleware,
  UnauthorizedException,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import * as jwt from 'jsonwebtoken';
import { ConfigService } from '@nestjs/config';
import { UsersRepository } from '@core_be/data-access';
import { Logger } from 'nestjs-pino';

@Injectable()
export class TokenMiddleware implements NestMiddleware {
  private readonly jwtSecret: string;
  private readonly jwtAlgorithm: jwt.Algorithm;
  constructor(
    private readonly configService: ConfigService,
    private readonly usersRepository: UsersRepository,
    private readonly logger: Logger
  ) {
    this.jwtSecret = this.configService.getOrThrow<string>('JWT_SECRET');
    this.jwtAlgorithm = this.configService.getOrThrow<string>(
      'JWT_ALGORITHM'
    ) as jwt.Algorithm;
  }
  async use(req: Request, res: Response, next: NextFunction) {
    const bearerHeader = req.headers.authorization;
      try {
      if (bearerHeader) {
        const bearerToken = bearerHeader.slice(7).trim(); // 'Bearer '.length === 7
        if (!bearerToken) {
          next(new UnauthorizedException('JWT token missing in Authorization header'))
        }
        jwt.verify(
          bearerToken,
          this.jwtSecret,
          { algorithms: [this.jwtAlgorithm] },
          async (err, decoded) => {
            if (err) {
              if (err.name === 'TokenExpiredError') {
                next(new UnauthorizedException('JWT token expired'));
              } else {
                next(new UnauthorizedException());
              }
            } else {
              if (!decoded) {
                return next(new UnauthorizedException('Token decoding failed'));
              }

              const userName = (decoded as any).username;
              if (!userName) {
                return next(
                  new UnauthorizedException('Token does not contain username')
                );
              }
              const userDetails = await this.usersRepository.findOne({
                username: userName,
              });
              if (!userDetails) {
                return next(new UnauthorizedException('User not found'));
              }
              req.user = userDetails;
              next();
            }
          }
        );
      } else {
        this.logger.error({
          timestamp: new Date().toISOString(),
          event: 'JWT_MIDDLEWARE_NO_AUTH_HEADER',
          endpoint: req.url,
          method: req.method,
          ip: req.ip || req.connection?.remoteAddress,
          message: 'Missing or invalid Authorization header'
        }, 'JWT Middleware Failed');

        next(new UnauthorizedException('Invalid or missing Authorization header'));
      }
    } catch (error) {
      this.logger.error({
        timestamp: new Date().toISOString(),
        event: 'JWT_MIDDLEWARE_ERROR',
        endpoint: req.url,
        method: req.method,
        error: error.message,
        message: 'Unexpected error in JWT middleware'
      }, 'JWT Middleware Error');

      next(new HttpException(
        'Internal server error',
        HttpStatus.INTERNAL_SERVER_ERROR
      ));
    }
  }
}
