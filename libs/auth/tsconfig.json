{"extends": "../../tsconfig.base.json", "compilerOptions": {"forceConsistentCasingInFileNames": true, "strict": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "composite": true, "declaration": true, "types": ["node"]}, "files": [], "include": ["src/**/*.ts", "../../apps/core/**/*.ts"], "exclude": ["../../../**/*.spec.ts"], "references": [{"path": "./tsconfig.lib.json"}, {"path": "./tsconfig.spec.json"}]}