import { Injectable } from '@nestjs/common';
import { LibFcmService } from './fcm.service';
import { PrismaService } from '@core/prisma-client';
import {
  BaseMessageType,
  DeviceType,
  NotificationPriority,
  NotificationStatus,
  NotificationTriggerType,
  NotificationType,
} from '@core_be/global';
import { Logger } from 'nestjs-pino';

@Injectable()
export class LibNotificationService {
  constructor(
    private readonly fcmService: LibFcmService,
    private readonly prisma: PrismaService,
    private readonly logger: Logger
  ) {}

  async generateAndDispatchNotification(messages: BaseMessageType[]) {
    try {
      const sendResults = await this.fcmService.sendBulkMessages(messages);

      const notificationPromises = messages.map(async (message, index) => {
        const sendResult = sendResults[index];
        message.notificationStatus = sendResult.success
          ? NotificationStatus.SENT
          : NotificationStatus.FAILED;
        message.sent_at = new Date();
        try {
          await this.createNotifications([message]);
        } catch (notificationError) {
          console.error(
            `Failed to create notification for token: ${message.token}, Error: ${notificationError.message}`
          );
        }
      });

      await Promise.all(notificationPromises);
    } catch (error) {
      console.error('Suppressed Error: ', error.message);
    }
  }

  async createNotifications(
    messages: {
      token: string;
      notificationStatus: NotificationStatus;
      sent_at: Date;
      payload: { notification: { title: string; body: string } };
    }[]
  ): Promise<boolean> {
    const notificationPromises = [];
    for (const message of messages) {
      const { payload, notificationStatus, sent_at } = message;
      try {
        const userId: number = await this.getUserIdFromToken(message.token);

        const notificationPromise = this.prisma.notification
          .create({
            data: {
              title: payload.notification.title,
              body: payload.notification.body,
              status: notificationStatus,
              type: NotificationType.PUSH,
              priority: NotificationPriority.MEDIUM,
              trigger_type: NotificationTriggerType.AUTOMATED,
              scheduled_at: new Date(),
              sent_at,
              created_by: userId,
            },
          })
          .then((notification) => {
            return this.prisma.notification_recipients.create({
              data: {
                notification_id: notification.notification_id,
                user_id: userId,
                status: notificationStatus,
                created_by: userId,
              },
            });
          });

        notificationPromises.push(notificationPromise);
      } catch (error) {
        console.error(
          `Failed to create notification for token: ${message.token}, Error: ${error.message}`
        );
      }
    }

    try {
      await Promise.all(notificationPromises);
      return true;
    } catch (error) {
      console.error(
        `Failed to create one or more notifications: ${error.message}`
      );
      return false;
    }
  }

  private async getUserIdFromToken(token: string): Promise<number> {
    const userDevice = await this.prisma.user_devices.findFirst({
      where: { fcm_token: token },
    });

    return userDevice?.user_id;
  }

  async sendPushNotifications() {
    try {
      const { fcmTokens, notificationIds } = await this.findReadyToSend();

      if (!fcmTokens.length) {
        this.logger.log('No ready notifications to send.');
        return;
      }

      await this.fcmService.sendBulkMessages(fcmTokens);

      if (notificationIds.length) {
        await this.prisma.notification.updateMany({
          where: { notification_id: { in: notificationIds } },
          data: { status: NotificationStatus.SENT, sent_at: new Date() },
        });
      }

      this.logger.log(`Notifications sent successfully.`);
    } catch (error) {
      this.logger.error(
        `Error while processing notifications: ${error.message}`
      );
    }
  }

  async sendPushNotification(
    userId: number,
    notification: {
      title: string;
      body: string;
    }
  ) {
    try {
      const userDevices =
        (await this.prisma.user_devices.findMany({
          where: {
            user_id: userId,
          },
        })) || [];
      let notified = false;
      for (const userDevice of userDevices) {
        if (!userDevice.fcm_token) {
          this.logger.error('FCM token is not available.');
          return;
        }
        const message = {
          token: userDevice.fcm_token,
          payload: {
            notification: {
              title: notification.title,
              body: notification.body,
            },
          },
        };
        const response = await this.fcmService.sendMessage(
          message,
          userDevice.device_type as DeviceType
        );
        if (response.success && !notified) {
          notified = true;
        }
      }
      if (notified) {
        const sentAt = new Date();
        const newNotificationData = {
          title: notification.title,
          body: notification.body,
          status: NotificationStatus.SENT,
          created_by: userId,
          sent_at: sentAt,
        };
        const newNotification = await this.prisma.notification.create({
          data: newNotificationData,
        });
        await this.prisma.notification_recipients.create({
          data: {
            user_id: userId,
            notification_id: newNotification.notification_id,
            status: NotificationStatus.SENT,
            sent_at: sentAt,
          },
        });
        this.logger.log(`Notification sent successfully.`);
      }
    } catch (error) {
      this.logger.error(
        `Error while processing notification: ${error.message}`
      );
    }
  }

  private async findReadyToSend() {
    const now = new Date().toISOString();
    const notifications = await this.prisma.notification.findMany({
      where: {
        OR: [
          {
            status: NotificationStatus.READY_TO_SEND,
            scheduled_at: { lte: now },
          },
          {
            status: NotificationStatus.SCHEDULED,
            scheduled_at: { lte: now },
          },
        ],
      },
      include: {
        notification_recipients: {
          include: {
            user: {
              include: {
                user_devices: { where: { fcm_token: { not: null } } },
              },
            },
          },
        },
      },
    });

    if (!notifications.length) return { fcmTokens: [], notificationIds: [] };

    const notificationIds = new Set<number>();
    const fcmTokens = notifications.flatMap((notification) => {
      notificationIds.add(notification.notification_id);

      return notification.notification_recipients.flatMap((recipient) => {
        if (!recipient.user?.user_devices?.length) return [];

        return recipient.user.user_devices
          .filter((device) => device.fcm_token)
          .map((device) => ({
            token: device.fcm_token,
            payload: {
              notification: {
                title: notification.title,
                body: notification.body,
              },
            },
          }));
      });
    });

    return { fcmTokens, notificationIds: [...notificationIds] };
  }
}
