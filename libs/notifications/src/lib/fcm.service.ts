import { Injectable, OnModuleInit } from '@nestjs/common';
import * as admin from 'firebase-admin';
import {
  BaseMessage,
  MulticastMessage,
  getMessaging,
} from 'firebase-admin/messaging';
import { ConfigService } from '@nestjs/config';
import { DeviceType } from '@core_be/global';

@Injectable()
export class LibFcmService implements OnModuleInit {
  private firebaseApp?: admin.app.App;
  private privateKeyBase64: string;
  private clientEmail: string;
  private projectId: string;
  constructor(private configService: ConfigService) {
    this.privateKeyBase64 = this.configService.getOrThrow<string>(
      'FIREBASE_PRIVATE_KEY_BASE_64'
    );
    this.clientEmail = this.configService.getOrThrow<string>(
      'FIREBASE_CLIENT_EMAIL'
    );
    this.projectId = this.configService.getOrThrow<string>(
      'FIREBASE_PROJECT_ID'
    );
  }

  onModuleInit() {
    if (this.firebaseApp) return;

    const privateKey = Buffer.from(this.privateKeyBase64, 'base64').toString(
      'utf8'
    );

    if (!admin.apps.length) {
      this.firebaseApp = admin.initializeApp({
        credential: admin.credential.cert({
          clientEmail: this.clientEmail,
          privateKey: privateKey,
          projectId: this.projectId,
        }),
      });
    } else {
      admin.app(); // If it's already initialized, use the existing app instance
    }
  }

  async sendMessageByTokens(
    deviceTokens: string[],
    message: BaseMessage,
    dryRun?: boolean
  ) {
    const multiMessage: MulticastMessage = {
      ...message,
      tokens: deviceTokens,
    };

    const messaging = getMessaging(this.firebaseApp);

    const result = await messaging.sendEachForMulticast(multiMessage, dryRun);

    return result;
  }

  async sendMessageByTopic(topicName: string, message: BaseMessage) {
    const messaging = admin.messaging();

    const response = await messaging.send({
      ...message,
      topic: topicName,
    });

    return response;
  }

  async sendBulkMessages(
    messages: { token: string; payload: admin.messaging.MessagingPayload }[]
  ) {
    if (!messages.length) return [];

    try {
      const promises = messages.map((message) =>
        admin
          .messaging()
          .send({
            token: message.token,
            notification: message.payload.notification || undefined,
            data: message.payload.data || undefined,
          })
          .then(() => ({
            token: message.token,
            success: true,
            error: null,
          }))
          .catch((error) => ({
            token: message.token,
            success: false,
            error: error.message || 'Unknown error',
          }))
      );

      return await Promise.all(promises);
    } catch (error) {
      console.error('FCM Bulk Message Sending Failed: ', error);
      return messages.map((msg) => ({
        token: msg.token,
        success: false,
        error: 'FCM Bulk Send Error',
      }));
    }
  }

  async sendMessage(
    message: {
      token: string;
      payload: admin.messaging.MessagingPayload;
    },
    devideType: DeviceType
  ) {
    if (!message) return [];

    const iosMessage = {
      token: message.token,
      notification: message.payload.notification || undefined,
      data: message.payload.data || undefined,
      apns: {
        payload: {
          aps: {
            sound: 'default',
          },
        },
      },
    };
    const androidMessage = {
      token: message.token,
      data: {
        title: message.payload.notification.title,
        body: message.payload.notification.body,
      },
      android: {
        priority: 'high' as 'high',
      },
    };
    try {
      return admin
        .messaging()
        .send(devideType === DeviceType.IOS ? iosMessage : androidMessage)
        .then(() => ({
          token: message.token,
          success: true,
          error: null,
        }))
        .catch((error) => ({
          token: message.token,
          success: false,
          error: error.message || 'Unknown error',
        }));
    } catch (error) {
      console.error('FCM Bulk Message Sending Failed: ', error);
      return error;
    }
  }
}
