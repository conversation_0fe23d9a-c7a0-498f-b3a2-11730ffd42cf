import { Injectable } from '@nestjs/common';
import { PrismaService, Prisma } from '@core/prisma-client';

@Injectable()
export class AuthRepository {
  private readonly repo: Prisma.usersDelegate;
  constructor(readonly prisma: PrismaService) {
    this.repo = this.prisma.users;
  }

  async findUserByEmail(email: string) {
    return this.repo.findUnique({ where: { email, is_deleted: false } });
  }
}
