import { PrismaClientModule } from '@core/prisma-client';
import { Global, Module } from '@nestjs/common';
import { UsersRepository } from './users/users.repository';
import { ConversationRepository } from './conversations/conversation.repository';
import { SessionRepository } from './sessions/session.repository';
import { HealingRoundRepository } from './healing-rounds/healing-round.repository';
import { SummaryRepository } from './summaries/summary.repository';
import { SessionAilmentRepository } from './session-ailments/session-ailment.repository';
import { ProfileRepository } from './profiles/profile.repository';
import { AilmentsRepository } from './ailments/ailment.repository';
import { TermsRepository } from './terms/terms.repository';
import { RoleRepository } from './roles/role.repository';
import { WhitelistRepository } from './whitelist/whitelist.repository';
import { UsersRoleRepository } from './users/user-role.repository';
import { UsersDeviceRepository } from './users/user-device.repository';
import { OnBoardingRepository } from './onboarding/onboarding.repository';
import { PrivacyPolicyRepository } from './privacy-policy/privacy-policy.repository';
import { FeedbackRepository } from './feedback/feedback.repository';
import { MetricsRepository } from './metrics/metrics.repository';
import { NotificationsTopicsRepository } from './notifications-topics/notifications-topics.repository';
import { SessionFollowUpRepository } from './session-follow-up/session-follow-up.repository';
import { NotificationsPreferencesRepository } from './notifications-preferences/notifications-preferences.repository';
@Global()
@Module({
  imports: [PrismaClientModule],
  controllers: [],
  providers: [
    UsersRepository,
    ConversationRepository,
    SessionRepository,
    HealingRoundRepository,
    SummaryRepository,
    SessionAilmentRepository,
    ProfileRepository,
    AilmentsRepository,
    TermsRepository,
    RoleRepository,
    WhitelistRepository,
    UsersRoleRepository,
    UsersDeviceRepository,
    OnBoardingRepository,
    PrivacyPolicyRepository,
    FeedbackRepository,
    MetricsRepository,
    NotificationsTopicsRepository,
    SessionFollowUpRepository,
    NotificationsPreferencesRepository,
  ],
  exports: [
    UsersRepository,
    ConversationRepository,
    SessionRepository,
    HealingRoundRepository,
    SummaryRepository,
    SessionAilmentRepository,
    ProfileRepository,
    AilmentsRepository,
    TermsRepository,
    RoleRepository,
    WhitelistRepository,
    UsersRoleRepository,
    UsersDeviceRepository,
    OnBoardingRepository,
    PrivacyPolicyRepository,
    FeedbackRepository,
    MetricsRepository,
    NotificationsTopicsRepository,
    SessionFollowUpRepository,
    NotificationsPreferencesRepository,
  ],
})
export class LibDataAccessModule {}
