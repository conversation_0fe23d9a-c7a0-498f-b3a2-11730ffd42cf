import { Prisma, PrismaService } from '@core/prisma-client';
import { WaitListUserType } from '@core_be/global';
import { Injectable } from '@nestjs/common';

@Injectable()
export class WaitlistRepository {
  private readonly repo: Prisma.waitlistDelegate;
  constructor(readonly prisma: PrismaService) {
    this.repo = this.prisma.waitlist;
  }

  async create(
    data: Prisma.waitlistCreateInput,
    prisma?: Prisma.TransactionClient
  ) {
    const client = prisma || this.prisma;
    return client.waitlist.create({ data });
  }

  async findByEmailAndType(email: string, userType: WaitListUserType) {
    return this.repo.findFirst({
      where: {
        email,
        user_type: userType,
        is_deleted: false,
      },
    });
  }
}
