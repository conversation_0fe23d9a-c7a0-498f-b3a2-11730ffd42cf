import { Test, TestingModule } from '@nestjs/testing';
import { SessionFollowUpRepository } from './session-follow-up.repository';

describe('SessionFollowUpService', () => {
  let service: SessionFollowUpRepository;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [SessionFollowUpRepository],
    }).compile();

    service = module.get<SessionFollowUpRepository>(SessionFollowUpRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
