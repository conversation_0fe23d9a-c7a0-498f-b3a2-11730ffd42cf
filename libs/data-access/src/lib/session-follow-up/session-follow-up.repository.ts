import { Prisma, PrismaService } from '@core/prisma-client';
import { Injectable } from '@nestjs/common';
import { DefaultArgs } from '@prisma/core/runtime/library';

@Injectable()
export class SessionFollowUpRepository {
  private repo: Prisma.session_follow_upDelegate;

  constructor(readonly prisma: PrismaService) {
    this.repo = this.prisma.session_follow_up;
  }

  async create(data: Prisma.session_follow_upUncheckedCreateInput) {
    return this.repo.create({ data });
  }

  async findAll(params: {
    skip?: number;
    limit?: number;
    take?: number;
    cursor?: Prisma.session_follow_upWhereUniqueInput;
    where?: Prisma.session_follow_upWhereInput;
    orderBy?: Prisma.session_follow_upOrderByWithAggregationInput;
    select?: Prisma.session_follow_upSelect;
  }) {
    const { skip, limit, take, cursor, where, orderBy, select } = params;
    return this.repo.findMany({
      skip,
      take: limit || take,
      cursor,
      where,
      orderBy,
      select,
    });
  }

  async findAllWithInclude(params: {
    skip?: number;
    limit?: number;
    take?: number;
    cursor?: Prisma.session_follow_upWhereUniqueInput;
    where?: Prisma.session_follow_upWhereInput;
    orderBy?: Prisma.session_follow_upOrderByWithAggregationInput;
    include?: Prisma.session_follow_upInclude;
  }) {
    const { skip, limit, take, cursor, where, orderBy, include } = params;
    return this.repo.findMany({
      skip,
      take: limit || take,
      cursor,
      where,
      orderBy,
      include,
    });
  }

  async findOneWithInclude(params: {
    where: Prisma.session_follow_upWhereInput;
    include?: Prisma.session_follow_upInclude;
  }) {
    const { where, include } = params;
    return this.repo.findFirst({
      where,
      include: include || {
        healing_sessions: true,
      },
    });
  }

  async findOneWithIncludeUnique(params: {
    where: Prisma.session_follow_upWhereUniqueInput;
    include?: Prisma.session_follow_upInclude;
  }) {
    const { where, include } = params;
    return this.repo.findUnique({
      where,
      include: include || {
        healing_sessions: true,
      },
    });
  }

  async findOneWithSelectUnique(
    params: Prisma.session_follow_upFindUniqueArgs
  ) {
    return this.repo.findUnique(params);
  }

  async findOneWithSelect(params: {
    where: Prisma.session_follow_upWhereInput;
    select?: Prisma.session_follow_upSelect;
  }) {
    const { where, select } = params;
    return this.repo.findFirst({
      where,
      select,
    });
  }

  async getSessionInfo(params: {
    where: Prisma.session_follow_upWhereInput;
    orderBy?: Prisma.session_follow_upOrderByWithAggregationInput;
    include?: Prisma.session_follow_upInclude;
  }) {
    const { where, orderBy } = params;
    return this.repo.findFirst({
      where,
      orderBy: orderBy || {
        created_at: 'desc',
      },
    });
  }

  async findFirst(params: {
    where: Prisma.session_follow_upWhereInput;
    orderBy?: Prisma.session_follow_upOrderByWithAggregationInput;
    include?: Prisma.session_follow_upInclude;
  }) {
    const { where, orderBy, include } = params;
    return this.repo.findFirst({
      where,
      orderBy: orderBy || {
        created_at: 'desc',
      },
      include: include || {
        healing_sessions: true,
      },
    });
  }

  async aggregate(
    aggregate: Prisma.Subset<
      Prisma.Session_follow_upAggregateArgs<DefaultArgs>,
      Prisma.Session_follow_upAggregateArgs<DefaultArgs>
    >
  ) {
    return this.repo.aggregate(aggregate);
  }

  async update(params: {
    where: Prisma.session_follow_upWhereUniqueInput;
    data: Prisma.session_follow_upUpdateInput;
  }) {
    const { where, data } = params;
    return this.repo.update({ where, data });
  }

  async updateMany(params: {
    where: Prisma.session_follow_upWhereInput;
    data: Prisma.session_follow_upUpdateInput;
  }) {
    const { where, data } = params;
    return this.repo.updateMany({ where, data });
  }

  async count(params: { where?: Prisma.session_follow_upWhereInput }) {
    const { where } = params;
    return this.repo.count({ where });
  }
}
