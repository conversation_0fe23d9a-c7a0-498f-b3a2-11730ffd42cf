import { Injectable } from '@nestjs/common';
import { PrismaService, Prisma } from '@core/prisma-client';

@Injectable()
export class TermsRepository {
  private readonly repo: Prisma.terms_and_conditionsDelegate;
  constructor(readonly prisma: PrismaService) {
    this.repo = this.prisma.terms_and_conditions;
  }

  async create(data: Prisma.terms_and_conditionsCreateInput) {
    return this.repo.create({ data });
  }

  async update(params: {
    where: Prisma.terms_and_conditionsWhereUniqueInput;
    data: Prisma.terms_and_conditionsUpdateInput;
  }) {
    const { where, data } = params;
    return this.repo.update({ data, where });
  }

  async delete(where: Prisma.terms_and_conditionsWhereUniqueInput, data: any) {
    return this.repo.update({ data, where });
  }

  async findById(terms_id: number) {
    return this.repo.findFirst({ where: { terms_id } });
  }

  async findOne(
    where: Prisma.terms_and_conditionsWhereUniqueInput,
    include?: Prisma.terms_and_conditionsInclude
  ) {
    return this.repo.findUnique({
      where,
      include: include || {
        user_terms_agreement: true,
      },
    });
  }

  async findAll(params: {
    skip?: number;
    limit?: number;
    cursor?: Prisma.terms_and_conditionsWhereUniqueInput;
    where?: Prisma.terms_and_conditionsWhereInput;
    orderBy?: Prisma.terms_and_conditionsOrderByWithAggregationInput;
  }) {
    const { skip, limit, cursor, where, orderBy } = params;
    return this.repo.findMany({
      skip,
      take: limit,
      cursor,
      where,
      orderBy,
    });
  }

  async count(params: { where?: Prisma.terms_and_conditionsWhereInput }) {
    const { where } = params;
    return this.repo.count({ where });
  }

  async findLast() {
    // Assuming your User entity has a createdAt field to order by
    return this.repo.findFirst({
      orderBy: { created_at: 'desc' }, // Get the last user based on createdAt field
    });
  }

  async saveUserTerms(
    data: Prisma.user_terms_agreementCreateInput,
    prisma?: Prisma.TransactionClient
  ) {
    const client = prisma || this.prisma;
    return client.user_terms_agreement.create({ data });
  }

  async getByUserTerms(where: Prisma.user_terms_agreementWhereInput) {
    return this.prisma.user_terms_agreement.findFirst({ where });
  }

  async getUserTermsStatus(user_id: number) {
    const userTermsStatus = await this.prisma.terms_and_conditions.findFirst({
      where: {
        is_deleted: false,
        is_active: true,
      },
      include: {
        user_terms_agreement: {
          where: {
            user_id: user_id,
            is_terms_accepted: true,
          },
        },
      },
    });

    return userTermsStatus?.user_terms_agreement;
  }

  async getUserTerms(user_id: number) {
    const userTerms = await this.prisma.terms_and_conditions.findFirst({
      where: {
        is_deleted: false,
        is_active: true,
      },
      include: {
        user_terms_agreement: {
          where: {
            user_id: +user_id,
            is_terms_accepted: true,
          },
        },
      },
    });

    return userTerms;
  }
}
