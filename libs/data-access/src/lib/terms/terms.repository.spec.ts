import { Test, TestingModule } from '@nestjs/testing';
import { TermsRepository } from './terms.repository';

describe('TermAndConditionsService', () => {
  let service: TermsRepository;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [TermsRepository],
    }).compile();

    service = module.get<TermsRepository>(TermsRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
