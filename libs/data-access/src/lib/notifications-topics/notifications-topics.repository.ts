import { Injectable } from '@nestjs/common';
import { PrismaService, Prisma } from '@core/prisma-client';

@Injectable()
export class NotificationsTopicsRepository {
  private readonly repo: Prisma.notification_topicsDelegate;

  constructor(readonly prisma: PrismaService) {
    this.repo = this.prisma.notification_topics;
  }

  async create(data: Prisma.notification_topicsCreateInput) {
    return this.repo.create({ data });
  }

  async update(
    where: Prisma.notification_topicsWhereUniqueInput,
    data: Prisma.notification_topicsUpdateInput
  ) {
    return this.repo.update({ data, where });
  }

  async delete(where: Prisma.notification_topicsWhereUniqueInput, data: any) {
    return this.repo.update({ data, where });
  }

  async findOne(
    where: Prisma.notification_topicsWhereUniqueInput,
    include?: Prisma.notification_topicsInclude
  ) {
    return this.repo.findUnique({
      where,
      include: include || {
        user_notification_preferences: true,
        notification: true,
      },
    });
  }

  async findAll(params: {
    skip?: number;
    limit?: number;
    cursor?: Prisma.notification_topicsWhereUniqueInput;
    where?: Prisma.notification_topicsWhereInput;
    orderBy?: Prisma.notification_topicsOrderByWithAggregationInput;
    include?: any;
  }) {
    const { skip, limit, cursor, where, orderBy, include } = params;
    return this.repo.findMany({
      skip,
      take: limit,
      cursor,
      where,
      orderBy,
      include,
    });
  }

  async count(params: { where?: Prisma.notification_topicsWhereInput }) {
    const { where } = params;
    return this.repo.count({ where });
  }
}
