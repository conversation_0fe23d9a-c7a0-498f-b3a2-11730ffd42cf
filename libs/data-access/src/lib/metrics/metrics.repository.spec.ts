import { Test, TestingModule } from '@nestjs/testing';
import { MetricsRepository } from './metrics.repository';

describe('HealingRoundService', () => {
  let service: MetricsRepository;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [MetricsRepository],
    }).compile();

    service = module.get<MetricsRepository>(MetricsRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
