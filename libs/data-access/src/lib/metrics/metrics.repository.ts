import { Injectable } from '@nestjs/common';
import { PrismaService, Prisma } from '@core/prisma-client';

@Injectable()
export class MetricsRepository {
  private repo: Prisma.metricsDelegate;

  constructor(readonly prisma: PrismaService) {
    this.repo = this.prisma.metrics;
  }

  async create(data: Prisma.metricsCreateInput) {
    return this.repo.create({ data });
  }

  async update(params: {
    where: Prisma.metricsWhereUniqueInput;
    data: Prisma.metricsUpdateInput;
  }) {
    const { where, data } = params;
    return this.repo.update({ data, where });
  }

  async findById(metric_id: number) {
    return this.repo.findFirst({ where: { metric_id } });
  }

  async findOneWithSelectUnique(params: {
    where: Prisma.metricsWhereUniqueInput;
    select?: Prisma.metricsSelect;
  }) {
    const { where, select } = params;
    return this.repo.findUnique({
      where,
      select,
    });
  }

  async findOne(where: Prisma.metricsWhereUniqueInput) {
    return this.repo.findUnique({
      where,
    });
  }

  async findAll(params: {
    skip?: number;
    limit?: number;
    take?: number;
    cursor?: Prisma.metricsWhereUniqueInput;
    where?: Prisma.metricsWhereInput;
    orderBy?: Prisma.metricsOrderByWithAggregationInput;
    select?: Prisma.metricsSelect;
  }) {
    const { skip, limit, take, cursor, where, orderBy, select } = params;
    return this.repo.findMany({
      skip,
      take: limit || take,
      cursor,
      where,
      orderBy,
      select,
    });
  }
}
