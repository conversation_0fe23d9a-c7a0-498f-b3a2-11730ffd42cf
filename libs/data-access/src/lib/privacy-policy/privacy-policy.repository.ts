import { Injectable } from '@nestjs/common';
import { PrismaService, Prisma } from '@core/prisma-client';

@Injectable()
export class PrivacyPolicyRepository {
  private readonly repo: Prisma.privacy_policyDelegate;

  constructor(readonly prisma: PrismaService) {
    this.repo = this.prisma.privacy_policy;
  }

  async create(data: Prisma.privacy_policyCreateInput) {
    return this.repo.create({ data });
  }

  async update(
    where: Prisma.privacy_policyWhereUniqueInput,
    data: Prisma.privacy_policyUpdateInput
  ) {
    return this.repo.update({ data, where });
  }

  async delete(where: Prisma.privacy_policyWhereUniqueInput, data: any) {
    return this.repo.update({ data, where });
  }

  async findOne(
    where: Prisma.privacy_policyWhereUniqueInput,
    include?: Prisma.privacy_policyInclude
  ) {
    return this.repo.findUnique({
      where,
      include: include || {
        user_privacy_policy: true,
      },
    });
  }

  async findAll() {
    return this.repo.findMany();
  }

  async getUserPrivacyPolicyStatus(user_id: number) {
    const userPolicyStatus = await this.prisma.privacy_policy.findFirst({
      where: {
        is_deleted: false,
        is_active: true,
      },
      include: {
        user_privacy_policy: {
          where: {
            user_id: user_id,
            is_privacy_policy_accepted: true,
          },
        },
      },
    });

    return userPolicyStatus?.user_privacy_policy;
  }

  async getUserPrivacyPolicy(user_id: number) {
    const userPrivacyPolicy = await this.prisma.user_privacy_policy.findFirst({
      where: {
        user_id: user_id,
        is_privacy_policy_accepted: true,
      },
    });
    return userPrivacyPolicy;
  }

  async createPrivacyPolicy(
    data: Prisma.user_privacy_policyCreateInput,
    prisma?: Prisma.TransactionClient
  ) {
    const client = prisma || this.prisma;
    return await client.user_privacy_policy.create({
      data,
    });
  }
}
