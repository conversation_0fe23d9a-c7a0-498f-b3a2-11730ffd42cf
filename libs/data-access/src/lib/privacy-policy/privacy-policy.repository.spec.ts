import { Test, TestingModule } from '@nestjs/testing';
import { PrivacyPolicyRepository } from './privacy-policy.repository';

describe('PrivacyPolicyService', () => {
  let service: PrivacyPolicyRepository;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [PrivacyPolicyRepository],
    }).compile();

    service = module.get<PrivacyPolicyRepository>(PrivacyPolicyRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
