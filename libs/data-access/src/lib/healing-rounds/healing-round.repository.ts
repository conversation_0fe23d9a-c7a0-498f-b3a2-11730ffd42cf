import { Injectable } from '@nestjs/common';
import { PrismaService, Prisma } from '@core/prisma-client';

@Injectable()
export class HealingRoundRepository {
  private repo: Prisma.healing_roundsDelegate;

  constructor(readonly prisma: PrismaService) {
    this.repo = this.prisma.healing_rounds;
  }

  async create(data: Prisma.healing_roundsCreateInput) {
    return this.repo.create({ data });
  }

  async findAll(params: {
    skip?: number;
    limit?: number;
    take?: number;
    cursor?: Prisma.healing_roundsWhereUniqueInput;
    where?: Prisma.healing_roundsWhereInput;
    select?: Prisma.healing_roundsSelect;
    orderBy?: Prisma.healing_roundsOrderByWithAggregationInput;
  }) {
    const { skip, limit, cursor, where, select, orderBy, take } = params;
    return this.repo.findMany({
      skip,
      take: limit || take,
      cursor,
      where,
      select,
      orderBy,
    });
  }

  async findOneWithInclude(params: {
    where: Prisma.healing_roundsWhereInput;
    include?: Prisma.healing_roundsInclude;
    orderBy?: Prisma.healing_roundsOrderByWithRelationInput;
  }) {
    const { where, include, orderBy } = params;
    return this.repo.findFirst({
      where,
      orderBy: orderBy || { created_at: 'desc' },
      include: include || {
        session_summaries: true,
        session_ailments: true,
        conversations: true,
        healing_sessions: true,
        profiles: true,
      },
    });
  }

  async findOneWithSelect(params: {
    where: Prisma.healing_roundsWhereInput;
    select?: Prisma.healing_roundsSelect;
    orderBy?: Prisma.healing_roundsOrderByWithAggregationInput;
  }) {
    const { where, select, orderBy } = params;
    return this.repo.findFirst({
      where,
      select,
      orderBy,
    });
  }

  async findOneWithSelectUnique(params: {
    where: Prisma.healing_roundsWhereUniqueInput;
    select?: Prisma.healing_roundsSelect;
  }) {
    const { where, select } = params;
    return this.repo.findUnique({
      where,
      select,
    });
  }

  async findFirst(
    where: Prisma.healing_roundsWhereInput,
    include?: Prisma.healing_roundsInclude
  ) {
    return this.repo.findFirst({
      where,
      include,
    });
  }

  async findOne(params: {
    where: any;
    include?: Prisma.healing_roundsInclude;
  }) {
    const { where, include } = params;
    return this.repo.findFirst({
      where,
    });
  }

  async findUnique(params: { where: Prisma.healing_roundsWhereUniqueInput }) {
    const { where } = params;
    return this.repo.findUnique({
      where,
    });
  }

  async update(params: {
    where: Prisma.healing_roundsWhereUniqueInput;
    data: Prisma.healing_roundsUpdateInput;
  }) {
    const { where, data } = params;
    return this.repo.update({ where, data });
  }

  async updateMany(params: {
    where: Prisma.healing_roundsWhereInput;
    data: Prisma.healing_roundsUpdateInput;
  }) {
    const { where, data } = params;
    return this.repo.updateMany({ where, data });
  }

  async aggregate(aggregate: Prisma.Healing_roundsAggregateArgs) {
    return this.repo.aggregate(aggregate);
  }

  async count(params: { where?: Prisma.healing_roundsWhereInput }) {
    const { where } = params;
    return this.repo.count({ where });
  }
}
