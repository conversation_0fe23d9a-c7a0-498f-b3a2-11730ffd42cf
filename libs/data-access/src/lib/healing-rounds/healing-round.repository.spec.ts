import { Test, TestingModule } from '@nestjs/testing';
import { HealingRoundRepository } from './healing-round.repository';

describe('HealingRoundService', () => {
  let service: HealingRoundRepository;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [HealingRoundRepository],
    }).compile();

    service = module.get<HealingRoundRepository>(HealingRoundRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
