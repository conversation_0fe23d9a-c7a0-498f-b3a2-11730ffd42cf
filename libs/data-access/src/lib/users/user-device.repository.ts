import { Prisma, PrismaService } from '@core/prisma-client';
import { BadRequestException, Injectable } from '@nestjs/common';

@Injectable()
export class UsersDeviceRepository {
  private readonly repo: Prisma.user_devicesDelegate;
  constructor(readonly prisma: PrismaService) {
    this.repo = this.prisma.user_devices;
  }

  async createUserDevice(data: Prisma.user_devicesCreateInput,prisma?: Prisma.TransactionClient) {
    const client = prisma || this.prisma;
    return client.user_devices.create({ data });
  }

  async updateUserDevice(params: {
    where: Prisma.user_devicesWhereUniqueInput;
    data: Prisma.user_devicesUpdateInput;
  }) {
    const { where, data } = params;
    return this.prisma.user_devices.update({ data, where });
  }

  async getDefaultUserDevice(where: Prisma.user_devicesWhereInput) {
    return this.prisma.user_devices.findFirst({ where });
  }

  async getUserDeviceInfo(user_id: number) {
    try {
      return await this.prisma.user_devices.findFirst({ where: { user_id } });
    } catch (error) {
      throw new BadRequestException(error || 'Please try again');
    }
  }
}
