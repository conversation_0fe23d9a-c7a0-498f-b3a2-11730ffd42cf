import { Prisma, PrismaService } from '@core/prisma-client';
import { BadRequestException, Injectable } from '@nestjs/common';
import { Role } from '@core_be/global';

@Injectable()
export class UsersRoleRepository {
  private readonly repo: Prisma.user_rolesDelegate;
  constructor(readonly prisma: PrismaService) {
    this.repo = this.prisma.user_roles;
  }

  async createUserRole(data: Prisma.user_rolesCreateInput,prisma?: Prisma.TransactionClient) {
    const client = prisma || this.prisma;
    return await client.user_roles.create({ data });
  }

  async updateUserRole(params: {
    where: Prisma.user_rolesWhereUniqueInput;
    data: Prisma.user_rolesUpdateInput;
  }) {
    const { where, data } = params;
    return await this.prisma.user_roles.update({ data, where });
  }

  async deleteUserRole(where: Prisma.user_rolesWhereUniqueInput) {
    return await this.prisma.user_roles.delete({ where });
  }

  async userRole(where: Prisma.user_rolesWhereUniqueInput) {
    return await this.prisma.user_roles.findUnique({
      where,
      include: {
        roles: true,
        users: true,
      },
    });
  }

  async userRoles(params: {
    skip?: number;
    take?: number;
    cursor?: Prisma.user_rolesWhereUniqueInput;
    where?: Prisma.user_rolesWhereInput;
    orderBy?: Prisma.user_rolesOrderByWithAggregationInput;
  }) {
    const { skip, take, cursor, where, orderBy } = params;
    return await this.prisma.user_roles.findMany({
      skip,
      take,
      cursor,
      where,
      orderBy,
      include: { roles: true, users: true },
    });
  }

  async createUserRolesWithUserId(user_id: number) {
    try {
      const roles = await this.prisma.roles.findFirst({
        where: { name: Role.Patient },
      });

      if (!roles?.role_id) {
        throw new BadRequestException('Role not found!');
      }

      const createUserRoleData = {
        roles: { connect: { role_id: roles.role_id } },
        users: { connect: { user_id: user_id } },
      };

      await this.createUserRole(createUserRoleData);
    } catch (error) {
      throw new BadRequestException(error || 'Please try again');
    }
  }
}
