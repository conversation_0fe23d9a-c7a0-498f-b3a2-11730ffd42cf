import { BadRequestException, Injectable } from '@nestjs/common';
import { PrismaService, Prisma } from '@core/prisma-client';
import { OnboardingStatus, Role } from '@core_be/global';

@Injectable()
export class UsersRepository {
  private repo: Prisma.usersDelegate;
  constructor(readonly prisma: PrismaService) {
    this.repo = this.prisma.users;
  }

  async create(
    data: Prisma.usersCreateInput,
    prisma?: Prisma.TransactionClient
  ) {
    const client = prisma || this.prisma;
    return client.users.create({ data });
  }

  async update(params: {
    where: Prisma.usersWhereUniqueInput;
    data: Prisma.usersUpdateInput;
  }) {
    const { where, data } = params;
    return this.repo.update({ data, where });
  }

  async hardDelete(where: Prisma.usersWhereUniqueInput) {
    return this.repo.delete({ where });
  }

  async delete(where: Prisma.usersWhereUniqueInput, data: any) {
    return this.repo.update({ data, where });
  }

  async findById(user_id: number) {
    return this.repo.findFirst({ where: { user_id } });
  }

  async findOne(
    where: Prisma.usersWhereUniqueInput,
    include?: Prisma.usersInclude
  ) {
    return this.repo.findUnique({
      where,
      include: include || {
        profiles: true,
        subscriptions: true,
        user_devices: true,
        user_roles: true,
      },
    });
  }

  async findAll(params: {
    skip?: number;
    limit?: number;
    cursor?: Prisma.usersWhereUniqueInput;
    where?: Prisma.usersWhereInput;
    orderBy?: Prisma.usersOrderByWithAggregationInput;
    include?: any;
  }) {
    const { skip, limit, cursor, where, orderBy, include } = params;
    return this.repo.findMany({
      skip,
      take: limit,
      cursor,
      where,
      orderBy,
      include,
    });
  }

  async count(params: { where?: Prisma.usersWhereInput }) {
    const { where } = params;
    return this.repo.count({ where });
  }

  async findLast() {
    // Assuming your User entity has a createdAt field to order by
    return this.repo.findFirst({
      orderBy: { created_at: 'desc' }, // Get the last user based on createdAt field
    });
  }

  async findFirst(params: {
    where: Prisma.usersWhereInput;
    include?: Prisma.usersInclude;
  }) {
    const { where, include } = params;
    return this.repo.findFirst({ where, include });
  }

  // async findByName_REMOVED(username: string) {
  //   const data = this.repo.findUnique({
  //     where: {
  //       username: username,
  //       is_deleted: false,
  //     },
  //   });
  //   return data;
  // }

  // async createPatient(data: Prisma.patientCreateInput) {
  //   return this.prisma.patient.create({ data });
  // }

  // async getUserProfiles_REMOVED(where: any) {
  //   return this.repo.findUnique(where);
  // }

  // async userProfilesByUserId_REMOVED(params: {
  //   userId: Prisma.IntFilter<'UserProfile'>;
  // }) {
  //   const { userId } = params;
  //   // return this.prisma.patient.findMany({
  //   //   //where: { user_id: userId },
  //   // });
  // }

  async findUserRole(id: number, prisma?: Prisma.TransactionClient) {
    const client = prisma || this.prisma;
    return await client.users.findUnique({
      where: { user_id: id },
      include: {
        user_roles: {
          include: {
            roles: true,
          },
        },
      },
    });
  }

  async findUserByToken(token: string) {
    return this.repo.findFirst({ where: { password_reset_token: token } });
  }

  async findUserWithValidResetToken(email: string) {
    return this.repo.findFirst({
      where: {
        email: {
          equals: email.toLowerCase(),
          mode: 'insensitive',
        },
        password_reset_token_expiry: {
          gt: new Date(),
        },
      },
    });
  }

  async findOneByEmail(email: string) {
    return this.repo.findFirst({
      where: {
        email: {
          equals: email.toLowerCase(),
          mode: 'insensitive',
        },
      },
      include: {
        profiles: true,
        subscriptions: true,
        user_devices: true,
        user_roles: true,
      },
    });
  }

  async findOneByUsername(username: string) {
    return this.repo.findFirst({
      where: {
        username: {
          equals: username.toLowerCase(),
          mode: 'insensitive',
        },
      },
      include: {
        profiles: true,
        subscriptions: true,
        user_devices: true,
        user_roles: true,
      },
    });
  }
}
