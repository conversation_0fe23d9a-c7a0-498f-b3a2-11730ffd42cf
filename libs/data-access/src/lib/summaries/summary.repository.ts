import { Prisma, PrismaService } from '@core/prisma-client';
import { Injectable } from '@nestjs/common';

@Injectable()
export class SummaryRepository {
  private repo: Prisma.session_summariesDelegate;

  constructor(readonly prisma: PrismaService) {
    this.repo = this.prisma.session_summaries;
  }

  async createUnchecked(data: Prisma.session_summariesUncheckedCreateInput) {
    return this.repo.create({ data });
  }

  async create(data: Prisma.session_summariesCreateInput) {
    const summary = await this.repo.create({
      data,
      select: { summary_id: true },
    });
    return summary.summary_id;
  }

  async findAll(params: {
    skip?: number;
    limit?: number;
    cursor?: Prisma.session_summariesWhereUniqueInput;
    where?: Prisma.session_summariesWhereInput;
    orderBy?: Prisma.session_summariesOrderByWithAggregationInput;
  }) {
    const { skip, limit, cursor, where, orderBy } = params;
    return this.repo.findMany({
      skip,
      take: limit,
      cursor,
      where,
      orderBy,
    });
  }

  async findOne(
    where: Prisma.session_summariesWhereUniqueInput,
    include?: Prisma.session_summariesInclude
  ) {
    return this.repo.findUnique({
      where,
      include: include || {
        healing_sessions: true,
        healing_rounds: true,
        session_ailments: true,
      },
    });
  }

  async findFirst(params: {
    where: Prisma.session_summariesWhereInput;
    include?: Prisma.session_summariesInclude;
    orderBy?: Prisma.session_summariesOrderByWithAggregationInput;
  }) {
    const { where, orderBy, include } = params;

    return this.repo.findFirst({
      where,
      orderBy: orderBy || {
        created_at: 'desc',
      },
      include: include || {
        healing_sessions: true,
        healing_rounds: true,
        session_ailments: true,
      },
    });
  }

  async getLatestSessionSummary(params: {
    where: Prisma.session_summariesWhereInput;
    orderBy?: Prisma.session_summariesOrderByWithAggregationInput;
  }) {
    const { where, orderBy } = params;
    return this.repo.findFirst({
      where,
      orderBy: orderBy || {
        created_at: 'desc',
      },
    });
  }

  async findFirstSelect(params: {
    where: Prisma.session_summariesWhereInput;
    orderBy?: Prisma.session_summariesOrderByWithAggregationInput;
    select?: Prisma.session_summariesSelect;
  }) {
    const { where, orderBy, select } = params;
    return this.repo.findFirst({
      where,
      orderBy: orderBy || {
        created_at: 'desc',
      },
      select: select || {
        healing_rounds: true,
        session_ailments: true,
        healing_sessions: true,
      },
    });
  }

  async update(params: {
    session_id: number;
    data: Prisma.session_summariesUpdateInput;
  }): Promise<number> {
    const { session_id, data } = params;
    const record = await this.repo.findFirst({
      where: { session_id },
      select: { summary_id: true },
    });
    if (!record) {
      throw new Error(`Record with session_id ${session_id} not found.`);
    }
    await this.repo.update({
      where: { summary_id: record.summary_id },
      data,
    });
    return record.summary_id;
  }
}
