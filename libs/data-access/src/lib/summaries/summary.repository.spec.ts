import { Test, TestingModule } from '@nestjs/testing';
import { SummaryRepository } from './summary.repository';

describe('SummaryService', () => {
  let service: SummaryRepository;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [SummaryRepository],
    }).compile();

    service = module.get<SummaryRepository>(SummaryRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
