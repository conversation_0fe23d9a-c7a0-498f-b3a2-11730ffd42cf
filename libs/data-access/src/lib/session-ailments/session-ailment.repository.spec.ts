import { Test, TestingModule } from '@nestjs/testing';
import { SessionAilmentRepository } from './session-ailment.repository';
import { beforeEach, describe, it } from 'node:test';

describe('SessionAilmentRepository', () => {
  let service: SessionAilmentRepository;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [SessionAilmentRepository],
    }).compile();

    service = module.get<SessionAilmentRepository>(SessionAilmentRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
