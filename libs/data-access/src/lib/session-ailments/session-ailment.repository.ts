import { Prisma, PrismaService } from '@core/prisma-client';
import { Injectable } from '@nestjs/common';
import { AilmentsDto } from '@core_be/global';

type SessionAilment = {
  session_ailment_id: number;
  round_id: number | null;
  name: string;
  level: number;
};
@Injectable()
export class SessionAilmentRepository {
  private repo: Prisma.session_ailmentsDelegate;
  public ailmentTransitions: AilmentsDto[] = [];
  constructor(readonly prisma: PrismaService) {
    this.repo = this.prisma.session_ailments;
  }

  async create(data: Prisma.session_ailmentsUncheckedCreateInput) {
    return this.repo.create({ data });
  }

  async findAll(params: {
    skip?: number;
    limit?: number;
    take?: number;
    cursor?: Prisma.session_ailmentsWhereUniqueInput;
    where?: Prisma.session_ailmentsWhereInput;
    orderBy?: Prisma.session_ailmentsOrderByWithAggregationInput;
    select?: Prisma.session_ailmentsSelect;
  }) {
    const { skip, limit, take, cursor, where, orderBy, select } = params;
    return this.repo.findMany({
      skip,
      take: limit || take,
      cursor,
      where,
      orderBy,
      select,
    });
  }

  async findAllWithInclude(params: {
    skip?: number;
    limit?: number;
    take?: number;
    cursor?: Prisma.session_ailmentsWhereUniqueInput;
    where?: Prisma.session_ailmentsWhereInput;
    orderBy?: Prisma.session_ailmentsOrderByWithAggregationInput;
    include?: Prisma.session_ailmentsInclude;
  }) {
    const { skip, limit, take, cursor, where, orderBy, include } = params;
    return this.repo.findMany({
      skip,
      take: limit || take,
      cursor,
      where,
      orderBy,
      include,
    });
  }

  async findOneWithInclude(params: {
    where: Prisma.session_ailmentsWhereInput;
    include?: Prisma.session_ailmentsInclude;
  }) {
    const { where, include } = params;
    return this.repo.findFirst({
      where,
      include: include || {
        healing_sessions: true,
        session_summaries: true,
        healing_rounds: true,
      },
    });
  }

  async findOneWithIncludeUnique(params: {
    where: Prisma.session_ailmentsWhereUniqueInput;
    include?: Prisma.session_ailmentsInclude;
  }) {
    const { where, include } = params;
    return this.repo.findUnique({
      where,
      include: include || {
        healing_sessions: true,
        session_summaries: true,
        healing_rounds: true,
      },
    });
  }

  async findOneWithSelectUnique(params: {
    where: Prisma.session_ailmentsWhereUniqueInput;
    select?: Prisma.session_ailmentsSelect;
  }) {
    const { where, select } = params;
    return this.repo.findUnique({
      where,
      select,
    });
  }

  async findOneWithSelect(params: {
    where: Prisma.session_ailmentsWhereInput;
    select?: Prisma.session_ailmentsSelect;
  }) {
    const { where, select } = params;
    return this.repo.findFirst({
      where,
      select,
    });
  }

  async findFirst(params: {
    where: Prisma.session_ailmentsWhereInput;
    orderBy?: Prisma.session_ailmentsOrderByWithAggregationInput;
    include?: Prisma.session_ailmentsInclude;
  }) {
    const { where, orderBy, include } = params;
    return this.repo.findFirst({
      where,
      orderBy: orderBy || {
        created_at: 'desc',
      },
      include: include || {
        healing_sessions: true,
        session_summaries: true,
        healing_rounds: true,
      },
    });
  }

  async aggregate(aggregate: Prisma.Session_ailmentsAggregateArgs) {
    return this.repo.aggregate(aggregate);
  }

  async update(params: {
    where: Prisma.session_ailmentsWhereUniqueInput;
    data: Prisma.session_ailmentsUpdateInput;
  }) {
    const { where, data } = params;
    return this.repo.update({ data, where });
  }

  async findAilments(where: Prisma.session_ailmentsWhereInput) {
    const results = await this.repo.findMany({
      where,
      orderBy: { name: 'asc' },
      select: {
        session_ailment_id: true,
        name: true,
        level: true,
        description: true,
        round_id: true,
      },
    });

    if (!results || results.length === 0) {
      return [];
    }

    this.ailmentTransitions = [];
    let previousLevel = 0;
    const sortedGroups: typeof results = [];
    const grouped = results.reduce((groups, record) => {
      if (!groups[record.name.toLowerCase()])
        groups[record.name.toLowerCase()] = [];
      groups[record.name.toLowerCase()].push(record);
      return groups;
    }, {} as Record<string, typeof results>);
    Object.entries(grouped).forEach(([name, group]) => {
      const sortedGroup = group.sort(
        (a, b) => a.session_ailment_id - b.session_ailment_id
      );
      if (sortedGroup[0].round_id != null) {
        let level = 0;
        for (let i = 0; i < sortedGroup.length; i++) {
          this.getAilments(sortedGroup[i], level, sortedGroup[i].level);
          level = sortedGroup[i].level;
        }
      } else {
        sortedGroups.push(...sortedGroup);
      }
    });
    sortedGroups.forEach((current, index) => {
      const next = sortedGroups[index + 1];
      if (!current.round_id) {
        this.getAilments(current, current.level, 0);
        previousLevel = current.level || 0;
      } else if (
        (next && next.round_id !== current.round_id) ||
        (next && next.name.toLowerCase() !== current.name.toLowerCase())
      ) {
        this.getAilments(current, previousLevel, current.level);
        previousLevel = current.level || 0;
      } else if (
        next &&
        next.round_id === current.round_id + 1 &&
        next.name.toLowerCase() === current.name.toLowerCase()
      ) {
        this.getAilments(current, current.level, next.level);
        previousLevel = next.level || 0;
      } else {
        const lastRecord = sortedGroups[sortedGroups.length - 1];
        if (lastRecord) {
          this.getAilments(lastRecord, previousLevel, lastRecord.level);
        }
      }
    });

    this.ailmentTransitions = Object.values(
      this.ailmentTransitions.reduce((acc, current) => {
        const key = current.name.toLowerCase();
        if (
          !acc[key] ||
          acc[key].session_ailment_id < current.session_ailment_id
        ) {
          acc[key] = current;
        }
        return acc;
      }, {} as Record<string, AilmentsDto>)
    );

    return this.ailmentTransitions;
  }

  getAilments(
    current: AilmentsDto,
    pain_level_before: number,
    pain_level_after: number
  ) {
    this.ailmentTransitions.push({
      session_ailment_id: current.session_ailment_id,
      name: current.name,
      description: current.description || '',
      pain_level_before,
      pain_level_after,
    });
  }

  //we will remove this method after use of compareRoundFeedback()
  async getRoundStatus(session_id: number, round_id: number): Promise<boolean> {
    const results = await this.repo.findMany({
      where: {
        session_id,
      },
      orderBy: { name: 'asc' },
      select: {
        session_ailment_id: true,
        name: true,
        level: true,
        description: true,
        round_id: true,
        created_at: true,
      },
    });

    let status = false;
    const updatedList = results.filter((record) => {
      return record.round_id === null || record.round_id <= round_id;
    });

    if (!updatedList || updatedList.length === 0) {
      return false;
    }

    const grouped = updatedList.reduce((groups, record) => {
      if (!groups[record.name.toLowerCase()])
        groups[record.name.toLowerCase()] = [];
      groups[record.name.toLowerCase()].push(record);
      return groups;
    }, {} as Record<string, typeof updatedList>);

    const entries = Object.entries(grouped);
    for (let i = 0, iL = entries.length; i < iL; i++) {
      const [name, group] = entries[i];
      const sortedGroup = group.sort((a, b) => {
        if (a.round_id === null) return -1;
        if (b.round_id === null) return 1;
        return a.round_id! - b.round_id!;
      });

      const currentRound = sortedGroup.find(
        (record) => record.round_id === round_id
      );
      if (!currentRound) {
        continue;
      }
      const previousRound = sortedGroup
        .filter((record) => record.created_at !== null)
        .sort((a, b) => {
          const dateA = new Date(a.created_at as any);
          const dateB = new Date(b.created_at as any);
          return dateB.getTime() - dateA.getTime();
        })[1];

      const roundStatus =
        currentRound &&
        previousRound &&
        currentRound.level < previousRound.level
          ? true
          : false;

      if (roundStatus) {
        status = true;
        break;
      }
    }
    return status;
  }

  async compareRoundFeedback(
    session_id: number,
    round_id: number
  ): Promise<boolean> {
    const sessionAilments = await this.repo.findMany({
      where: {
        session_id,
      },
      orderBy: { session_ailment_id: 'asc' },
      select: {
        session_ailment_id: true,
        name: true,
        level: true,
        round_id: true,
      },
    });
    if (!sessionAilments) {
      return false;
    }

    const selectedRoundRecords = sessionAilments.filter(
      (record) => record.round_id === round_id
    );
    const previousRoundId = this.findPreviousRoundId(sessionAilments, round_id);
    const previousRoundRecords = sessionAilments.filter(
      (record) => record.round_id === previousRoundId
    );

    for (const selectedRecord of selectedRoundRecords) {
      const matchingPreviousRecord = previousRoundRecords.find(
        (previousRecord) =>
          previousRecord.name.toLowerCase() ===
          selectedRecord.name.toLowerCase()
      );

      if (
        matchingPreviousRecord &&
        selectedRecord.level < matchingPreviousRecord.level
      ) {
        return true;
      }
    }
    return false;
  }

  private findPreviousRoundId(
    sessionAilments: SessionAilment[],
    selectedRoundId: number
  ): number | null {
    const roundIds = [
      ...new Set(sessionAilments.map((record) => record.round_id)),
    ];
    const selectedIndex = roundIds.indexOf(selectedRoundId);

    if (selectedIndex === 0) {
      return null;
    }
    return roundIds[selectedIndex - 1];
  }

  async checkAilmentsPainZero(summaryContent: string): Promise<boolean> {
    const ailments: Record<string, number> = {};

    const lines = summaryContent.split('\n');

    for (const line of lines) {
      const match = line.match(/-\s*([^:]+):\s*(\d+)/i);
      if (match) {
        const ailment = match[1].trim();
        const value = Number(match[2]);
        ailments[ailment] = value;
      }
    }

    const allZero = Object.values(ailments).every((value) => value === 0);

    return allZero;
  }
}
