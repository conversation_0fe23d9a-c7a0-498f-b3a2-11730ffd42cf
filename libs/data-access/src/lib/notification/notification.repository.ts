import { Prisma, PrismaService } from '@core/prisma-client';
import { Injectable } from '@nestjs/common';

@Injectable()
export class NotificationRepository {
  private readonly repo: Prisma.notificationDelegate;
  constructor(readonly prisma: PrismaService) {
    this.repo = this.prisma.notification;
  }

  async create(data: Prisma.notificationCreateInput) {
    return this.repo.create({ data });
  }

  async update(
    where: Prisma.notificationWhereUniqueInput,
    data: Prisma.notificationUpdateInput
  ) {
    return this.repo.update({ data, where });
  }

  async findFirst(where: Prisma.notificationWhereInput) {
    return this.repo.findFirst({ where });
  }

  async findMany(
    where: Prisma.notificationWhereInput,
    orderBy?: Prisma.notificationOrderByWithAggregationInput,
    select?: Prisma.notificationSelect
  ) {
    return this.repo.findMany({ where, orderBy, select });
  }

  async findOne(
    where: Prisma.notificationWhereUniqueInput,
    include?: Prisma.notificationInclude
  ) {
    return this.repo.findUnique({
      where,
      include: include || {
        notification_topic: true,
        notification_recipients: true,
      },
    });
  }

  async delete(where: Prisma.notificationWhereUniqueInput, data: Prisma.notificationUpdateInput) {
    return this.repo.update({ data, where });
  }

  async findAll(params: {
    skip?: number;
    limit?: number;
    cursor?: Prisma.notificationWhereUniqueInput;
    where?: Prisma.notificationWhereInput;
    orderBy?: Prisma.notificationOrderByWithAggregationInput;
    include?: any;
  }) {
    const { skip, limit, cursor, where, orderBy, include } = params;
    return this.repo.findMany({
      skip,
      take: limit,
      cursor,
      where,
      orderBy,
      include,
    });
  }

  async createManyRecipients(
    data: Prisma.notification_recipientsCreateManyInput[]
  ) {
    return this.prisma.notification_recipients.createMany({
      data,
      skipDuplicates: true,
    });
  }

  async createRecipients(data: Prisma.notification_recipientsCreateInput) {
    return this.prisma.notification_recipients.create({ data });
  }

  async updateRecipients(
    where: Prisma.notification_recipientsWhereUniqueInput,
    data: Prisma.notification_recipientsUpdateInput
  ) {
    return this.prisma.notification_recipients.update({
      data,
      where,
    });
  }

  async updateManyRecipients(
    where: Prisma.notification_recipientsWhereInput,
    data: Prisma.notification_recipientsUpdateManyMutationInput
  ) {
    return this.prisma.notification_recipients.updateMany({
      where,
      data
    });
  }

  async findFirstRecipients(where: Prisma.notification_recipientsWhereInput) {
    return this.prisma.notification_recipients.findFirst({ where });
  }

  async findManyRecipients(
    where: Prisma.notification_recipientsWhereInput,
    orderBy?: Prisma.notification_recipientsOrderByWithAggregationInput,
    select?: Prisma.notification_recipientsSelect
  ) {
    return this.prisma.notification_recipients.findMany({ where, orderBy, select });
  }

  async deleteManyRecipients(
    where: Prisma.notification_recipientsWhereInput,
    data: Prisma.notification_recipientsUpdateManyMutationInput
  ) {
    return this.prisma.notification_recipients.updateMany({
      where,
      data
    });
  }

  async count(params: { where?: Prisma.notificationWhereInput }) {
    const { where } = params;
    return this.repo.count({ where });
  }
}
