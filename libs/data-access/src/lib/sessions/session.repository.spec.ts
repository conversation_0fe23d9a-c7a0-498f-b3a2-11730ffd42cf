import { Test, TestingModule } from '@nestjs/testing';
import { SessionRepository } from './session.repository';

describe('SessionService', () => {
  let service: SessionRepository;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [SessionRepository],
    }).compile();

    service = module.get<SessionRepository>(SessionRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
