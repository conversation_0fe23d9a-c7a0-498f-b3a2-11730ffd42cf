import { Prisma, PrismaService } from '@core/prisma-client';
import { Injectable } from '@nestjs/common';
import { DefaultArgs } from '@prisma/client/runtime/library';
import { SessionResponseDto } from '@core_be/global';

@Injectable()
export class SessionRepository {
  private repo: Prisma.healing_sessionsDelegate;

  constructor(readonly prisma: PrismaService) {
    this.repo = this.prisma.healing_sessions;
  }

  async create(data: Prisma.healing_sessionsUncheckedCreateInput) {
    return this.repo.create({ data });
  }

  async findAll(params: {
    skip?: number;
    limit?: number;
    take?: number;
    cursor?: Prisma.healing_sessionsWhereUniqueInput;
    where?: Prisma.healing_sessionsWhereInput;
    orderBy?: Prisma.healing_sessionsOrderByWithAggregationInput;
    select?: Prisma.healing_sessionsSelect;
  }) {
    const { skip, limit, take, cursor, where, orderBy, select } = params;
    return this.repo.findMany({
      skip,
      take: limit || take,
      cursor,
      where,
      orderBy,
      select,
    });
  }

  async findAllWithInclude(params: {
    skip?: number;
    limit?: number;
    take?: number;
    cursor?: Prisma.healing_sessionsWhereUniqueInput;
    where?: Prisma.healing_sessionsWhereInput;
    orderBy?: Prisma.healing_sessionsOrderByWithAggregationInput;
    include?: Prisma.healing_sessionsInclude;
  }) {
    const { skip, limit, take, cursor, where, orderBy, include } = params;
    return this.repo.findMany({
      skip,
      take: limit || take,
      cursor,
      where,
      orderBy,
      include,
    });
  }

  async findOneWithInclude(params: {
    where: Prisma.healing_sessionsWhereInput;
    include?: Prisma.healing_sessionsInclude;
  }) {
    const { where, include } = params;
    return this.repo.findFirst({
      where,
      include: include || {
        conversations: true,
      },
    });
  }

  async findOneWithIncludeUnique(params: {
    where: Prisma.healing_sessionsWhereUniqueInput;
    include?: Prisma.healing_sessionsInclude;
  }) {
    const { where, include } = params;
    return this.repo.findUnique({
      where,
      include: include || {
        conversations: true,
      },
    });
  }

  async findOneWithSelectUnique(params: Prisma.healing_sessionsFindUniqueArgs) {
    return this.repo.findUnique(params);
  }

  async findOneWithSelect(params: {
    where: Prisma.healing_sessionsWhereInput;
    select?: Prisma.healing_sessionsSelect;
  }) {
    const { where, select } = params;
    return this.repo.findFirst({
      where,
      select,
    });
  }

  async getSessionInfo(params: {
    where: Prisma.healing_sessionsWhereInput;
    orderBy?: Prisma.healing_sessionsOrderByWithAggregationInput;
    include?: Prisma.healing_sessionsInclude;
  }) {
    const { where, orderBy } = params;
    return this.repo.findFirst({
      where,
      orderBy: orderBy || {
        created_at: 'desc',
      },
    });
  }

  async findFirst(params: {
    where: Prisma.healing_sessionsWhereInput;
    orderBy?: Prisma.healing_sessionsOrderByWithAggregationInput;
    include?: Prisma.healing_sessionsInclude;
  }) {
    const { where, orderBy, include } = params;
    return this.repo.findFirst({
      where,
      orderBy: orderBy || {
        created_at: 'desc',
      },
      include: include || {
        conversations: true,
        session_summaries: true,
        profiles: true,
        healing_rounds: true,
        session_ailments: true,
      },
    });
  }

  async aggregate(aggregate: Prisma.Healing_sessionsAggregateArgs) {
    return this.repo.aggregate(aggregate);
  }

  async update(params: {
    where: Prisma.healing_sessionsWhereUniqueInput;
    data: Prisma.healing_sessionsUpdateInput;
  }) {
    const { where, data } = params;
    return this.repo.update({ where, data });
  }

  async updateMany(params: {
    where: Prisma.healing_sessionsWhereInput;
    data: Prisma.healing_sessionsUpdateInput;
  }) {
    const { where, data } = params;
    return this.repo.updateMany({ where, data });
  }

  async updateById(params: {
    session_id: number;
    data: Prisma.healing_sessionsUpdateInput;
  }) {
    const { data, session_id } = params;
    return this.repo.update({ data, where: { session_id } });
  }

  async count(params: { where?: Prisma.healing_sessionsWhereInput }) {
    const { where } = params;
    return this.repo.count({ where });
  }

  async getHealingVideos() {
    const data = {
      videos: [
        {
          id: 1,
          title: 'Peace on the dance floor.',
          thumbnail: 'https://img.youtube.com/vi/I96dmnJ2K8M/hqdefault.jpg',
          duration: '01:15',
          url: 'https://www.youtube.com/embed/I96dmnJ2K8M?si=2rngX4A8wIeLoZCn',
          video_id: 'I96dmnJ2K8M',
        },
        {
          id: 2,
          title: 'Charlie Goldsmith Live healing event 2022.',
          thumbnail: 'https://img.youtube.com/vi/xwXnaeBKR-w/hqdefault.jpg',
          duration: '13:03',
          url: 'https://www.youtube.com/embed/xwXnaeBKR-w?si=_RqaNydwI9RQPJJY',
          video_id: 'xwXnaeBKR-w',
        },
        {
          id: 3,
          title:
            'A method to process anxiety by Charlie Goldsmith from The Healer.',
          thumbnail: 'https://img.youtube.com/vi/caYz69aWyCo/hqdefault.jpg',
          duration: '45:37',
          url: 'https://www.youtube.com/embed/caYz69aWyCo?si=ZdZwRVSIOqam0fP1',
          video_id: 'caYz69aWyCo',
        },
        {
          id: 4,
          title: 'Charlie Goldsmith on the Parenting Together Apart Podcast',
          thumbnail: 'https://img.youtube.com/vi/gXHN_BScPUM/hqdefault.jpg',
          duration: '31:07',
          url: 'https://www.youtube.com/embed/gXHN_BScPUM?si=tPdTOroIk8tDkE8t',
          video_id: 'gXHN_BScPUM',
        },
        {
          id: 5,
          title: 'The Healer Trailer - Charlie Goldsmith',
          thumbnail: 'https://img.youtube.com/vi/klMNe2ZOlyY/hqdefault.jpg',
          duration: '02:06',
          url: 'https://www.youtube.com/embed/klMNe2ZOlyY?si=KLaPcFFpfhT5bten',
          video_id: 'klMNe2ZOlyY',
        },
      ],
    };

    return data;
  }
}
