import { Injectable } from '@nestjs/common';
import { PrismaService, Prisma } from '@core/prisma-client';

@Injectable()
export class ConversationRepository {
  private repo: Prisma.conversationsDelegate;

  constructor(readonly prisma: PrismaService) {
    this.repo = this.prisma.conversations;
  }

  async create(data: Prisma.conversationsCreateInput) {
    return this.repo.create({ data });
  }

  async createMany(params: {
    data: Prisma.conversationsCreateManyInput[];
    skipDuplicates?: boolean;
  }): Promise<any> {
    try {
      return await this.repo.createMany({
        data: params.data,
        skipDuplicates: params.skipDuplicates,
      });
    } catch (error: any) {
      throw new Error(`Failed to create conversations: ${error.message}`);
    }
  }

  async findAll(params: {
    skip?: number;
    limit?: number;
    take?: number;
    cursor?: Prisma.conversationsWhereUniqueInput;
    where?: Prisma.conversationsWhereInput;
    select?: Prisma.conversationsSelect;
    orderBy?: Prisma.conversationsOrderByWithAggregationInput;
  }) {
    const { skip, limit, cursor, where, select, orderBy, take } = params;
    return this.repo.findMany({
      skip,
      take: limit || take,
      cursor,
      where,
      select,
      orderBy,
    });
  }

  async findOneWithInclude(params: {
    where: Prisma.conversationsWhereUniqueInput;
    include?: Prisma.conversationsInclude;
  }) {
    const { where, include } = params;
    return this.repo.findUnique({
      where,
      include: include || {
        healing_sessions: true,
        healing_rounds: true,
        profiles: true,
        openai_models: true,
      },
    });
  }

  async findOneWithSelect(params: {
    where: Prisma.conversationsWhereUniqueInput;
    select?: Prisma.conversationsSelect;
  }) {
    const { where, select } = params;
    return this.repo.findUnique({
      where,
      select,
    });
  }

  async findFirst(params: {
    where: Prisma.conversationsWhereInput;
    include?: Prisma.conversationsInclude;
  }) {
    const { where, include } = params;
    return this.repo.findFirst({
      where,
      include,
    });
  }

  async update(params: {
    where: Prisma.conversationsWhereUniqueInput;
    data: Prisma.conversationsUpdateInput;
  }) {
    const { where, data } = params;
    return this.repo.update({ data, where });
  }

  async count(params: { where?: Prisma.conversationsWhereInput }) {
    const { where } = params;
    return this.repo.count({ where });
  }
}
