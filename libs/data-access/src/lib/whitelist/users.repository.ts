import { Injectable } from '@nestjs/common';
import { PrismaService, Prisma } from '@core/prisma-client';

@Injectable()
export class UsersRepository {
  private repo: Prisma.usersDelegate;
  constructor(readonly prisma: PrismaService) {
    this.repo = this.prisma.users;
  }

  async create(data: Prisma.usersCreateInput) {
    return this.repo.create({ data });
  }

  async update(params: {
    where: Prisma.usersWhereUniqueInput;
    data: Prisma.usersUpdateInput;
  }) {
    const { where, data } = params;
    return this.repo.update({ data, where });
  }

  async delete(where: Prisma.usersWhereUniqueInput, data: any) {
    return this.repo.update({ data, where });
  }

  async findById(user_id: number) {
    return this.repo.findFirst({ where: { user_id } });
  }

  async findOne(
    where: Prisma.usersWhereUniqueInput,
    include?: Prisma.usersInclude
  ) {
    return this.repo.findUnique({
      where,
      include: include || {
        profiles: true,
        subscriptions: true,
        user_devices: true,
        user_roles: true,
      },
    });
  }

  async findAll(params: {
    skip?: number;
    limit?: number;
    cursor?: Prisma.usersWhereUniqueInput;
    where?: Prisma.usersWhereInput;
    orderBy?: Prisma.usersOrderByWithAggregationInput;
  }) {
    const { skip, limit, cursor, where, orderBy } = params;
    return this.repo.findMany({
      skip,
      take: limit,
      cursor,
      where,
      orderBy,
    });
  }

  async count(params: { where?: Prisma.usersWhereInput }) {
    const { where } = params;
    return this.repo.count({ where });
  }

  async findLast() {
    // Assuming your User entity has a createdAt field to order by
    return this.repo.findFirst({
      orderBy: { created_at: 'desc' }, // Get the last user based on createdAt field
    });
  }

  async findByName_REMOVED(username: string) {
    const data = this.repo.findUnique({
      where: {
        username: username,
        is_deleted: false,
      },
    });
    return data;
  }

  // async createPatient(data: Prisma.patientCreateInput) {
  //   return this.prisma.patient.create({ data });
  // }

  async getUserProfiles_REMOVED(where: any) {
    return this.repo.findUnique(where);
  }

  async userProfilesByUserId_REMOVED(params: {
    userId: Prisma.IntFilter<'UserProfile'>;
  }) {
    const { userId } = params;
    // return this.prisma.patient.findMany({
    //   //where: { user_id: userId },
    // });
  }

  // need to resolve this - we dont need this as findOne will do the job - fix the auth.service
  async userRoleOne(where: any) {
    return await this.repo.findUnique({
      where,
      include: {
        user_roles: {
          include: {
            roles: true,
          },
        },
      },
    });
  }

  async findUserByToken(token: string) {
    return this.repo.findFirst({ where: { password_reset_token: token } });
  }
}
