import { Prisma, PrismaService } from '@core/prisma-client';
import { Injectable } from '@nestjs/common';

@Injectable()
export class WhitelistRepository {
  private readonly repo: Prisma.whitelistDelegate;
  constructor(readonly prisma: PrismaService) {
    this.repo = this.prisma.whitelist;
  }
  async findOne(email: string): Promise<boolean> {
    const result = await this.repo.findFirst({
      where: {
        entity_type: 'email',
        entity_value: email,
        is_deleted: false,
        status: 'active',
      },
    });
    return !!result;
  }
}
