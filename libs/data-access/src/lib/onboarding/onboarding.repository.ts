import { Prisma, PrismaService } from '@core/prisma-client';
import { Injectable } from '@nestjs/common';
import { OnboardingStatus } from '@core_be/global';

@Injectable()
export class OnBoardingRepository {
  private readonly repo: Prisma.onboarding_statusDelegate;
  constructor(readonly prisma: PrismaService) {
    this.repo = this.prisma.onboarding_status;
  }

  async createOnBoarding(data: Prisma.onboarding_statusCreateInput,prisma?: Prisma.TransactionClient) {
    const client = prisma || this.prisma;
    return client.onboarding_status.create({
      data: { ...data, status: OnboardingStatus.PENDING },
    });
  }

  async updateManyOnBoarding(params: {
    where: any;
    data: Prisma.onboarding_statusUpdateInput;
  }) {
    const { where, data } = params;
    return this.prisma.onboarding_status.updateMany({
      where,
      data,
    });
  }

  async getDefaultUserOnBoarding(where: Prisma.onboarding_statusWhereInput) {
    return this.prisma.onboarding_status.findFirst({ where });
  }

  async deleteOnBoarding(where: any) {
    return this.prisma.onboarding_status.delete({ where });
  }
}
