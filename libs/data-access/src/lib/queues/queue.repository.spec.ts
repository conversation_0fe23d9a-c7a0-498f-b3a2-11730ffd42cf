import { Test, TestingModule } from '@nestjs/testing';
import { QueueRepository } from './queue.repository';

describe('QueueService', () => {
  let service: QueueRepository;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [QueueRepository],
    }).compile();

    service = module.get<QueueRepository>(QueueRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
