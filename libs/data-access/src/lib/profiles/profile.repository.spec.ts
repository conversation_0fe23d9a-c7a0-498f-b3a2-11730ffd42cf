import { Test, TestingModule } from '@nestjs/testing';
import { ProfileRepository } from './profile.repository';
import { beforeEach, describe, it } from 'node:test';

describe('ProfileService', () => {
  let service: ProfileRepository;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ProfileRepository],
    }).compile();

    service = module.get<ProfileRepository>(ProfileRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
