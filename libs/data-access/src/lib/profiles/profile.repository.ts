import { Prisma, PrismaService } from '@core/prisma-client';
import { Injectable } from '@nestjs/common';

@Injectable()
export class ProfileRepository {
  private repo: Prisma.profilesDelegate;

  constructor(readonly prisma: PrismaService) {
    this.repo = this.prisma.profiles;
  }

  async create(data: Prisma.profilesUncheckedCreateInput) {
    return this.repo.create({ data });
  }

  async findAll(params: {
    skip?: number;
    limit?: number;
    take?: number;
    cursor?: Prisma.profilesWhereUniqueInput;
    where?: Prisma.profilesWhereInput;
    orderBy?: Prisma.profilesOrderByWithAggregationInput;
    select?: Prisma.profilesSelect;
  }) {
    const { skip, limit, take, cursor, where, orderBy, select } = params;
    return this.repo.findMany({
      skip,
      take: limit || take,
      cursor,
      where,
      orderBy,
      select,
    });
  }

  async findAllWithInclude(params: {
    skip?: number;
    limit?: number;
    take?: number;
    cursor?: Prisma.profilesWhereUniqueInput;
    where?: Prisma.profilesWhereInput;
    orderBy?: Prisma.profilesOrderByWithAggregationInput;
    include?: Prisma.profilesInclude;
  }) {
    const { skip, limit, take, cursor, where, orderBy, include } = params;
    return this.repo.findMany({
      skip,
      take: limit || take,
      cursor,
      where,
      orderBy,
      include,
    });
  }

  async findOneWithInclude(params: {
    where: Prisma.profilesWhereInput;
    include?: Prisma.profilesInclude;
  }) {
    const { where, include } = params;
    return this.repo.findFirst({
      where,
      include: include || {
        conversations: true,
      },
    });
  }

  async findOneWithIncludeUnique(params: {
    where: Prisma.profilesWhereUniqueInput;
    include?: Prisma.profilesInclude;
  }) {
    const { where, include } = params;
    return this.repo.findUnique({
      where,
      include: include || {
        conversations: true,
      },
    });
  }

  async findOneWithSelectUnique(params: {
    where: Prisma.profilesWhereUniqueInput;
    select?: Prisma.profilesSelect;
  }) {
    const { where, select } = params;
    return this.repo.findUnique({
      where,
      select,
    });
  }

  async findOneWithSelect(params: {
    where: Prisma.profilesWhereInput;
    select?: Prisma.profilesSelect;
  }) {
    const { where, select } = params;
    return this.repo.findFirst({
      where,
      select,
    });
  }

  async getHealerProfileInfo(params: {
    where: Prisma.profilesWhereInput;
    orderBy?: Prisma.profilesOrderByWithAggregationInput;
  }) {
    const { where, orderBy } = params;
    return this.repo.findFirst({
      where,
      orderBy: orderBy || {
        created_at: 'desc',
      },
    });
  }

  async findFirst(params: {
    where: Prisma.profilesWhereInput;
    orderBy?: Prisma.profilesOrderByWithAggregationInput;
    include?: Prisma.profilesInclude;
  }) {
    const { where, orderBy, include } = params;
    return this.repo.findFirst({
      where,
      orderBy: orderBy || {
        created_at: 'desc',
      },
      include: include || {
        conversations: true,
        healing_rounds: true,
      },
    });
  }

  async aggregate(aggregate: Prisma.ProfilesAggregateArgs) {
    return this.repo.aggregate(aggregate);
  }

  async update(params: {
    where: Prisma.profilesWhereUniqueInput;
    data: Prisma.profilesUpdateInput;
  }) {
    const { where, data } = params;
    return this.repo.update({ data, where });
  }
}
