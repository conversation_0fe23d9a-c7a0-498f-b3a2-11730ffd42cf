import { Injectable } from '@nestjs/common';
import { PrismaService, Prisma } from '@core/prisma-client';

@Injectable()
export class RoleRepository {
  private readonly repo: Prisma.rolesDelegate;

  constructor(private readonly prisma: PrismaService) {
    this.repo = this.prisma.roles;
  }

  async create(data: Prisma.rolesCreateInput) {
    return this.prisma.roles.create({ data });
  }

  async findOne(id: number) {
    return this.prisma.roles.findUnique({ where: { role_id: id } });
  }

  async findUserRole(id: number) {
    return await this.repo.findUnique({
      where: { role_id: id },
      include: {
        user_roles: {
          include: {
            roles: true,
          },
        },
      },
    });
  }

  async findAll() {
    return this.repo.findMany();
  }

  async update(id: number, data: Prisma.rolesUpdateInput) {
    return this.repo.update({ where: { role_id: id }, data });
  }

  async delete(id: number, deletedBy: number) {
    return this.repo.update({
      where: { role_id: id },
      data: {
        is_deleted: true,
        deleted_by: deletedBy,
        deleted_at: new Date(),
      },
    });
  }
}
