import { Injectable } from '@nestjs/common';
import { PrismaService, Prisma } from '@core/prisma-client';

@Injectable()
export class NotificationsPreferencesRepository {
  private readonly repo: Prisma.user_notification_preferencesDelegate;

  constructor(readonly prisma: PrismaService) {
    this.repo = this.prisma.user_notification_preferences;
  }

  async create(data: Prisma.user_notification_preferencesCreateInput) {
    return this.repo.create({ data });
  }

  async createMany(data: Prisma.user_notification_preferencesCreateManyInput[]) {
    return this.repo.createMany({
      data,
      skipDuplicates: true,
    });
  }

  async update(
    where: Prisma.user_notification_preferencesWhereUniqueInput,
    data: Prisma.user_notification_preferencesUpdateInput
  ) {
    return this.repo.update({ data, where });
  }

  async upsert(
    where: Prisma.user_notification_preferencesWhereUniqueInput,
    update: Prisma.user_notification_preferencesUpdateInput,
    create: Prisma.user_notification_preferencesCreateInput
  ) {
    return this.repo.upsert({
      where,
      update,
      create,
    });
  }

  async findMany(
    where: Prisma.user_notification_preferencesWhereInput,
    include?: Prisma.user_notification_preferencesInclude
  ){
    return await this.repo.findMany({where,include});
  }

  async findUnique(where: Prisma.user_notification_preferencesWhereUniqueInput){
    return await this.repo.findUnique({where});
  }
}
