import { Test, TestingModule } from '@nestjs/testing';
import { AilmentsRepository } from './ailment.repository';
import { beforeEach, describe, it } from 'node:test';

describe('AilmentsService', () => {
  let service: AilmentsRepository;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [AilmentsRepository],
    }).compile();

    service = module.get<AilmentsRepository>(AilmentsRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
