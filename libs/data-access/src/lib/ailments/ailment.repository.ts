import { Prisma, PrismaService } from '@core/prisma-client';
import { Injectable } from '@nestjs/common';

@Injectable()
export class AilmentsRepository {
  private repo: Prisma.ailmentsDelegate;

  constructor(readonly prisma: PrismaService) {
    this.repo = this.prisma.ailments;
  }

  async create(data: Prisma.ailmentsUncheckedCreateInput) {
    return this.repo.create({ data });
  }

  async update(params: {
    where: Prisma.ailmentsWhereUniqueInput;
    data: Prisma.ailmentsUpdateInput;
  }) {
    const { where, data } = params;
    return this.repo.update({ data, where });
  }

  async findOneWithSelectUnique(params: {
    where: Prisma.ailmentsWhereUniqueInput;
    select?: Prisma.ailmentsSelect;
  }) {
    const { where, select } = params;
    return this.repo.findUnique({
      where,
      select,
    });
  }

  async findUnique(params: { where: Prisma.ailmentsWhereUniqueInput }) {
    const { where } = params;
    return this.repo.findUnique({
      where,
    });
  }

  async findAll(params: {
    skip?: number;
    limit?: number;
    take?: number;
    cursor?: Prisma.ailmentsWhereUniqueInput;
    where?: Prisma.ailmentsWhereInput;
    orderBy?: Prisma.ailmentsOrderByWithAggregationInput;
    select?: Prisma.ailmentsSelect;
  }) {
    const { skip, limit, take, cursor, where, orderBy, select } = params;
    return this.repo.findMany({
      skip,
      take: limit || take,
      cursor,
      where,
      orderBy,
      select,
    });
  }
}
