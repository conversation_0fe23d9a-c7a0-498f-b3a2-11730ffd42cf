import { Prisma, PrismaService } from '@core/prisma-client';
import { WaitListUserType } from '@core_be/global';
import { Injectable } from '@nestjs/common';

@Injectable()
export class HealerStatusRepository {
  private readonly repo: Prisma.healer_statusDelegate;
  constructor(readonly prisma: PrismaService) {
    this.repo = this.prisma.healer_status;
  }

  /**
   * Upsert a healer_status record.
   *
   * @param data   - Prisma.healer_statusUpsertArgs (must include `where`, `create`, and `update`)
   * @param tx     - Optional Prisma.TransactionClient for transactional context
   */
  async upsert(
    data: Prisma.healer_statusUpsertArgs,
    tx?: Prisma.TransactionClient
  ) {
    const client = tx ? tx.healer_status : this.repo;
    return client.upsert(data);
  }

  async findOne(
    where: Prisma.healer_statusWhereUniqueInput,
    include?: Prisma.healer_statusInclude
  ) {
    return this.repo.findUnique({
      where,
      include: include,
    });
  }
}
