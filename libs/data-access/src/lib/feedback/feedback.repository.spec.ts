import { Test, TestingModule } from '@nestjs/testing';
import { FeedbackRepository } from './feedback.repository';

describe('FeedbackRepositoryService', () => {
  let service: FeedbackRepository;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [FeedbackRepository],
    }).compile();

    service = module.get<FeedbackRepository>(FeedbackRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
