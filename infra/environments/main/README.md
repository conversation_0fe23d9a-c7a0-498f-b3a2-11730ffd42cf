# Ennie.app Production Terraform instruction

In order to provision the terraform stack, sensitive terraform variable has to be set via environment variables.

- TF_VAR_postgres_admin_username
- TF_VAR_postgres_admin_password

```sh
export TF_VAR_postgres_admin_username="{{POSTGRESQL_ADMIN_USERNAME}}"
export TF_VAR_postgres_admin_password="{{POSTGRESQL_PASSWORD}}"
```

## Provisioning Sequence

The `private_link_service` module in terraform deployment depends on the actual application stack deployed from helm chart.

Without refactoring the stack, it would be easier to deploy part of the terraform stack, deploying the application stack on AKS, and provision the remaining infrastructure.

1. The base stack up to the `vm` module in `main.tf`
2. Ennie.app deployment with Helm chart via GitHub Action
3. Perform `private_link_service` and `frontdoor` deployment from the base stack
