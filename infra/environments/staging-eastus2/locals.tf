locals {
  deployment_name       = "${var.project}-${var.environment}"
  # Common tags for all resources
  common_tags = {
    environment = var.environment
    project     = var.project
    managed_by  = "terraform"
  }

  # Resource naming convention
  resource_names = {
    resource_group          = "rg-${local.deployment_name}"
    log_analytics_workspace = "log-${local.deployment_name}"
    app_insights            = "appinsights-${local.deployment_name}"

    vnet                           = "vnet-${local.deployment_name}"
    vnet_link_cognitive_services   = "vnet-link-cognitive-services-${local.deployment_name}"
    vnet_link_openai               = "vnet-link-openai-${local.deployment_name}"
    vnet_link_ai_services          = "vnet-link-ai-services-${local.deployment_name}"
    dns_private_cognitive_services = "privatelink.cognitiveservices.azure.com"
    dns_private_openai             = "privatelink.openai.azure.com"
    dns_private_ai_services        = "privatelink.services.ai.azure.com"
    ai_services_subnet             = "ai-subnet-${local.deployment_name}"

    ai_services        = "ai-${local.deployment_name}-${var.location}"
    ai_foundry_project = "prj-${local.deployment_name}-${var.location}"
  }
}
