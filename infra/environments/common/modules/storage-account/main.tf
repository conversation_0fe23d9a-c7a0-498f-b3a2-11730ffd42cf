resource "azurerm_storage_account" "terraformbackend" {
  name                     = local.resource_names.storage_account
  resource_group_name      = var.resource_group_name
  location                 = var.location
  account_tier             = "Standard"
  account_replication_type = "LRS" #locally redundant
  tags                     = local.common_tags
}

resource "azurerm_storage_container" "terraformbackend" {
  name                  = local.resource_names.storage_container
  storage_account_id    = azurerm_storage_account.terraformbackend.id
  container_access_type = "private"
}

