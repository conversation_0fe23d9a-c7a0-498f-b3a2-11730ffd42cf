resource "azurerm_resource_group" "main" {
  name     = local.resource_names.resource_group
  location = var.location
  tags     = local.common_tags
}

module "storage_account" {
  source              = "./modules/storage-account"
  location            = var.location
  environment         = var.environment
  project             = var.project
  resource_group_name = azurerm_resource_group.main.name
  tags                = local.common_tags
}

module "database" {
  source               = "../../modules/database"
  location             = var.location
  resource_group_name  = azurerm_resource_group.main.name
  environment          = var.environment
  project              = var.project
  tags                 = local.common_tags
  postgres_admin_username = var.postgres_admin_username
  postgres_admin_password = var.postgres_admin_password
  sku_name                = "GP_Standard_D2ads_v5"
  additional_allowed_ips = [
      {
        name             = "allow-metabase-0"
        start_ip_address = "*************"
        end_ip_address   = "*************"
      },
      {
        name             = "allow-metabase-1"
        start_ip_address = "************"
        end_ip_address   = "************"
      },
      {
        name             = "allow-metabase-2"
        start_ip_address = "*************"
        end_ip_address   = "*************"
      }
    ]
}
