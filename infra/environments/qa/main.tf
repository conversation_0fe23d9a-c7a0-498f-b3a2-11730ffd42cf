resource "azurerm_resource_group" "main" {
  name     = local.resource_names.resource_group
  location = var.location
  tags     = local.common_tags
}

module "networking" {
  source                                               = "../../modules/networking"
  resource_group_name                                  = azurerm_resource_group.main.name
  location                                             = var.location
  environment                                          = var.environment
  tags                                                 = local.common_tags
  vnet_name                                            = local.resource_names.vnet
  address_space                                        = [local.network.vnet_cidr]
  subnet_prefixes                                      = local.network.subnet_cidrs
  subnet_names                                         = local.network.subnet_names
  subnet_private_link_service_network_policies_enabled = local.network.subnet_private_link_service_network_policies_enabled
}

module "dns" {
  source              = "../../modules/dns"
  resource_group_name = azurerm_resource_group.main.name
  environment         = var.environment
  tags                = local.common_tags

  dns_name_private     = local.resource_names.dns_private
  dns_name_public      = local.resource_names.dns_public
  dns_name_private_acr = local.resource_names.dns_private_acr
  dns_name_private_kv  = local.resource_names.dns_private_kv

  vnet_id            = module.networking.vnet_id
  vnet_link_name     = local.resource_names.vnet_link
  vnet_link_name_acr = local.resource_names.vnet_link_acr
  vnet_link_name_kv  = local.resource_names.vnet_link_kv

  depends_on = [
    module.networking
  ]
}


module "monitoring" {
  source = "../../modules/monitoring"

  resource_group_name          = local.resource_names.resource_group
  log_analytics_workspace_name = local.resource_names.log_analytics_workspace
  app_insights_name            = local.resource_names.app_insights
  tags                         = local.common_tags
}


module "ai_foundry" {
  source = "../../modules/ai_foundry"

  resource_group_name = local.resource_names.resource_group
  project = var.project
  environment = var.environment
  location            = var.location
  tags                = local.common_tags

  log_analytics_workspace_name   = local.resource_names.log_analytics_workspace
  log_analytics_workspace_id     = module.monitoring.log_analytics_workspace_id
  app_insights_name              = local.resource_names.app_insights
  app_insights_id                = module.monitoring.app_insights_id
  app_insights_connection_string = module.monitoring.app_insights_connection_string
  ai_services_name               = local.resource_names.ai_services
  ai_foundry_project_name        = local.resource_names.ai_foundry_project
  ai_services_subnet_id = module.networking.ai_subnet_id

  vnet_id = module.networking.vnet_id

  dns_name_private_cognitive_services = local.resource_names.dns_private_cognitive_services
  dns_name_private_openai = local.resource_names.dns_private_openai
  dns_name_private_ai_services = local.resource_names.dns_private_ai_services

  vnet_link_name_cognitive_services = local.resource_names.vnet_link_cognitive_services
  vnet_link_name_openai = local.resource_names.vnet_link_openai
  vnet_link_name_ai_services = local.resource_names.vnet_link_ai_services


  deployment_name           = local.deployment_name
  model_name                = "gpt-4o"
  model_deployment_name     = "gpt-4o"
  model_version             = "2024-11-20"
  model_format              = "OpenAI"
  model_sku_name            = "GlobalStandard"
  model_deployment_capacity = var.model_capacity
  version_upgrade_option    = "NoAutoUpgrade"

  depends_on = [
    module.monitoring,
    module.networking,
    module.dns
  ]
}


module "acr" {
  source              = "../../modules/acr"
  acr_name            = local.resource_names.acr
  resource_group_name = azurerm_resource_group.main.name
  location            = var.location
  project             = var.project
  environment         = var.environment
  tags                = local.common_tags
  subnet_id_pe        = module.networking.subnet_ids[3]

  dns_private_zone_acr_id   = module.dns.dns_private_zone_acr_id
  dns_private_zone_acr_name = module.dns.dns_private_zone_acr_name
}

module "keyvault" {
  source                   = "../../modules/keyvault"
  resource_group_name      = azurerm_resource_group.main.name
  location                 = var.location
  environment              = var.environment
  project                  = var.project
  tags                     = local.common_tags
  subnet_id_pe             = module.networking.subnet_ids[3]
  dns_private_zone_kv_id   = module.dns.dns_private_zone_kv_id
  dns_private_zone_kv_name = module.dns.dns_private_zone_kv_name
}

module "aks" {
  source              = "../../modules/aks"
  cluster_name        = local.resource_names.aks_cluster
  location            = var.location
  environment         = var.environment
  project             = var.project
  aks_tenant_id       = var.aks_tenant_id
  resource_group_name = azurerm_resource_group.main.name

  kubernetes_version = local.aks_config.kubernetes_version
  node_count         = local.aks_config.node_pool.node_count
  vm_size            = local.aks_config.node_pool.vm_size
  min_node_count     = local.aks_config.node_pool.min_count
  max_node_count     = local.aks_config.node_pool.max_count
  tags               = local.common_tags

  acr_id                = module.acr.acr_id
  kv_id                 = module.keyvault.key_vault_id
  dns_private_zone_id   = module.dns.dns_private_zone_id
  dns_private_zone_name = module.dns.dns_private_zone_name
  aks_subnet_id         = module.networking.aks_subnet_id
  vnet_id               = module.networking.vnet_id

  ai_id                      = module.ai_foundry.ai_foundry_project_id
  log_analytics_workspace_id = module.monitoring.log_analytics_workspace_id

  depends_on = [
    module.keyvault,
    module.acr,
    module.monitoring,
    module.ai_foundry
  ]
}

module "federated_identity" {
  source                  = "../../modules/federated_identity"
  resource_group_name     = module.aks.node_resource_group
  location                = var.location
  environment             = var.environment
  project                 = var.project
  aks_kubelet_identity_id = module.aks.aks_kubelet_identity_id
  aks_oidc_issuer_url     = module.aks.aks_oidc_issuer_url

  depends_on = [
    module.aks
  ]
}


module "vm" {
  source               = "../../modules/vm"
  resource_group_name  = azurerm_resource_group.main.name
  project              = var.project
  environment          = var.environment
  location             = var.location
  tags                 = local.common_tags
  vm_subnet_id         = module.networking.subnet_ids[1]
  bastion_subnet_id    = module.networking.subnet_ids[2]
  admin_username       = "azureuser"
  admin_ssh_public_key = "ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIBMgs9gqDmpl3FXA4ZKdDKpffWnzPhYhxi3kzX23xN0S <EMAIL>"
}


module "private_link_service" {
  source                  = "../../modules/private_link_service"
  resource_group_name     = azurerm_resource_group.main.name
  location                = var.location
  environment             = var.environment
  project                 = var.project
  aks_node_resource_group = module.aks.node_resource_group
  tags                    = local.common_tags
  privatelink_subnet_id   = module.networking.privatelink_subnet_id


  depends_on = [
    module.aks
  ]
}


module "frontdoor" {
  source                     = "../../modules/frontdoor"
  resource_group_name        = azurerm_resource_group.main.name
  location                   = var.location
  project                    = var.project
  environment                = var.environment
  tags                       = local.common_tags
  dns_public_zone_id         = module.dns.dns_public_zone_id
  dns_public_zone_name       = module.dns.dns_public_zone_name
  api_cname_record_name      = "api"
  private_link_service_id    = module.private_link_service.private_link_service_id
  private_link_service_alias = module.private_link_service.private_link_service_alias
  log_analytics_workspace_id = module.monitoring.log_analytics_workspace_id

  depends_on = [
    module.dns,
    module.private_link_service
  ]
}
