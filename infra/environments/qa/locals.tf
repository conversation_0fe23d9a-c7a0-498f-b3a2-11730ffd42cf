locals {
  deployment_name = "${var.project}-${var.environment}"

  # Common tags for all resources
  common_tags = {
    environment = var.environment
    project     = var.project
    managed_by  = "terraform"
  }

  # Resource naming convention
  resource_names = {
    resource_group                 = "rg-${local.deployment_name}"
    aks_resource_group             = "aks-rg-${local.deployment_name}"
    aks_cluster                    = "aks-${local.deployment_name}"
    vnet                           = "vnet-${local.deployment_name}"
    front_door                     = "fd-${local.deployment_name}"
    acr                            = "acr${var.project}${var.environment}${var.location}"
    dns_private                    = "${local.deployment_name}.privatelink.eastus.azmk8s.io"
    dns_private_acr                = "privatelink.azurecr.io"
    dns_private_kv                 = "privatelink.vaultcore.azure.net"
    dns_private_cognitive_services = "privatelink.cognitiveservices.azure.com"
    dns_private_openai             = "privatelink.openai.azure.com"
    dns_private_ai_services        = "privatelink.services.ai.azure.com"
    dns_public                     = "${var.environment}.ennie.app"
    vnet_link                      = "vnet-link-${local.deployment_name}"
    vnet_link_acr                  = "vnet-link-acr-${local.deployment_name}"
    vnet_link_kv                   = "vnet-link-kv-${local.deployment_name}"
    vnet_link_cognitive_services   = "vnet-link-cognitive-services-${local.deployment_name}"
    vnet_link_openai               = "vnet-link-openai-${local.deployment_name}"
    vnet_link_ai_services          = "vnet-link-ai-services-${local.deployment_name}"
    acmebot                        = "acmebot-${local.deployment_name}"
    log_analytics_workspace        = "log-${local.deployment_name}"
    app_insights                   = "appinsights-${local.deployment_name}"
    ai_services                    = "ai-${local.deployment_name}"
    ai_foundry_project             = "prj-${local.deployment_name}"
  }

  # Network configuration
  network = {
    vnet_cidr                                            = "10.10.0.0/16"
    subnet_cidrs                                         = ["10.10.0.0/24", "10.10.2.0/24", "10.10.1.0/24", "10.10.3.0/24", "10.10.4.0/24", "10.10.5.0/24"]
    subnet_names                                         = ["aks-subnet-${var.project}-${var.environment}", "vm-subnet-${var.project}-${var.environment}", "AzureBastionSubnet", "pe-subnet-${var.project}-${var.environment}", "privatelink-subnet-${var.project}-${var.environment}", "ai-subnet-${var.project}-${var.environment}"]
    subnet_private_link_service_network_policies_enabled = [true, true, true, true, false, true]
  }

  # AKS configuration
  aks_config = {
    kubernetes_version = var.kubernetes_version
    node_pool = {
      name       = "default"
      node_count = var.node_count
      vm_size    = var.vm_size
      min_count  = var.min_node_count
      max_count  = var.max_node_count
    }
  }
}
