data "azurerm_application_insights" "main" {
  name                = local.resource_names.app_insights
  resource_group_name = local.resource_names.resource_group
}

data "azurerm_log_analytics_workspace" "main" {
  name                = local.resource_names.log_analytics_workspace
  resource_group_name = local.resource_names.resource_group
}

data "azurerm_virtual_network" "main_vnet" {
  name                = local.resource_names.vnet
  resource_group_name = local.resource_names.resource_group
}

data "azurerm_subnet" "ai_services_subnet" {
  virtual_network_name = local.resource_names.vnet
  name                 = local.resource_names.ai_services_subnet
  resource_group_name  = local.resource_names.resource_group
}

module "ai_foundry" {
  source = "../../modules/ai_foundry_backup"

  resource_group_name = local.resource_names.resource_group
  project = var.project
  environment = var.environment
  location            = var.location
  tags                = local.common_tags

  log_analytics_workspace_name   = local.resource_names.log_analytics_workspace
  log_analytics_workspace_id     = data.azurerm_log_analytics_workspace.main.id
  app_insights_name              = local.resource_names.app_insights
  app_insights_id                = data.azurerm_application_insights.main.id
  app_insights_connection_string = data.azurerm_application_insights.main.connection_string
  ai_services_name               = local.resource_names.ai_services
  ai_foundry_project_name        = local.resource_names.ai_foundry_project
  ai_services_subnet_id          = data.azurerm_subnet.ai_services_subnet.id

  vnet_id = data.azurerm_virtual_network.main_vnet.id

  dns_name_private_cognitive_services = local.resource_names.dns_private_cognitive_services
  dns_name_private_openai = local.resource_names.dns_private_openai
  dns_name_private_ai_services = local.resource_names.dns_private_ai_services

  vnet_link_name_cognitive_services = local.resource_names.vnet_link_cognitive_services
  vnet_link_name_openai = local.resource_names.vnet_link_openai
  vnet_link_name_ai_services = local.resource_names.vnet_link_ai_services

  deployment_name           = local.deployment_name
  model_name                = "gpt-4o"
  model_deployment_name     = "gpt-4o"
  model_version             = "2024-11-20"
  model_format              = "OpenAI"
  model_sku_name            = "GlobalStandard"
  model_deployment_capacity = var.model_capacity
  version_upgrade_option    = "NoAutoUpgrade"
}
