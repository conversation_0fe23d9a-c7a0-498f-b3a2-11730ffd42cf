terraform {
  required_version = ">= 1.0"

  cloud {
    organization = "Ennie-non-prod"

    workspaces {
      name = "Ennie-local-dev"
    }
  }

  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~> 4.0"
    }
    azapi = {
      source = "azure/azapi"
    }
  }
}

provider "azurerm" {
  subscription_id = "909943f3-eb04-4bfa-8fa8-f451f05773c1"
  features {}
}

provider "azapi" {
  subscription_id = "909943f3-eb04-4bfa-8fa8-f451f05773c1"
}
