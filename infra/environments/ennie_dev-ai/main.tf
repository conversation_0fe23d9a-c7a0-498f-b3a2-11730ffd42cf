module "networking" {
  source                                               = "../../modules/networking"
  resource_group_name                                  = local.resource_names.resource_group
  location                                             = var.location
  environment                                          = var.environment
  tags                                                 = local.common_tags
  vnet_name                                            = local.resource_names.vnet
  address_space                                        = [local.network.vnet_cidr]
  subnet_prefixes                                      = local.network.subnet_cidrs
  subnet_names                                         = local.network.subnet_names
  subnet_private_link_service_network_policies_enabled = local.network.subnet_private_link_service_network_policies_enabled
}

module "dns" {
  source              = "../../modules/dns"
  resource_group_name = local.resource_names.resource_group
  environment         = var.environment
  tags                = local.common_tags

  dns_name_public      = local.resource_names.dns_public
  dns_name_private     = local.resource_names.dns_private
  dns_name_private_acr = local.resource_names.dns_private_acr
  dns_name_private_kv  = local.resource_names.dns_private_kv

  vnet_id            = module.networking.vnet_id
  vnet_link_name     = local.resource_names.vnet_link
  vnet_link_name_acr = local.resource_names.vnet_link_acr
  vnet_link_name_kv  = local.resource_names.vnet_link_kv

  depends_on = [
    module.networking
  ]
}

module "monitoring" {
  source = "../../modules/monitoring"

  resource_group_name          = local.resource_names.resource_group
  log_analytics_workspace_name = local.resource_names.log_analytics_workspace
  app_insights_name           = local.resource_names.app_insights
  tags                        = local.common_tags
}

module "ai_foundry" {
  source = "../../modules/ai_foundry"

  resource_group_name = local.resource_names.resource_group
  project = var.project
  environment = var.environment
  location = var.location
  tags = local.common_tags

  log_analytics_workspace_name = local.resource_names.log_analytics_workspace
  log_analytics_workspace_id = module.monitoring.log_analytics_workspace_id
  app_insights_name = local.resource_names.app_insights
  app_insights_id = module.monitoring.app_insights_id
  app_insights_connection_string = module.monitoring.app_insights_connection_string

  ai_services_name = local.resource_names.ai_services
  ai_foundry_project_name = local.resource_names.ai_foundry_project
  ai_services_subnet_id = module.networking.ai_subnet_id

  vnet_id = module.networking.vnet_id

  dns_name_private_cognitive_services = local.resource_names.dns_private_cognitive_services
  dns_name_private_openai = local.resource_names.dns_private_openai
  dns_name_private_ai_services = local.resource_names.dns_private_ai_services

  vnet_link_name_cognitive_services = local.resource_names.vnet_link_cognitive_services
  vnet_link_name_openai = local.resource_names.vnet_link_openai
  vnet_link_name_ai_services = local.resource_names.vnet_link_ai_services

  deployment_name = local.deployment_name
  model_name = "gpt-4o"
  model_deployment_name = "gpt-4o"
  model_version = "2024-11-20"
  model_format = "OpenAI"
  model_sku_name = "GlobalStandard"
  model_deployment_capacity = var.model_capacity
  version_upgrade_option = "NoAutoUpgrade"

  depends_on = [
    module.monitoring,
    module.networking
  ]
}
