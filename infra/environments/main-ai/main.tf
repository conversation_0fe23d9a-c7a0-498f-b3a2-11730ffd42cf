module "monitoring" {
  source = "../../modules/monitoring"

  resource_group_name          = local.resource_names.resource_group
  log_analytics_workspace_name = local.resource_names.log_analytics_workspace
  app_insights_name           = local.resource_names.app_insights
  tags                        = local.common_tags
}

module "ai_foundry" {
  source = "../../modules/ai_foundry"

  resource_group_name = local.resource_names.resource_group
  location = var.location
  tags = local.common_tags

  log_analytics_workspace_name = local.resource_names.log_analytics_workspace
  log_analytics_workspace_id = module.monitoring.log_analytics_workspace_id
  app_insights_name = local.resource_names.app_insights
  app_insights_id = module.monitoring.app_insights_id
  app_insights_connection_string = module.monitoring.app_insights_connection_string
  ai_services_name = local.resource_names.ai_services
  ai_foundry_project_name = local.resource_names.ai_foundry_project

  deployment_name = local.deployment_name
  model_name = "gpt-4o"
  model_deployment_name = "gpt-4o"
  model_version = "2024-11-20"
  model_format = "OpenAI"
  model_sku_name = "GlobalStandard"
  model_deployment_capacity = var.model_capacity
  version_upgrade_option = "NoAutoUpgrade"

  depends_on = [module.monitoring]
}
