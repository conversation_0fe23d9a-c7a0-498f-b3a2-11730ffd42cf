terraform {
  required_version = ">= 1.0"

  backend "azurerm" {
    resource_group_name  = "rg-ennie-common"
    storage_account_name = "storageenniecommon"
    container_name       = "container-ennie-common"
    key                  = "main-ai.terraform.tfstate"
  }

  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~> 4.0"
    }
    azapi = {
      source  = "azure/azapi"
    }
  }
}

provider "azurerm" {
  subscription_id = "155ea8d4-5ebb-477f-9754-d32023664687"
  features {}
}

provider "azapi" {
  subscription_id = "155ea8d4-5ebb-477f-9754-d32023664687"
}
