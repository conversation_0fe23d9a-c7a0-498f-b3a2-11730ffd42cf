# Infrastructure Overview

This directory contains the Terraform infrastructure as code (IaC) for the Healthis backend application, providing a complete Azure-based cloud infrastructure solution.

## 🏗️ Architecture Overview

The infrastructure is designed as a multi-environment, modular system supporting:
- **Multi-environment deployment**: `common`, `develop`, `staging`, `qa`, and `main` environments
- **Microservices architecture**: Supporting AI, analytics, core, and scheduler applications
- **Azure-native services**: Leveraging Azure's managed services for scalability and reliability
- **Security-first approach**: Private networking, Key Vault integration, and secure access patterns

## 📁 Directory Structure

```
infra/
├── environments/           # Environment-specific configurations
│   ├── common/            # Shared infrastructure (database, storage, OpenAI)
│   ├── develop/           # Development environment
│   ├── staging/           # Staging environment
│   ├── qa/                # Quality assurance environment
│   └── main/              # Production environment
├── modules/               # Reusable Terraform modules
│   ├── aks/               # Azure Kubernetes Service
│   ├── acr/               # Azure Container Registry
│   ├── database/          # PostgreSQL Flexible Server
│   ├── dns/               # DNS zones (public/private)
│   ├── frontdoor/         # Azure Front Door (CDN/WAF)
│   ├── keyvault/          # Azure Key Vault
│   ├── networking/        # Virtual Network and subnets
│   ├── openai/            # OpenAI service integration
│   ├── private_link_service/ # Private Link Service
│   └── vm/                # Virtual Machine (GitHub Actions runner)
└── .terraform-version     # Terraform version (1.2.3)
```

## 🎯 Core Infrastructure Components

### Common Infrastructure (`environments/common/`)
- **Resource Group**: Central resource organization
- **Storage Account**: Blob storage for application assets
- **PostgreSQL Database**: Flexible server for application data
- **OpenAI Service**: AI model integration for the application

### Environment-Specific Infrastructure
Each environment (`develop`, `staging`, `qa`, `main`) includes:

#### Networking (`modules/networking/`)
- Virtual Network with multiple subnets:
  - Application subnet
  - VM subnet (for GitHub Actions runner)
  - Bastion subnet (secure access)
  - AKS subnet (Kubernetes cluster)
  - Private Link subnet (secure service connections)

#### Container Infrastructure
- **Azure Container Registry (ACR)**: Private container image storage
- **Azure Kubernetes Service (AKS)**: Managed Kubernetes cluster
  - Auto-scaling node pools
  - Integration with ACR and Key Vault
  - Private DNS resolution

#### Security & Access
- **Azure Key Vault**: Secrets and certificate management
- **Private Link Services**: Secure internal service communication
- **Bastion Host**: Secure VM access without public IPs
- **DNS Zones**: Private and public DNS management

#### Application Delivery
- **Azure Front Door**: Global CDN and WAF
- **Custom Domains**: Environment-specific subdomains
- **SSL/TLS**: Automated certificate management

#### CI/CD Infrastructure
- **Virtual Machine**: Self-hosted GitHub Actions runner
- **Automated deployment**: Terraform-managed infrastructure updates

## 🚀 Deployment Strategy

### Two-Phase Deployment Process

#### Phase 1: Core Infrastructure
1. Deploy common infrastructure (database, storage, OpenAI)
2. Deploy environment-specific networking and container services
3. Configure AKS cluster with ACR and Key Vault integration

#### Phase 2: Application Delivery
1. Deploy application via Helm charts to AKS
2. Enable Private Link Services (requires AKS load balancers)
3. Configure Front Door and custom domains
4. Set up DNS records and SSL certificates

### Environment Promotion
- **Develop** → **QA** → **Staging** → **Main**
- Each environment has isolated resources
- Shared common infrastructure reduces costs
- Environment-specific configurations via `terraform.tfvars`

## 🔧 Technology Stack

- **Infrastructure as Code**: Terraform 1.2.3
- **Cloud Provider**: Microsoft Azure
- **Container Orchestration**: Azure Kubernetes Service (AKS)
- **Container Registry**: Azure Container Registry (ACR)
- **Database**: Azure Database for PostgreSQL Flexible Server
- **Networking**: Azure Virtual Network with Private Link
- **Security**: Azure Key Vault, Private Endpoints
- **CDN/WAF**: Azure Front Door
- **DNS**: Azure DNS (public and private zones)
- **CI/CD**: GitHub Actions with self-hosted runners

## 🛡️ Security Features

- **Private Networking**: All services communicate over private networks
- **No Public IPs**: Services are accessed through Front Door or Bastion
- **Secret Management**: Centralized secrets in Key Vault
- **Network Security**: Subnet isolation and private endpoints
- **Access Control**: Role-based access control (RBAC)
- **SSL/TLS**: End-to-end encryption

## 📊 Scalability & Reliability

- **Auto-scaling**: AKS node pools scale based on demand
- **High Availability**: Multi-zone deployments
- **Load Balancing**: Azure Load Balancer and Front Door
- **Monitoring**: Azure Monitor integration
- **Backup**: Automated database backups
- **Disaster Recovery**: Cross-region replication capabilities

## 🔄 Maintenance & Operations

- **Infrastructure Updates**: Managed through Terraform
- **Application Updates**: Deployed via Helm charts
- **Monitoring**: Azure Monitor and Application Insights
- **Logging**: Centralized logging in Azure
- **Backup & Recovery**: Automated backup strategies

## 📝 Usage Notes

- Requires Azure CLI and Terraform installation
- Environment-specific variables in `terraform.tfvars`
- GitHub Actions integration for automated deployments
- Manual steps required for DNS configuration and certificate approval
- Private endpoint approval requires manual confirmation in Azure portal

This infrastructure provides a production-ready, scalable, and secure foundation for the Healthis backend application across multiple environments.
