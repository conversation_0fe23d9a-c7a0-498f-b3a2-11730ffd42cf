# Terraform

## GitHub Actions and Azure Login
- Permissions for GitHub Actions to access Azure
-- Azure > Subscriptions > <sub> > IAM > Add Custom Role (see permissions block below)
``` bash
        "permissions": [
            {
                "actions": [
                    "Microsoft.ContainerRegistry/registries/artifacts/delete",
                    "Microsoft.ContainerRegistry/registries/push/write",
                    "Microsoft.ContainerRegistry/registries/pull/read",
                    "Microsoft.ContainerRegistry/registries/deleted/read",
                    "Microsoft.ContainerRegistry/registries/packages/archives/read",
                    "Microsoft.ContainerRegistry/registries/packages/archives/write",
                    "Microsoft.ContainerRegistry/registries/packages/archives/delete",
                    "Microsoft.ContainerRegistry/registries/packages/archives/versions/read",
                    "Microsoft.ContainerRegistry/registries/packages/archives/versions/write",
                    "Microsoft.ContainerRegistry/registries/importImage/action",
                    "Microsoft.ContainerRegistry/registries/write",
                    "Microsoft.ContainerRegistry/checkNameAvailability/read",
                    "Microsoft.ContainerService/managedClusters/listClusterUserCredential/action",
                    "Microsoft.ContainerService/managedClusters/*",
                    "Microsoft.DBforPostgreSQL/flexibleServers/firewallRules/write",
                    "Microsoft.DBforPostgreSQL/flexibleServers/firewallRules/delete",
                    "Microsoft.DBforPostgreSQL/flexibleServers/read",
                    "Microsoft.DBforPostgreSQL/flexibleServers/firewallRules/read"
                ],
                "notActions": [],
                "dataActions": [],
                "notDataActions": []
            }
        ]
```
-- Add role assignment
-- Assign the custom role created above to the preferred enterprise application (e.g. "Github Actions") *note: this should already be created for you*
--
-- Azure > App Registrations > <select enterprise application from previous step>
-- Manage > Certificates and Secrets > New Client Secret
-- Generate secret, use details to setup JSON object as follows and add as environment secret to github for referencing within Github Actions
```json
{
  "clientId": "XXX-XX-XX-XX-XXX", #available on app overview "Application (client) ID"
  "clientSecret": "~XXX~", #client secret value
  "tenantId": "XXX-XX-XX-XX-XXX", #available on app overview "Directory (tenant) ID"
  "subscriptionId": "XXX-XX-XX-XX-XXX" # available on subscriptions list
}
```

## GitHub Actions and Azure ACR
**Note: only available after deploying a specific environment via Terraform below**

After deploying the ACR resources (environment specific), you can navigate to the specific container registry > Settings > Access Keys

Add the username and password to Github as secrets for the specific environment deployment.

### Conclusion
The above secrets can then be referenced by github actions found in `.github` folder.


# Usage

Deploying environments using Terraform:

`az login`

## Deploy common infrastructure
- Update `./infra/environments/common/terraform.tfvars`
- Update `./infra/environments/common/providers.tfvars`

**Note: you will need to comment out the `backend` block and then re-add after the initial deployment which deploys the necessary infrastructure**

```
$ cd ./infra
$ tfenv install
$ tfenv use
$ cd environments/common
$ terraform init
$ terraform apply -var-file="terraform.tfvars"
```

## Deploy Specific Environment
- Update `./infra/environments/<env>/terraform.tfvars`
- Update `./infra/environments/<env>/providers.tfvars`

```
$ cd ./infra
$ tfenv install
$ tfenv use
$ cd environments/<env>
$ terraform init
$ terraform apply -var-file="terraform.tfvars"
```

## Terraform Step One
1. Terraform - Create resource group and acr
*NOTE: this can take 60m for permissions to propagate before you can deploy as AKS may not be able to pull from ACR immediately*

## Azure
2. VM - connect to VM via bastion host to configure and run Github Action Runner as a service
3. Connect to the VM via Bastion and run the shell script found in /infra/modules/vm/install-tools.sh

*Note: for easy installation, copy the contents to the VM using `vim` editor - this will typically allow all tools to install up to the `actions-runner` step*

## Create the runner and start the configuration experience


## Github and code
- Update kv identity in helm values-<env>.yaml for your environment prior to deploying, this will allow AKS to pull secrets
- Trigger deploy

## Terraform Step Two
- Enable the private link services and front door in your environment - these rely upon AKS load balancers initialisd by the helm deployment
- Run a terraform deployment

### ClickOps
- Private Endpoint must be confirmed manually for the priavte link service
- Custom domain on front door must be manually approved by adding DNS CNAME for certificates
- Public sub-domain NS records must be added to parent public DNS zone, e.g. `qa.ennie.app` NS records added to `ennie.app`
