output "vnet_id" {
  description = "ID of the created virtual network"
  value       = azurerm_virtual_network.vnet.id
}

output "vnet_name" {
  description = "Name of the created virtual network"
  value       = azurerm_virtual_network.vnet.name
}

output "subnet_ids" {
  description = "IDs of the created subnets"
  value       = azurerm_subnet.subnet[*].id
}

output "subnet_names" {
  description = "Names of the created subnets"
  value       = azurerm_subnet.subnet[*].name
}

# output "nsg_id" {
#   description = "ID of the created Network Security Group"
#   value       = azurerm_network_security_group.nsg.id
# }

# output "route_table_id" {
#   description = "ID of the created Route Table"
#   value       = azurerm_route_table.rt.id
# }

output "pe_subnet_id" {
  description = "ID of the created Private Endpoint subnet"
  value       = azurerm_subnet.subnet[3].id
}

output "privatelink_subnet_id" {
  description = "ID of the created Private Link subnet"
  value       = azurerm_subnet.subnet[4].id
}

output "aks_subnet_id" {
  description = "ID of the created AKS subnet"
  value       = azurerm_subnet.subnet[0].id
}