locals {
  # Common tags - derived from variables but standardized across resources
  common_tags = merge(
    {
      module     = "networking"
    },
    var.tags
  )

  # Naming convention for resources
  resource_names = {
    nsg         = "nsg-${var.vnet_name}"
    route_table = "rt-${var.vnet_name}"
  }

  # Security rules for NSG - complex data structure that won't change during runtime
  nsg_rules = {
    https = {
      name                       = "allow-https-inbound"
      priority                   = 100
      direction                  = "Inbound"
      access                     = "Allow"
      protocol                   = "Tcp"
      source_port_range         = "*"
      destination_port_range    = "443"
      source_address_prefix     = "*"
      destination_address_prefix = "*"
    }
    http = {
      name                       = "allow-http-inbound"
      priority                   = 110
      direction                  = "Inbound"
      access                     = "Allow"
      protocol                   = "Tcp"
      source_port_range         = "*"
      destination_port_range    = "80"
      source_address_prefix     = "*"
      destination_address_prefix = "*"
    }
    aks = {
      name                       = "allow-aks-ports"
      priority                   = 120
      direction                  = "Inbound"
      access                     = "Allow"
      protocol                   = "*"
      source_port_range         = "*"
      destination_port_ranges    = ["22", "443", "9000"]
      source_address_prefix     = "*"
      destination_address_prefix = "*"
    }
  }

  # Service endpoints configuration - static configuration
  service_endpoints = var.enable_service_endpoints ? [
    "Microsoft.ContainerRegistry",
    "Microsoft.Storage"
  ] : []

  # Subnet configuration validation
  subnet_validation = {
    valid_length = length(var.subnet_names) == length(var.subnet_prefixes)
    max_subnets  = length(var.subnet_names) <= 20  # Azure limit
  }
}