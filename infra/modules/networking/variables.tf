variable "resource_group_name" {
  description = "Name of the resource group"
  type        = string
}

variable "location" {
  description = "Azure region where resources will be created"
  type        = string
}

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string

  validation {
    condition     = contains(["dev", "staging", "qa", "prod"], var.environment)
    error_message = "Environment must be one of: dev, staging, qa, prod."
  }
}

variable "vnet_name" {
  description = "Name of the virtual network"
  type        = string

  validation {
    condition     = can(regex("^vnet-", var.vnet_name))
    error_message = "Virtual network name must start with 'vnet-'."
  }
}

variable "address_space" {
  description = "Address space for the virtual network"
  type        = list(string)

  validation {
    condition     = length(var.address_space) > 0
    error_message = "At least one address space must be provided."
  }
}

variable "subnet_names" {
  description = "List of subnet names"
  type        = list(string)

  validation {
    condition     = length(var.subnet_names) > 0
    error_message = "At least one subnet name must be provided."
  }
}

variable "subnet_prefixes" {
  description = "List of subnet address prefixes"
  type        = list(string)

  validation {
    condition     = length(var.subnet_prefixes) > 0
    error_message = "At least one subnet prefix must be provided."
  }
}


variable "subnet_private_link_service_network_policies_enabled" {
  description = "List of subnet private link service network policies enabled"
  type        = list(bool)

  validation {
    condition     = length(var.subnet_private_link_service_network_policies_enabled) > 0
    error_message = "At least one subnet private link service network policies enabled must be provided."
  }
}

# Optional variables with defaults
variable "enable_service_endpoints" {
  description = "Enable service endpoints for subnets"
  type        = bool
  default     = true
}

variable "tags" {
  description = "Additional tags for resources"
  type        = map(string)
  default     = {}
}