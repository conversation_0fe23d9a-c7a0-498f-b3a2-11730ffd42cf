# Validate subnet configuration
resource "null_resource" "subnet_validation" {
  count = local.subnet_validation.valid_length && local.subnet_validation.max_subnets ? 0 : "ERROR: Invalid subnet configuration"
}

resource "azurerm_virtual_network" "vnet" {
  name                = var.vnet_name
  resource_group_name = var.resource_group_name
  location            = var.location
  address_space       = var.address_space
  tags                = local.common_tags
}

resource "azurerm_subnet" "subnet" {
  count                = length(var.subnet_names)
  name                 = var.subnet_names[count.index]
  resource_group_name  = var.resource_group_name
  virtual_network_name = azurerm_virtual_network.vnet.name
  address_prefixes     = [var.subnet_prefixes[count.index]]
  service_endpoints    = local.service_endpoints
  private_link_service_network_policies_enabled = var.subnet_private_link_service_network_policies_enabled[count.index]
}