resource "azurerm_private_dns_zone" "dns_private" {
  name                = var.dns_name_private
  resource_group_name = var.resource_group_name
  tags                = local.common_tags
}

resource "azurerm_private_dns_zone_virtual_network_link" "dns_private_link" {
  name                  = var.vnet_link_name
  resource_group_name   = var.resource_group_name
  private_dns_zone_name = azurerm_private_dns_zone.dns_private.name
  virtual_network_id    = var.vnet_id
  registration_enabled  = false  # Optional, enable auto-registration of VMs
  tags                  = local.common_tags
}

resource "azurerm_private_dns_zone" "dns_private_acr" {
  name                = var.dns_name_private_acr
  resource_group_name = var.resource_group_name
  tags                = local.common_tags
}

resource "azurerm_private_dns_zone_virtual_network_link" "dns_private_link_acr" {
  name                  = var.vnet_link_name_acr
  resource_group_name   = var.resource_group_name
  private_dns_zone_name = azurerm_private_dns_zone.dns_private_acr.name
  virtual_network_id    = var.vnet_id
  tags                  = local.common_tags
}


resource "azurerm_private_dns_zone" "dns_private_kv" {
  name                = var.dns_name_private_kv
  resource_group_name = var.resource_group_name
  tags                = local.common_tags
}

resource "azurerm_private_dns_zone_virtual_network_link" "dns_private_link_kv" {
  name                  = var.vnet_link_name_kv
  resource_group_name   = var.resource_group_name
  private_dns_zone_name = azurerm_private_dns_zone.dns_private_kv.name
  virtual_network_id    = var.vnet_id
  tags                  = local.common_tags
}

resource "azurerm_dns_zone" "dns_public" {
  name                = var.dns_name_public
  resource_group_name = var.resource_group_name
  tags                = local.common_tags
}
