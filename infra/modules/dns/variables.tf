variable "dns_name_private" {
  description = "The url of the private DNS zone"
  type        = string
}

variable "dns_name_public" {
  description = "The url of the public DNS zone"
  type        = string
}

variable "dns_name_private_acr" {
  description = "The url of the private DNS zone for ACR"
  type        = string
}

variable "tags" {
  description = "Additional tags for resources"
  type        = map(string)
  default     = {}
}

variable "resource_group_name" {
  description = "The name of the resource group"
  type        = string
}

variable "environment" {
  description = "The environment (dev, staging, prod)"
  type        = string
}

variable "project" {
  description = "The name of the project"
  type        = string
  default     = "demo"

  validation {
    condition     = can(regex("^[a-z0-9-]+$", var.project))
    error_message = "Project name must consist of lowercase letters, numbers, and hyphens."
  }
}

variable "vnet_id" {
  description = "The ID of the vnet"
  type        = string
}

variable "vnet_link_name" {
  description = "The name of the vnet link"
  type        = string
}

variable "vnet_link_name_acr" {
  description = "The name of the vnet link for ACR"
  type        = string
}

variable "vnet_link_name_kv" {
  description = "The name of the vnet link for KeyVault"
  type        = string
}

variable "dns_name_private_kv" {
  description = "The url of the private DNS zone for KeyVault"
  type        = string
}