variable "resource_group_name" {
  description = "The name of the resource group"
  type        = string
}

variable "tags" {
  description = "The tags to apply to the resource group"
  type        = map(string)
}

variable "location" {
  description = "The location of the resource group"
  type        = string
}

variable "environment" {
  description = "The environment"
  type        = string
}

variable "project" {
  description = "The project"
  type        = string
}

variable "aks_node_resource_group" {
  description = "The name of the AKS node resource group where the load balancer is created"
  type        = string
}

variable "privatelink_subnet_id" {
  description = "The ID of the Private Endpoint subnet"
  type        = string
}