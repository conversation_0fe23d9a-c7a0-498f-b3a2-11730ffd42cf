data "azurerm_client_config" "current" {}

# Data source to get the AKS load balancer
data "azurerm_lb" "aks_lb" {
  name                = "kubernetes-internal"
  resource_group_name = var.aks_node_resource_group
}

resource "azurerm_private_link_service" "aks_fd_private_link" {
  name                = "privatelink-${var.project}-${var.environment}"
  resource_group_name = var.resource_group_name
  location            = var.location
  load_balancer_frontend_ip_configuration_ids = [data.azurerm_lb.aks_lb.frontend_ip_configuration[0].id]

  nat_ip_configuration {
    name                       = "primary"
    subnet_id                  = var.privatelink_subnet_id
    primary                    = true
  }

  lifecycle {
    ignore_changes = [
      load_balancer_frontend_ip_configuration_ids
    ]
  }
}
