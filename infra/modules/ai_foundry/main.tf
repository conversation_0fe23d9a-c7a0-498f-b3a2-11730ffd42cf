# Reference existing resource group
data "azurerm_resource_group" "main" {
  name = var.resource_group_name
}

# Create Cognitive Services AI account using azapi
resource "azapi_resource" "ai_services" {
  type      = "Microsoft.CognitiveServices/accounts@2025-06-01"
  name      = var.ai_services_name
  location  = var.location
  parent_id = data.azurerm_resource_group.main.id

  identity {
    type = "SystemAssigned"
  }

  body = {
    sku = {
      name = "S0"
    }
    kind = "AIServices"
    properties = {
      apiProperties = {}
      customSubDomainName = var.ai_services_name
      networkAcls = {
        bypass = "AzureServices"
        defaultAction = "Deny"
        virtualNetworkRules = [
          {
            id = var.ai_services_subnet_id,
            ignoreMissingVnetServiceEndpoint = false
          }
        ]
        ipRules = []
      }
      allowProjectManagement = true
      publicNetworkAccess = "Enabled"
      disableLocalAuth = true
    }
  }

  tags = var.tags
  schema_validation_enabled = false

  lifecycle {
    ignore_changes = [
      body.properties.networkAcls.ipRules
    ]
  }
}

resource "azurerm_private_dns_zone" "dns_private_cognitive_services" {
  name                = var.dns_name_private_cognitive_services
  resource_group_name = var.resource_group_name
  tags                = local.common_tags
}

resource "azurerm_private_dns_zone_virtual_network_link" "dns_private_link_cognitive_services" {
  name                  = var.vnet_link_name_cognitive_services
  resource_group_name   = var.resource_group_name
  private_dns_zone_name = azurerm_private_dns_zone.dns_private_cognitive_services.name
  virtual_network_id    = var.vnet_id
  tags                  = local.common_tags
}

resource "azurerm_private_dns_zone" "dns_private_openai" {
  name                = var.dns_name_private_openai
  resource_group_name = var.resource_group_name
  tags                = local.common_tags
}

resource "azurerm_private_dns_zone_virtual_network_link" "dns_private_link_openai" {
  name                  = var.vnet_link_name_openai
  resource_group_name   = var.resource_group_name
  private_dns_zone_name = azurerm_private_dns_zone.dns_private_openai.name
  virtual_network_id    = var.vnet_id
  tags                  = local.common_tags
}

resource "azurerm_private_dns_zone" "dns_private_ai_services" {
  name                = var.dns_name_private_ai_services
  resource_group_name = var.resource_group_name
  tags                = local.common_tags
}

resource "azurerm_private_dns_zone_virtual_network_link" "dns_private_link_ai_services" {
  name                  = var.vnet_link_name_ai_services
  resource_group_name   = var.resource_group_name
  private_dns_zone_name = azurerm_private_dns_zone.dns_private_ai_services.name
  virtual_network_id    = var.vnet_id
  tags                  = local.common_tags
}

# AI Foundry Service Private Endpoint
resource "azurerm_private_endpoint" "ai_services" {
  name                = "pvep-ai-services-${var.project}-${var.environment}"
  location            = var.location
  resource_group_name = var.resource_group_name
  subnet_id           = var.ai_services_subnet_id

  private_service_connection {
    name                           = "ai-services-connection-${var.project}-${var.environment}"
    private_connection_resource_id = azapi_resource.ai_services.id
    is_manual_connection           = false
    subresource_names              = ["account"]
  }

  private_dns_zone_group {
    name                 = "ai-services-dns-zone-group"
    private_dns_zone_ids = [
      azurerm_private_dns_zone.dns_private_cognitive_services.id,
      azurerm_private_dns_zone.dns_private_openai.id,
      azurerm_private_dns_zone.dns_private_ai_services.id
    ]
  }
}


# Create diagnostic settings for AI services
resource "azurerm_monitor_diagnostic_setting" "ai_services" {
  name                       = "diag-${var.ai_services_name}"
  target_resource_id         = azapi_resource.ai_services.id
  log_analytics_workspace_id = var.log_analytics_workspace_id

  enabled_log {
    category_group = "Audit"
  }

  enabled_log {
    category_group = "AllLogs"
  }

  enabled_metric {
    category = "AllMetrics"
  }

  depends_on = [azapi_resource.ai_services]
}

resource "azapi_resource" "ai_foundry_project" {
  name      = var.ai_foundry_project_name
  type      = "Microsoft.CognitiveServices/accounts/projects@2025-04-01-preview"
  location  = var.location
  parent_id = azapi_resource.ai_services.id

  identity {
    type = "SystemAssigned"
  }

  body = {
    kind = "AIServices"
    properties = {}
  }

  depends_on = [azapi_resource.ai_services]

  schema_validation_enabled = false
}

# Create Application Insights connection at AI Services account level
resource "azapi_resource" "ai_services_app_insights_connection" {
  type      = "Microsoft.CognitiveServices/accounts/connections@2025-04-01-preview"
  name      = "connection-${var.app_insights_name}"
  parent_id = azapi_resource.ai_services.id

  body = {
    properties = {
      authType = "ApiKey"
      category = "AppInsights"
      credentials = {
        key = var.app_insights_connection_string
      }
      target = var.app_insights_id
      useWorkspaceManagedIdentity = false
      isSharedToAll = true
      sharedUserList = []
      peRequirement = "NotRequired"
      peStatus = "NotApplicable"
      metadata = {
        ApiType = "Azure"
        ResourceId = var.app_insights_id
      }
    }
  }

  depends_on = [azapi_resource.ai_services]
  schema_validation_enabled = false
}

# Create RAI Policy for content filtering
resource "azapi_resource" "rai_policy" {
  type      = "Microsoft.CognitiveServices/accounts/raiPolicies@2025-04-01-preview"
  name      = "rai-policy-${var.deployment_name}-${var.model_deployment_name}"
  parent_id = azapi_resource.ai_services.id

  body = {
    properties = {
      mode = "Asynchronous_filter"
      basePolicyName = "Microsoft.DefaultV2"
      contentFilters = [
        for filter in var.content_filters : {
          name = filter.name
          severityThreshold = filter.severity_threshold
          blocking = filter.blocking
          enabled = filter.enabled
          source = filter.source
        }
      ]
    }
  }

  depends_on = [azapi_resource.ai_services]
  schema_validation_enabled = false
}

# Create model deployment
resource "azapi_resource" "model_deployment" {
  type      = "Microsoft.CognitiveServices/accounts/deployments@2025-04-01-preview"
  name      = "${var.deployment_name}-${var.model_deployment_name}"
  parent_id = azapi_resource.ai_services.id

  body = {
    sku = {
      name = var.model_sku_name
      capacity = var.model_deployment_capacity
    }
    properties = {
      model = {
        format = var.model_format
        name = var.model_name
        version = var.model_version
      }
      versionUpgradeOption = var.version_upgrade_option
      currentCapacity = var.model_deployment_capacity
      raiPolicyName = azapi_resource.rai_policy.name
    }
  }

  depends_on = [azapi_resource.ai_services, azapi_resource.rai_policy]
  schema_validation_enabled = false
}
