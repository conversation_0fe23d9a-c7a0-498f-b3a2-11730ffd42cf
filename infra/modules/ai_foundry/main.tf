# Reference existing resource group
data "azurerm_resource_group" "main" {
  name = var.resource_group_name
}

# Create Cognitive Services AI account using azapi
resource "azapi_resource" "ai_services" {
  type      = "Microsoft.CognitiveServices/accounts@2025-04-01-preview"
  name      = var.ai_services_name
  location  = var.location
  parent_id = data.azurerm_resource_group.main.id

  identity {
    type = "SystemAssigned"
  }

  body = {
    sku = {
      name = "S0"
    }
    kind = "AIServices"
    properties = {
      apiProperties = {}
      customSubDomainName = var.ai_services_name
      networkAcls = {
        defaultAction = "Allow"
        virtualNetworkRules = []
        ipRules = []
      }
      allowProjectManagement = true
      publicNetworkAccess = "Enabled"
      disableLocalAuth = false
    }
  }

  tags = var.tags
  schema_validation_enabled = false
}

# Create diagnostic settings for AI services
resource "azurerm_monitor_diagnostic_setting" "ai_services" {
  name                       = "diag-${var.ai_services_name}"
  target_resource_id         = azapi_resource.ai_services.id
  log_analytics_workspace_id = var.log_analytics_workspace_id

  enabled_log {
    category_group = "Audit"
  }

  enabled_log {
    category_group = "AllLogs"
  }

  enabled_metric {
    category = "AllMetrics"
  }

  depends_on = [azapi_resource.ai_services]
}

resource "azapi_resource" "ai_foundry_project" {
  name      = var.ai_foundry_project_name
  type      = "Microsoft.CognitiveServices/accounts/projects@2025-04-01-preview"
  location  = var.location
  parent_id = azapi_resource.ai_services.id

  identity {
    type = "SystemAssigned"
  }

  body = {
    kind = "AIServices"
    properties = {}
  }

  depends_on = [azapi_resource.ai_services]

  schema_validation_enabled = false
}

# Create Application Insights connection at AI Services account level
resource "azapi_resource" "ai_services_app_insights_connection" {
  type      = "Microsoft.CognitiveServices/accounts/connections@2025-04-01-preview"
  name      = "connection-${var.app_insights_name}"
  parent_id = azapi_resource.ai_services.id

  body = {
    properties = {
      authType = "ApiKey"
      category = "AppInsights"
      credentials = {
        key = var.app_insights_connection_string
      }
      target = var.app_insights_id
      useWorkspaceManagedIdentity = false
      isSharedToAll = true
      sharedUserList = []
      peRequirement = "NotRequired"
      peStatus = "NotApplicable"
      metadata = {
        ApiType = "Azure"
        ResourceId = var.app_insights_id
      }
    }
  }

  depends_on = [azapi_resource.ai_services]
  schema_validation_enabled = false
}

# Create RAI Policy for content filtering
resource "azapi_resource" "rai_policy" {
  type      = "Microsoft.CognitiveServices/accounts/raiPolicies@2025-04-01-preview"
  name      = "rai-policy-${var.deployment_name}-${var.model_deployment_name}"
  parent_id = azapi_resource.ai_services.id

  body = {
    properties = {
      mode = "Asynchronous_filter"
      basePolicyName = "Microsoft.DefaultV2"
      contentFilters = [
        for filter in var.content_filters : {
          name = filter.name
          severityThreshold = filter.severity_threshold
          blocking = filter.blocking
          enabled = filter.enabled
          source = filter.source
        }
      ]
    }
  }

  depends_on = [azapi_resource.ai_services]
  schema_validation_enabled = false
}

# Create model deployment
resource "azapi_resource" "model_deployment" {
  type      = "Microsoft.CognitiveServices/accounts/deployments@2025-04-01-preview"
  name      = "${var.deployment_name}-${var.model_deployment_name}"
  parent_id = azapi_resource.ai_services.id

  body = {
    sku = {
      name = var.model_sku_name
      capacity = var.model_deployment_capacity
    }
    properties = {
      model = {
        format = var.model_format
        name = var.model_name
        version = var.model_version
      }
      versionUpgradeOption = var.version_upgrade_option
      currentCapacity = var.model_deployment_capacity
      raiPolicyName = azapi_resource.rai_policy.name
    }
  }

  depends_on = [azapi_resource.ai_services, azapi_resource.rai_policy]
  schema_validation_enabled = false
}
