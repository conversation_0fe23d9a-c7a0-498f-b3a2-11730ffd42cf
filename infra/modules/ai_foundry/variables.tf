variable "resource_group_name" {
  description = "Name of the Azure resource group"
  type        = string
}

variable "location" {
  description = "Azure region for resources"
  type        = string
  default     = "eastus"
}

variable "tags" {
  description = "Tags to apply to all resources"
  type        = map(string)
}

variable "log_analytics_workspace_name" {
  description = "Name of the Log Analytics Workspace"
  type        = string
}

variable "log_analytics_workspace_id" {
  description = "ID of the Log Analytics Workspace for diagnostic settings"
  type        = string
}

variable "app_insights_name" {
  description = "Name of the Application Insights"
  type        = string
}

variable "app_insights_id" {
  description = "ID of the Application Insights resource"
  type        = string
}

variable "app_insights_connection_string" {
  description = "Connection string of the Application Insights resource"
  type        = string
  sensitive   = true
}

variable "ai_foundry_project_name" {
  description = "Name of the AI Foundry Project"
  type        = string
}

variable "ai_services_name" {
  description = "Name of the AI Services"
  type        = string
}

variable "deployment_name" {
  description = "Name of the AI Foundry Deployment"
  type        = string
}

variable "model_deployment_name" {
  description = "Name of the model deployment"
  type        = string
  default     = "gpt-4o"
}

variable "model_name" {
  description = "Name of the AI model"
  type        = string
  default     = "gpt-4o"
}

variable "model_version" {
  description = "Version of the AI model"
  type        = string
  default     = "2024-11-20"
}

variable "model_format" {
  description = "Format of the AI model"
  type        = string
  default     = "OpenAI"
}

variable "model_sku_name" {
  description = "SKU name for the model deployment"
  type        = string
  default     = "GlobalStandard"
}

variable "model_deployment_capacity" {
  description = "Capacity for the model deployment"
  type        = number
  default     = 10
}

variable "version_upgrade_option" {
  description = "Version upgrade option for the model"
  type        = string
  default     = "NoAutoUpgrade"
}

variable "content_filters" {
  description = "Configuration for content filters"
  type = list(object({
    name               = string
    severity_threshold = optional(string)
    blocking          = bool
    enabled           = bool
    source            = string
  }))
  default = [
    {
      name               = "Violence"
      severity_threshold = "High"
      blocking          = true
      enabled           = true
      source            = "Prompt"
    },
    {
      name               = "Hate"
      severity_threshold = "High"
      blocking          = true
      enabled           = true
      source            = "Prompt"
    },
    {
      name               = "Sexual"
      severity_threshold = "High"
      blocking          = true
      enabled           = true
      source            = "Prompt"
    },
    {
      name               = "Selfharm"
      severity_threshold = "High"
      blocking          = true
      enabled           = true
      source            = "Prompt"
    },
    {
      name               = "Jailbreak"
      severity_threshold = null
      blocking          = true
      enabled           = true
      source            = "Prompt"
    },
    {
      name               = "Indirect Attack"
      severity_threshold = null
      blocking          = true
      enabled           = true
      source            = "Prompt"
    },
    {
      name               = "Violence"
      severity_threshold = "High"
      blocking          = true
      enabled           = true
      source            = "Completion"
    },
    {
      name               = "Hate"
      severity_threshold = "High"
      blocking          = true
      enabled           = true
      source            = "Completion"
    },
    {
      name               = "Sexual"
      severity_threshold = "High"
      blocking          = true
      enabled           = true
      source            = "Completion"
    },
    {
      name               = "Selfharm"
      severity_threshold = "High"
      blocking          = true
      enabled           = true
      source            = "Completion"
    },
    {
      name               = "Protected Material Text"
      severity_threshold = null
      blocking          = false
      enabled           = true
      source            = "Completion"
    },
    {
      name               = "Protected Material Code"
      severity_threshold = null
      blocking          = false
      enabled           = true
      source            = "Completion"
    }
  ]
}
