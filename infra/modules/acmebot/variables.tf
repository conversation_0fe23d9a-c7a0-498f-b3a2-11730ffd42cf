variable "tags" {
  description = "Additional tags for resources"
  type        = map(string)
  default     = {}
}

variable "environment" {
  description = "The environment"
  type        = string
}

variable "resource_group_name" {
  description = "The name of the resource group"
  type        = string
}

variable "location" {
  description = "The location of the resource group"
  type        = string
}

variable "vault_uri" {
  description = "The URI of the key vault"
  type        = string
}

variable "name" {
  description = "The name of the acmebot"
  type        = string
}

variable "key_vault_id" {
  description = "The ID of the key vault"
  type        = string
}