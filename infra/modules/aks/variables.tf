variable "aks_tenant_id" {
  description = "The tenant ID of the AKS cluster"
  type        = string
}

variable "cluster_name" {
  description = "The name of the AKS cluster"
  type        = string
}

variable "environment" {
  description = "The environment (dev, staging, prod)"
  type        = string
}

variable "project" {
  description = "The project"
  type        = string
}

variable "tags" {
  description = "Additional tags for resources"
  type        = map(string)
  default     = {}
}

variable "location" {
  description = "The Azure region where the AKS cluster will be created"
  type        = string
}

variable "resource_group_name" {
  description = "The name of the resource group"
  type        = string
}

# variable "aks_resource_group_name" {
#   description = "The name of the resource group for the AKS cluster"
#   type        = string
# }

variable "acr_id" {
  description = "The id of the azure container registry"
  type        = string
}

variable "kv_id" {
  description = "The id of the key vault"
  type        = string
}


variable "kubernetes_version" {
  description = "The version of Kubernetes"
  type        = string
  default     = "1.27.7"
}

variable "node_count" {
  description = "The initial number of nodes in the node pool"
  type        = number
  default     = 2
}

variable "vm_size" {
  description = "The size of the Virtual Machine"
  type        = string
  default     = "Standard_D2s_v3"
}

variable "min_node_count" {
  description = "The minimum number of nodes in the node pool"
  type        = number
  default     = 1
}

variable "max_node_count" {
  description = "The maximum number of nodes in the node pool"
  type        = number
  default     = 5
}


variable "dns_private_zone_name" {
  description = "The name of the private DNS zone"
  type        = string
}

variable "dns_private_zone_id" {
  description = "The resource ID of the private DNS zone"
  type        = string
}

variable "vnet_id" {
  description = "The resource ID of the virtual network"
  type        = string
}

variable "aks_subnet_id" {
  description = "The resource ID of the AKS subnet"
  type        = string
}

variable "ai_id" {
  description = "The resource ID of the Azure AI"
  type        = string
}

variable "log_analytics_workspace_id" {
  description = "The resource ID of the Log Analytics workspace"
  type        = string
}

variable "log_streams" {
  type    = list(string)
  default = [
    "Microsoft-ContainerLog",
    "Microsoft-ContainerLogV2",
    "Microsoft-KubeEvents",
    "Microsoft-KubePodInventory"
  ]
}
