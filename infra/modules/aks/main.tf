data "azurerm_client_config" "current" {}

resource "azurerm_user_assigned_identity" "aks_identity" {
  name                = "${var.cluster_name}-identity"
  resource_group_name = var.resource_group_name
  location            = var.location
}

resource "azurerm_role_assignment" "aks_to_dns_private_dns_zone_contributor" {
  scope                = var.dns_private_zone_id
  role_definition_name = "Private DNS Zone Contributor"
  principal_id         = azurerm_user_assigned_identity.aks_identity.principal_id
}

resource "azurerm_role_assignment" "aks_to_vnet_contributor" {
  scope                = var.vnet_id
  role_definition_name = "Network Contributor"
  principal_id         = azurerm_user_assigned_identity.aks_identity.principal_id
}

resource "azurerm_kubernetes_cluster" "aks" {
  name                       = var.cluster_name
  location                   = var.location
  resource_group_name        = var.resource_group_name
  dns_prefix_private_cluster = "${var.cluster_name}-dns"
  kubernetes_version         = var.kubernetes_version
  tags                       = local.common_tags
  private_dns_zone_id        = var.dns_private_zone_id
  private_cluster_enabled    = true

  # used to group all the internal objects of this cluster
  node_resource_group = "${var.cluster_name}-rg-node"

  key_vault_secrets_provider {
    secret_rotation_enabled = true
  }

  identity {
    type         = "UserAssigned"
    identity_ids = [azurerm_user_assigned_identity.aks_identity.id]
  }

  default_node_pool {
    name                 = "default"
    vm_size              = var.vm_size
    min_count            = var.min_node_count
    max_count            = var.max_node_count
    auto_scaling_enabled = true
    vnet_subnet_id       = var.aks_subnet_id
    tags                 = local.common_tags

    upgrade_settings {
      drain_timeout_in_minutes      = 0
      max_surge                     = "10%"
      node_soak_duration_in_minutes = 0
    }
  }

  oidc_issuer_enabled       = true
  workload_identity_enabled = true

  web_app_routing {
    dns_zone_ids = [var.dns_private_zone_id]
  }
  oms_agent {
    log_analytics_workspace_id = var.log_analytics_workspace_id
    msi_auth_for_monitoring_enabled = true
  }
}

# Grant the AKS cluster kubelet identity access to the ACR
resource "azurerm_role_assignment" "aks_to_acr" {
  scope                = var.acr_id
  role_definition_name = "AcrPull"
  principal_id         = azurerm_kubernetes_cluster.aks.kubelet_identity[0].object_id

  depends_on = [
    azurerm_kubernetes_cluster.aks
  ]
}

resource "azurerm_role_assignment" "aks_to_ai" {
  scope                = var.ai_id
  role_definition_name = "Azure AI User"
  principal_id         = azurerm_kubernetes_cluster.aks.kubelet_identity[0].object_id

  depends_on = [
    azurerm_kubernetes_cluster.aks
  ]
}

resource "azurerm_role_assignment" "aks_to_web_app_routing_private_dns_zone" {
  scope                = var.dns_private_zone_id
  role_definition_name = "Private DNS Zone Contributor"
  principal_id         = azurerm_kubernetes_cluster.aks.web_app_routing[0].web_app_routing_identity[0].object_id
}


# Grant the AKS cluster secret identity access to the key vault
resource "azurerm_role_assignment" "aks_to_kv" {
  scope                = var.kv_id
  role_definition_name = "Key Vault Secrets User"
  principal_id         = azurerm_kubernetes_cluster.aks.key_vault_secrets_provider[0].secret_identity[0].object_id

  depends_on = [
    azurerm_kubernetes_cluster.aks
  ]
}

resource "azurerm_key_vault_access_policy" "aks_to_kv_policy" {
  key_vault_id = var.kv_id
  tenant_id    = var.aks_tenant_id
  object_id    = azurerm_kubernetes_cluster.aks.key_vault_secrets_provider[0].secret_identity[0].object_id

  secret_permissions = [
    "Get"
  ]

  certificate_permissions = []

  key_permissions = []

  storage_permissions = []

  depends_on = [
    azurerm_kubernetes_cluster.aks
  ]
}

resource "azurerm_monitor_data_collection_rule" "dcr" {
  name                = "MSCI-${var.location}-${var.cluster_name}"
  resource_group_name = var.resource_group_name
  location            = var.location
  kind                = "Linux"
  tags                = local.common_tags
  destinations {
    log_analytics {
      workspace_resource_id = var.log_analytics_workspace_id
      name                  = "ciworkspace"
    }
  }

  data_flow {
    streams      = var.log_streams
    destinations = ["ciworkspace"]
  }

  data_sources {
    extension {
      input_data_sources = []
      streams            = var.log_streams
      extension_name     = "ContainerInsights"
      extension_json     = jsonencode({
        "dataCollectionSettings" : {
            "interval": "1m",
            "namespaceFilteringMode": "Off",
            "enableContainerLogV2": true
        }
      })
      name               = "ContainerInsightsExtension"
    }
  }
}

resource "azurerm_monitor_data_collection_rule_association" "dcra" {
  name                        = "ContainerInsightsExtension"
  target_resource_id          = azurerm_kubernetes_cluster.aks.id
  data_collection_rule_id     = azurerm_monitor_data_collection_rule.dcr.id
}
