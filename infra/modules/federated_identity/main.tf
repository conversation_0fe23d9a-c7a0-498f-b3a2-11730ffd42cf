data "azurerm_client_config" "current" {}

# Federated identity credential for GitHub Actions
resource "azurerm_federated_identity_credential" "azure_ai_user" {
  name                = "federated-identity-credential-azure-ai-user-${var.project}-${var.environment}"
  resource_group_name = var.resource_group_name
  audience            = ["api://AzureADTokenExchange"]
  issuer              = var.aks_oidc_issuer_url
  parent_id           = var.aks_kubelet_identity_id
  subject             = "system:serviceaccount:default:workload-identity-sa-ai-foundry"
}
