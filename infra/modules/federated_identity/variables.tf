variable "project" {
  description = "The name of the project"
  type        = string
  default     = "demo"

  validation {
    condition     = can(regex("^[a-z0-9-]+$", var.project))
    error_message = "Project name must consist of lowercase letters, numbers, and hyphens."
  }
}

variable "location" {
  description = "The Azure region where the federated identity will be created"
  type        = string
}

variable "resource_group_name" {
  description = "The name of the resource group"
  type        = string
}

variable "environment" {
  description = "The environment (dev, staging, prod)"
  type        = string
}

variable "tags" {
  description = "Additional tags for resources"
  type        = map(string)
  default     = {}
}

variable "aks_kubelet_identity_id" {
  description = "The ID of the AKS kubelet identity"
  type        = string
}

variable "aks_oidc_issuer_url" {
  description = "The OIDC issuer URL for the AKS cluster"
  type        = string
}
