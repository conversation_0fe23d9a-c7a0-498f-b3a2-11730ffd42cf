variable "location" {
  description = "The Azure region where the AKS cluster will be created"
  type        = string
}

variable "project" {
  description = "The project name"
  type        = string
}

variable "resource_group_name" {
  description = "The name of the resource group"
  type        = string
}

variable "environment" {
  description = "The environment (dev, staging, prod)"
  type        = string
}

variable "tags" {
  description = "Additional tags for resources"
  type        = map(string)
  default     = {}
}

variable "acr_name" {
  description = "The name of the container"
  type        = string
}

variable "subnet_id_pe" {
  description = "The subnet ID for the ACR"
  type        = string
}

variable "dns_private_zone_acr_id" {
  description = "The ID of the private DNS zone for ACR"
  type        = string
}

variable "dns_private_zone_acr_name" {
  description = "The name of the private DNS zone for ACR"
  type        = string
}