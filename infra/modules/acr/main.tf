# Azure Container Registry
resource "azurerm_container_registry" "container" {
  name                = var.acr_name
  location            = var.location
  resource_group_name = var.resource_group_name
  sku                 = "Premium"
  admin_enabled       = true
  public_network_access_enabled = false
  tags                = var.tags
}

resource "azurerm_private_endpoint" "acr" {
  name                = "pvep-acr-${var.project}-${var.environment}"
  location            = var.location
  resource_group_name = var.resource_group_name
  subnet_id           = var.subnet_id_pe

  private_service_connection {
    name                           = "acr-connection-${var.project}-${var.environment}"
    private_connection_resource_id = azurerm_container_registry.container.id
    is_manual_connection           = false
    subresource_names              = ["registry"]
  }

  private_dns_zone_group {
    name                 = var.dns_private_zone_acr_name
    private_dns_zone_ids = [var.dns_private_zone_acr_id]
  }
}