# Bastion Host VM Module
data "azurerm_subscription" "current" {}

resource "azurerm_user_assigned_identity" "identity-vm" {
  name                = "identity-vm"
  resource_group_name = var.resource_group_name
  location            = var.location
}

resource "azurerm_role_assignment" "vm-contributor" {
  scope                = data.azurerm_subscription.current.id
  role_definition_name = "Contributor"
  principal_id         = azurerm_user_assigned_identity.identity-vm.principal_id
}

resource "azurerm_network_interface" "vm" {
  name                 = "vm-ni-${var.project}-${var.environment}"
  resource_group_name  = var.resource_group_name
  location             = var.location

  ip_configuration {
    name                          = "internal"
    subnet_id                     = var.vm_subnet_id
    private_ip_address_allocation = "Dynamic"
    public_ip_address_id          = null
  }
}

resource "azurerm_linux_virtual_machine" "vm-linux" {
  name                            = "vm-linux-jumpbox-${var.project}-${var.environment}"
  resource_group_name             = var.resource_group_name
  location                        = var.location
  size                            = "Standard_D2s_v3"

  disable_password_authentication = true
  admin_username                  = var.admin_username
  admin_ssh_key {
    username   = var.admin_username
    public_key = var.admin_ssh_public_key
  }
  # admin_password                  = "@Aa123456789"
  network_interface_ids           = [azurerm_network_interface.vm.id]
  priority                        = "Spot"
  eviction_policy                 = "Deallocate"

  # custom_data = filebase64("../../modules/vm/install-tools.sh")

  identity {
    type         = "UserAssigned"
    identity_ids = [azurerm_user_assigned_identity.identity-vm.id]
  }

  os_disk {
    name                 = "os-disk-vm"
    caching              = "ReadWrite"
    storage_account_type = "Standard_LRS"
  }

  source_image_reference {
    publisher = "canonical"
    offer     = "0001-com-ubuntu-server-jammy"
    sku       = "22_04-lts-gen2"
    version   = "latest"
  }

  additional_capabilities {
    hibernation_enabled = false
  }

  boot_diagnostics {
    storage_account_uri = null
  }
}

resource "azurerm_public_ip" "bastion" {
  name                = "pip-${var.project}-${var.environment}"
  location            = var.location
  resource_group_name = var.resource_group_name
  allocation_method   = "Static"
  sku                 = "Standard"
  tags                = local.common_tags
}

resource "azurerm_bastion_host" "bastion" {
  name                   = "bastion-${var.project}-${var.environment}"
  location               = var.location
  resource_group_name    = var.resource_group_name
  sku                    = "Standard"
  copy_paste_enabled     = true
  file_copy_enabled      = false
  shareable_link_enabled = false
  tunneling_enabled      = true
  ip_connect_enabled     = false

  ip_configuration {
    name                 = "configuration"
    subnet_id            = var.bastion_subnet_id
    public_ip_address_id = azurerm_public_ip.bastion.id
  }

  tags = local.common_tags
}
