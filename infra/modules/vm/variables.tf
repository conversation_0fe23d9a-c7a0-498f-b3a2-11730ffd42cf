variable "project" {
  description = "The project name."
  type        = string
}

variable "environment" {
  description = "The environment name."
  type        = string
}

variable "location" {
  description = "The Azure location for the resources."
  type        = string
}

variable "resource_group_name" {
  description = "The name of the resource group in which to create the resources."
  type        = string
}

variable "vm_subnet_id" {
  description = "The subnet ID to which the bastion host will be attached."
  type        = string
}

variable "admin_username" {
  description = "The admin username for the VM."
  type        = string
}

variable "admin_ssh_public_key" {
  description = "The SSH public key for the admin user."
  type        = string
}

variable "bastion_subnet_id" {
  description = "The subnet ID for the Azure Bastion host."
  type        = string
}

variable "tags" {
  description = "Common tags for the resources."
  type        = map(string)
}
