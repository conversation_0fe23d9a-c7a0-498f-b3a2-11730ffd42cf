data "azurerm_client_config" "current" {}

# Azure Key Vault resource
resource "azurerm_key_vault" "kv" {
  name                        = local.key_vault_name
  location                    = var.location
  resource_group_name         = var.resource_group_name
  tenant_id                   = data.azurerm_client_config.current.tenant_id
  sku_name                    = "standard"
  tags                        = local.common_tags

  lifecycle {
    ignore_changes = [access_policy]
  }
}

resource "azurerm_key_vault_secret" "secrets" {
  for_each = toset(local.secrets)
  name = each.key
  value        = "<placeholder>"
  key_vault_id = azurerm_key_vault.kv.id

  lifecycle {
    ignore_changes = [ value ] # ensure secrets are not overwritten by new deploys
  }
}

resource "azurerm_private_endpoint" "kv" {
  name                = "pvep-kv-${var.project}-${var.environment}"
  location            = var.location
  resource_group_name = var.resource_group_name
  subnet_id           = var.subnet_id_pe

  private_service_connection {
    name                           = "kv-connection-${var.project}-${var.environment}"
    private_connection_resource_id = azurerm_key_vault.kv.id
    is_manual_connection           = false
    subresource_names              = ["vault"]
  }

  private_dns_zone_group {
    name                 = var.dns_private_zone_kv_name
    private_dns_zone_ids = [var.dns_private_zone_kv_id]
  }
}
