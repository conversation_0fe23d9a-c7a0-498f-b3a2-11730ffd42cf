locals {
  # Common tags - derived from variables but standardized across resources
  common_tags = merge(
    {
      module     = "keyvault"
    },
    var.tags
  )

  key_vault_name      = "kv-${var.project}-${var.environment}"

  secrets = [
    "auth-api-key",
    "database-url",
    "elevenlabs-api-key",
    "firebase-private-key",
    "firebase-private-key-base-64",
    "jwt-secret",
    "openai-token",
    "rabbitmq-password",
    "rabbitmq-url",
    "sendgrid-api-key",
    "swagger-password",
    "swagger-user"
  ]
}
