variable "project" {
  description = "The name of the project"
  type        = string
  default     = "demo"

  validation {
    condition     = can(regex("^[a-z0-9-]+$", var.project))
    error_message = "Project name must consist of lowercase letters, numbers, and hyphens."
  }
}

variable "location" {
  description = "The Azure region where the AKS cluster will be created"
  type        = string
}

variable "resource_group_name" {
  description = "The name of the resource group"
  type        = string
}

variable "environment" {
  description = "The environment (dev, staging, prod)"
  type        = string
}

variable "tags" {
  description = "Additional tags for resources"
  type        = map(string)
  default     = {}
}

variable "subnet_id_pe" {
  description = "The subnet ID for the private endpoint"
  type        = string
}

variable "dns_private_zone_kv_id" {
  description = "The ID of the private DNS zone for KeyVault"
  type        = string
}

variable "dns_private_zone_kv_name" {
  description = "The name of the private DNS zone for KeyVault"
  type        = string
}