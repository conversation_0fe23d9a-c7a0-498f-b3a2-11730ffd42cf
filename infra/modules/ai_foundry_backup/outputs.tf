output "ai_services_id" {
  description = "ID of the AI Services account"
  value       = azapi_resource.ai_services.id
}

output "ai_services_name" {
  description = "Name of the AI Services account"
  value       = azapi_resource.ai_services.name
}

output "ai_services_diagnostic_setting_id" {
  description = "ID of the AI Services diagnostic setting"
  value       = azurerm_monitor_diagnostic_setting.ai_services.id
}

output "rai_policy_id" {
  description = "ID of the RAI Policy"
  value       = azapi_resource.rai_policy.id
}

output "rai_policy_name" {
  description = "Name of the RAI Policy"
  value       = azapi_resource.rai_policy.name
}

output "model_deployment_id" {
  description = "ID of the model deployment"
  value       = azapi_resource.model_deployment.id
}

output "model_deployment_name" {
  description = "Name of the model deployment"
  value       = azapi_resource.model_deployment.name
}

output "ai_services_app_insights_connection_id" {
  description = "ID of the AI Services Application Insights connection"
  value       = azapi_resource.ai_services_app_insights_connection.id
}

output "ai_services_app_insights_connection_name" {
  description = "Name of the AI Services Application Insights connection"
  value       = azapi_resource.ai_services_app_insights_connection.name
}

output "ai_foundry_project_id" {
  description = "ID of the Cognitive Services AI project"
  value       = azapi_resource.ai_foundry_project.id
}

output "ai_foundry_project_name" {
  description = "Name of the Cognitive Services AI project"
  value       = azapi_resource.ai_foundry_project.name
}
