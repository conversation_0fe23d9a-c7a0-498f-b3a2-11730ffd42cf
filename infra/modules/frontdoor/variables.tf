variable "project" {
  description = "The name of the project"
  type        = string
  default     = "demo"

  validation {
    condition     = can(regex("^[a-z0-9-]+$", var.project))
    error_message = "Project name must consist of lowercase letters, numbers, and hyphens."
  }
}

variable "location" {
  description = "The Azure region where the AKS cluster will be created"
  type        = string
}

variable "resource_group_name" {
  description = "The name of the resource group"
  type        = string
}

variable "environment" {
  description = "The environment (dev, staging, prod)"
  type        = string
}

variable "tags" {
  description = "Additional tags for resources"
  type        = map(string)
  default     = {}
}

# variable "dns_private_zone_id" {
#   type        = string
#   description = "The DNS zone ID"
# }

# variable "dns_private_zone_name" {
#   type        = string
#   description = "The DNS zone name"
# }

# variable "front_door_custom_domain_name" {
#   type        = string
#   description = "The custom domain name for the Front Door"
# }

variable "dns_public_zone_name" {
  type        = string
  description = "The public DNS name"
}

variable "dns_public_zone_id" {
  type        = string
  description = "The public DNS zone ID"
}


variable "api_cname_record_name" {
  type        = string
  description = "The CNAME record name for the API"
}

variable "private_link_service_id" {
  type        = string
  description = "The ID of the private link service"
}

variable "private_link_service_alias" {
  type        = string
  description = "The alias of the private link service"
}

variable "log_analytics_workspace_id" {
  type        = string
  description = "The ID of the log analytics workspace"
}
