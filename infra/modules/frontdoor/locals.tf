locals {
  # Common tags - derived from variables but standardized across resources
  common_tags = merge(
    {
      module     = "frontdoor"
    },
    var.tags
  )

  front_door_profile_name      = "fd-${var.project}-${var.environment}"
  front_door_endpoint_name     = "fd-${var.project}-${var.environment}"
  front_door_origin_group_name = "${var.project}-${var.environment}-origin-group"

  firewall_policy_name         = "fw-${var.project}-${var.environment}"
  # custom_domain_name           = "domain-${var.project}-${var.environment}"

  front_door_origin_name       = "${var.project}-${var.environment}-origin"
  front_door_route_name        = "${var.project}-${var.environment}-route"
}