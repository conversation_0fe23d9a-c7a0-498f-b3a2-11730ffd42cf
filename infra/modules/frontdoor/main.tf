

resource "azurerm_cdn_frontdoor_profile" "front_door" {
  name                = local.front_door_profile_name
  resource_group_name = var.resource_group_name
  sku_name            = "Premium_AzureFrontDoor"
  response_timeout_seconds = 60
  tags = var.tags
}

resource "azurerm_cdn_frontdoor_endpoint" "endpoint" {
  name                     = "endpoint-${var.project}-${var.environment}"
  cdn_frontdoor_profile_id = azurerm_cdn_frontdoor_profile.front_door.id
}


resource "azurerm_cdn_frontdoor_origin_group" "origin_group" {
  name                     = local.front_door_origin_group_name
  cdn_frontdoor_profile_id = azurerm_cdn_frontdoor_profile.front_door.id
  session_affinity_enabled = true

  load_balancing {
    sample_size                 = 4
    successful_samples_required = 3
  }

  health_probe {
    path                = "/"
    request_type        = "GET"
    protocol            = "Http"
    interval_in_seconds = 100
  }
}

resource "azurerm_cdn_frontdoor_origin" "origin" {
  name                          = local.front_door_origin_name
  cdn_frontdoor_origin_group_id = azurerm_cdn_frontdoor_origin_group.origin_group.id

  enabled                        = true
  host_name                      = var.private_link_service_alias
  http_port                      = 80
  https_port                     = 443
  priority                       = 1
  weight                         = 1000
  certificate_name_check_enabled = true

  private_link {
    request_message        = "Request access for Private Link Origin CDN Frontdoor"
    location               = var.location
    private_link_target_id = var.private_link_service_id
  }
}

resource "azurerm_cdn_frontdoor_route" "front_door_route" {
  name                          = local.front_door_route_name
  cdn_frontdoor_endpoint_id     = azurerm_cdn_frontdoor_endpoint.endpoint.id
  cdn_frontdoor_origin_group_id = azurerm_cdn_frontdoor_origin_group.origin_group.id
  cdn_frontdoor_origin_ids      = [azurerm_cdn_frontdoor_origin.origin.id]
  cdn_frontdoor_custom_domain_ids = [azurerm_cdn_frontdoor_custom_domain.custom_domain.id]

  supported_protocols    = ["Http", "Https"]
  patterns_to_match      = ["/*"]
  forwarding_protocol    = "HttpOnly"
  link_to_default_domain = false
  https_redirect_enabled = true

  depends_on = [
    azurerm_cdn_frontdoor_custom_domain.custom_domain
  ]
}



resource "azurerm_cdn_frontdoor_custom_domain" "custom_domain" {
  name                     = "domain-${var.project}-${var.environment}"
  cdn_frontdoor_profile_id = azurerm_cdn_frontdoor_profile.front_door.id
  dns_zone_id              = var.dns_public_zone_id
  host_name                = "${var.api_cname_record_name}.${var.dns_public_zone_name}"

  tls {
    certificate_type    = "ManagedCertificate"
  }
}

resource "azurerm_dns_cname_record" "azure_endpoint" {
  name                = var.api_cname_record_name
  zone_name           = var.dns_public_zone_name
  resource_group_name = var.resource_group_name
  ttl                 = 300
  record              = azurerm_cdn_frontdoor_endpoint.endpoint.host_name
}

resource "azurerm_cdn_frontdoor_firewall_policy" "waf_policy" {
  name                = "WAF${var.project}${var.environment}"
  resource_group_name = var.resource_group_name
  sku_name            = azurerm_cdn_frontdoor_profile.front_door.sku_name
  enabled             = true
  mode                = "Prevention"

  managed_rule {
    type    = "Microsoft_DefaultRuleSet"
    version = "2.1"
    action  = "Block"
  }

  managed_rule {
    type    = "Microsoft_BotManagerRuleSet"
    version = "1.0"
    action  = "Block"
  }
}

resource "azurerm_cdn_frontdoor_security_policy" "security_policy" {
  name                     = "security-policy-${var.project}-${var.environment}"
  cdn_frontdoor_profile_id = azurerm_cdn_frontdoor_profile.front_door.id

  security_policies {
    firewall {
      cdn_frontdoor_firewall_policy_id = azurerm_cdn_frontdoor_firewall_policy.waf_policy.id

      association {
        domain {
          cdn_frontdoor_domain_id = azurerm_cdn_frontdoor_custom_domain.custom_domain.id
        }
        patterns_to_match = ["/*"]
      }
    }
  }
}

# Create diagnostic settings for Front Door services
resource "azurerm_monitor_diagnostic_setting" "fd_services" {
  name                       = "diag-${local.front_door_profile_name}"
  target_resource_id         = azurerm_cdn_frontdoor_profile.front_door.id
  log_analytics_workspace_id = var.log_analytics_workspace_id

  enabled_log {
    category_group = "Audit"
  }

  enabled_log {
    category_group = "AllLogs"
  }

  enabled_metric {
    category = "AllMetrics"
  }

  depends_on = [
    azurerm_cdn_frontdoor_profile.front_door
  ]
}
