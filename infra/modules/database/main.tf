# PostgreSQL Flexible Server (Single Availability Zone)
resource "azurerm_postgresql_flexible_server" "postgres" {
  name                    = "pg-${var.project}-${var.environment}"
  location                = var.location
  resource_group_name     = var.resource_group_name
  sku_name                = var.sku_name
  # storage_mb              = var.postgres_storage_mb
  version                 = "14"
  administrator_login     = var.postgres_admin_username
  administrator_password  = var.postgres_admin_password
  zone                    = "1"
  tags                    = local.common_tags

  identity {
    type = "SystemAssigned"
    identity_ids = []
  }
}

resource "azurerm_postgresql_flexible_server_configuration" "pgbouncer" {
  name      = "pgbouncer.enabled"
  value     = "true"
  server_id = azurerm_postgresql_flexible_server.postgres.id
}

resource "azurerm_postgresql_flexible_server_firewall_rule" "allow_azure_services" {
  name             = "allow-azure-services"
  server_id        = azurerm_postgresql_flexible_server.postgres.id
  start_ip_address = "0.0.0.0"
  end_ip_address   = "0.0.0.0"
}

resource "azurerm_postgresql_flexible_server_firewall_rule" "allow_honest_fox" {
  name             = "allow-honest-fox"
  server_id        = azurerm_postgresql_flexible_server.postgres.id
  start_ip_address = "************"
  end_ip_address   = "************"
}

# Dynamic firewall rules for additional IP addresses
resource "azurerm_postgresql_flexible_server_firewall_rule" "additional_ips" {
  for_each = { for idx, ip in var.additional_allowed_ips : ip.name => ip }

  name             = each.value.name
  server_id        = azurerm_postgresql_flexible_server.postgres.id
  start_ip_address = each.value.start_ip_address
  end_ip_address   = each.value.end_ip_address
}

