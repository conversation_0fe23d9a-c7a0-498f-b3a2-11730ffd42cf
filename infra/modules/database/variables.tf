variable "tags" {
  description = "Additional tags for resources"
  type        = map(string)
  default     = {}
}

variable "resource_group_name" {
  description = "The name of the resource group"
  type        = string
}

variable "environment" {
  description = "The environment (dev, staging, prod)"
  type        = string
}

variable "project" {
  description = "The name of the project"
  type        = string

  validation {
    condition     = can(regex("^[a-z0-9-]+$", var.project))
    error_message = "Project name must consist of lowercase letters, numbers, and hyphens."
  }
}

variable "location" {
  description = "The Azure region where the AKS cluster will be created"
  type        = string
}

variable "postgres_admin_username" {
  description = "The postgres admin username"
  type        = string
}

variable "postgres_admin_password" {
  description = "The postgres admin password"
  type        = string
}

variable "sku_name" {
  description = "The SKU name for the PostgreSQL Flexible Server"
  type        = string
  default     = "GP_Standard_D2ds_v5"
}

variable "additional_allowed_ips" {
  description = "List of additional IP addresses to allow access to the PostgreSQL server"
  type        = list(object({
    name             = string
    start_ip_address = string
    end_ip_address   = string
  }))
  default     = []
}
