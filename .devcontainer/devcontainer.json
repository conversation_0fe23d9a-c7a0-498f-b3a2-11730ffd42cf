// For format details, see https://aka.ms/devcontainer.json. For config options, see the
// README at: https://github.com/devcontainers/templates/tree/main/src/docker-existing-dockerfile
{
  "name": "Healthis Development Environment",
  "build": {
    // Sets the run context to one level up instead of the .devcontainer folder.
    "context": "..",
    "privileged": true,
    // Update the 'dockerFile' property if you aren't using the standard 'Dockerfile' filename.
    "dockerfile": "../docker/microservices/DOCKERFILE",
    "target": "dev",
    "runArgs": ["--network=host"],
    "capAdd": ["NET_RAW", "NET_ADMIN"],
    "args": {
      "network": "host"
    }
  },
  "containerEnv": {
    "NODE_ENV": "development",
    "DATABASE_URL": "postgres://postgres:<EMAIL>:5432/core"
  },
  //"features": {
  //	"ghcr.io/devcontainers/features/node:1": {}
  //}

  // Features to add to the dev container. More info: https://containers.dev/features.
  // "features": {},

  // Use 'forwardPorts' to make a list of ports inside the container available locally.
  // "forwardPorts": [],

  // Uncomment the next line to run commands after the container is created.
  // "postCreateCommand": "cat /etc/os-release",
  "forwardPorts": [3000, 3001, 4000],

  "portsAttributes": {
    "3000": {
      "label": "core",
      "onAutoForward": "notify"
    },
    "3001": {
      "label": "ai",
      "onAutoForward": "notify"
    },
    "4000": {
      "label": "scheduler",
      "onAutoForward": "notify"
    }
  },

  //"postCreateCommand": "",

  // Configure tool-specific properties.
  "customizations": {
    "vscode": {
      "extensions": [
        "firsttris.vscode-jest-runner",
        "esbenp.prettier-vscode",
        "nrwl.angular-console",
        "dbaeumer.eslint",
        "mhutchie.git-graph",
        "bocovo.prisma-erd-visualizer",
        "prisma.prisma",
        "eamodio.gitlens",
        "streetsidesoftware.code-spell-checker"
      ]
    }
  }

  // Uncomment to connect as an existing user other than the container default. More info: https://aka.ms/dev-containers-non-root.
  // "remoteUser": "devcontainer"
}
