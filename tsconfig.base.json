{
  "compileOnSave": false,
  "compilerOptions": {
    "rootDir": ".",
    "baseUrl": "./",
    "sourceMap": true,
    "moduleResolution": "NodeNext",
    "target": "ESNext",
    "module": "NodeNext",
    "lib": ["es2020", "dom"],
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "importHelpers": true,
    "skipLibCheck": true,
    "skipDefaultLibCheck": true,
    "strict": false,
    "paths": {
      "@core/libs": ["libs/index.ts"],
      "@core/prisma-client": ["libs/db/prisma-client/src/index.ts"],
      "@core/prisma-schema": ["libs/db/prisma-schema/prisma/schema.prisma"],
      "@core_be/aiml": ["libs/aiml/src/index.ts"],
      "@core_be/global": ["libs/global/src/index.ts"],
      "@core_be/auth": ["libs/auth/src/index.ts"],
      "@core_be/data-access": ["libs/data-access/src/index.ts"],
      "@core_be/email": ["libs/email/src/index.ts"],
      "@core_be/exception-filter": ["libs/filters/global-exception-filter.ts"],
      "@core_be/notifications": ["libs/notifications/src/index.ts"],
      "@core_be/ailmentservice": ["apps/ai/src/app/api/ailments/services/ailments.service.ts"],
      "@core_be/sessionservice": ["apps/ai/src/app/api/sessions/services/session.service.ts"],
      "@core_be/socket": ["libs/socket/src/index.ts"],

    }
  },
  "exclude": [
    "node_modules",
    "tmp",
    "db_data",
    "dist",
    "pgadmin-data",
    "**/*.spec.ts"
  ]
}
