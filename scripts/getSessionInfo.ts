// getSessionInfo.ts
// Script to retrieve healing session information, healing rounds, session summaries, session ailments, and user/healer profiles by session ID from PostgreSQL
// Outputs results in markdown format with UTC timestamps
//
// Environment Variables:
// - DB_HOST (or DATABASE_HOST): Database host (default: localhost)
// - DB_PORT (or DATABASE_PORT): Database port (default: 5432)
// - DB_NAME (or DATABASE_NAME or POSTGRES_DB): Database name (default: healthis)
// - DB_USER (or DATABASE_USER or POSTGRES_USER): Database user (default: postgres)
// - DB_PASSWORD (or DATABASE_PASSWORD or POSTGRES_PASSWORD): Database password (required)
//
// Usage: npx tsx scripts/getSessionInfo.ts <session_id> [--save]
// Output: Markdown formatted tables with UTC timestamps for profiles, session info, healing rounds, summaries, and ailments
// Optional: Save output to ./scripts/result/session-{session_id}.md with --save flag

import { Client } from 'pg';
import { writeFileSync, mkdirSync } from 'fs';
import { join, dirname } from 'path';

// Try to load dotenv if available
try {
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  require('dotenv').config();
} catch (error) {
  // dotenv not available, will use environment variables only
  console.log('ℹ️  dotenv not available, using environment variables only');
}

// Database Configuration from environment variables
const DB_HOST = process.env.DB_HOST || process.env.DATABASE_HOST || 'localhost';
const DB_PORT = parseInt(process.env.DB_PORT || process.env.DATABASE_PORT || '5432');
const DB_NAME = process.env.DB_NAME || process.env.DATABASE_NAME || process.env.POSTGRES_DB || 'healthis';
const DB_USER = process.env.DB_USER || process.env.DATABASE_USER || process.env.POSTGRES_USER || 'postgres';
const DB_PASSWORD = process.env.DB_PASSWORD || process.env.DATABASE_PASSWORD || process.env.POSTGRES_PASSWORD || '';

// Validate required environment variables
if (!DB_PASSWORD) {
  console.error('❌ Error: Database password is required. Set DB_PASSWORD, DATABASE_PASSWORD, or POSTGRES_PASSWORD environment variable.');
  process.exit(1);
}

// Database configuration
const dbConfig = {
  host: DB_HOST,
  port: DB_PORT,
  database: DB_NAME,
  user: DB_USER,
  password: DB_PASSWORD,
  ssl: {
    rejectUnauthorized: false, // For self-signed certificates or development
    // If you have a proper SSL certificate, use:
    // rejectUnauthorized: true
  },
};

class SessionInfoRetriever {
  private client: Client;

  constructor() {
    this.client = new Client(dbConfig);
  }

  async connect(): Promise<void> {
    try {
      console.log(`🔗 Connecting to database: ${DB_HOST}:${DB_PORT}/${DB_NAME} as ${DB_USER}`);
      await this.client.connect();
      console.log('✅ Connected to PostgreSQL database');
    } catch (error) {
      console.error('❌ Failed to connect to database:', error.message);
      console.error('💡 Make sure the following environment variables are set:');
      console.error('   - DB_HOST (or DATABASE_HOST)');
      console.error('   - DB_PORT (or DATABASE_PORT)');
      console.error('   - DB_NAME (or DATABASE_NAME or POSTGRES_DB)');
      console.error('   - DB_USER (or DATABASE_USER or POSTGRES_USER)');
      console.error('   - DB_PASSWORD (or DATABASE_PASSWORD or POSTGRES_PASSWORD)');
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    try {
      await this.client.end();
      console.log('✅ Disconnected from database');
    } catch (error) {
      console.error('❌ Error disconnecting:', error.message);
    }
  }

  async getSessionInfo(sessionId: string): Promise<any> {
    try {
      const query = `
        SELECT
          session_id,
          profile_id,
          created_at,
          session_start_at,
          status,
          sub_status,
          sub_status_updated_at,
          queue_number,
          queue_start_time,
          last_session_offer_at,
          updated_at,
          session_type,
          healer_id,
          cloud_region,
          thread_id,
          session_end_at
        FROM healing_sessions
        WHERE session_id = $1
        ORDER BY session_id DESC;
      `;

      console.log(`🔍 Searching for session with ID: ${sessionId}`);

      const result = await this.client.query(query, [sessionId]);

      if (result.rows.length === 0) {
        console.log(`⚠️  No session found with ID: ${sessionId}`);
        return null;
      }

      console.log(`✅ Found ${result.rows.length} session(s)`);
      return result.rows;
    } catch (error) {
      console.error('❌ Error retrieving session info:', error.message);
      throw error;
    }
  }

  async getHealingRounds(sessionId: string): Promise<any[]> {
    try {
      const query = `
        SELECT
          session_id,
          round_id,
          round_number,
          healer_id,
          round_start_at,
          feedback_start_at,
          round_end_at,
          assistant_thread_id,
          cloud_region,
          status,
          is_patient_confirmed,
          is_healer_confirmed,
          check_in_count,
          is_positive_feedback
        FROM healing_rounds
        WHERE session_id = $1
        ORDER BY round_id ASC;
      `;

      console.log(`🔍 Searching for healing rounds for session ID: ${sessionId}`);

      const result = await this.client.query(query, [sessionId]);

      console.log(`✅ Found ${result.rows.length} healing round(s)`);
      return result.rows;
    } catch (error) {
      console.error('❌ Error retrieving healing rounds:', error.message);
      throw error;
    }
  }

  async getSessionSummaries(sessionId: string): Promise<any[]> {
    try {
      const query = `
        SELECT
          session_id,
          round_id,
          summary_id,
          content,
          created_at
        FROM session_summaries
        WHERE session_id = $1
        ORDER BY created_at ASC;
      `;

      console.log(`🔍 Searching for session summaries for session ID: ${sessionId}`);

      const result = await this.client.query(query, [sessionId]);

      console.log(`✅ Found ${result.rows.length} session summar${result.rows.length === 1 ? 'y' : 'ies'}`);
      return result.rows;
    } catch (error) {
      console.error('❌ Error retrieving session summaries:', error.message);
      throw error;
    }
  }

  async getSessionAilments(sessionId: string): Promise<any[]> {
    try {
      const query = `
        SELECT
          session_id,
          summary_id,
          round_id,
          session_ailment_id,
          name,
          description,
          level,
          created_at
        FROM
          session_ailments
        WHERE session_id = $1
        ORDER BY created_at ASC;
      `;

      console.log(`🔍 Searching for session ailments for session ID: ${sessionId}`);

      const result = await this.client.query(query, [sessionId]);

      console.log(`✅ Found ${result.rows.length} session ailment${result.rows.length === 1 ? '' : 's'}`);
      return result.rows;
    } catch (error) {
      console.error('❌ Error retrieving session ailments:', error.message);
      throw error;
    }
  }

  async getProfile(profileId: string): Promise<any> {
    try {
      const query = `
        SELECT
          p.profile_id,
          p.profile_type,
          p.created_at,
          p.updated_at,
          u.username,
          u.last_login_date
        FROM profiles p
        JOIN users u ON p.user_id = u.user_id
        WHERE p.profile_id = $1;
      `;

      const result = await this.client.query(query, [profileId]);

      return result.rows[0] || null;
    } catch (error) {
      console.error(`❌ Error retrieving profile ${profileId}:`, error.message);
      return null;
    }
  }

  async getProfiles(sessionInfo: any[], healingRounds: any[]): Promise<{userProfile: any, healerProfiles: any[]}> {
    try {
      console.log('🔍 Searching for user and healer profiles...');

      // Get user profile from session
      const userProfileId = sessionInfo[0]?.profile_id;
      let userProfile = null;
      if (userProfileId) {
        userProfile = await this.getProfile(userProfileId);
      }

      // Collect all unique healer IDs from session and healing rounds
      const healerIds = new Set();

      // Add healer ID from session if exists
      if (sessionInfo[0]?.healer_id) {
        healerIds.add(sessionInfo[0].healer_id);
      }

      // Add healer IDs from healing rounds
      healingRounds.forEach(round => {
        if (round.healer_id) {
          healerIds.add(round.healer_id);
        }
      });

      // Get healer profiles
      const healerProfiles: any[] = [];
      for (const healerId of Array.from(healerIds)) {
        const healerProfile = await this.getProfile(healerId as string);
        if (healerProfile) {
          healerProfiles.push(healerProfile);
        }
      }

      console.log(`✅ Found user profile: ${userProfile ? 'Yes' : 'No'}`);
      console.log(`✅ Found ${healerProfiles.length} healer profile${healerProfiles.length === 1 ? '' : 's'}`);

      return { userProfile, healerProfiles };
    } catch (error) {
      console.error('❌ Error retrieving profiles:', error.message);
      return { userProfile: null, healerProfiles: [] };
    }
  }

  private formatTimestampUTC(timestamp: any): string {
    if (!timestamp) return 'N/A';

    try {
      const date = new Date(timestamp);
      return date.toISOString().replace('T', ' ').replace('.000Z', ' UTC');
    } catch (error) {
      return 'Invalid Date';
    }
  }

  private outputMarkdown(sessions: any[], healingRounds: any[], sessionSummaries: any[], sessionAilments: any[], profiles: {userProfile: any, healerProfiles: any[]}, sessionId: string, saveToFile: boolean): void {
    const lines: string[] = [];

    lines.push('# 📊 Healing Session Information\n');

    // Add profiles section
    if (profiles.userProfile || profiles.healerProfiles.length > 0) {
      lines.push('## 👥 Profiles\n');

      // Add user profile
      if (profiles.userProfile) {
        lines.push('### 👤 User Profile\n');
        lines.push('| **Profile ID** | **Type** | **Username** | **Created At** | **Updated At** | **Last Login** |');
        lines.push('|----------------|----------|--------------|----------------|----------------|----------------|');

        const userProfile = profiles.userProfile;
        lines.push(`| \`${userProfile.profile_id || 'N/A'}\` | \`${userProfile.profile_type || 'N/A'}\` | ${userProfile.username || 'N/A'} | ${this.formatTimestampUTC(userProfile.created_at)} | ${this.formatTimestampUTC(userProfile.updated_at)} | ${this.formatTimestampUTC(userProfile.last_login_date)} |`);
        lines.push('');
      }

      // Add healer profiles
      if (profiles.healerProfiles.length > 0) {
        lines.push('### 🩺 Healer Profile(s)\n');
        lines.push('| **Profile ID** | **Type** | **Username** | **Created At** | **Updated At** | **Last Login** |');
        lines.push('|----------------|----------|--------------|----------------|----------------|----------------|');

        profiles.healerProfiles.forEach((healerProfile) => {
          lines.push(`| \`${healerProfile.profile_id || 'N/A'}\` | \`${healerProfile.profile_type || 'N/A'}\` | ${healerProfile.username || 'N/A'} | ${this.formatTimestampUTC(healerProfile.created_at)} | ${this.formatTimestampUTC(healerProfile.updated_at)} | ${this.formatTimestampUTC(healerProfile.last_login_date)} |`);
        });
        lines.push('');
      }

      lines.push('---\n');
    }

    // Create wide table header for session information
    lines.push('| **Session ID** | **Profile ID** | **Created At** | **Start At** | **End At** | **Status** | **Sub Status** | **Type** | **Healer ID** | **Queue #** | **Queue Start** | **Region** | **Thread ID** |');
    lines.push('|----------------|----------------|----------------|-------------|-------------|------------|---------------|----------|---------------|-------------|-----------------|------------|---------------|');

    // Add each session as a single row
    sessions.forEach((session) => {
      lines.push(`| \`${session.session_id || 'N/A'}\` | \`${session.profile_id || 'N/A'}\` | ${this.formatTimestampUTC(session.created_at)} | ${this.formatTimestampUTC(session.session_start_at)} | ${this.formatTimestampUTC(session.session_end_at)} | \`${session.status || 'N/A'}\` | \`${session.sub_status || 'N/A'}\` | \`${session.session_type || 'N/A'}\` | \`${session.healer_id || 'N/A'}\` | ${session.queue_number || 'N/A'} | ${this.formatTimestampUTC(session.queue_start_time)} | \`${session.cloud_region || 'N/A'}\` | \`${session.thread_id || 'N/A'}\` |`);
    });

    lines.push('');

    // Add session-level summaries and ailments
    const sessionLevelSummaries = sessionSummaries.filter(summary => summary.round_id === null);
    const sessionLevelAilments = sessionAilments.filter(ailment => ailment.round_id === null);

    if (sessionLevelSummaries.length > 0 || sessionLevelAilments.length > 0) {
      lines.push('## Session Details\n');

      // Add session-level summaries
      if (sessionLevelSummaries.length > 0) {
        lines.push('### 📝 Session Summaries\n');
        lines.push('| **Summary ID** | **Content** | **Created At** |');
        lines.push('|----------------|-------------|----------------|');

        sessionLevelSummaries.forEach((summary) => {
          const content = summary.content ? summary.content.replace(/\|/g, '\\|').replace(/\n/g, ' ') : '*No content*';
          lines.push(`| \`${summary.summary_id || 'N/A'}\` | ${content} | ${this.formatTimestampUTC(summary.created_at)} |`);
        });
        lines.push('');
      }

      // Add session-level ailments
      if (sessionLevelAilments.length > 0) {
        lines.push('### 🩺 Session Ailments\n');
        lines.push('| **Ailment ID** | **Name** | **Description** | **Level** | **Summary ID** | **Created At** |');
        lines.push('|----------------|----------|-----------------|-----------|----------------|----------------|');

        sessionLevelAilments.forEach((ailment) => {
          lines.push(`| \`${ailment.session_ailment_id || 'N/A'}\` | ${ailment.name || 'N/A'} | ${ailment.description || 'N/A'} | ${ailment.level || 'N/A'} | \`${ailment.summary_id || 'N/A'}\` | ${this.formatTimestampUTC(ailment.created_at)} |`);
        });
        lines.push('');
      }
    }

    // Add healing rounds section
    if (healingRounds && healingRounds.length > 0) {
      lines.push('\n---\n');
      lines.push('# 🔄 Healing Rounds\n');

      // Create wide table header for healing rounds
      lines.push('| **Round #** | **Round ID** | **Healer ID** | **Start At** | **Feedback Start** | **End At** | **Status** | **Patient ✓** | **Healer ✓** | **Check-ins** | **Feedback** | **Thread ID** | **Region** |');
      lines.push('|-------------|--------------|---------------|--------------|-------------------|------------|------------|---------------|---------------|---------------|--------------|---------------|------------|');

      // Add each round as a single row
      healingRounds.forEach((round) => {
        const patientConfirmed = round.is_patient_confirmed ? '✅' : '❌';
        const healerConfirmed = round.is_healer_confirmed ? '✅' : '❌';
        const feedback = round.is_positive_feedback === null ? 'N/A' : (round.is_positive_feedback ? '👍' : '👎');

        lines.push(`| ${round.round_number || 'N/A'} | \`${round.round_id || 'N/A'}\` | \`${round.healer_id || 'N/A'}\` | ${this.formatTimestampUTC(round.round_start_at)} | ${this.formatTimestampUTC(round.feedback_start_at)} | ${this.formatTimestampUTC(round.round_end_at)} | \`${round.status || 'N/A'}\` | ${patientConfirmed} | ${healerConfirmed} | ${round.check_in_count || 0} | ${feedback} | \`${round.assistant_thread_id || 'N/A'}\` | \`${round.cloud_region || 'N/A'}\` |`);
      });

      lines.push('');

      // Add summaries and ailments for each round
      healingRounds.forEach((round, index) => {
        const hasRoundSummaries = sessionSummaries.some(summary => summary.round_id === round.round_id);
        const hasRoundAilments = sessionAilments.some(ailment => ailment.round_id === round.round_id);

        if (hasRoundSummaries || hasRoundAilments) {
          lines.push(`## Round ${round.round_number || index + 1} Details\n`);

          // Add round-specific summaries
          const roundSummaries = sessionSummaries.filter(summary => summary.round_id === round.round_id);
          if (roundSummaries.length > 0) {
            lines.push('### 📝 Round Summaries\n');
            lines.push('| **Summary ID** | **Content** | **Created At** |');
            lines.push('|----------------|-------------|----------------|');

            roundSummaries.forEach((summary) => {
              const content = summary.content ? summary.content.replace(/\|/g, '\\|').replace(/\n/g, ' ') : '*No content*';
              lines.push(`| \`${summary.summary_id || 'N/A'}\` | ${content} | ${this.formatTimestampUTC(summary.created_at)} |`);
            });
            lines.push('');
          }

          // Add round-specific ailments
          const roundAilments = sessionAilments.filter(ailment => ailment.round_id === round.round_id);
          if (roundAilments.length > 0) {
            lines.push('### 🩺 Round Ailments\n');
            lines.push('| **Ailment ID** | **Name** | **Description** | **Level** | **Summary ID** | **Created At** |');
            lines.push('|----------------|----------|-----------------|-----------|----------------|----------------|');

            roundAilments.forEach((ailment) => {
              lines.push(`| \`${ailment.session_ailment_id || 'N/A'}\` | ${ailment.name || 'N/A'} | ${ailment.description || 'N/A'} | ${ailment.level || 'N/A'} | \`${ailment.summary_id || 'N/A'}\` | ${this.formatTimestampUTC(ailment.created_at)} |`);
            });
            lines.push('');
          }

          if (index < healingRounds.length - 1) {
            lines.push('\n');
          }
        }
      });
    } else {
      lines.push('\n---\n');
      lines.push('# 🔄 Healing Rounds\n');
      lines.push('*No healing rounds found for this session.*\n');
    }

    lines.push('\n---');
    lines.push(`\n*Generated on: ${new Date().toISOString().replace('T', ' ').replace('.000Z', ' UTC')}*`);

    const output = lines.join('\n');

    if (saveToFile) {
      try {
        // Create the result directory if it doesn't exist
        const resultDir = join(process.cwd(), 'scripts', 'result');
        mkdirSync(resultDir, { recursive: true });

        // Generate filename with session ID
        const filename = `session-${sessionId}.md`;
        const filepath = join(resultDir, filename);

        writeFileSync(filepath, output);
        console.log(`✅ Session information saved to: ${filepath}`);
      } catch (error) {
        console.error(`❌ Failed to write to file: ${error.message}`);
        console.log('\n📄 Output:\n');
        console.log(output);
      }
    } else {
      console.log(output);
    }
  }

  async run(sessionId: string, saveToFile: boolean): Promise<void> {
    try {
      console.log('🚀 Starting session info retrieval process...');

      // Connect to database
      await this.connect();

      // Get session information
      const sessionInfo = await this.getSessionInfo(sessionId);

      if (sessionInfo && sessionInfo.length > 0) {
        // Get healing rounds for this session
        const healingRounds = await this.getHealingRounds(sessionId);

        // Get session summaries for this session
        const sessionSummaries = await this.getSessionSummaries(sessionId);

        // Get session ailments for this session
        const sessionAilments = await this.getSessionAilments(sessionId);

        // Get user and healer profiles
        const profiles = await this.getProfiles(sessionInfo, healingRounds);

        console.log('');
        this.outputMarkdown(sessionInfo, healingRounds, sessionSummaries, sessionAilments, profiles, sessionId, saveToFile);
      }
    } catch (error) {
      console.error('💥 Fatal error:', error.message);
      process.exit(1);
    } finally {
      await this.disconnect();
    }
  }
}

// Parse command line arguments
const args = process.argv.slice(2);

if (args.length === 0) {
  console.error('❌ Error: Session ID is required as an argument');
  console.log('');
  console.log('Usage: npx tsx scripts/getSessionInfo.ts <session_id> [--save]');
  console.log('Example: npx tsx scripts/getSessionInfo.ts abc123-def456');
  console.log('Example: npx tsx scripts/getSessionInfo.ts abc123-def456 --save');
  console.log('');
  console.log('Options:');
  console.log('  --save  Save output to ./scripts/result/session-{session_id}.md');
  console.log('');
  console.log('Environment Variables (required):');
  console.log('  DB_PASSWORD (or DATABASE_PASSWORD or POSTGRES_PASSWORD)');
  console.log('');
  console.log('Environment Variables (optional with defaults):');
  console.log('  DB_HOST (default: localhost)');
  console.log('  DB_PORT (default: 5432)');
  console.log('  DB_NAME (default: healthis)');
  console.log('  DB_USER (default: postgres)');
  process.exit(1);
}

// Parse arguments
let sessionId = '';
let saveToFile = false;

for (let i = 0; i < args.length; i++) {
  if (args[i] === '--save') {
    saveToFile = true;
  } else if (!sessionId) {
    sessionId = args[i];
  }
}

if (!sessionId || sessionId.trim() === '') {
  console.error('❌ Error: Session ID cannot be empty');
  process.exit(1);
}

// Run the session info retriever
const retriever = new SessionInfoRetriever();
retriever.run(sessionId.trim(), saveToFile).catch(console.error);
