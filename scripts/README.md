# CSV to Whitelist Importer

A Node.js script to import email addresses from a CSV file into a PostgreSQL database whitelist table.

## Features

- ✅ Reads emails from CSV file
- ✅ Validates email format
- ✅ Batch processing for large datasets
- ✅ Proactive duplicate prevention (checks existing emails before insertion)
- ✅ Progress tracking and error reporting
- ✅ SSL support for secure database connections
- ✅ Requires existing whitelist table (doesn't create tables)

## Prerequisites

- Node.js 16+ (for ES modules support)
- PostgreSQL database with existing whitelist table
- CSV file with email addresses
- Network access to the database server

## Installation

1. Install dependencies:

```bash
npm install
```

2. Configure database connection in the script:

The script has hardcoded database configuration. Update these values in `csvToWhitelist.js`:

```javascript
const DB_HOST = 'host';
const DB_PORT = 5432;
const DB_NAME = 'database';
const DB_USER = 'username';
const DB_PASSWORD = 'your_password';
```

3. Optional: Set environment variables for import configuration:

```bash
export BATCH_SIZE=100
```

## CSV Format

The CSV file should have a header row and contain email addresses in the first column:

```csv
email
<EMAIL>
<EMAIL>
<EMAIL>
```

## Usage

### Basic Usage

```bash
npm start
```

### Direct Node Execution

```bash
node uploadWhitelist.js
```

### With Custom Batch Size

```bash
BATCH_SIZE=50 node uploadWhitelist.js
```

## Database Schema

The script requires an existing whitelist table.

**Note:** The script will check if the table exists but will not create it. You must create the table using your Prisma schema or SQL commands first.

## Configuration Options

| Configuration           | Value                       | Description                                                                        |
| ----------------------- | --------------------------- | ---------------------------------------------------------------------------------- |
| **Database Connection** | Hardcoded in script         | Update `DB_HOST`, `DB_PORT`, `DB_NAME`, `DB_USER`, `DB_PASSWORD` in the script     |
| **CSV File Path**       | `./emails.csv`              | Path to CSV file (relative to script location)                                     |
| **Entity Type**         | `email`                     | Type of entity being whitelisted                                                   |
| **Created By**          | `1`                         | User ID who created the entries                                                    |
| **Batch Size**          | `100`                       | Number of records to process in each batch (configurable via `BATCH_SIZE` env var) |
| **SSL**                 | `rejectUnauthorized: false` | SSL configuration for secure database connections                                  |

## Output

The script provides detailed logging:

- ✅ Connection status
- 📊 CSV reading progress
- 🔍 Duplicate email checking
- 🚀 Import progress for new emails only
- 📈 Batch processing updates
- 📊 Final summary with success/error counts and skipped duplicates
- ⚠️ Warning for invalid emails
- ❌ Error details for failed operations

## Error Handling

- Invalid emails are skipped with warnings
- Database connection errors are handled gracefully
- Batch processing errors are logged but don't stop the process
- Duplicate emails are proactively checked and skipped before insertion
- SSL connection issues are handled with appropriate error messages

## Example Output

```
🚀 Starting CSV to Whitelist import process...

✅ Connected to PostgreSQL database
✅ Whitelist table exists
📊 Read 10 valid emails from CSV
🔍 Checking for existing emails to prevent duplicates...
📊 Found 3 existing emails, 7 new emails to insert
🚀 Starting to insert 7 new emails...
📈 Progress: 7/7 new emails inserted
✅ Successfully inserted: 7 new emails
❌ Errors: 0 emails
📁 Total emails in CSV: 10
🔄 Skipped existing emails: 3
✅ Disconnected from database
```

## Troubleshooting

### Common Issues

1. **Database Connection Failed**

   - Check database credentials in the script
   - Verify PostgreSQL is running and accessible
   - Check network connectivity to the database server
   - Ensure SSL configuration is correct for remote connections

2. **SSL Connection Issues**

   - The script uses `rejectUnauthorized: false` for development
   - For production, consider using proper SSL certificates
   - Check if your database server requires SSL connections

3. **CSV File Not Found**

   - Ensure the CSV file path is correct
   - Check file permissions
   - The default path is `./emails.csv` relative to the script location

4. **Permission Denied**

   - Verify database user has INSERT permissions on the whitelist table
   - Check if the whitelist table exists

5. **Invalid Email Format**
   - The script will skip invalid emails and continue
   - Check your CSV for malformed email addresses

### SSL Configuration

The script is configured with SSL support for secure database connections:

```javascript
ssl: {
  rejectUnauthorized: false, // For self-signed certificates or development
}
```

For production environments, consider:

- Using proper SSL certificates
- Setting `rejectUnauthorized: true`
- Implementing certificate validation

### Debug Mode

For more detailed logging, you can modify the script to add console.log statements or use a logging library.

## License

ISC
