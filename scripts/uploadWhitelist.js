// csvToWhitelist.js
// Script to read emails from CSV and insert into PostgreSQL whitelist table

import { Client } from 'pg';
import { createReadStream } from 'fs';
import { createInterface } from 'readline';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
// Database Configuration
const DB_HOST = 'Host';
const DB_PORT = 5432;
const DB_NAME = 'Database';
const DB_USER = 'Username';
const DB_PASSWORD = 'Your_password';

// Database configuration
const dbConfig = {
  host: DB_HOST,
  port: DB_PORT,
  database: DB_NAME,
  user: DB_USER,
  password: DB_PASSWORD,
  ssl: {
    rejectUnauthorized: false, // For self-signed certificates or development
    // If you have a proper SSL certificate, use:
    // rejectUnauthorized: true
  },
};

// Configuration
const CSV_FILE_PATH = join(__dirname, 'emails.csv');
const ENTITY_TYPE = 'email';
const CREATED_BY = 1;
const BATCH_SIZE = parseInt(process.env.BATCH_SIZE) || 100;

class WhitelistImporter {
  constructor() {
    this.client = new Client(dbConfig);
    this.emails = [];
    this.successCount = 0;
    this.errorCount = 0;
    this.errors = [];
  }

  async connect() {
    try {
      await this.client.connect();
      console.log('✅ Connected to PostgreSQL database');
    } catch (error) {
      console.error('❌ Failed to connect to database:', error.message);
      throw error;
    }
  }

  async disconnect() {
    try {
      await this.client.end();
      console.log('✅ Disconnected from database');
    } catch (error) {
      console.error('❌ Error disconnecting:', error.message);
    }
  }

  async readCSV() {
    return new Promise((resolve, reject) => {
      const emails = [];
      const fileStream = createReadStream(CSV_FILE_PATH);
      const rl = createInterface({
        input: fileStream,
        crlfDelay: Infinity,
      });

      let lineCount = 0;
      rl.on('line', (line) => {
        lineCount++;
        if (lineCount === 1) {
          // Skip header row
          return;
        }

        const email = line.trim();
        if (email && this.isValidEmail(email)) {
          emails.push(email);
        } else if (email) {
          console.warn(
            `⚠️  Skipping invalid email at line ${lineCount}: ${email}`
          );
        }
      });

      rl.on('close', () => {
        console.log(`📊 Read ${emails.length} valid emails from CSV`);
        resolve(emails);
      });

      rl.on('error', (error) => {
        reject(error);
      });
    });
  }

  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  async checkTableExists() {
    try {
      const checkQuery = `
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = 'whitelist'
        );
      `;

      const result = await this.client.query(checkQuery);
      if (!result.rows[0].exists) {
        throw new Error(
          'Whitelist table does not exist. Please create it first using your Prisma schema.'
        );
      }
      console.log('✅ Whitelist table exists');
    } catch (error) {
      console.error('❌ Table check failed:', error.message);
      throw error;
    }
  }

  async insertEmails(emails) {
    // First, check which emails already exist to avoid duplicates
    console.log('🔍 Checking for existing emails to prevent duplicates...');
    const existingEmails = await this.getExistingEmails(emails);
    const newEmails = emails.filter((email) => !existingEmails.includes(email));

    if (newEmails.length === 0) {
      console.log(
        'ℹ️  All emails already exist in the database. No new records to insert.'
      );
      return;
    }

    console.log(
      `📊 Found ${emails.length - newEmails.length} existing emails, ${
        newEmails.length
      } new emails to insert`
    );

    const insertQuery = `
      INSERT INTO whitelist (entity_type, entity_value, status, created_by, created_at)
      VALUES ($1, $2, $3, $4, $5)
    `;

    console.log(`🚀 Starting to insert ${newEmails.length} new emails...`);

    for (let i = 0; i < newEmails.length; i += BATCH_SIZE) {
      const batch = newEmails.slice(i, i + BATCH_SIZE);

      try {
        const promises = batch.map((email) =>
          this.client.query(insertQuery, [
            ENTITY_TYPE,
            email,
            'active',
            CREATED_BY,
            new Date(),
          ])
        );

        await Promise.all(promises);
        this.successCount += batch.length;

        if (i % (BATCH_SIZE * 10) === 0 || i + BATCH_SIZE >= newEmails.length) {
          console.log(
            `📈 Progress: ${this.successCount}/${newEmails.length} new emails inserted`
          );
        }
      } catch (error) {
        console.error(
          `❌ Error inserting batch starting at index ${i}:`,
          error.message
        );
        this.errorCount += batch.length;
        this.errors.push({ batch: i, error: error.message });
      }
    }
  }

  async getExistingEmails(emails) {
    try {
      // Use a more efficient query to check existing emails
      const placeholders = emails.map((_, index) => `$${index + 1}`).join(',');
      const checkQuery = `
        SELECT entity_value 
        FROM whitelist 
        WHERE entity_type = $${emails.length + 1} 
        AND entity_value IN (${placeholders})
        AND is_deleted = false
      `;

      const params = [...emails, ENTITY_TYPE];
      const result = await this.client.query(checkQuery, params);

      return result.rows.map((row) => row.entity_value);
    } catch (error) {
      console.error('❌ Error checking existing emails:', error.message);
      // If we can't check existing emails, return empty array to allow all inserts
      return [];
    }
  }

  async run() {
    try {
      console.log('🚀 Starting CSV to Whitelist import process...');

      // Connect to database
      await this.connect();

      // Check if table exists
      await this.checkTableExists();

      // Read emails from CSV
      this.emails = await this.readCSV();

      if (this.emails.length === 0) {
        console.log('⚠️  No valid emails found in CSV file');
        return;
      }

      // Insert emails into database
      await this.insertEmails(this.emails);

      // Print summary
      console.log('');
      console.log('📊 Import Summary:');
      console.log(`✅ Successfully inserted: ${this.successCount} new emails`);
      console.log(`❌ Errors: ${this.errorCount} emails`);
      console.log(`📁 Total emails in CSV: ${this.emails.length}`);
      console.log(
        `🔄 Skipped existing emails: ${
          this.emails.length - this.successCount - this.errorCount
        }`
      );

      if (this.errors.length > 0) {
        console.log('');
        console.log('⚠️  Errors encountered:');
        this.errors.forEach((error) => {
          console.log(`   Batch ${error.batch}: ${error.error}`);
        });
      }
    } catch (error) {
      console.error('💥 Fatal error:', error.message);
      process.exit(1);
    } finally {
      await this.disconnect();
    }
  }
}

// Run the importer
const importer = new WhitelistImporter();
importer.run().catch(console.error);
