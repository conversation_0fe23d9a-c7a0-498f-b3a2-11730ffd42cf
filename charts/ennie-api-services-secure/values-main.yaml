global:
  security:
    allowInsecureImages: true

appName: ennie
namespace: default
environment: main

app:
  nodeEnv: production
  logLevel: error,warn

configmap:
  name: ennie-configmap

certificate:
  dnsName: api.ennie.app
  rabbitName: rabbit.ennie.app
  azureFrontDoorName: api.main.ennie.app

image:
  name: acrenniemaineastus.azurecr.io/ennie-api/

azure:
  keyVault:
    tenantId: 83b3367d-ca40-406c-8705-3105993d9b84 # Directory ID of the key vault
    name: kv-ennie-main # Name of the Key Vault
    userAssignedIdentityId: 5b07c123-a3dc-45bb-99cb-bd390e2c55d5 # Client ID of the secret managed identity, in Azure something like: azurekeyvaultsecretsprovider-aks-demo-dev
  workloadIdentity:
    clientId: a5564e0b-9ffe-4982-8ebc-eb5283fb156f # Client ID for Azure Workload Identity - replace with production environment client ID
  openAi:
    url: https://healthis-ai.openai.azure.com/
    summaryAssistantId: asst_eHI25dgZyKPsXlDnq57DtbkC
    ailmentExtractionAssistantId: asst_vN50ebllzX4r1ee5fcRmtom9
    healingRoundAssistantId: asst_ns0FkK53Di2mwxEG6ulJSh0l
  aiFoundry:
    url: https://ai-ennie-main.services.ai.azure.com/api/projects/prj-ennie-main
    intakeConversationAgentId: asst_ZcZSfTECINyxmxxyE4cSzTeX
    intakeSummarizationAgentId: asst_AWSnqYMswpn56dv7tuDvMFp9
    summaryAssistantId: asst_un3g8H5L91NXc3vVPobqIgwu
    ailmentExtractionAssistantId: asst_XuSy9CYEHW0aoTuRngw3J8vQ
    healingRoundAssistantId: asst_Z8BPwTPm3tdBWWHC9ase2aZR

elevenLabs:
  intakeAgentId: agent_01jy33kym9f7a9rydzb08j0260
  reportAgentId: agent_01jy33xzhcef1s9q9bqtpk2fna
  checkInAgentId: agent_01jy33t1s0fmvayf4145bkpxh1

healing:
  session:
    availabilityTimeout: '1800000' # 30 minutes
    placeboPercentage: '30'
    minSessionCountForPlacebo: '3'

secrets:
  - name: database-url
    key: DATABASE_URL
  - name: jwt-secret
    key: JWT_SECRET
  - name: sendgrid-api-key
    key: SENDGRID_API_KEY
  - name: firebase-private-key-base-64
    key: FIREBASE_PRIVATE_KEY_BASE_64
  - name: firebase-private-key
    key: FIREBASE_PRIVATE_KEY
  - name: rabbitmq-url
    key: RABBITMQ_URL
  - name: rabbitmq-password
    key: RABBITMQ_PASSWORD
  - name: swagger-user
    key: SWAGGER_USER
  - name: swagger-password
    key: SWAGGER_PASSWORD
  - name: auth-api-key
    key: AUTH_API_KEY
  - name: elevenlabs-api-key
    key: ELEVENLABS_API_KEY

rabbitmq:
  enabled: true
  image:
    debug: true
    registry: docker.io
    repository: bitnamilegacy/rabbitmq
    tag: 4.1.3-debian-12-r1

  fullnameOverride: ennie-rabbitmq
  auth:
    username: royaldeconsecratefor
    existingPasswordSecret: rabbitmq-password
    updatePassword: true
  podManagementPolicy: Parallel
  livenessProbe:
    enabled: false
  readinessProbe:
    enabled: false
  extraVolumeMounts:
    - name: ennie-rabbitmq-secret-volume
      readOnly: true
      mountPath: /mnt/secrets-store
  extraVolumes:
    - name: ennie-rabbitmq-secret-volume
      csi:
        driver: secrets-store.csi.k8s.io
        readOnly: true
        volumeAttributes:
          secretProviderClass: ennie-secret
  extraEnvVars:
    - name: RABBITMQ_PASSWORD
      valueFrom:
        secretKeyRef:
          name: rabbitmq-password
          key: RABBITMQ_PASSWORD
