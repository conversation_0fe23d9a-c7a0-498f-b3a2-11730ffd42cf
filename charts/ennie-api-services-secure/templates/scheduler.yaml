apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.appName }}-scheduler
  namespace: {{ .Values.namespace }}
  labels:
    helm-revision: "{{ .Release.Revision }}"
    app: {{ .Values.appName }}-scheduler
spec:
  replicas: 1
  selector:
    matchLabels:
      app: {{ .Values.appName }}-scheduler
  template:
    metadata:
      labels:
        app: {{ .Values.appName }}-scheduler
        azure.workload.identity/use: "true"
    spec:
      serviceAccountName: workload-identity-sa-ai-foundry
      containers:
        - name: {{ .Values.appName }}-scheduler
          image: {{ .Values.image.name }}scheduler:bf327d7130bda46597323877346f7ce2cc4f6db5 # Replaced by CI/CD
          ports:
            - containerPort: 4000
          volumeMounts:
            - name: {{ .Values.appName }}-core-secret-volume
              readOnly: true
              mountPath: /mnt/secrets-store
          envFrom:
            - configMapRef:
                name: {{ .Values.configmap.name }}
          env:
          {{- range $secrets := .Values.secrets }}
          - name: {{ $secrets.key }}
            valueFrom:
              secretKeyRef:
                name: {{ $secrets.name }}
                key: {{ $secrets.key }}
          {{- end }}
      volumes:
        - name: {{ .Values.appName }}-core-secret-volume
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: {{ .Values.appName }}-secret