# apiVersion: v1
# kind: Namespace
# metadata:
#   name: cert-manager

# ---

# apiVersion: cert-manager.io/v1
# kind: Certificate
# metadata:
#   name: {{ .Values.environment }}-api-cert  # Choose a descriptive name
#   namespace: {{ .Values.namespace }}
# spec:
#   secretName: {{ .Values.environment }}-api-tls
#   dnsNames:
#   - {{ .Values.certificate.dnsName }}
#   - {{ .Values.certificate.rabbitName }}
#   - {{ .Values.certificate.azureFrontDoorName }}
#   issuerRef:
#     name: letsencrypt-issuer
#     kind: ClusterIssuer