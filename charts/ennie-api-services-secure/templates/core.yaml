apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.appName }}-core
  namespace: {{ .Values.namespace }}
  labels:
    helm-revision: "{{ .Release.Revision }}"
    app: {{ .Values.appName }}-core
spec:
  replicas: 1
  selector:
    matchLabels:
      app: {{ .Values.appName }}-core
  template:
    metadata:
      labels:
        app: {{ .Values.appName }}-core
    spec:
      containers:
        - name: {{ .Values.appName }}-core
          image: {{ .Values.image.name }}core:bf327d7130bda46597323877346f7ce2cc4f6db5 # Replaced by CI/CD
          ports:
            - containerPort: 3000
          volumeMounts:
            - name: {{ .Values.appName }}-core-secret-volume
              readOnly: true
              mountPath: /mnt/secrets-store
          envFrom:
            - configMapRef:
                name: {{ .Values.configmap.name }}  # Reference to the ConfigMap
          env:
            {{- range $secrets := .Values.secrets }}
            - name: {{ $secrets.key }}
              valueFrom:
                secretKeyRef:
                  name: {{ $secrets.name }}
                  key: {{ $secrets.key }}
            {{- end }}
      volumes:
        - name: {{ .Values.appName }}-core-secret-volume
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: {{ .Values.appName }}-secret
---
apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.appName }}-core-service
spec:
  selector:
    app: {{ .Values.appName }}-core
  ports:
    - protocol: TCP
      port: 80
      targetPort: 3000
  type: ClusterIP