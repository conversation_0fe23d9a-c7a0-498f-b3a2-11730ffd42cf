apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.appName }}-ai
  namespace: {{ .Values.namespace }}
  labels:
    helm-revision: "{{ .Release.Revision }}"
    app: {{ .Values.appName }}-ai
spec:
  replicas: 1
  selector:
    matchLabels:
      app: {{ .Values.appName }}-ai
  template:
    metadata:
      labels:
        app: {{ .Values.appName }}-ai
        azure.workload.identity/use: "true"
    spec:
      serviceAccountName: workload-identity-sa-ai-foundry
      containers:
        - name: {{ .Values.appName }}-ai
          image: {{ .Values.image.name }}ai:bf327d7130bda46597323877346f7ce2cc4f6db5 # Replaced by CI/CD
          ports:
            - containerPort: 3001
          volumeMounts:
            - name: {{ .Values.appName }}-ai-secret-volume
              readOnly: true
              mountPath: /mnt/secrets-store
          envFrom:
            - configMapRef:
                name: {{ .Values.configmap.name }}  # Reference to the ConfigMap
          env:
            {{- range $secrets := .Values.secrets }}
            - name: {{ $secrets.key }}
              valueFrom:
                secretKeyRef:
                  name: {{ $secrets.name }}
                  key: {{ $secrets.key }}
            {{- end }}
      volumes:
        - name: {{ .Values.appName }}-ai-secret-volume
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: {{ .Values.appName }}-secret
---
apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.appName }}-ai-service
spec:
  selector:
    app: {{ .Values.appName }}-ai
  ports:
    - protocol: TCP
      port: 80
      targetPort: 3001
  type: ClusterIP
