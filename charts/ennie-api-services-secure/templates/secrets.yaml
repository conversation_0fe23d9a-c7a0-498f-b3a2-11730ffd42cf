apiVersion: secrets-store.csi.x-k8s.io/v1
kind: SecretProviderClass
metadata:
  name: {{ .Values.appName }}-secret
  namespace: {{ .Values.namespace }}
spec:
  provider: azure
  parameters:
    cloudName: AzurePublicCloud
    keyvaultName: {{ .Values.azure.keyVault.name }}
    objects: |
      array:
        {{- range $secrets := .Values.secrets }}
        - |
          objectName: {{ $secrets.name }}
          objectType: secret
        {{- end }}
    tenantId: {{ .Values.azure.keyVault.tenantId }} #directory id of key vault
    usePodIdentity: "false"
    useVMManagedIdentity: "true"
    userAssignedIdentityID: {{ .Values.azure.keyVault.userAssignedIdentityId }} #clientid of managed identity
  secretObjects:
  {{- range $secrets := .Values.secrets }}
  - data:
    - key: {{ $secrets.key }}
      objectName: {{ $secrets.name }}
    secretName: {{ $secrets.name }}
    type: Opaque
  {{- end }}