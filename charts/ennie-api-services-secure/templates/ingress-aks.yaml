apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: api-ingress-aks
  annotations:
    nginx.ingress.kubernetes.io/use-regex: "true"
spec:
  ingressClassName: nginx-internal
  rules:
    - http:
        paths:
          - path: /api/ailments.*|/api/sessions.*|/api/conversations.*|/api/healer.*|/api/patient.*|/api/ai.*|/socket.io.*
            pathType: ImplementationSpecific
            backend:
              service:
                name: {{ .Values.appName }}-ai-service
                port:
                  number: 80
          - path: /
            pathType: Prefix
            backend:
              service:
                name: {{ .Values.appName }}-core-service
                port:
                  number: 80