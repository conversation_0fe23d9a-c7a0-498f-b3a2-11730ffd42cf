# apiVersion: cert-manager.io/v1
# kind: ClusterIssuer
# metadata:
#   name: letsencrypt-issuer
#   namespace: {{ .Values.namespace }}
# spec:
#   acme:
#     # You must replace this email address with your own.
#     # Let's Encrypt will use this to contact you about expiring
#     # certificates, and issues related to your account.
#     email: <EMAIL>
#     server: https://acme-v02.api.letsencrypt.org/directory
#     privateKeySecretRef:
#       # Secret resource that will be used to store the account's private key.
#       # This is your identity with your ACME provider. Any secret name
#       # may be chosen. It will be populated with data automatically,
#       # so generally nothing further needs to be done with
#       # the secret. If you lose this identity/secret, you will be able to
#       # generate a new one and generate certificates for any/all domains
#       # managed using your previous account, but you will be unable to revoke
#       # any certificates generated using that previous account.
#       name: letsencrypt-issuer-key
#     # Add a single challenge solver, HTTP01 using nginx
#     solvers:
#     - http01:
#         ingress:
#           ingressClassName: nginx