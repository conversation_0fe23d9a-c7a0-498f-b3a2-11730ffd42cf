kind: ConfigMap
apiVersion: v1
metadata:
  name: {{ .Values.appName }}-configmap
  namespace: {{ .Values.namespace }}
data:
  # Generic APP ENVs
  JWT_EXPIRATION: '30d'
  JWT_ALGORITHM: 'HS256'
  SITE_URL: https://www.ennie.app
  PORT_CORE: '3000'
  AI_PORT: '3001'
  SCHEDULER_PORT: '4000'
  NODE_ENV: {{ .Values.app.nodeEnv }}
  LOG_LEVEL: {{ .Values.app.logLevel }}

  # Email ENVs
  SENDGRID_FROM_EMAIL: '<EMAIL>'
  FORGOT_PASSWORD_SENDGRID_TEMPLATE_ID: d-fb6ec008d5014e73aa9b17ddaa5610a0
  PATIENT_WAITLISTED_SENDGRID_TEMPLATE_ID: d-e5a3e004db7545dc8474c9d75730e244
  HEALER_WAITLISTED_SENDGRID_TEMPLATE_ID: d-4819ca75539b45f28be209981133a899
  HEALER_WELCOME_SENDGRID_TEMPLATE_ID: d-725ef73ac8554ce3a6f424284a1086dc
  PATIENT_WELCOME_SENDGRID_TEMPLATE_ID: d-efb5e93feaf9403d9228c056a85a88c2

  # Eleven Labs related ENVs
  ELEVENLABS_INTAKE_AGENT_ID: {{ .Values.elevenLabs.intakeAgentId }}
  ELEVENLABS_REPORT_AGENT_ID: {{ .Values.elevenLabs.reportAgentId }}
  ELEVENLABS_CHECK_IN_AGENT_ID: {{ .Values.elevenLabs.checkInAgentId }}

  # Firebase related ENVs
  FIREBASE_CLIENT_EMAIL: <EMAIL>
  FIREBASE_PROJECT_ID: ennie-457023

  # Open AI related ENVs
  # TODO: remove after migration to Azure AI Foundry is complete in all environments
  OPENAI_MODE: azure
  OPENAI_URL: {{ .Values.azure.openAi.url }}
  # AILMENT_EXTRACTION_ASSISTANT_ID: {{ .Values.azure.openAi.ailmentExtractionAssistantId }}
  # HEALING_ROUND_ASSISTANT_ID: {{ .Values.azure.openAi.healingRoundAssistantId }}
  OPENAI_API_VERSION: 2025-01-01-preview

  # Azure AI Foundry related ENVs
  AI_FOUNDRY_PROJECT_ENDPOINT: {{ .Values.azure.aiFoundry.url }}
  INTAKE_CONVERSATION_AGENT_ID: {{ .Values.azure.aiFoundry.intakeConversationAgentId }}
  INTAKE_SUMMARIZATION_AGENT_ID: {{ .Values.azure.aiFoundry.intakeSummarizationAgentId }}
  AILMENT_EXTRACTION_ASSISTANT_ID: {{ .Values.azure.aiFoundry.ailmentExtractionAssistantId }}
  HEALING_ROUND_ASSISTANT_ID: {{ .Values.azure.aiFoundry.healingRoundAssistantId }}

  # Healing Session/Round timeout variables
  SESSION_INTAKE_TIMEOUT: '43200000'
  SESSION_IN_QUEUE_TIMEOUT: '2592000000'
  SESSION_CONFIRMATION_REQUIRED_TIMEOUT: '18000000'
  SESSION_IN_QUEUE_AVAILABLE_TIMEOUT: '3600000'
  SESSION_OFFER_TIMEOUT: '30000'
  AVERAGE_SESSION_DURATION: '600000'
  SESSION_AVAILABILITY_TIMEOUT: "{{ .Values.healing.session.availabilityTimeout }}"
  HEALING_SESSION_PLACEBO_PERCENTAGE:  "{{ .Values.healing.session.placeboPercentage }}"
  MIN_SESSION_COUNT_FOR_PLACEBO: "{{ .Values.healing.session.minSessionCountForPlacebo }}"

  HEALING_ROUND_HEALER_ASSIGNED_TIMEOUT: '20000'
  HEALING_ROUND_HEALER_IN_PROGRESS_TIMEOUT: '300000'
  HEALING_ROUND_CHECK_IN_COUNT: '3'
  HEALING_ROUND_FEEDBACK_REQUIRED_TIMEOUT: '300000'

  #Cache
  REDIS_HOST: redis
  REDIS_PORT: '6379'
  REDIS_TTL: '180000'
  WS_CACHE_PREFIX: EnnieWSCache

  # RabbitMQ
  RABBITMQ_QUEUE: analytics_queue
  AI_MICROSERVICE_PORT: '4001'
  AI_HOST_URL: localhost

  SESSION_INTAKE_GREETING_MESSAGE: 'Hi, welcome to your symptoms assessment. I’ll be your guide. First, tell me about your situation.'
  ENTER_IN_QUEUE_NOTIFICATION_BODY: 'Ennie Queue Update | You are now in queue to avail your healing.'
  IN_FRONT_OF_QUEUE_NOTIFICATION_BODY: 'Ennie Queue Update | You are now in front of the queue, your healing will start soon.'
  CONFIRMATION_REQUIRED_NOTIFICATION_BODY: 'Ennie Queue Update | You are now ready to be connected to the healer. Please confirm your availability now.'
  AI_CONFIRMATION_REQUIRED_NOTIFICATION_BODY: 'Ennie Queue Update | Its been a while since we heard from you, You are ready to be connected to a healer. Let us know if you are available to take your healing session.'
  AVAILABLE_NOTIFICATION_BODY: 'Ennie Queue Update | We are connecting you to a healer now to start your healing process.'
