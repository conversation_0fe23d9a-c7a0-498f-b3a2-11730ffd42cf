appName: ennie
namespace: default
environment: dev

app:
  nodeEnv: development
  logLevel: error,warn,log,debug

configmap:
  name: ennie-configmap

certificate:
  dnsName: api-dev.ennie.app
  rabbitName: rabbit-dev.ennie.app
  azureFrontDoorName: api.dev.ennie.app

image:
  name: acrenniedeveastus.azurecr.io/ennie-api/

azure:
  keyVault:
    tenantId: 83b3367d-ca40-406c-8705-3105993d9b84 # Directory ID of the key vault
    name: kv-ennie-dev # Name of the Key Vault
    userAssignedIdentityId: 6e183c6c-fc96-4e49-9b30-dc6022af0b79 # Client ID of the secret managed identity, in Azure something like: azurekeyvaultsecretsprovider-aks-demo-dev
  workloadIdentity:
    clientId: 197b29d7-75e9-49de-b0b1-68ef31e89353 # Client ID for Azure Workload Identity
  openAi:
    url: https://healthis-ai.openai.azure.com/
    summaryAssistantId: asst_eHI25dgZyKPsXlDnq57DtbkC
    ailmentExtractionAssistantId: asst_vN50ebllzX4r1ee5fcRmtom9
    healingRoundAssistantId: asst_ns0FkK53Di2mwxEG6ulJSh0l
  aiFoundry:
    url: https://ai-ennie-dev.services.ai.azure.com/api/projects/prj-ennie-dev
    intakeConversationAgentId: asst_yFYLNiM991qCETQQh2nLnWXF
    intakeSummarizationAgentId: asst_YyMk6QN4HDsxvAvoxqyXOK4v
    summaryAssistantId: asst_1wCRmHub0rlpT61gBvL23Enl
    ailmentExtractionAssistantId: asst_xhlqmdxJUebJ0v7LZ7TvR7Sp
    healingRoundAssistantId: asst_50tc7x86ulYO7flXAbkn505q

elevenLabs:
  intakeAgentId: tJ4DiqtF1ujVHRIm9XZA
  reportAgentId: wRn3G3UBpJ8cosZq2UxE
  checkInAgentId: x4ij21lXVDlfhhcQMbIW

healing:
  session:
    availabilityTimeout: '1800000' # 30 minutes
    placeboPercentage: '30' # 30%
    minSessionCountForPlacebo: '3' # Minimum number of sessions required to consider placebo

secrets:
  - name: database-url
    key: DATABASE_URL
  - name: jwt-secret
    key: JWT_SECRET
  - name: sendgrid-api-key
    key: SENDGRID_API_KEY
  - name: firebase-private-key-base-64
    key: FIREBASE_PRIVATE_KEY_BASE_64
  - name: firebase-private-key
    key: FIREBASE_PRIVATE_KEY
  - name: rabbitmq-url
    key: RABBITMQ_URL
  - name: rabbitmq-password
    key: RABBITMQ_PASSWORD
  - name: swagger-user
    key: SWAGGER_USER
  - name: swagger-password
    key: SWAGGER_PASSWORD
  - name: auth-api-key
    key: AUTH_API_KEY
  - name: elevenlabs-api-key
    key: ELEVENLABS_API_KEY

rabbitmq:
  enabled: true
  image:
    debug: true
  fullnameOverride: ennie-rabbitmq
  auth:
    username: uproothappy-go-lucky
    existingPasswordSecret: rabbitmq-password
    updatePassword: true
  podManagementPolicy: Parallel
  livenessProbe:
    enabled: false
  readinessProbe:
    enabled: false
  extraVolumeMounts:
    - name: ennie-rabbitmq-secret-volume
      readOnly: true
      mountPath: /mnt/secrets-store
  extraVolumes:
    - name: ennie-rabbitmq-secret-volume
      csi:
        driver: secrets-store.csi.k8s.io
        readOnly: true
        volumeAttributes:
          secretProviderClass: ennie-secret
  extraEnvVars:
    - name: RABBITMQ_PASSWORD
      valueFrom:
        secretKeyRef:
          name: rabbitmq-password
          key: RABBITMQ_PASSWORD
  # customLivenessProbe:
  #   exec:
  #     command:
  #       - rabbitmq-diagnostics
  #       - -q
  #       - status
  #   initialDelaySeconds: 60
  #   periodSeconds: 30
  #   timeoutSeconds: 5
  #   failureThreshold: 3
