appName: ennie
namespace: default
environment: qa

app:
  nodeEnv: qa
  logLevel: error,warn,log

configmap:
  name: ennie-configmap

certificate:
  dnsName: api-qa.ennie.app
  rabbitName: rabbit-qa.ennie.app
  azureFrontDoorName: api.qa.ennie.app

image:
  name: acrennieqaeastus.azurecr.io/ennie-api/

azure:
  keyVault:
    tenantId: 83b3367d-ca40-406c-8705-3105993d9b84 # Directory ID of the key vault
    name: kv-ennie-qa # Name of the Key Vault
    userAssignedIdentityId: 7c563391-4d63-44df-bd64-3295e94226a4 # Client ID of the secret managed identity, in Azure something like: azurekeyvaultsecretsprovider-aks-demo-dev
  workloadIdentity:
    clientId: bfd2d94e-fa1b-4b08-a3d4-4d213a085ba4 # Client ID for Azure Workload Identity - replace with QA environment client ID
  openAi:
    url: https://healthis-ai.openai.azure.com/
    summaryAssistantId: asst_eHI25dgZyKPsXlDnq57DtbkC
    ailmentExtractionAssistantId: asst_vN50ebllzX4r1ee5fcRmtom9
    healingRoundAssistantId: asst_ns0FkK53Di2mwxEG6ulJSh0l
  aiFoundry:
    url: https://ai-ennie-qa.services.ai.azure.com/api/projects/prj-ennie-qa
    intakeConversationAgentId: asst_iNu1JnjX6sYPIgpgrZ4VzUCo
    intakeSummarizationAgentId: asst_6pTsIT15GawxKz09lEKgpl5c
    summaryAssistantId: asst_C368FAklRVqdyri3CdinXZr7
    ailmentExtractionAssistantId: asst_b4rzXobGrsucmM37sB6pWAq3
    healingRoundAssistantId: asst_abcTWNEbyLGlT3CregNGO1RP

healing:
  session:
    availabilityTimeout: '120000' # 2 minutes
    placeboPercentage: '100'
    minSessionCountForPlacebo: '3'

elevenLabs:
  intakeAgentId: tJ4DiqtF1ujVHRIm9XZA
  reportAgentId: wRn3G3UBpJ8cosZq2UxE
  checkInAgentId: x4ij21lXVDlfhhcQMbIW

secrets:
  - name: database-url
    key: DATABASE_URL
  - name: jwt-secret
    key: JWT_SECRET
  - name: sendgrid-api-key
    key: SENDGRID_API_KEY
  - name: firebase-private-key-base-64
    key: FIREBASE_PRIVATE_KEY_BASE_64
  - name: firebase-private-key
    key: FIREBASE_PRIVATE_KEY
  - name: openai-token
    key: OPENAI_TOKEN
  - name: rabbitmq-url
    key: RABBITMQ_URL
  - name: rabbitmq-password
    key: RABBITMQ_PASSWORD
  - name: swagger-user
    key: SWAGGER_USER
  - name: swagger-password
    key: SWAGGER_PASSWORD
  - name: auth-api-key
    key: AUTH_API_KEY
  - name: elevenlabs-api-key
    key: ELEVENLABS_API_KEY

rabbitmq:
  enabled: true
  image:
    debug: true
  fullnameOverride: ennie-rabbitmq
  auth:
    username: atopyumatunethically
    existingPasswordSecret: rabbitmq-password
    updatePassword: true
  podManagementPolicy: Parallel
  livenessProbe:
    enabled: false
  readinessProbe:
    enabled: false
  extraVolumeMounts:
    - name: ennie-rabbitmq-secret-volume
      readOnly: true
      mountPath: /mnt/secrets-store
  extraVolumes:
    - name: ennie-rabbitmq-secret-volume
      csi:
        driver: secrets-store.csi.k8s.io
        readOnly: true
        volumeAttributes:
          secretProviderClass: ennie-secret
  extraEnvVars:
    - name: RABBITMQ_PASSWORD
      valueFrom:
        secretKeyRef:
          name: rabbitmq-password
          key: RABBITMQ_PASSWORD
