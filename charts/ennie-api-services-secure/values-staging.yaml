appName: ennie
namespace: default
environment: staging

app:
  nodeEnv: staging
  logLevel: error,warn,log

configmap:
  name: ennie-configmap

certificate:
  dnsName: api-staging.ennie.app
  rabbitName: rabbit-staging.ennie.app
  azureFrontDoorName: api.staging.ennie.app

image:
  name: acrenniestagingeastus.azurecr.io/ennie-api/

azure:
  keyVault:
    tenantId: 83b3367d-ca40-406c-8705-3105993d9b84 # Directory ID of the key vault
    name: kv-ennie-staging # Name of the Key Vault
    userAssignedIdentityId: 86e09a50-c59c-4591-9a6b-029d75610a7c # Client ID of the secret managed identity, in Azure something like: azurekeyvaultsecretsprovider-aks-demo-dev
  workloadIdentity:
    clientId: 08afa697-fbf1-44c3-b74c-8f88a5730338 # Client ID for Azure Workload Identity - replace with staging environment client ID
  openAi:
    url: https://healthis-ai.openai.azure.com/
    summaryAssistantId: asst_eHI25dgZyKPsXlDnq57DtbkC
    ailmentExtractionAssistantId: asst_vN50ebllzX4r1ee5fcRmtom9
    healingRoundAssistantId: asst_ns0FkK53Di2mwxEG6ulJSh0l
  aiFoundry:
    url: https://ai-ennie-staging.services.ai.azure.com/api/projects/prj-ennie-staging
    intakeConversationAgentId: asst_iQBSyIJtG0SNnuuPLfzRwxP1
    intakeSummarizationAgentId: asst_dNWKJY8NXko4GYlqfRbC7OrT
    summaryAssistantId: asst_vrOabs32l89xNG3iCswB7sV0
    ailmentExtractionAssistantId: asst_IDWgAeujsit8JpAJ6IjVt07A
    healingRoundAssistantId: asst_6nrwpkVW2vJ06eM4ocJybRhP

healing:
  session:
    availabilityTimeout: '1800000' # 30 minutes
    placeboPercentage: '30'
    minSessionCountForPlacebo: '3'

elevenLabs:
  intakeAgentId: agent_01jy33kym9f7a9rydzb08j0260
  reportAgentId: agent_01jy33xzhcef1s9q9bqtpk2fna
  checkInAgentId: agent_01jy33t1s0fmvayf4145bkpxh1

secrets:
  - name: database-url
    key: DATABASE_URL
  - name: jwt-secret
    key: JWT_SECRET
  - name: sendgrid-api-key
    key: SENDGRID_API_KEY
  - name: firebase-private-key-base-64
    key: FIREBASE_PRIVATE_KEY_BASE_64
  - name: firebase-private-key
    key: FIREBASE_PRIVATE_KEY
  - name: openai-token
    key: OPENAI_TOKEN
  - name: rabbitmq-url
    key: RABBITMQ_URL
  - name: rabbitmq-password
    key: RABBITMQ_PASSWORD
  - name: swagger-user
    key: SWAGGER_USER
  - name: swagger-password
    key: SWAGGER_PASSWORD
  - name: auth-api-key
    key: AUTH_API_KEY
  - name: elevenlabs-api-key
    key: ELEVENLABS_API_KEY

rabbitmq:
  enabled: true
  image:
    debug: true
  fullnameOverride: ennie-rabbitmq
  auth:
    username: deflateughindeedfond
    existingPasswordSecret: rabbitmq-password
    updatePassword: true
  podManagementPolicy: Parallel
  livenessProbe:
    enabled: false
  readinessProbe:
    enabled: false
  extraVolumeMounts:
    - name: ennie-rabbitmq-secret-volume
      readOnly: true
      mountPath: /mnt/secrets-store
  extraVolumes:
    - name: ennie-rabbitmq-secret-volume
      csi:
        driver: secrets-store.csi.k8s.io
        readOnly: true
        volumeAttributes:
          secretProviderClass: ennie-secret
  extraEnvVars:
    - name: RABBITMQ_PASSWORD
      valueFrom:
        secretKeyRef:
          name: rabbitmq-password
          key: RABBITMQ_PASSWORD
