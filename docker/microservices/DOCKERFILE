#
# Development
#
FROM node:22.15.0-alpine AS dev
# ACCESS ENV's
ENV NODE_ENV=development
ENV USER=node
ENV GROUPNAME=$USER
ENV UID=1001
ENV GID=$UID
ENV APPDIR=/usr/src/app
ENV HOMEDIR=/home/<USER>
ENV SHELL /bin/bash
# Create app folder
WORKDIR ${APPDIR}
# add the missing shared libraries from alpine base image
# RUN echo http://dl-2.alpinelinux.org/alpine/edge/community/ >> /etc/apk/repositories  # infra/speed-up removed
RUN apk update
RUN apk add bash
RUN apk add sudo
RUN apk add git
RUN sudo --version
RUN apk add --no-cache libc6-compat openssl
RUN apk add -U shadow
# Development user preparation
RUN deluser --remove-home ${USER}
RUN addgroup -S ${USER}
RUN adduser -h ${HOMEDIR} -S -G ${USER} ${GROUPNAME}
RUN adduser $USER wheel
RUN usermod -aG ${GROUPNAME} ${USER}
RUN echo "Kf7uY9d_HPDmmX_Hc2hG" | passwd --stdin ${USER}
RUN echo '%wheel ALL=(ALL) ALL' > /etc/sudoers.d/wheel
# Set to dev environment
ENV NODE_ENV=development
# Copy source code into app folder
COPY --chown=$USER:$GROUPNAME . .
RUN chown ${USER}:${GROUPNAME} /usr/src/app
RUN chmod 750 /usr/src/app
USER ${USER}
# Install dependencies
RUN yarn config set loglevel verbose
RUN yarn install --frozen-lockfile --ignore-scripts
RUN npx prisma generate

#
# Build
#
FROM dev AS build
# Create app folder
WORKDIR /usr/src/app
# ACCESS ENV's
ENV NODE_ENV=build
ENV USER=node
ARG APP_NAME
# Build
USER ${USER}
ENV NX_DAEMON=false
RUN yarn config set loglevel error
RUN yarn nx build $APP_NAME --verbose
RUN echo "done with build: ${APP_NAME}"

#
# Production Server
#
FROM node:22.15.0-alpine AS prod
# needed for runtime
RUN apk add --no-cache libc6-compat
RUN apk add -U shadow
# Create app folder
WORKDIR /usr/src/app
# ACCESS ENV's
ENV NODE_ENV=production
ARG APP_NAME
ENV USER=node
ENV GROUPNAME=$USER
ENV UID=1001
ENV GID=$UID
ENV HOMEDIR=/home/<USER>
RUN apk add --no-cache openssl
# Re-create non-root user for Docker
RUN deluser --remove-home $USER
RUN addgroup --system --gid $GID $GROUPNAME
RUN adduser -h ${HOMEDIR} -S -u ${UID} -G ${USER} ${GROUPNAME}
RUN usermod -aG ${GROUPNAME} ${USER}
# Runtime installs
RUN yarn config set loglevel verbose
RUN yarn global add pm2@latest
# Copy only the necessary files
COPY --chown=$USER:$GROUPNAME --from=build /usr/src/app/dist/apps/${APP_NAME} dist
COPY --chown=$USER:$GROUPNAME --from=build /usr/src/app/node_modules node_modules
RUN chown ${USER}:${GROUPNAME} /usr/src/app
RUN chmod 750 /usr/src/app
# Build
USER ${USER}
RUN yarn config set loglevel error
ENTRYPOINT ["pm2-runtime","dist/main.js"]