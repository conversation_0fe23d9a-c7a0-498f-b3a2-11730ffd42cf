import fs from 'fs';

import { AIProjectClient } from '@azure/ai-projects';
import { DefaultAzureCredential } from '@azure/identity';

interface AgentConfig {
  id?: string;
  name: string;
  description: string;
  instruction_file: string;
  model: string;
  temperature?: number;
  topP?: number;
}

// Get JSON config path from command line arguments
const configPath = process.argv[2];
if (!configPath) {
  console.error('Usage: tsx create-azure-ai-agent.ts <path-to-config.json>');
  process.exit(1);
}

// Validate that the file exists
if (!fs.existsSync(configPath)) {
  console.error(`Error: Config file not found: ${configPath}`);
  process.exit(1);
}

// load agentsConfig from specified JSON file
const configJson = JSON.parse(fs.readFileSync(configPath, 'utf8'));
const endpoint = configJson.endpoint;
const agentsConfig: AgentConfig[] = configJson.agents;

if (!endpoint) {
  throw new Error('AI_FOUNDRY_PROJECT_ENDPOINT is not set');
} else {
  console.log(`Using endpoint: ${endpoint}`);
}

const client = new AIProjectClient(endpoint, new DefaultAzureCredential());

(async () => {
  try {
    // Compare agent name with agentsConfig to determine if it is an update or a new agent
    for await (const azureAgent of client.agents.listAgents()) {
      const agentConfig = agentsConfig.find((agent) => agent.name === azureAgent.name);
      if (agentConfig) {
        agentConfig.id = azureAgent.id;
      }
    }

    for (const agentConfig of agentsConfig) {
      // load agent prompt from file
      const prompt = fs.readFileSync(agentConfig.instruction_file, 'utf8');

      if (agentConfig.id) {
        const agent = await client.agents.updateAgent(agentConfig.id, {
          model: agentConfig.model,
          name: agentConfig.name,
          description: agentConfig.description,
          instructions: prompt,
          temperature: agentConfig.temperature,
          topP: agentConfig.topP,
        });
        console.log(`Updated agent ${agent.name} (${agent.id})`);
      } else {
        const agent = await client.agents.createAgent(agentConfig.model, {
        name: agentConfig.name,
        description: agentConfig.description,
        instructions: prompt,
        temperature: agentConfig.temperature,
        topP: agentConfig.topP,
        });
        console.log(`Created agent ${agent.name} (${agent.id})`);
      }
    }
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
})();
