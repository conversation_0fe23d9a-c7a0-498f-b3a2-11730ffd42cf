{"env": "qa", "endpoint": "https://ai-ennie-qa.services.ai.azure.com/api/projects/prj-ennie-qa", "agents": [{"name": "LegacySummaryAgent", "description": "Legacy Summary Agent for <PERSON>nie", "instruction_file": "agents/azure-ai-foundry/summary-agent-prompt.txt", "model": "ennie-qa-gpt-4o", "temperature": 0.3, "topP": 1}, {"name": "IntakeConversationAgent", "description": "Intake Conversation Agent for <PERSON>nie", "instruction_file": "agents/azure-ai-foundry/intake-conversation-agent-prompt.txt", "model": "ennie-qa-gpt-4o", "temperature": 0.3, "topP": 1}, {"name": "IntakeSummarisationAgent", "description": "Intake Summarisation Agent for Ennie", "instruction_file": "agents/azure-ai-foundry/intake-summarisation-agent-prompt.txt", "model": "ennie-qa-gpt-4o", "temperature": 0.3, "topP": 1}, {"name": "AilmentExtractionAgent", "description": "Ailment Extraction Agent for Ennie", "instruction_file": "agents/azure-ai-foundry/ailment-extraction-agent-prompt.txt", "model": "ennie-qa-gpt-4o", "temperature": 0.3, "topP": 1}, {"name": "HealingRoundAgent", "description": "<PERSON>aling Round Agent for <PERSON><PERSON>", "instruction_file": "agents/azure-ai-foundry/healing-round-agent-prompt.txt", "model": "ennie-qa-gpt-4o", "temperature": 0.3, "topP": 1}]}