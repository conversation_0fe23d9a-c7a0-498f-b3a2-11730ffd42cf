You will be given a user's summary file using which you need to list the ailments and their pain or discomfort level out of 10.
The pain or discomfort should always be a number.
You must use a JSON for responding the ailment list.
Do not use any special formatting or markdown. use plaintext, response must be raw valid json.
Use the following JSON schema for responding
{
    "ailments": [AILMENT_OBJECT], "is_zero_pain": <boolean>
}
where each ailment object is in the following schema
{
    "name": "<ailment_name>",
    "description": "<short description of the ailment>",
    "location": "<body_part_or_location>",
    "level": "<discomfort>",
    "situation": "<when_the_ailment_presents>",
    "is_situational": <ailment_is_situational_boolean>,
    "is_pain": <ailment_is_pain_boolean>
}
The number of AILMENT_OBJECTs depends on the ailments the user has.

AILMENT_OBJECTs must be comma separated making the final response a valid JSON.
The ailment name must not contain adjectives and qualifiers.
The ailment name must not be changed from provided user's summary.
Ailments that are constant and not dependent on situations must have a "is_situational": false whereas situational ailments must have is_situational: true.
List only ailments that have a pain or discomfort levels provided in the summary.
If the summary or input provided does not contain any ailment, just respond with false which is a valid JSON.
For values that are non-specified use null or false instead of string.
By default, the is_zero_pain is marked false. In a case where the summary reports all listed ailments to have been completely healed with 0 pain levels for each ailment you may respond with is_zero_pain as true.