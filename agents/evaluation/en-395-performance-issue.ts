/**
 * This script is to test the ailment extraction agent.
 * For details, see https://honestfox.atlassian.net/jira/software/c/projects/EN/boards/203?selectedIssue=EN-395
 */
import dotenv from 'dotenv';

dotenv.config();

import { AIProjectClient } from '@azure/ai-projects';
import {
  MessageStreamEvent,
  DoneEvent,
  MessageDeltaChunk,
  MessageDeltaTextContent,
  AgentRunResponse,
  RunStreamEvent,
} from '@azure/ai-agents';
import { DefaultAzureCredential } from '@azure/identity';

import { processText, IncrementalText } from './process-text';

const endpoint = process.env.AI_FOUNDRY_PROJECT_ENDPOINT || '';
const ailmentExtractionAgentId = process.env.AILMENT_EXTRACTION_ASSISTANT_ID || '';

const client = new AIProjectClient(endpoint, new DefaultAzureCredential());

const priorMessages: ({
  role: 'user' | 'assistant';
  content: string;
}[]) = [
  {
    role: 'user',
    content: "__SUMMARY__:Case Summary: - Ailment: Foggy brain - Duration: Not specified - Pain Level: 7 out of 10 - Type: Not specified - Additional symptoms: Warm leg"
  }
]

async function testAilmentExtractionAgent(agentId: string) {
  const startTime = Date.now();
    try {
      console.log(`[${Date.now()}, +${Date.now() - startTime}ms] Creating agent thread`)
    const thread = await client.agents.threads.create({});
    console.log(`[${Date.now()}, +${Date.now() - startTime}ms] Agent thread ${thread.id} created`)

    for (const message of priorMessages) {
      console.log(`[${Date.now()}, +${Date.now() - startTime}ms] Creating agent message`)
      await client.agents.messages.create(thread.id, message.role, message.content);
      console.log(`[${Date.now()}, +${Date.now() - startTime}ms] Agent message created`)
    }

    console.log(`[${Date.now()}, +${Date.now() - startTime}ms] Creating agent run`)
    const agentRun: AgentRunResponse = client.agents.runs.create(thread.id, agentId)
    console.log(`[${Date.now()}, +${Date.now() - startTime}ms] Agent run created`)
    const streamMessages = await agentRun.stream();
    console.log(`[${Date.now()}, +${Date.now() - startTime}ms] Stream messages created`)
    let output = '';
    const incremental: IncrementalText = {
      latest: '',
      accumulator: [],
      sent: [],
    };
    for await (const eventMessage of streamMessages) {
      switch (eventMessage.event) {
        case RunStreamEvent.ThreadRunCreated: {
          console.log(`[${Date.now()}, +${Date.now() - startTime}ms] Agent run created`)
          break;
        }
        case RunStreamEvent.ThreadRunQueued: {
          console.log(`[${Date.now()}, +${Date.now() - startTime}ms] Agent run queued`)
          break;
        }
        case RunStreamEvent.ThreadRunCompleted: {
          console.log(`[${Date.now()}, +${Date.now() - startTime}ms] Agent run completed`)
          return output;
        }
        case MessageStreamEvent.ThreadMessageDelta: {
          const textDelta = (eventMessage.data as MessageDeltaChunk).delta;
          const textDeltaValue = textDelta.content
            .filter(
              (content): content is MessageDeltaTextContent =>
                content.type === 'text'
            )
            .map((content) => content.text?.value)
            .join('');

          output += textDeltaValue;

          incremental.accumulator = processText('' + textDeltaValue, '');
          if (incremental.accumulator.length > 0) {
            incremental.latest = incremental.accumulator.join(' ').trim();
          }
          break;
        }
        case DoneEvent.Done:
          console.log(`[${Date.now()}, +${Date.now() - startTime}ms] Agent run completed`)
          return output;
      }
    }
  } catch (error) {
    console.error(`[${Date.now()}, +${Date.now() - startTime}ms] Error: ${error}`)
    console.error(error)
  }
}

(async() => {
  const output = await testAilmentExtractionAgent(ailmentExtractionAgentId);
  console.log(`${output}`);
})();
