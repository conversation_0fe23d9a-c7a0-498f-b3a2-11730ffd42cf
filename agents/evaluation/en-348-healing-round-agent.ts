/**
 * This script is to test the healing round agent.
 * For details, see https://honestfox.atlassian.net/jira/software/c/projects/EN/boards/203?selectedIssue=EN-348
 */
import dotenv from 'dotenv';

dotenv.config();

import { AIProjectClient } from '@azure/ai-projects';
import {
  MessageStreamEvent,
  DoneEvent,
  MessageDeltaChunk,
  MessageDeltaTextContent,
} from '@azure/ai-agents';
import { DefaultAzureCredential } from '@azure/identity';

import nlp from 'compromise';

type IncrementalText = {
  latest: string;
  accumulator: string[];
  sent: string[];
};

const endpoint = process.env.AI_FOUNDRY_PROJECT_ENDPOINT || '';
const healingRoundAgentId = process.env.HEALING_ROUND_ASSISTANT_ID || '';

const client = new AIProjectClient(endpoint, new DefaultAzureCredential());

const priorMessages: ({
  role: 'user' | 'assistant';
  content: string;
}[]) = [
  {
    role: 'user',
    content: "__SYSTEM_INPUT__: Case Summary: - Ailment: Pain in the right foot - Duration: Primarily affects in the morning - Pain Level: 7 out of 10 - Type: Heel pain - No additional symptoms or triggers reported. Intake Ailments noted for Patient: name: pain in the right foot | discomfort level: 7"
  },
  {
    role: 'assistant',
    content: ' '
  },
  {
    role: 'user',
    content: "__CONVERSATION__: Question: Hi! What information would you like me to pass onto the test healer? Answer: The pain is going down a little bit. Question: That's great to hear that the pain is decreasing. Is there any other information the test energy healer should know? Answer: No. Question: Thank you for the update. If you need anything else, feel free to reach out. Take care! __CONVERSATION_STATUS__: COMPLETED __CONVERSATION_TYPE__: REPORT_SYMPTOMS"
  },
  {
    role: 'assistant',
    content: 'The pain in the right foot has decreased slightly.'
  },
  {
    role: 'user',
    content: '__GATHER_ROUND_FEEDBACK__'
  },
  {
    role: 'assistant',
    content: '__TO_USER__: Can you share your current pain or discomfort level for the pain in your right foot, which was initially at level 7?'
  },
  {
    role: 'user',
    content: "__CASE_SUMMARY__: Case Summary: - Ailment: Pain in the right foot - Duration: Primarily affects in the morning - Pain Level: 7 out of 10 - Type: Heel pain - No additional symptoms or triggers reported. __CONVERSATION__: Question: Hi! The test healer would like to get an update on your symptoms, has the pain level improved? Answer: Yes. Question: That's great to hear! Could you let me know what your current pain level is? Answer: Five. Question: Thank you for the update. Is there any other information the test energy healer should know? Answer: No. Question: Thank you for the update. Take care! __CONVERSATION_STATUS__: COMPLETED __CONVERSATION_TYPE__: ENERGY_HEALING_ROUND_CHECK_IN"
  }
]

function processText(newText: string, buffer: string): string[] {
  // Append new text to the buffer
  buffer += newText.replace(/\\n[-]*/ig,'');

  // Use Compromise to parse sentences
  const doc = nlp(buffer);
  const sentences = doc.sentences().out('array');
  const completeSentences: string[] = [];

  if (sentences.length === 0) {
    // No complete sentences found yet
    return completeSentences;
  }

  // Determine the position where the last complete sentence ends
  let lastCompleteIndex = 0;

  // Iterate through sentences to determine completeness
  for (let i = 0; i < sentences.length; i++) {
    const sentence = sentences[i].trim();
    // Heuristics to determine if the sentence is complete
    // A simple approach: Check if the sentence ends with a sentence-ending punctuation
    if (/[.!?:]["')\]\\n-]*$/.test(sentence)) {
      completeSentences.push(sentence);
      // Update the last complete index to the end of this sentence in the buffer
      lastCompleteIndex = buffer.indexOf(sentence, lastCompleteIndex) + sentence.length;
    } else {
      // Incomplete sentence detected; stop processing further
      break;
    }
  }

  if (completeSentences.length > 0) {
    // Remove the extracted sentences from the buffer
    buffer = buffer.slice(lastCompleteIndex);
  }

  return completeSentences;
}

async function testHealingRoundAgent(agentId: string) {
  const thread = await client.agents.threads.create({});

  for (const message of priorMessages) {
    await client.agents.messages.create(thread.id, message.role, message.content);
  }

  const streamMessages = await client.agents.runs.create(thread.id, agentId).stream();
  let output = '';
  const incremental: IncrementalText = {
    latest: '',
    accumulator: [],
    sent: [],
  };
  for await (const eventMessage of streamMessages) {
    switch (eventMessage.event) {
      case MessageStreamEvent.ThreadMessageDelta: {
        const textDelta = (eventMessage.data as MessageDeltaChunk).delta;
        const textDeltaValue = textDelta.content
          .filter(
            (content): content is MessageDeltaTextContent =>
              content.type === 'text'
          )
          .map((content) => content.text?.value)
          .join('');

        output += textDeltaValue;

        incremental.accumulator = processText('' + textDeltaValue, '');
        if (incremental.accumulator.length > 0) {
          incremental.latest = incremental.accumulator.join(' ').trim();
        }
        break;
      }
      case DoneEvent.Done:
        return output;
    }
  }
}

(async () => {
  for (let i = 1; i <= 100; i++) {
    const output = await testHealingRoundAgent(healingRoundAgentId);
    console.log(`Run ${i}: ${output}`);
  }
})();
