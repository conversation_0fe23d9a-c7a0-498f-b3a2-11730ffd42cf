import nlp from 'compromise';

export type IncrementalText = {
  latest: string;
  accumulator: string[];
  sent: string[];
};

export function processText(newText: string, buffer: string): string[] {
  // Append new text to the buffer
  buffer += newText.replace(/\\n[-]*/ig,'');

  // Use Compromise to parse sentences
  const doc = nlp(buffer);
  const sentences = doc.sentences().out('array');
  const completeSentences: string[] = [];

  if (sentences.length === 0) {
    // No complete sentences found yet
    return completeSentences;
  }

  // Determine the position where the last complete sentence ends
  let lastCompleteIndex = 0;

  // Iterate through sentences to determine completeness
  for (let i = 0; i < sentences.length; i++) {
    const sentence = sentences[i].trim();
    // Heuristics to determine if the sentence is complete
    // A simple approach: Check if the sentence ends with a sentence-ending punctuation
    if (/[.!?:]["')\]\\n-]*$/.test(sentence)) {
      completeSentences.push(sentence);
      // Update the last complete index to the end of this sentence in the buffer
      lastCompleteIndex = buffer.indexOf(sentence, lastCompleteIndex) + sentence.length;
    } else {
      // Incomplete sentence detected; stop processing further
      break;
    }
  }

  if (completeSentences.length > 0) {
    // Remove the extracted sentences from the buffer
    buffer = buffer.slice(lastCompleteIndex);
  }

  return completeSentences;
}
