{"type": "system", "name": "end_call", "description": "Gracefully conclude conversations when appropriate\n\nCall this function when:\n1. AFTER IN-TAKE DETAILS CONFIRMATION\n- After the user confirm all the details of the in-take session, call this tools.\n- When the call is ended with in-take details confirmed, tell the user that they will be put in queue for an energy healing session.\n- Wish the user get well soon and bid farewell.\n- Do not ask further question.\n\n2. EXPLICIT ENDINGS\n- User says goodbye variants: \"bye,\" \"see you,\" \"that's all,\" etc.\n- User directly declines help: \"no thanks,\" \"I'm good,\" etc.\n- User indicates completion: \"that's what I needed,\" \"all set,\" etc.\n\n3. IMPLICIT ENDINGS\n- User gives minimal/disengaged responses after their needs are met\n- User expresses intention to leave: \"I need to go,\" \"getting late,\" etc.\n- Natural conversation conclusion after all queries are resolved\n\nDO NOT:\n- Call this function during active problem-solving\n- End conversation when user expresses new concerns\n- Use generic closings without acknowledging the specific interaction\n- Continue conversation after user has clearly indicated ending\n- Add \"Let me know if you need anything else\" after user says goodbye. YOU MUST NOT ASK questions at the end of the call, such as \"Is there anything else I can help you with today\" or \"Is there is anything I can help you with, please let me know\".\n\nContext:\nYour role is to gather information from users about their chronic pain condition and its symptoms as part of an energy healing intake process. You will relay these details to the test energy healer who will be working with them. ", "response_timeout_secs": 20, "disable_interruptions": true, "force_pre_tool_speech": false, "assignments": [], "params": {"system_tool_type": "end_call", "transfers": []}}