{"name": "@core_be/source", "version": "0.0.0", "license": "UNLICENSED", "scripts": {"dotenv:push": "npx dotenv-vault@latest push", "dotenv:pull": "npx dotenv-vault@latest pull", "setup": "npm i . && npm docker && npm db", "docker:dev": "docker compose up -d redis database rabbitmq reverse-proxy", "docker": "docker compose up -d", "db": "npx nx run prisma-schema:prisma generate && npx nx run prisma-schema:prisma migrate deploy --name=core", "lint": "npx nx run-many -t lint -p core ai scheduler", "lint:ai": "npx nx lint ai", "lint:core": "npx nx lint core", "lint:scheduler": "npx nx lint scheduler", "build": "npx nx run-many -t build -p core ai scheduler", "build:ai": "npx nx build ai", "build:core": "npx nx build core", "build:scheduler": "npx nx build scheduler", "test": "npx nx run-many -t test -p core ai scheduler", "test:ai": "npx nx test ai", "test:core": "npx nx test core", "test:scheduler": "npx nx test scheduler", "test:integration": "npx nx e2e integration-e2e", "format": "npx nx format --all", "format:ai": "npx nx format ai", "format:core": "npx nx format core", "format:scheduler": "npx nx format scheduler", "start": "npx nx run-many -t serve -p scheduler", "start:ai": "npx nx serve ai", "start:core": "npx nx serve core", "start:scheduler": "npx nx serve scheduler", "ai:deploy": "npx nx deploy ai"}, "prisma": {"schema": "./libs/db/prisma-schema/prisma/schema.prisma"}, "private": true, "dependencies": {"@azure/ai-projects": "^1.0.0-beta.8", "@azure/identity": "^4.10.1", "@azure/openai": "^2.0.0-beta.3", "@elevenlabs/elevenlabs-js": "^2.6.0", "@faker-js/faker": "^9.3.0", "@fastify/basic-auth": "^5.1.1", "@fastify/static": "^7.0.4", "@nest-lab/fastify-multer": "^1.2.0", "@nestjs/axios": "^3.0.2", "@nestjs/cache-manager": "^2.3.0", "@nestjs/class-transformer": "^0.4.0", "@nestjs/class-validator": "^0.13.4", "@nestjs/common": "^10.0.2", "@nestjs/config": "^3.2.2", "@nestjs/core": "^10.0.2", "@nestjs/event-emitter": "^2.1.1", "@nestjs/jwt": "^10.2.0", "@nestjs/microservices": "^10.4.15", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.0.2", "@nestjs/platform-fastify": "^10.3.10", "@nestjs/platform-socket.io": "^10.4.7", "@nestjs/schedule": "^4.1.1", "@nestjs/swagger": "^7.3.1", "@nestjs/websockets": "^10.4.7", "@prisma/client": "^5.19.1", "@sendgrid/mail": "^8.1.3", "amqp-connection-manager": "^4.1.14", "amqplib": "^0.10.5", "axios": "^1.6.0", "bcrypt": "^6.0.0", "cache-manager": "^5.7.6", "cache-manager-redis-store": "^3.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "compromise": "^14.14.4", "csv-parser": "^3.0.0", "dd-trace": "^5.32.0", "fastify": "^4.28.1", "firebase-admin": "^12.3.0", "helmet": "^7.1.0", "hyperid": "^3.3.0", "ioredis": "^5.4.2", "joi": "^17.13.3", "multer": "^1.4.5-lts.1", "nestjs-ddtrace": "^5.0.0", "nestjs-pino": "^4.1.0", "openai": "^4.71.1", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pino-http": "^10.3.0", "pino-pretty": "^13.0.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.0", "socket.io": "^4.8.1", "swagger-ui-express": "^5.0.0", "tslib": "^2.3.0"}, "devDependencies": {"@nestjs/schematics": "^10.0.1", "@nestjs/testing": "^10.0.2", "@nx/eslint": "18.3.4", "@nx/eslint-plugin": "18.3.4", "@nx/jest": "18.3.5", "@nx/js": "18.3.5", "@nx/nest": "18.3.5", "@nx/node": "18.3.5", "@nx/web": "18.3.5", "@nx/webpack": "18.3.5", "@nx/workspace": "18.3.4", "@swc-node/register": "~1.8.0", "@swc/core": "~1.3.85", "@swc/helpers": "~0.5.2", "@types/amqplib": "^0.10.5", "@types/bcrypt": "^5.0.2", "@types/cron": "^2.4.0", "@types/jest": "^30.0.0", "@types/multer": "^1.4.11", "@types/node": "~18.16.9", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@typescript-eslint/eslint-plugin": "^7.3.0", "@typescript-eslint/parser": "^7.3.0", "eslint": "~8.57.0", "eslint-config-prettier": "^9.0.0", "jest": "^29.7.0", "jest-environment-node": "^29.4.1", "nx": "^18.3.4", "prettier": "^2.6.2", "prisma": "^5.19.1", "ts-jest": "^29.1.0", "ts-node": "10.9.1", "tsx": "^4.20.3", "typescript": "~5.4.2", "webpack-cli": "^5.1.4"}, "optionalDependencies": {"@nx/nx-darwin-arm64": "18.0.4", "@nx/nx-darwin-x64": "18.0.4", "@nx/nx-linux-x64-gnu": "18.0.4", "@nx/nx-win32-x64-msvc": "18.0.4"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}