# Healthis BE

<a alt="Nx logo" href="https://nx.dev" target="_blank" rel="noreferrer"><img src="https://raw.githubusercontent.com/nrwl/nx/master/images/nx-logo.png" width="45"></a>

✨ **This workspace has been generated by [Nx, Smart Monorepos · Fast CI.](https://nx.dev)** ✨

## Integrate with editors

Enhance your Nx experience by installing [Nx Console](https://nx.dev/nx-console) for your favorite editor. Nx Console
provides an interactive UI to view your projects, run tasks, generate code, and more! Available for VSCode, IntelliJ and
comes with a LSP for Vim users.

# HealthThis BE - Quick Start Guide for Newbies

This guide provides a simplified, step-by-step process to get the HealthThis BE project up and running, even if you're new to development.

## Prerequisites

Before you begin, ensure you have the following installed on your system:

*   **Node.js and npm:**  You can download them from [https://nodejs.org/](https://nodejs.org/).  npm usually comes bundled with Node.js.
*   **Docker Desktop:**  Download and install Docker Desktop from [https://www.docker.com/products/docker-desktop/](https://www.docker.com/products/docker-desktop/).  Make sure Docker Desktop is running after installation.
*   **VSCode:**  Download and install VSCode from [https://code.visualstudio.com/](https://code.visualstudio.com/)

## Local Setup Instructions

Follow these steps to set up the project locally:

1.  **Clone the Repository:**  Open your terminal or command prompt and navigate to the directory where you want to store the project.  Then, clone the HealthThis BE repository using Git:

    ```bash
    git clone <repository URL>  # Replace <repository URL> with the actual URL of the repository
    cd <healththis-be directory name> # Replace <healththis-be directory name> with the actual directory name
    ```

2.  **Install Dependencies:**  Navigate to the project directory in your terminal. Install the necessary npm packages.

    ```bash
    nvm use
    yarn
    ```

3.  **Create ENV:**

    ```bash
    npx dotenv-vault@latest pull
    ```

3.  **Run Docker Setup:**  This will start the PostgreSQL database in a Docker container.

    ```bash
    yarn docker:dev
    ```

    **Important:** Ensure that Docker Desktop is running *before* executing this command.

5.  **Seed the Database:**  This command prepares your database.

    ```bash
    yarn db
    ```

    **Important:** Note, if this fails due to credentials, it may be due to docker builds or prisma using an outdated env file
    **Workaround:** Test connection to the DB with PG Admin, if your env and docker container are correct, delete the folder `/db-data` and re-run the command

8.  **Start the project!**

    ```bash
    yarn start
    ```

This will start the HealthThis BE application.  You should see messages indicating that the application is running and listening on a specific port (usually `http://localhost:3000` or `http://localhost:4000`.)

## VS Code - Optional Setup (But Recommended)

To enhance your development experience within VS Code:

1.  **Install Recommended Extensions:** VS Code might prompt you to install recommended extensions for this project.  Accept these suggestions for better code completion, debugging, and other features.

2.  **Trust the Workspace:** VS Code may ask if you trust the workspace. Click "Yes, I trust the authors" to enable workspace-specific settings and features.

## Verify the Setup

Once the application is running:

*   Open your web browser.
*   Go to the specified address (e.g., `http://localhost:3000` or `http://localhost:4000`).

You should see the HealthThis BE application's user interface or API response, depending on what the project provides.

## Common Issues and Troubleshooting

*   **Docker Not Running:** If you encounter errors related to the database, ensure that Docker Desktop is running and the `npm run docker` command executed successfully.

*   **Port Conflicts:** If the specified port (3000, 4000, or 8000) is already in use by another application, you'll need to change the port in the `.env` file. Find the `PORT` environment variable and change its value.

*   **Permission Issues:** On some operating systems, you might encounter permission issues during installation. Try running the commands as an administrator or using `sudo` (on Linux/macOS).

*   **Template.env is not valid:** The reason for this is that the `.template.env` can contain sensitive information.  so when its checked in it becomes a security risk.
Thus the dev needs to recreate the `template.env` and add the correct secrets.

 If you face any other issues, consult the project's documentation or ask for help from the project's community (if one exists).

## VSCODE Docker Development Environment

You can use vscode dev container to easily start developing

1. set the env database_url to host.docker.internal instead of localhost.
2. after that you may `npx prisma migrate dev` as needed.
3. The password for the node user on the dev container is `Sunvera*Dev*123`

## Notes for Production Deployment

1. We are supposed to target `prod` on the DOCKERFILE for production use cases.
2. The docker build process requires environment variables to proceed, refer `docker-compose.yaml`
3. Once you have the docker image, upload the image to a repository like Elastic Container Registry or Azure Container Repository
4. Use Kubernetes or Elastic Container Service to deploy the images.
5. There are 3 services for this project core, ai and scheduler.
6. These connect to a database and have to be on the same subnet. Env variables to be provided to reach the DB
7. Maintain a .env for the build and operation - Prerably move secrets to Azure Key Store / Vault

## Nx Commands

Refer [Nx Readme](./Nx.md)

## Start the application

1. Run `npm run start` to start the development server. Happy coding!
2. You might need to run `npm run docker` before you start the application

## Build for production

Run `npx nx build core` to build the application. The build artifacts are stored in the output directory (e.g. `dist/` or `build/`), ready to be deployed.


## Deploying fresh to Azure end-to-end, high level notes for future elaboration

1. Terraform
2. Helm install cert-manager and ingress-nginx
3. Update kubernetes load balancer to use `/healthz` for health probes
4. Create credentials for github
4. CI/CD should do the rest
